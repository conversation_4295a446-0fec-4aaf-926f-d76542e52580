/**
 * Dashboard TDD Tests
 * Test-Driven Development tests for dashboard functionality
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Dashboard utility functions to be implemented
interface DashboardMetrics {
  total_cves: number;
  critical_cves: number;
  high_cves: number;
  medium_cves: number;
  low_cves: number;
  compliance_score: number;
  trends: {
    cves_this_week: number;
    cves_last_week: number;
  };
}

interface VulnerabilityTrend {
  date: string;
  critical: number;
  high: number;
  medium: number;
  low: number;
}

// Functions to be implemented based on TDD
class DashboardCalculator {
  static calculateComplianceScore(metrics: DashboardMetrics): number {
    const totalCves = metrics.total_cves;
    const criticalAndHigh = metrics.critical_cves + metrics.high_cves;
    
    if (totalCves === 0) return 100;
    
    const riskScore = (criticalAndHigh / totalCves) * 100;
    return Math.max(0, Math.min(100, 100 - riskScore));
  }

  static calculateTrendPercentage(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }

  static categorizeRiskLevel(score: number): 'low' | 'medium' | 'high' | 'critical' {
    if (score >= 9.0) return 'critical';
    if (score >= 7.0) return 'high';
    if (score >= 4.0) return 'medium';
    return 'low';
  }

  static aggregateVulnerabilityTrends(trends: VulnerabilityTrend[]): {
    totalIncrease: number;
    criticalIncrease: number;
    averageDaily: number;
  } {
    if (trends.length < 2) {
      return { totalIncrease: 0, criticalIncrease: 0, averageDaily: 0 };
    }

    const first = trends[0];
    const last = trends[trends.length - 1];
    
    const totalFirst = first.critical + first.high + first.medium + first.low;
    const totalLast = last.critical + last.high + last.medium + last.low;
    
    const totalIncrease = totalLast - totalFirst;
    const criticalIncrease = last.critical - first.critical;
    const averageDaily = totalIncrease / (trends.length - 1);

    return { totalIncrease, criticalIncrease, averageDaily };
  }

  static filterCriticalVulnerabilities(vulnerabilities: any[], threshold: number = 9.0): any[] {
    return vulnerabilities.filter(vuln => vuln.score >= threshold);
  }

  static calculateApplicationRisk(applications: any[]): {
    totalRisk: number;
    highRiskApps: number;
    averageRisk: number;
  } {
    if (applications.length === 0) {
      return { totalRisk: 0, highRiskApps: 0, averageRisk: 0 };
    }

    const risks = applications.map(app => app.risk_score || 0);
    const totalRisk = risks.reduce((sum, risk) => sum + risk, 0);
    const averageRisk = totalRisk / applications.length;
    const highRiskApps = applications.filter(app => (app.risk_score || 0) >= 7.0).length;

    return { totalRisk, averageRisk, highRiskApps };
  }
}

describe('Dashboard TDD Tests', () => {
  describe('Compliance Score Calculation', () => {
    it('should return 100% compliance for zero CVEs', () => {
      const metrics: DashboardMetrics = {
        total_cves: 0,
        critical_cves: 0,
        high_cves: 0,
        medium_cves: 0,
        low_cves: 0,
        compliance_score: 0,
        trends: { cves_this_week: 0, cves_last_week: 0 }
      };

      const score = DashboardCalculator.calculateComplianceScore(metrics);
      expect(score).toBe(100);
    });

    it('should calculate compliance score based on critical and high CVEs', () => {
      const metrics: DashboardMetrics = {
        total_cves: 100,
        critical_cves: 5,
        high_cves: 15,
        medium_cves: 40,
        low_cves: 40,
        compliance_score: 0,
        trends: { cves_this_week: 10, cves_last_week: 8 }
      };

      const score = DashboardCalculator.calculateComplianceScore(metrics);
      expect(score).toBe(80); // 20% critical+high = 80% compliance
    });

    it('should return 0% compliance for all critical CVEs', () => {
      const metrics: DashboardMetrics = {
        total_cves: 50,
        critical_cves: 50,
        high_cves: 0,
        medium_cves: 0,
        low_cves: 0,
        compliance_score: 0,
        trends: { cves_this_week: 5, cves_last_week: 3 }
      };

      const score = DashboardCalculator.calculateComplianceScore(metrics);
      expect(score).toBe(0);
    });

    it('should handle edge case with more critical+high than total', () => {
      const metrics: DashboardMetrics = {
        total_cves: 10,
        critical_cves: 8,
        high_cves: 5, // This would exceed total, but function should handle it
        medium_cves: 0,
        low_cves: 0,
        compliance_score: 0,
        trends: { cves_this_week: 2, cves_last_week: 1 }
      };

      const score = DashboardCalculator.calculateComplianceScore(metrics);
      expect(score).toBeGreaterThanOrEqual(0);
      expect(score).toBeLessThanOrEqual(100);
    });
  });

  describe('Trend Percentage Calculation', () => {
    it('should calculate positive trend percentage', () => {
      const percentage = DashboardCalculator.calculateTrendPercentage(15, 10);
      expect(percentage).toBe(50);
    });

    it('should calculate negative trend percentage', () => {
      const percentage = DashboardCalculator.calculateTrendPercentage(8, 12);
      expect(percentage).toBeCloseTo(-33.33, 1);
    });

    it('should handle zero previous value', () => {
      const percentage = DashboardCalculator.calculateTrendPercentage(5, 0);
      expect(percentage).toBe(100);
    });

    it('should handle zero current value with non-zero previous', () => {
      const percentage = DashboardCalculator.calculateTrendPercentage(0, 10);
      expect(percentage).toBe(-100);
    });

    it('should handle both values being zero', () => {
      const percentage = DashboardCalculator.calculateTrendPercentage(0, 0);
      expect(percentage).toBe(0);
    });
  });

  describe('Risk Level Categorization', () => {
    it('should categorize critical risk correctly', () => {
      expect(DashboardCalculator.categorizeRiskLevel(9.8)).toBe('critical');
      expect(DashboardCalculator.categorizeRiskLevel(9.0)).toBe('critical');
    });

    it('should categorize high risk correctly', () => {
      expect(DashboardCalculator.categorizeRiskLevel(8.5)).toBe('high');
      expect(DashboardCalculator.categorizeRiskLevel(7.0)).toBe('high');
    });

    it('should categorize medium risk correctly', () => {
      expect(DashboardCalculator.categorizeRiskLevel(6.5)).toBe('medium');
      expect(DashboardCalculator.categorizeRiskLevel(4.0)).toBe('medium');
    });

    it('should categorize low risk correctly', () => {
      expect(DashboardCalculator.categorizeRiskLevel(3.5)).toBe('low');
      expect(DashboardCalculator.categorizeRiskLevel(0.0)).toBe('low');
    });

    it('should handle edge cases', () => {
      expect(DashboardCalculator.categorizeRiskLevel(6.9)).toBe('medium');
      expect(DashboardCalculator.categorizeRiskLevel(8.9)).toBe('high');
    });
  });

  describe('Vulnerability Trends Aggregation', () => {
    it('should aggregate trends correctly', () => {
      const trends: VulnerabilityTrend[] = [
        { date: '2024-01-15', critical: 20, high: 145, medium: 478, low: 567 },
        { date: '2024-01-16', critical: 21, high: 148, medium: 482, low: 571 },
        { date: '2024-01-17', critical: 23, high: 155, medium: 487, low: 577 }
      ];

      const result = DashboardCalculator.aggregateVulnerabilityTrends(trends);
      
      expect(result.totalIncrease).toBe(27); // (23+155+487+577) - (20+145+478+567)
      expect(result.criticalIncrease).toBe(3); // 23 - 20
      expect(result.averageDaily).toBe(13.5); // 27 / 2 days
    });

    it('should handle single trend data point', () => {
      const trends: VulnerabilityTrend[] = [
        { date: '2024-01-15', critical: 20, high: 145, medium: 478, low: 567 }
      ];

      const result = DashboardCalculator.aggregateVulnerabilityTrends(trends);
      
      expect(result.totalIncrease).toBe(0);
      expect(result.criticalIncrease).toBe(0);
      expect(result.averageDaily).toBe(0);
    });

    it('should handle empty trends array', () => {
      const trends: VulnerabilityTrend[] = [];

      const result = DashboardCalculator.aggregateVulnerabilityTrends(trends);
      
      expect(result.totalIncrease).toBe(0);
      expect(result.criticalIncrease).toBe(0);
      expect(result.averageDaily).toBe(0);
    });
  });

  describe('Critical Vulnerabilities Filtering', () => {
    const vulnerabilities = [
      { id: 'CVE-2024-0001', score: 9.8, severity: 'CRITICAL' },
      { id: 'CVE-2024-0002', score: 8.5, severity: 'HIGH' },
      { id: 'CVE-2024-0003', score: 9.2, severity: 'CRITICAL' },
      { id: 'CVE-2024-0004', score: 6.5, severity: 'MEDIUM' },
      { id: 'CVE-2024-0005', score: 9.0, severity: 'CRITICAL' }
    ];

    it('should filter critical vulnerabilities with default threshold', () => {
      const critical = DashboardCalculator.filterCriticalVulnerabilities(vulnerabilities);
      
      expect(critical).toHaveLength(3);
      expect(critical.every(vuln => vuln.score >= 9.0)).toBe(true);
    });

    it('should filter with custom threshold', () => {
      const critical = DashboardCalculator.filterCriticalVulnerabilities(vulnerabilities, 8.0);
      
      expect(critical).toHaveLength(4);
      expect(critical.every(vuln => vuln.score >= 8.0)).toBe(true);
    });

    it('should return empty array when no vulnerabilities meet threshold', () => {
      const critical = DashboardCalculator.filterCriticalVulnerabilities(vulnerabilities, 10.0);
      
      expect(critical).toHaveLength(0);
    });

    it('should handle empty vulnerabilities array', () => {
      const critical = DashboardCalculator.filterCriticalVulnerabilities([]);
      
      expect(critical).toHaveLength(0);
    });
  });

  describe('Application Risk Calculation', () => {
    const applications = [
      { id: '1', name: 'App 1', risk_score: 8.5 },
      { id: '2', name: 'App 2', risk_score: 6.2 },
      { id: '3', name: 'App 3', risk_score: 9.1 },
      { id: '4', name: 'App 4', risk_score: 4.8 },
      { id: '5', name: 'App 5', risk_score: 7.3 }
    ];

    it('should calculate application risk metrics correctly', () => {
      const result = DashboardCalculator.calculateApplicationRisk(applications);
      
      expect(result.totalRisk).toBe(35.9);
      expect(result.averageRisk).toBeCloseTo(7.18, 2);
      expect(result.highRiskApps).toBe(3); // Apps with score >= 7.0
    });

    it('should handle applications without risk scores', () => {
      const appsWithoutRisk = [
        { id: '1', name: 'App 1' },
        { id: '2', name: 'App 2', risk_score: 8.0 }
      ];

      const result = DashboardCalculator.calculateApplicationRisk(appsWithoutRisk);
      
      expect(result.totalRisk).toBe(8.0);
      expect(result.averageRisk).toBe(4.0);
      expect(result.highRiskApps).toBe(1);
    });

    it('should handle empty applications array', () => {
      const result = DashboardCalculator.calculateApplicationRisk([]);
      
      expect(result.totalRisk).toBe(0);
      expect(result.averageRisk).toBe(0);
      expect(result.highRiskApps).toBe(0);
    });
  });

  describe('Integration Tests', () => {
    it('should calculate comprehensive dashboard metrics', () => {
      const metrics: DashboardMetrics = {
        total_cves: 200,
        critical_cves: 10,
        high_cves: 30,
        medium_cves: 80,
        low_cves: 80,
        compliance_score: 0,
        trends: { cves_this_week: 25, cves_last_week: 20 }
      };

      const complianceScore = DashboardCalculator.calculateComplianceScore(metrics);
      const trendPercentage = DashboardCalculator.calculateTrendPercentage(
        metrics.trends.cves_this_week,
        metrics.trends.cves_last_week
      );

      expect(complianceScore).toBe(80); // 20% critical+high = 80% compliance
      expect(trendPercentage).toBe(25); // 25% increase
    });

    it('should handle real-world data scenarios', () => {
      const vulnerabilities = [
        { id: 'CVE-2024-0001', score: 9.8 },
        { id: 'CVE-2024-0002', score: 8.5 },
        { id: 'CVE-2024-0003', score: 7.2 }
      ];

      const applications = [
        { id: '1', risk_score: 8.5 },
        { id: '2', risk_score: 6.0 }
      ];

      const criticalVulns = DashboardCalculator.filterCriticalVulnerabilities(vulnerabilities);
      const appRisk = DashboardCalculator.calculateApplicationRisk(applications);

      expect(criticalVulns).toHaveLength(1);
      expect(appRisk.highRiskApps).toBe(1);
      expect(appRisk.averageRisk).toBe(7.25);
    });
  });
});
