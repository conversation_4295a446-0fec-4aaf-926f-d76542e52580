/**
 * Dashboard Playwright+Behave Step Definitions
 * Integration of Playwright E2E tests with Behave BDD scenarios
 */

import { Given, When, Then, Before, After } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { <PERSON>, Browser, BrowserContext } from '@playwright/test';
import { chromium } from '@playwright/test';

// World context for sharing state between steps
interface DashboardWorld {
  browser: Browser;
  context: BrowserContext;
  page: Page;
  dashboardData: any;
  currentUser: any;
}

let world: DashboardWorld;

// Test data
const TEST_USERS = {
  security_analyst: {
    email: '<EMAIL>',
    password: 'password123',
    role: 'security_analyst'
  },
  admin: {
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin'
  }
};

const MOCK_DASHBOARD_DATA = {
  metrics: {
    total_cves: 1247,
    critical_cves: 23,
    high_cves: 156,
    medium_cves: 489,
    low_cves: 579,
    total_applications: 45,
    vulnerable_applications: 12,
    compliance_score: 87.5,
    last_updated: '2024-01-22T10:30:00Z'
  },
  trends: [
    { date: '2024-01-15', critical: 20, high: 145, medium: 478, low: 567 },
    { date: '2024-01-16', critical: 21, high: 148, medium: 482, low: 571 },
    { date: '2024-01-17', critical: 22, high: 152, medium: 485, low: 575 }
  ],
  vulnerabilities: [
    {
      id: 'CVE-2024-0001',
      severity: 'CRITICAL',
      score: 9.8,
      affected_apps: 3,
      description: 'Critical remote code execution vulnerability'
    },
    {
      id: 'CVE-2024-0002',
      severity: 'HIGH',
      score: 8.5,
      affected_apps: 5,
      description: 'High severity privilege escalation'
    }
  ],
  activities: [
    {
      id: '1',
      type: 'cve_discovered',
      description: 'New critical CVE discovered',
      timestamp: '2024-01-22T09:15:00Z',
      user: 'system'
    },
    {
      id: '2',
      type: 'application_updated',
      description: 'Customer Portal updated to v2.1.0',
      timestamp: '2024-01-22T08:30:00Z',
      user: '<EMAIL>'
    }
  ]
};

// Setup and teardown
Before(async function() {
  world = {} as DashboardWorld;
  world.browser = await chromium.launch({ headless: true });
  world.context = await world.browser.newContext();
  world.page = await world.context.newPage();
  
  // Setup API mocking
  await setupApiMocks(world.page);
});

After(async function() {
  if (world.page) await world.page.close();
  if (world.context) await world.context.close();
  if (world.browser) await world.browser.close();
});

async function setupApiMocks(page: Page) {
  const API_BASE = 'http://localhost:8001/api/v1';
  
  // Mock dashboard metrics
  await page.route(`${API_BASE}/dashboard/metrics`, async route => {
    await route.fulfill({
      json: MOCK_DASHBOARD_DATA.metrics
    });
  });

  // Mock trends data
  await page.route(`${API_BASE}/dashboard/trends*`, async route => {
    await route.fulfill({
      json: MOCK_DASHBOARD_DATA.trends
    });
  });

  // Mock vulnerabilities
  await page.route(`${API_BASE}/dashboard/vulnerabilities*`, async route => {
    await route.fulfill({
      json: MOCK_DASHBOARD_DATA.vulnerabilities
    });
  });

  // Mock activities
  await page.route(`${API_BASE}/dashboard/activities*`, async route => {
    await route.fulfill({
      json: MOCK_DASHBOARD_DATA.activities
    });
  });

  // Mock authentication
  await page.route(`${API_BASE}/auth/login`, async route => {
    const request = route.request();
    const postData = request.postData();
    
    if (postData?.includes('<EMAIL>')) {
      await route.fulfill({
        json: {
          access_token: 'mock-token',
          token_type: 'bearer',
          user: TEST_USERS.security_analyst
        }
      });
    } else {
      await route.fulfill({
        status: 401,
        json: { error: 'Invalid credentials' }
      });
    }
  });
}

// Step definitions

// Background steps
Given('I am logged in as a security analyst', async function() {
  world.currentUser = TEST_USERS.security_analyst;
  
  await world.page.goto('/login');
  await world.page.fill('[data-testid="email-input"]', world.currentUser.email);
  await world.page.fill('[data-testid="password-input"]', world.currentUser.password);
  await world.page.click('[data-testid="login-button"]');
  await world.page.waitForURL('/dashboard');
});

Given('I have access to the dashboard', async function() {
  // Verify user has dashboard access
  await expect(world.page.getByTestId('dashboard-container')).toBeVisible();
});

// Data setup steps
Given('the system has vulnerability data', async function() {
  world.dashboardData = MOCK_DASHBOARD_DATA;
  // Data is already mocked in setupApiMocks
});

Given('the system has historical vulnerability data', async function() {
  // Historical data is included in trends mock
  expect(MOCK_DASHBOARD_DATA.trends.length).toBeGreaterThan(1);
});

Given('there are critical vulnerabilities in the system', async function() {
  const criticalVulns = MOCK_DASHBOARD_DATA.vulnerabilities.filter(
    v => v.severity === 'CRITICAL'
  );
  expect(criticalVulns.length).toBeGreaterThan(0);
});

Given('there have been recent security activities', async function() {
  expect(MOCK_DASHBOARD_DATA.activities.length).toBeGreaterThan(0);
});

Given('there are applications in the portfolio', async function() {
  expect(MOCK_DASHBOARD_DATA.metrics.total_applications).toBeGreaterThan(0);
});

// Navigation steps
When('I navigate to the dashboard', async function() {
  await world.page.goto('/dashboard');
  await world.page.waitForLoadState('networkidle');
});

When('I view the dashboard trends section', async function() {
  await expect(world.page.getByTestId('vulnerability-trends-section')).toBeVisible();
});

When('I view the dashboard', async function() {
  await world.page.waitForSelector('[data-testid="dashboard-container"]');
});

When('I view the application metrics section', async function() {
  await expect(world.page.getByTestId('application-metrics-section')).toBeVisible();
});

// Assertion steps
Then('I should see the total number of CVEs', async function() {
  await expect(world.page.getByText('1,247')).toBeVisible();
});

Then('I should see the count of critical vulnerabilities', async function() {
  await expect(world.page.getByText('23')).toBeVisible();
});

Then('I should see the count of high severity vulnerabilities', async function() {
  await expect(world.page.getByText('156')).toBeVisible();
});

Then('I should see the count of medium severity vulnerabilities', async function() {
  await expect(world.page.getByText('489')).toBeVisible();
});

Then('I should see the count of low severity vulnerabilities', async function() {
  await expect(world.page.getByText('579')).toBeVisible();
});

Then('I should see the overall compliance score', async function() {
  await expect(world.page.getByText('87.5%')).toBeVisible();
});

Then('I should see a trend chart showing vulnerability changes over time', async function() {
  await expect(world.page.getByTestId('vulnerability-trends-chart')).toBeVisible();
});

Then('I should see the percentage change from the previous period', async function() {
  await expect(world.page.getByTestId('trend-percentage')).toBeVisible();
});

Then('I should see trend indicators \\(up\\/down arrows\\)', async function() {
  const trendIndicator = world.page.getByTestId('trend-indicator');
  await expect(trendIndicator).toBeVisible();
  
  // Should have either up or down arrow
  const hasUpArrow = await trendIndicator.locator('[data-icon="arrow-up"]').isVisible();
  const hasDownArrow = await trendIndicator.locator('[data-icon="arrow-down"]').isVisible();
  expect(hasUpArrow || hasDownArrow).toBe(true);
});

Then('the trend data should be accurate and up-to-date', async function() {
  // Verify last updated timestamp
  await expect(world.page.getByTestId('last-updated')).toBeVisible();
  
  // Verify data matches expected values
  const chartData = await world.page.getByTestId('vulnerability-trends-chart').getAttribute('data-values');
  expect(chartData).toBeTruthy();
});

Then('I should see a list of top critical vulnerabilities', async function() {
  await expect(world.page.getByTestId('top-vulnerabilities-list')).toBeVisible();
  await expect(world.page.getByText('CVE-2024-0001')).toBeVisible();
});

Then('each vulnerability should show its CVE ID', async function() {
  await expect(world.page.getByText('CVE-2024-0001')).toBeVisible();
});

Then('each vulnerability should show its CVSS score', async function() {
  await expect(world.page.getByText('9.8')).toBeVisible();
});

Then('each vulnerability should show the number of affected applications', async function() {
  await expect(world.page.getByText('3 apps affected')).toBeVisible();
});

Then('each vulnerability should show a brief description', async function() {
  await expect(world.page.getByText('Critical remote code execution vulnerability')).toBeVisible();
});

Then('I should see a list of recent activities', async function() {
  await expect(world.page.getByTestId('recent-activities-list')).toBeVisible();
});

Then('each activity should show the activity type', async function() {
  await expect(world.page.getByTestId('activity-type')).toBeVisible();
});

Then('each activity should show a description', async function() {
  await expect(world.page.getByText('New critical CVE discovered')).toBeVisible();
});

Then('each activity should show the timestamp', async function() {
  await expect(world.page.getByTestId('activity-timestamp')).toBeVisible();
});

Then('each activity should show the user who performed it', async function() {
  await expect(world.page.getByTestId('activity-user')).toBeVisible();
});

Then('activities should be ordered by most recent first', async function() {
  const activities = world.page.getByTestId('activity-item');
  const count = await activities.count();
  
  if (count > 1) {
    const firstTimestamp = await activities.first().getByTestId('activity-timestamp').textContent();
    const secondTimestamp = await activities.nth(1).getByTestId('activity-timestamp').textContent();
    
    // First should be more recent (this is a simplified check)
    expect(firstTimestamp).toBeTruthy();
    expect(secondTimestamp).toBeTruthy();
  }
});

Then('I should see the total number of applications', async function() {
  await expect(world.page.getByText('45')).toBeVisible();
});

Then('I should see applications grouped by environment', async function() {
  await expect(world.page.getByTestId('apps-by-environment')).toBeVisible();
});

Then('I should see applications grouped by criticality level', async function() {
  await expect(world.page.getByTestId('apps-by-criticality')).toBeVisible();
});

Then('I should see the number of vulnerable applications', async function() {
  await expect(world.page.getByText('12')).toBeVisible();
});

Then('I should see compliance status breakdown', async function() {
  await expect(world.page.getByTestId('compliance-breakdown')).toBeVisible();
});

// Real-time updates
When('new vulnerability data becomes available', async function() {
  // Simulate new data by updating the mock
  await world.page.route('**/dashboard/metrics', async route => {
    await route.fulfill({
      json: {
        ...MOCK_DASHBOARD_DATA.metrics,
        total_cves: 1250, // Updated value
        critical_cves: 25  // Updated value
      }
    });
  });
  
  // Trigger update
  await world.page.evaluate(() => {
    window.dispatchEvent(new CustomEvent('dashboard-update'));
  });
});

Then('the dashboard metrics should update automatically', async function() {
  await expect(world.page.getByText('1,250')).toBeVisible();
  await expect(world.page.getByText('25')).toBeVisible();
});

Then('I should see a notification about the update', async function() {
  await expect(world.page.getByTestId('update-notification')).toBeVisible();
});

Then('the last updated timestamp should reflect the current time', async function() {
  const timestamp = await world.page.getByTestId('last-updated').textContent();
  expect(timestamp).toContain('ago'); // Should show relative time
});

// Performance tests
Given('the system contains over {int} CVEs', async function(count: number) {
  // Mock large dataset
  await world.page.route('**/dashboard/metrics', async route => {
    await route.fulfill({
      json: {
        ...MOCK_DASHBOARD_DATA.metrics,
        total_cves: count
      }
    });
  });
});

Given('there are more than {int} applications', async function(count: number) {
  await world.page.route('**/dashboard/metrics', async route => {
    await route.fulfill({
      json: {
        ...MOCK_DASHBOARD_DATA.metrics,
        total_applications: count
      }
    });
  });
});

When('I load the dashboard', async function() {
  const startTime = Date.now();
  await world.page.goto('/dashboard');
  await world.page.waitForLoadState('networkidle');
  world.loadTime = Date.now() - startTime;
});

Then('the page should load within {int} seconds', async function(seconds: number) {
  expect(world.loadTime).toBeLessThan(seconds * 1000);
});

Then('all metrics should be displayed correctly', async function() {
  await expect(world.page.getByTestId('total-cves-card')).toBeVisible();
  await expect(world.page.getByTestId('critical-cves-card')).toBeVisible();
  await expect(world.page.getByTestId('compliance-score-card')).toBeVisible();
});

Then('the interface should remain responsive', async function() {
  // Test responsiveness by clicking elements
  await world.page.click('[data-testid="critical-cves-card"]');
  await world.page.waitForTimeout(100); // Small delay to test responsiveness
  
  // Should be able to interact without lag
  expect(true).toBe(true); // If we get here, interface is responsive
});

export { world };
