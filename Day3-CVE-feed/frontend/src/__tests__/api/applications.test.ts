/**
 * Application Management API Tests
 * Comprehensive API testing for application management endpoints
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { rest } from 'msw';
import { setupServer } from 'msw/node';

// Mock data
const mockApplications = [
  {
    id: '1',
    name: 'Customer Portal',
    description: 'Main customer-facing web application',
    environment: 'production',
    criticality: 'high',
    owner: '<EMAIL>',
    version: '2.1.0',
    last_scan: '2024-01-22T10:30:00Z',
    vulnerability_count: 5,
    risk_score: 7.2,
    compliance_status: 'compliant',
    technologies: ['React', 'Node.js', 'PostgreSQL'],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-22T10:30:00Z'
  },
  {
    id: '2',
    name: '<PERSON>min Dashboard',
    description: 'Internal administration interface',
    environment: 'production',
    criticality: 'critical',
    owner: '<EMAIL>',
    version: '1.5.2',
    last_scan: '2024-01-22T09:15:00Z',
    vulnerability_count: 12,
    risk_score: 8.5,
    compliance_status: 'non_compliant',
    technologies: ['Vue.js', 'Python', 'Redis'],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-22T09:15:00Z'
  }
];

const mockApplicationDetails = {
  ...mockApplications[0],
  vulnerabilities: [
    {
      id: 'CVE-2024-0001',
      severity: 'HIGH',
      score: 8.5,
      status: 'open',
      discovered_at: '2024-01-20T10:00:00Z'
    },
    {
      id: 'CVE-2024-0002',
      severity: 'MEDIUM',
      score: 6.2,
      status: 'in_progress',
      discovered_at: '2024-01-19T15:30:00Z'
    }
  ],
  dependencies: [
    {
      name: 'react',
      version: '18.2.0',
      type: 'direct',
      vulnerabilities: 0
    },
    {
      name: 'lodash',
      version: '4.17.20',
      type: 'indirect',
      vulnerabilities: 1
    }
  ],
  scan_history: [
    {
      id: 'scan-1',
      timestamp: '2024-01-22T10:30:00Z',
      vulnerabilities_found: 5,
      status: 'completed'
    },
    {
      id: 'scan-2',
      timestamp: '2024-01-21T10:30:00Z',
      vulnerabilities_found: 7,
      status: 'completed'
    }
  ]
};

const mockCreateApplication = {
  name: 'New Application',
  description: 'Test application',
  environment: 'development',
  criticality: 'medium',
  owner: '<EMAIL>'
};

// Setup MSW server
const server = setupServer(
  // Get applications list
  rest.get('/api/v1/applications', (req, res, ctx) => {
    const page = req.url.searchParams.get('page') || '1';
    const limit = req.url.searchParams.get('limit') || '10';
    const environment = req.url.searchParams.get('environment');
    const criticality = req.url.searchParams.get('criticality');
    const search = req.url.searchParams.get('search');

    let filteredApps = [...mockApplications];

    if (environment) {
      filteredApps = filteredApps.filter(app => app.environment === environment);
    }
    if (criticality) {
      filteredApps = filteredApps.filter(app => app.criticality === criticality);
    }
    if (search) {
      filteredApps = filteredApps.filter(app => 
        app.name.toLowerCase().includes(search.toLowerCase()) ||
        app.description.toLowerCase().includes(search.toLowerCase())
      );
    }

    return res(ctx.json({
      applications: filteredApps,
      total: filteredApps.length,
      page: parseInt(page),
      limit: parseInt(limit),
      total_pages: Math.ceil(filteredApps.length / parseInt(limit))
    }));
  }),

  // Get application details
  rest.get('/api/v1/applications/:id', (req, res, ctx) => {
    const { id } = req.params;
    if (id === '1') {
      return res(ctx.json(mockApplicationDetails));
    }
    return res(ctx.status(404), ctx.json({ error: 'Application not found' }));
  }),

  // Create application
  rest.post('/api/v1/applications', (req, res, ctx) => {
    return res(ctx.status(201), ctx.json({
      id: '3',
      ...mockCreateApplication,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));
  }),

  // Update application
  rest.put('/api/v1/applications/:id', (req, res, ctx) => {
    const { id } = req.params;
    return res(ctx.json({
      ...mockApplications[0],
      id,
      updated_at: new Date().toISOString()
    }));
  }),

  // Delete application
  rest.delete('/api/v1/applications/:id', (req, res, ctx) => {
    return res(ctx.status(204));
  }),

  // Scan application
  rest.post('/api/v1/applications/:id/scan', (req, res, ctx) => {
    return res(ctx.json({
      scan_id: 'scan-123',
      status: 'initiated',
      message: 'Vulnerability scan initiated'
    }));
  }),

  // Get application vulnerabilities
  rest.get('/api/v1/applications/:id/vulnerabilities', (req, res, ctx) => {
    return res(ctx.json({
      vulnerabilities: mockApplicationDetails.vulnerabilities,
      total: mockApplicationDetails.vulnerabilities.length
    }));
  }),

  // Error scenarios
  rest.get('/api/v1/applications-error', (req, res, ctx) => {
    return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
  }),

  rest.get('/api/v1/applications-unauthorized', (req, res, ctx) => {
    return res(ctx.status(401), ctx.json({ error: 'Unauthorized' }));
  })
);

describe('Application Management API Tests', () => {
  beforeEach(() => {
    server.listen();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  describe('GET /api/v1/applications', () => {
    it('should fetch applications list successfully', async () => {
      const response = await fetch('/api/v1/applications');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.applications).toHaveLength(2);
      expect(data.total).toBe(2);
      expect(data.applications[0]).toHaveProperty('id');
      expect(data.applications[0]).toHaveProperty('name');
      expect(data.applications[0]).toHaveProperty('environment');
    });

    it('should support pagination', async () => {
      const response = await fetch('/api/v1/applications?page=1&limit=1');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.page).toBe(1);
      expect(data.limit).toBe(1);
      expect(data.total_pages).toBe(2);
    });

    it('should filter by environment', async () => {
      const response = await fetch('/api/v1/applications?environment=production');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.applications).toHaveLength(2);
      expect(data.applications.every(app => app.environment === 'production')).toBe(true);
    });

    it('should filter by criticality', async () => {
      const response = await fetch('/api/v1/applications?criticality=high');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.applications).toHaveLength(1);
      expect(data.applications[0].criticality).toBe('high');
    });

    it('should support search functionality', async () => {
      const response = await fetch('/api/v1/applications?search=customer');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.applications).toHaveLength(1);
      expect(data.applications[0].name).toBe('Customer Portal');
    });

    it('should handle empty results', async () => {
      const response = await fetch('/api/v1/applications?search=nonexistent');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.applications).toHaveLength(0);
      expect(data.total).toBe(0);
    });
  });

  describe('GET /api/v1/applications/:id', () => {
    it('should fetch application details successfully', async () => {
      const response = await fetch('/api/v1/applications/1');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.id).toBe('1');
      expect(data.name).toBe('Customer Portal');
      expect(data).toHaveProperty('vulnerabilities');
      expect(data).toHaveProperty('dependencies');
      expect(data).toHaveProperty('scan_history');
    });

    it('should return 404 for non-existent application', async () => {
      const response = await fetch('/api/v1/applications/999');
      
      expect(response.status).toBe(404);
      const data = await response.json();
      expect(data.error).toBe('Application not found');
    });

    it('should include vulnerability details', async () => {
      const response = await fetch('/api/v1/applications/1');
      const data = await response.json();

      expect(data.vulnerabilities).toHaveLength(2);
      expect(data.vulnerabilities[0]).toHaveProperty('id');
      expect(data.vulnerabilities[0]).toHaveProperty('severity');
      expect(data.vulnerabilities[0]).toHaveProperty('score');
      expect(data.vulnerabilities[0]).toHaveProperty('status');
    });

    it('should include dependency information', async () => {
      const response = await fetch('/api/v1/applications/1');
      const data = await response.json();

      expect(data.dependencies).toHaveLength(2);
      expect(data.dependencies[0]).toHaveProperty('name');
      expect(data.dependencies[0]).toHaveProperty('version');
      expect(data.dependencies[0]).toHaveProperty('type');
      expect(data.dependencies[0]).toHaveProperty('vulnerabilities');
    });
  });

  describe('POST /api/v1/applications', () => {
    it('should create new application successfully', async () => {
      const response = await fetch('/api/v1/applications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(mockCreateApplication)
      });

      expect(response.status).toBe(201);
      const data = await response.json();
      expect(data.id).toBe('3');
      expect(data.name).toBe('New Application');
      expect(data).toHaveProperty('created_at');
      expect(data).toHaveProperty('updated_at');
    });

    it('should validate required fields', async () => {
      const invalidData = { name: '' }; // Missing required fields
      
      server.use(
        rest.post('/api/v1/applications', (req, res, ctx) => {
          return res(ctx.status(400), ctx.json({
            error: 'Validation error',
            details: ['Name is required', 'Environment is required']
          }));
        })
      );

      const response = await fetch('/api/v1/applications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidData)
      });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toBe('Validation error');
      expect(data.details).toContain('Name is required');
    });
  });

  describe('PUT /api/v1/applications/:id', () => {
    it('should update application successfully', async () => {
      const updateData = {
        name: 'Updated Application',
        description: 'Updated description'
      };

      const response = await fetch('/api/v1/applications/1', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.id).toBe('1');
      expect(data).toHaveProperty('updated_at');
    });
  });

  describe('DELETE /api/v1/applications/:id', () => {
    it('should delete application successfully', async () => {
      const response = await fetch('/api/v1/applications/1', {
        method: 'DELETE'
      });

      expect(response.status).toBe(204);
    });
  });

  describe('POST /api/v1/applications/:id/scan', () => {
    it('should initiate vulnerability scan', async () => {
      const response = await fetch('/api/v1/applications/1/scan', {
        method: 'POST'
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.scan_id).toBe('scan-123');
      expect(data.status).toBe('initiated');
      expect(data.message).toBe('Vulnerability scan initiated');
    });
  });

  describe('GET /api/v1/applications/:id/vulnerabilities', () => {
    it('should fetch application vulnerabilities', async () => {
      const response = await fetch('/api/v1/applications/1/vulnerabilities');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.vulnerabilities).toHaveLength(2);
      expect(data.total).toBe(2);
      expect(data.vulnerabilities[0]).toHaveProperty('id');
      expect(data.vulnerabilities[0]).toHaveProperty('severity');
    });
  });

  describe('Error Handling', () => {
    it('should handle server errors gracefully', async () => {
      const response = await fetch('/api/v1/applications-error');
      
      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data).toHaveProperty('error');
    });

    it('should handle unauthorized access', async () => {
      const response = await fetch('/api/v1/applications-unauthorized');
      
      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.error).toBe('Unauthorized');
    });
  });

  describe('Data Validation', () => {
    it('should validate application data structure', async () => {
      const response = await fetch('/api/v1/applications');
      const data = await response.json();

      data.applications.forEach((app: any) => {
        expect(app).toHaveProperty('id');
        expect(app).toHaveProperty('name');
        expect(app).toHaveProperty('environment');
        expect(app).toHaveProperty('criticality');
        expect(app).toHaveProperty('owner');
        expect(app).toHaveProperty('vulnerability_count');
        expect(app).toHaveProperty('risk_score');
        expect(typeof app.vulnerability_count).toBe('number');
        expect(typeof app.risk_score).toBe('number');
      });
    });

    it('should validate environment values', async () => {
      const response = await fetch('/api/v1/applications');
      const data = await response.json();

      const validEnvironments = ['development', 'staging', 'production'];
      data.applications.forEach((app: any) => {
        expect(validEnvironments).toContain(app.environment);
      });
    });

    it('should validate criticality values', async () => {
      const response = await fetch('/api/v1/applications');
      const data = await response.json();

      const validCriticalities = ['low', 'medium', 'high', 'critical'];
      data.applications.forEach((app: any) => {
        expect(validCriticalities).toContain(app.criticality);
      });
    });
  });

  describe('Performance Tests', () => {
    it('should respond within acceptable time limits', async () => {
      const startTime = Date.now();
      const response = await fetch('/api/v1/applications');
      const endTime = Date.now();

      expect(response.status).toBe(200);
      expect(endTime - startTime).toBeLessThan(1000);
    });

    it('should handle large datasets efficiently', async () => {
      // Mock large dataset
      const largeDataset = Array(100).fill(null).map((_, index) => ({
        ...mockApplications[0],
        id: `app-${index}`,
        name: `Application ${index}`
      }));

      server.use(
        rest.get('/api/v1/applications', (req, res, ctx) => {
          return res(ctx.json({
            applications: largeDataset,
            total: largeDataset.length
          }));
        })
      );

      const startTime = Date.now();
      const response = await fetch('/api/v1/applications');
      const endTime = Date.now();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.applications).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(2000); // Should handle large datasets
    });
  });
});
