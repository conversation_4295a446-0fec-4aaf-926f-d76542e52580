"""Initial database schema with soft delete

Revision ID: 3a06de448b9e
Revises: 
Create Date: 2025-06-18 08:17:54.155512

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = '3a06de448b9e'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade database schema."""
    # Create applications table
    op.create_table(
        'applications',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('version', sa.String(length=100), nullable=True),
        sa.Column('owner', sa.String(length=255), nullable=True),
        sa.Column('environment', sa.String(length=50), nullable=True),
        sa.Column('criticality', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name', 'environment', name='uq_application_name_env_active')
    )
    op.create_index('ix_application_criticality_active', 'applications', ['criticality'])
    op.create_index('ix_application_name_active', 'applications', ['name'])
    op.create_index(op.f('ix_applications_created_at'), 'applications', ['created_at'])
    op.create_index(op.f('ix_applications_deleted_at'), 'applications', ['deleted_at'])
    op.create_index(op.f('ix_applications_updated_at'), 'applications', ['updated_at'])

    # Create components table
    op.create_table(
        'components',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('application_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('version', sa.String(length=100), nullable=True),
        sa.Column('vendor', sa.String(length=255), nullable=True),
        sa.Column('component_type', sa.String(length=50), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['application_id'], ['applications.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('application_id', 'name', 'version', name='uq_component_app_name_version_active')
    )
    op.create_index('ix_component_name_active', 'components', ['name'])
    op.create_index('ix_component_type_active', 'components', ['component_type'])
    op.create_index(op.f('ix_components_application_id'), 'components', ['application_id'])
    op.create_index(op.f('ix_components_created_at'), 'components', ['created_at'])
    op.create_index(op.f('ix_components_deleted_at'), 'components', ['deleted_at'])
    op.create_index(op.f('ix_components_updated_at'), 'components', ['updated_at'])

    # Create cpe_mappings table
    op.create_table(
        'cpe_mappings',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('component_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('cpe_string', sa.String(length=500), nullable=False),
        sa.Column('confidence', sa.Float(), nullable=True),
        sa.Column('mapping_source', sa.String(length=50), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['component_id'], ['components.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('component_id', 'cpe_string', name='uq_cpe_mapping_component_cpe_active')
    )
    op.create_index('ix_cpe_string_active', 'cpe_mappings', ['cpe_string'])
    op.create_index(op.f('ix_cpe_mappings_component_id'), 'cpe_mappings', ['component_id'])
    op.create_index(op.f('ix_cpe_mappings_created_at'), 'cpe_mappings', ['created_at'])
    op.create_index(op.f('ix_cpe_mappings_deleted_at'), 'cpe_mappings', ['deleted_at'])
    op.create_index(op.f('ix_cpe_mappings_updated_at'), 'cpe_mappings', ['updated_at'])

    # Create cves table
    op.create_table(
        'cves',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('cve_id', sa.String(length=20), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('published_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_modified_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('cvss_v3_score', sa.Float(), nullable=True),
        sa.Column('cvss_v3_vector', sa.String(length=100), nullable=True),
        sa.Column('cvss_v3_severity', sa.String(length=20), nullable=True),
        sa.Column('cvss_v2_score', sa.Float(), nullable=True),
        sa.Column('cvss_v2_vector', sa.String(length=100), nullable=True),
        sa.Column('cwe_ids', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('references', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('vendor_advisories', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('nvd_last_modified', sa.DateTime(timezone=True), nullable=True),
        sa.Column('source', sa.String(length=50), nullable=False, server_default='NVD'),
        sa.Column('raw_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('cve_id', name='uq_cve_id_active')
    )
    op.create_index('ix_cve_published_date_active', 'cves', ['published_date'])
    op.create_index('ix_cve_score_active', 'cves', ['cvss_v3_score'])
    op.create_index('ix_cve_severity_active', 'cves', ['cvss_v3_severity'])
    op.create_index(op.f('ix_cves_cve_id'), 'cves', ['cve_id'])
    op.create_index(op.f('ix_cves_created_at'), 'cves', ['created_at'])
    op.create_index(op.f('ix_cves_deleted_at'), 'cves', ['deleted_at'])
    op.create_index(op.f('ix_cves_last_modified_date'), 'cves', ['last_modified_date'])
    op.create_index(op.f('ix_cves_nvd_last_modified'), 'cves', ['nvd_last_modified'])
    op.create_index(op.f('ix_cves_published_date'), 'cves', ['published_date'])
    op.create_index(op.f('ix_cves_updated_at'), 'cves', ['updated_at'])

    # Create cve_cpe_applicability table
    op.create_table(
        'cve_cpe_applicability',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('cve_id', sa.String(length=20), nullable=False),
        sa.Column('cpe_string', sa.String(length=500), nullable=False),
        sa.Column('version_start_including', sa.String(length=100), nullable=True),
        sa.Column('version_start_excluding', sa.String(length=100), nullable=True),
        sa.Column('version_end_including', sa.String(length=100), nullable=True),
        sa.Column('version_end_excluding', sa.String(length=100), nullable=True),
        sa.Column('vulnerable', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('configuration_id', sa.String(length=100), nullable=True),
        sa.Column('source', sa.String(length=50), nullable=False, server_default='NVD'),
        sa.Column('confidence', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('cve_id', 'cpe_string', 'configuration_id', name='uq_cve_cpe_config_active')
    )
    op.create_index('ix_cve_cpe_cve_id_active', 'cve_cpe_applicability', ['cve_id'])
    op.create_index('ix_cve_cpe_string_active', 'cve_cpe_applicability', ['cpe_string'])
    op.create_index('ix_cve_cpe_vulnerable_active', 'cve_cpe_applicability', ['vulnerable'])
    op.create_index(op.f('ix_cve_cpe_applicability_created_at'), 'cve_cpe_applicability', ['created_at'])
    op.create_index(op.f('ix_cve_cpe_applicability_deleted_at'), 'cve_cpe_applicability', ['deleted_at'])
    op.create_index(op.f('ix_cve_cpe_applicability_updated_at'), 'cve_cpe_applicability', ['updated_at'])


def downgrade() -> None:
    """Downgrade database schema."""
    # Drop tables in reverse order
    op.drop_table('cve_cpe_applicability')
    op.drop_table('cves')
    op.drop_table('cpe_mappings')
    op.drop_table('components')
    op.drop_table('applications')
