Authentication
==============

The CVE Feed Service uses a comprehensive authentication and authorization system to secure access to vulnerability data and application inventories. This guide covers user management, authentication methods, and role-based access control.

Overview
--------

The authentication system provides:

* **JWT-based Authentication**: Secure token-based access for web applications
* **API Key Authentication**: Long-lived tokens for programmatic access
* **Role-Based Access Control (RBAC)**: Different permission levels for different user types
* **User Management**: Complete user lifecycle management

User Roles
----------

The system supports two primary roles:

IT Administrator (``it_admin``)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Permissions:**
* Full system administration
* User management (create, update, delete users)
* Application and component management
* CVE data management
* API key management
* System configuration

**Use Cases:**
* System setup and maintenance
* User provisioning and access control
* Data import and system administration

Security Analyst (``security_analyst``)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Permissions:**
* Application and component management
* Vulnerability feed access
* CVE data viewing
* Own API key management
* Password management

**Use Cases:**
* Daily vulnerability assessment
* Application inventory management
* Security reporting and analysis

Authentication Methods
----------------------

JWT Token Authentication
~~~~~~~~~~~~~~~~~~~~~~~~~

JWT (JSON Web Token) authentication is the primary method for interactive access:

**Login Process:**

.. code-block:: bash

   # Login request
   curl -X POST "http://localhost:8000/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
          "username": "your_username",
          "password": "your_password"
        }'

**Response:**

.. code-block:: json

   {
     "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "token_type": "bearer",
     "expires_in": 1800
   }

**Using the Token:**

.. code-block:: bash

   # Include token in Authorization header
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications"

**Token Properties:**
* **Expiration**: 30 minutes by default (configurable)
* **Payload**: Contains username and role information
* **Security**: Signed with server secret key

API Key Authentication
~~~~~~~~~~~~~~~~~~~~~~

API keys provide long-lived access for automated systems:

**Creating an API Key:**

.. code-block:: bash

   # Create API key (requires authentication)
   curl -X POST "http://localhost:8000/api/v1/auth/api-keys" \
        -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "CI/CD Pipeline Key"
        }'

**Response:**

.. code-block:: json

   {
     "id": "api-key-uuid",
     "user_id": "user-uuid",
     "name": "CI/CD Pipeline Key",
     "api_key": "cvef_1234567890abcdef...",
     "is_active": true,
     "created_at": "2025-01-18T10:00:00Z"
   }

**Using API Keys:**

.. code-block:: bash

   # Include API key in X-API-Key header
   curl -H "X-API-Key: cvef_1234567890abcdef..." \
        "http://localhost:8000/api/v1/cves/feed"

**API Key Management:**

.. code-block:: bash

   # List your API keys
   curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        "http://localhost:8000/api/v1/auth/api-keys"

   # Delete an API key
   curl -X DELETE -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        "http://localhost:8000/api/v1/auth/api-keys/{api_key_id}"

User Management
---------------

Creating Users (Admin Only)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Only IT administrators can create new users:

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/auth/users" \
        -H "Authorization: Bearer ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "username": "security_analyst_1",
          "email": "<EMAIL>",
          "full_name": "Jane Smith",
          "password": "secure_password_123",
          "role": "security_analyst"
        }'

**User Creation Response:**

.. code-block:: json

   {
     "id": "user-uuid",
     "username": "security_analyst_1",
     "email": "<EMAIL>",
     "full_name": "Jane Smith",
     "role": "security_analyst",
     "is_active": true,
     "created_at": "2025-01-18T10:00:00Z",
     "updated_at": "2025-01-18T10:00:00Z"
   }

Listing Users (Admin Only)
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -H "Authorization: Bearer ADMIN_TOKEN" \
        "http://localhost:8000/api/v1/auth/users"

Updating Users (Admin Only)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -X PATCH "http://localhost:8000/api/v1/auth/users/{user_id}" \
        -H "Authorization: Bearer ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "is_active": false,
          "role": "it_admin"
        }'

Self-Service Operations
-----------------------

Getting Current User Info
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/auth/me"

**Response:**

.. code-block:: json

   {
     "id": "user-uuid",
     "username": "your_username",
     "email": "<EMAIL>",
     "full_name": "Your Name",
     "role": "security_analyst",
     "is_active": true,
     "created_at": "2025-01-18T10:00:00Z",
     "updated_at": "2025-01-18T10:00:00Z",
     "api_keys": [
       {
         "id": "key-uuid",
         "name": "My API Key",
         "is_active": true,
         "last_used_at": "2025-01-18T09:30:00Z",
         "created_at": "2025-01-18T08:00:00Z"
       }
     ]
   }

Changing Password
~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/auth/change-password" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "current_password": "old_password",
          "new_password": "new_secure_password"
        }'

Security Best Practices
------------------------

Password Requirements
~~~~~~~~~~~~~~~~~~~~~

* **Minimum Length**: 8 characters
* **Complexity**: Use a mix of letters, numbers, and special characters
* **Uniqueness**: Don't reuse passwords from other systems
* **Regular Updates**: Change passwords periodically

Token Security
~~~~~~~~~~~~~~~

**JWT Tokens:**
* Store securely (avoid localStorage in browsers)
* Don't log tokens in application logs
* Handle expiration gracefully
* Use HTTPS in production

**API Keys:**
* Store in secure configuration management
* Rotate regularly
* Use descriptive names for tracking
* Delete unused keys promptly

Access Control
~~~~~~~~~~~~~~

* **Principle of Least Privilege**: Grant minimum necessary permissions
* **Regular Audits**: Review user access periodically
* **Deactivate Unused Accounts**: Disable accounts for departed users
* **Monitor Access**: Log and monitor authentication events

Common Authentication Patterns
------------------------------

Web Application Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   // Example JavaScript integration
   class CVEFeedClient {
     constructor(baseUrl) {
       this.baseUrl = baseUrl;
       this.token = localStorage.getItem('cve_feed_token');
     }

     async login(username, password) {
       const response = await fetch(`${this.baseUrl}/api/v1/auth/login`, {
         method: 'POST',
         headers: { 'Content-Type': 'application/json' },
         body: JSON.stringify({ username, password })
       });
       
       const data = await response.json();
       this.token = data.access_token;
       localStorage.setItem('cve_feed_token', this.token);
       return data;
     }

     async apiCall(endpoint, options = {}) {
       return fetch(`${this.baseUrl}${endpoint}`, {
         ...options,
         headers: {
           'Authorization': `Bearer ${this.token}`,
           ...options.headers
         }
       });
     }
   }

CLI Tool Integration
~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   #!/bin/bash
   # Example shell script for API access

   # Configuration
   API_BASE="http://localhost:8000/api/v1"
   API_KEY="your-api-key-here"

   # Function to make authenticated API calls
   api_call() {
     local endpoint="$1"
     local method="${2:-GET}"
     local data="$3"
     
     curl -s -X "$method" \
          -H "X-API-Key: $API_KEY" \
          -H "Content-Type: application/json" \
          ${data:+-d "$data"} \
          "$API_BASE$endpoint"
   }

   # Example usage
   api_call "/applications" "GET"

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Invalid Credentials**
   * Verify username and password
   * Check if account is active
   * Ensure correct API endpoint

**Token Expired**
   * Refresh token or re-authenticate
   * Check token expiration time
   * Implement automatic token refresh

**Insufficient Permissions**
   * Verify user role and permissions
   * Contact administrator for access
   * Check endpoint requirements

**API Key Issues**
   * Verify API key format and validity
   * Check if key is active
   * Ensure correct header format

Error Responses
~~~~~~~~~~~~~~~

The API returns standard HTTP status codes with detailed error messages:

.. code-block:: json

   {
     "detail": "Incorrect username or password"
   }

**Common Status Codes:**
* ``401 Unauthorized``: Invalid or missing credentials
* ``403 Forbidden``: Insufficient permissions
* ``422 Unprocessable Entity``: Invalid request data
* ``429 Too Many Requests``: Rate limiting applied
