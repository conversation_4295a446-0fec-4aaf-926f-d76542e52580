Component Management
===================

Components represent the individual software packages, libraries, and systems that make up your applications. Effective component management is crucial for accurate vulnerability assessment, as CVEs are matched against specific components through CPE mappings.

Overview
--------

Components in the CVE Feed Service represent:

* **Software Dependencies**: Libraries, frameworks, packages
* **System Software**: Operating systems, databases, web servers
* **Application Components**: Microservices, modules, plugins
* **Infrastructure**: Load balancers, proxies, monitoring tools

Component Properties
--------------------

Core Information
~~~~~~~~~~~~~~~~

* **Name**: Component identifier (e.g., "nginx", "spring-boot", "postgresql")
* **Version**: Specific version number (e.g., "1.20.1", "2.7.0")
* **Vendor**: Organization that develops the component (e.g., "nginx", "apache")

Classification
~~~~~~~~~~~~~~

* **Component Type**: Category of component (web_server, database, library, etc.)
* **Description**: Additional details about the component's role

Relationships
~~~~~~~~~~~~~

* **Application**: Parent application that uses this component
* **CPE Mappings**: Links to vulnerability identifiers

Creating Components
-------------------

Basic Component Creation
~~~~~~~~~~~~~~~~~~~~~~~~

Components are always created within the context of an application:

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/applications/{application_id}/components" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "nginx",
          "version": "1.20.1",
          "vendor": "nginx",
          "component_type": "web_server",
          "description": "Main web server for handling HTTP requests"
        }'

**Response:**

.. code-block:: json

   {
     "id": "component-uuid",
     "application_id": "app-uuid",
     "name": "nginx",
     "version": "1.20.1",
     "vendor": "nginx",
     "component_type": "web_server",
     "description": "Main web server for handling HTTP requests",
     "created_at": "2025-01-18T10:00:00Z",
     "updated_at": "2025-01-18T10:00:00Z",
     "deleted_at": null
   }

Minimal Component
~~~~~~~~~~~~~~~~~

Only name is required:

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/applications/{application_id}/components" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "redis"
        }'

Database Component
~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/applications/{application_id}/components" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "postgresql",
          "version": "13.8",
          "vendor": "postgresql",
          "component_type": "database",
          "description": "Primary application database"
        }'

Library Component
~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/applications/{application_id}/components" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "spring-boot",
          "version": "2.7.0",
          "vendor": "pivotal",
          "component_type": "framework",
          "description": "Java application framework"
        }'

Listing Components
------------------

List Application Components
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications/{application_id}/components"

**Response includes CPE mappings:**

.. code-block:: json

   [
     {
       "id": "component-uuid-1",
       "application_id": "app-uuid",
       "name": "nginx",
       "version": "1.20.1",
       "vendor": "nginx",
       "component_type": "web_server",
       "description": "Main web server",
       "created_at": "2025-01-18T10:00:00Z",
       "updated_at": "2025-01-18T10:00:00Z",
       "cpe_mappings": [
         {
           "id": "mapping-uuid",
           "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
           "confidence": 1.0,
           "mapping_source": "manual"
         }
       ]
     }
   ]

With Pagination
~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications/{application_id}/components?skip=0&limit=10"

Filtered by Component Type
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Get only database components
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications/{application_id}/components?component_type=database"

   # Get web servers
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications/{application_id}/components?component_type=web_server"

Retrieving Component Details
----------------------------

Get Specific Component
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/components/{component_id}"

**Response includes full CPE mapping details:**

.. code-block:: json

   {
     "id": "component-uuid",
     "application_id": "app-uuid",
     "name": "nginx",
     "version": "1.20.1",
     "vendor": "nginx",
     "component_type": "web_server",
     "description": "Main web server for handling HTTP requests",
     "created_at": "2025-01-18T10:00:00Z",
     "updated_at": "2025-01-18T10:00:00Z",
     "cpe_mappings": [
       {
         "id": "mapping-uuid",
         "component_id": "component-uuid",
         "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
         "confidence": 1.0,
         "mapping_source": "manual",
         "created_at": "2025-01-18T10:05:00Z",
         "updated_at": "2025-01-18T10:05:00Z"
       }
     ]
   }

Updating Components
-------------------

Version Updates
~~~~~~~~~~~~~~~

.. code-block:: bash

   # Update component version
   curl -X PATCH "http://localhost:8000/api/v1/components/{component_id}" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "version": "1.22.0"
        }'

Partial Updates
~~~~~~~~~~~~~~~

.. code-block:: bash

   # Update description and type
   curl -X PATCH "http://localhost:8000/api/v1/components/{component_id}" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "description": "Updated web server configuration",
          "component_type": "reverse_proxy"
        }'

Vendor Information
~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Update vendor information
   curl -X PATCH "http://localhost:8000/api/v1/components/{component_id}" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "vendor": "nginx_inc"
        }'

Deleting Components
-------------------

Soft Delete
~~~~~~~~~~~

.. code-block:: bash

   curl -X DELETE "http://localhost:8000/api/v1/components/{component_id}" \
        -H "Authorization: Bearer YOUR_TOKEN"

**Effects of Component Deletion:**
* Component is hidden from listings
* All associated CPE mappings are soft-deleted
* Vulnerability feeds will no longer include this component
* Historical data is preserved

Component Types
---------------

Common Component Types
~~~~~~~~~~~~~~~~~~~~~~

**Infrastructure Components:**
* ``web_server``: nginx, Apache HTTP Server, IIS
* ``database``: PostgreSQL, MySQL, MongoDB
* ``cache``: Redis, Memcached
* ``load_balancer``: HAProxy, F5, AWS ALB

**Application Components:**
* ``framework``: Spring Boot, Django, Express.js
* ``library``: Jackson, Lodash, Requests
* ``runtime``: Node.js, Python, Java JRE
* ``container``: Docker, containerd

**Security Components:**
* ``authentication``: OAuth libraries, SAML implementations
* ``encryption``: OpenSSL, Bouncy Castle
* ``firewall``: iptables, pfSense

**Monitoring & Operations:**
* ``monitoring``: Prometheus, Grafana, New Relic agents
* ``logging``: Logstash, Fluentd, rsyslog

Best Practices
--------------

Component Identification
~~~~~~~~~~~~~~~~~~~~~~~~

**Accurate Naming:**
* Use official component names
* Be consistent across applications
* Include namespace for language-specific packages (e.g., "org.springframework.boot")

**Version Precision:**
* Use specific version numbers, not ranges
* Include patch versions when available
* Update versions promptly when components are upgraded

**Vendor Information:**
* Use official vendor/organization names
* Be consistent with vendor naming
* Research correct vendor for open-source projects

Component Classification
~~~~~~~~~~~~~~~~~~~~~~~~

**Meaningful Types:**
* Use descriptive, consistent component types
* Create a taxonomy that works for your organization
* Document type definitions for team consistency

**Granularity Balance:**
* Include all security-relevant components
* Don't over-decompose into trivial components
* Focus on components that receive security updates

Lifecycle Management
~~~~~~~~~~~~~~~~~~~~

**Regular Updates:**
* Review component inventories regularly
* Update versions when applications are deployed
* Remove obsolete components promptly

**Change Tracking:**
* Document why components are added or removed
* Track component lifecycle in deployment processes
* Maintain audit trails for compliance

Common Workflows
----------------

Adding a New Component Stack
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Example: Adding a typical web application stack

.. code-block:: bash

   # 1. Web Server
   curl -X POST "http://localhost:8000/api/v1/applications/{app_id}/components" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "nginx",
          "version": "1.20.1",
          "vendor": "nginx",
          "component_type": "web_server"
        }'

   # 2. Application Runtime
   curl -X POST "http://localhost:8000/api/v1/applications/{app_id}/components" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "node",
          "version": "18.12.0",
          "vendor": "nodejs",
          "component_type": "runtime"
        }'

   # 3. Database
   curl -X POST "http://localhost:8000/api/v1/applications/{app_id}/components" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "postgresql",
          "version": "13.8",
          "vendor": "postgresql",
          "component_type": "database"
        }'

Component Version Upgrade
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # 1. Get current component details
   COMPONENT=$(curl -H "Authorization: Bearer YOUR_TOKEN" \
                    "http://localhost:8000/api/v1/components/{component_id}")

   # 2. Update to new version
   curl -X PATCH "http://localhost:8000/api/v1/components/{component_id}" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "version": "1.22.0"
        }'

   # 3. Update CPE mappings if needed (see CPE Mapping guide)

Bulk Component Review
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Get all components for review
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications/{app_id}/components" \
        | jq '.[] | {name: .name, version: .version, type: .component_type}'

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Duplicate Components**
   * Error: Component with same name and version already exists
   * Solution: Check existing components or use different version

**Invalid Component Type**
   * Error: Validation error for component_type
   * Solution: Use consistent, descriptive type names

**Component Not Found**
   * Error: 404 Not Found
   * Solution: Verify component ID and check if soft-deleted

**Permission Issues**
   * Error: 403 Forbidden
   * Solution: Verify user has security_analyst or it_admin role

Version Format Issues
~~~~~~~~~~~~~~~~~~~~~

**Inconsistent Versioning:**
* Use semantic versioning when possible (major.minor.patch)
* Be consistent within your organization
* Document versioning conventions

**Missing Versions:**
* Some components may not have clear versions
* Use "latest" or "unknown" consistently
* Research official version information when possible
