Vulnerability Feeds
===================

Vulnerability feeds are the core output of the CVE Feed Service, providing tailored CVE information based on your application inventories. This guide covers accessing feeds, filtering options, and interpreting vulnerability data.

Overview
--------

The CVE Feed Service provides several types of vulnerability feeds:

* **Tailored Feeds**: CVEs affecting specific applications based on CPE mappings
* **General Feeds**: All CVEs with filtering options
* **Individual CVE Details**: Comprehensive information about specific vulnerabilities

All feeds support filtering by severity, date ranges, and other criteria to help you focus on the most relevant vulnerabilities.

Tailored CVE Feeds
------------------

Application-Specific Feeds
~~~~~~~~~~~~~~~~~~~~~~~~~~

Get vulnerabilities affecting a specific application:

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/cves/feed?application_id={application_id}"

**Response Structure:**

.. code-block:: json

   {
     "cves": [
       {
         "id": "cve-uuid",
         "cve_id": "CVE-2023-1234",
         "description": "Buffer overflow in nginx HTTP/2 implementation...",
         "published_date": "2023-03-15T10:00:00Z",
         "last_modified_date": "2023-03-16T14:30:00Z",
         "cvss_v3_score": 7.5,
         "cvss_v3_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H",
         "cvss_v3_severity": "HIGH",
         "cvss_v2_score": 5.0,
         "cwe_ids": ["CWE-120"],
         "references": [
           {
             "url": "https://nginx.org/security_advisories.html",
             "source": "nginx",
             "tags": ["Vendor Advisory"]
           }
         ],
         "source": "NVD",
         "created_at": "2023-03-15T12:00:00Z",
         "updated_at": "2023-03-16T15:00:00Z"
       }
     ],
     "total": 1,
     "limit": 100,
     "offset": 0,
     "has_more": false
   }

Severity Filtering
~~~~~~~~~~~~~~~~~~

Filter by CVSS severity levels:

.. code-block:: bash

   # High and Critical vulnerabilities only
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/cves/feed?application_id={app_id}&severity=HIGH"

   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/cves/feed?application_id={app_id}&severity=CRITICAL"

**Available Severity Levels:**
* ``CRITICAL``: CVSS 9.0-10.0
* ``HIGH``: CVSS 7.0-8.9
* ``MEDIUM``: CVSS 4.0-6.9
* ``LOW``: CVSS 0.1-3.9

Pagination
~~~~~~~~~~

Handle large result sets with pagination:

.. code-block:: bash

   # First 50 results
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/cves/feed?application_id={app_id}&limit=50&offset=0"

   # Next 50 results
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/cves/feed?application_id={app_id}&limit=50&offset=50"

**Pagination Parameters:**
* ``limit``: Number of results per page (1-1000, default 100)
* ``offset``: Number of results to skip (default 0)
* ``has_more``: Boolean indicating if more results are available

General CVE Feeds
-----------------

All CVEs with Filtering
~~~~~~~~~~~~~~~~~~~~~~~

Access the complete CVE database with filtering:

.. code-block:: bash

   # All CVEs (paginated)
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/cves/"

   # High severity CVEs only
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/cves/?severity=HIGH"

Date-Based Filtering
~~~~~~~~~~~~~~~~~~~~

Filter CVEs by publication or modification dates:

.. code-block:: bash

   # CVEs published in the last 7 days
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/cves/?published_after=2025-01-11T00:00:00Z"

   # CVEs published in a specific date range
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/cves/?published_after=2025-01-01T00:00:00Z&published_before=2025-01-31T23:59:59Z"

**Date Format:**
* Use ISO 8601 format: ``YYYY-MM-DDTHH:MM:SSZ``
* Times are in UTC
* Both ``published_after`` and ``published_before`` are inclusive

Combined Filtering
~~~~~~~~~~~~~~~~~~

Combine multiple filters for precise results:

.. code-block:: bash

   # High/Critical CVEs from last month
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/cves/?severity=HIGH&published_after=2024-12-01T00:00:00Z&limit=20"

Individual CVE Details
----------------------

Detailed CVE Information
~~~~~~~~~~~~~~~~~~~~~~~~

Get comprehensive information about a specific CVE:

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/cves/CVE-2023-1234"

**Response includes CPE applicability:**

.. code-block:: json

   {
     "id": "cve-uuid",
     "cve_id": "CVE-2023-1234",
     "description": "Detailed vulnerability description...",
     "published_date": "2023-03-15T10:00:00Z",
     "last_modified_date": "2023-03-16T14:30:00Z",
     "cvss_v3_score": 7.5,
     "cvss_v3_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H",
     "cvss_v3_severity": "HIGH",
     "cwe_ids": ["CWE-120", "CWE-787"],
     "references": [
       {
         "url": "https://nginx.org/security_advisories.html",
         "source": "nginx",
         "tags": ["Vendor Advisory"]
       }
     ],
     "vendor_advisories": [
       {
         "vendor": "nginx",
         "advisory_id": "nginx-2023-001",
         "url": "https://nginx.org/security_advisories.html"
       }
     ],
     "cpe_applicability": [
       {
         "id": "applicability-uuid",
         "cve_id": "CVE-2023-1234",
         "cpe_string": "cpe:2.3:a:nginx:nginx:*:*:*:*:*:*:*:*",
         "version_start_including": "1.20.0",
         "version_end_excluding": "1.20.2",
         "vulnerable": true,
         "configuration_id": "config-1",
         "source": "NVD",
         "confidence": 1.0
       }
     ]
   }

Understanding CVE Data
----------------------

CVSS Scoring
~~~~~~~~~~~~

**CVSS v3.1 (Preferred):**
* ``cvss_v3_score``: Numeric score (0.0-10.0)
* ``cvss_v3_vector``: Detailed scoring vector
* ``cvss_v3_severity``: Qualitative severity rating

**CVSS v2 (Legacy):**
* ``cvss_v2_score``: Numeric score (0.0-10.0)
* ``cvss_v2_vector``: Scoring vector

**Severity Mapping:**
* ``CRITICAL``: 9.0-10.0
* ``HIGH``: 7.0-8.9
* ``MEDIUM``: 4.0-6.9
* ``LOW``: 0.1-3.9

CWE Classifications
~~~~~~~~~~~~~~~~~~~

Common Weakness Enumeration (CWE) identifies vulnerability types:

.. code-block:: json

   "cwe_ids": ["CWE-120", "CWE-787"]

**Common CWEs:**
* ``CWE-79``: Cross-site Scripting (XSS)
* ``CWE-89``: SQL Injection
* ``CWE-120``: Buffer Overflow
* ``CWE-200``: Information Exposure
* ``CWE-287``: Improper Authentication

References and Advisories
~~~~~~~~~~~~~~~~~~~~~~~~~

CVEs include links to additional information:

.. code-block:: json

   "references": [
     {
       "url": "https://example.com/advisory",
       "source": "vendor",
       "tags": ["Vendor Advisory", "Patch"]
     }
   ]

**Reference Types:**
* Vendor advisories
* Patch information
* Exploit details
* Technical analysis
* Mitigation guidance

CPE Applicability
~~~~~~~~~~~~~~~~~

Shows which products and versions are affected:

.. code-block:: json

   "cpe_applicability": [
     {
       "cpe_string": "cpe:2.3:a:nginx:nginx:*:*:*:*:*:*:*:*",
       "version_start_including": "1.20.0",
       "version_end_excluding": "1.20.2",
       "vulnerable": true
     }
   ]

**Version Range Interpretation:**
* ``version_start_including``: First vulnerable version (inclusive)
* ``version_start_excluding``: First vulnerable version (exclusive)
* ``version_end_including``: Last vulnerable version (inclusive)
* ``version_end_excluding``: Last vulnerable version (exclusive)

Feed Integration Patterns
-------------------------

Automated Monitoring
~~~~~~~~~~~~~~~~~~~~

**Shell Script Example:**

.. code-block:: bash

   #!/bin/bash
   # Daily vulnerability check script

   API_BASE="http://localhost:8000/api/v1"
   API_KEY="your-api-key"
   APP_ID="your-application-id"

   # Get high/critical vulnerabilities from last 24 hours
   YESTERDAY=$(date -d "yesterday" -u +"%Y-%m-%dT%H:%M:%SZ")
   
   RESPONSE=$(curl -s -H "X-API-Key: $API_KEY" \
     "$API_BASE/cves/feed?application_id=$APP_ID&severity=HIGH&published_after=$YESTERDAY")

   CVE_COUNT=$(echo "$RESPONSE" | jq '.total')

   if [ "$CVE_COUNT" -gt 0 ]; then
     echo "⚠️  $CVE_COUNT new high/critical vulnerabilities found!"
     echo "$RESPONSE" | jq -r '.cves[] | "- \(.cve_id): \(.cvss_v3_severity) (\(.cvss_v3_score))"'
     
     # Send alert (email, Slack, etc.)
     # send_alert "$RESPONSE"
   else
     echo "✅ No new high/critical vulnerabilities"
   fi

**Python Integration:**

.. code-block:: python

   import requests
   from datetime import datetime, timedelta

   class CVEFeedClient:
       def __init__(self, base_url, api_key):
           self.base_url = base_url
           self.headers = {"X-API-Key": api_key}

       def get_application_vulnerabilities(self, app_id, severity=None, days_back=7):
           """Get vulnerabilities for an application."""
           params = {"application_id": app_id}
           
           if severity:
               params["severity"] = severity
           
           if days_back:
               since = datetime.utcnow() - timedelta(days=days_back)
               params["published_after"] = since.isoformat() + "Z"

           response = requests.get(
               f"{self.base_url}/cves/feed",
               headers=self.headers,
               params=params
           )
           response.raise_for_status()
           return response.json()

       def get_critical_vulnerabilities(self, app_id):
           """Get critical vulnerabilities for an application."""
           return self.get_application_vulnerabilities(
               app_id, severity="CRITICAL", days_back=30
           )

   # Usage
   client = CVEFeedClient("http://localhost:8000/api/v1", "your-api-key")
   critical_cves = client.get_critical_vulnerabilities("your-app-id")

   for cve in critical_cves["cves"]:
       print(f"🚨 {cve['cve_id']}: {cve['description'][:100]}...")

Reporting and Analytics
~~~~~~~~~~~~~~~~~~~~~~

**Vulnerability Summary Report:**

.. code-block:: bash

   #!/bin/bash
   # Generate vulnerability summary report

   API_BASE="http://localhost:8000/api/v1"
   API_KEY="your-api-key"

   echo "# Vulnerability Summary Report"
   echo "Generated: $(date)"
   echo

   # Get all applications
   APPS=$(curl -s -H "X-API-Key: $API_KEY" "$API_BASE/applications")

   echo "$APPS" | jq -r '.[] | .id' | while read APP_ID; do
     APP_NAME=$(echo "$APPS" | jq -r ".[] | select(.id==\"$APP_ID\") | .name")
     
     # Get vulnerability counts by severity
     CRITICAL=$(curl -s -H "X-API-Key: $API_KEY" \
       "$API_BASE/cves/feed?application_id=$APP_ID&severity=CRITICAL" | jq '.total')
     HIGH=$(curl -s -H "X-API-Key: $API_KEY" \
       "$API_BASE/cves/feed?application_id=$APP_ID&severity=HIGH" | jq '.total')
     
     echo "## $APP_NAME"
     echo "- Critical: $CRITICAL"
     echo "- High: $HIGH"
     echo
   done

CI/CD Integration
~~~~~~~~~~~~~~~~

**Jenkins Pipeline Example:**

.. code-block:: groovy

   pipeline {
       agent any
       
       environment {
           CVE_FEED_API = 'http://cve-feed-service:8000/api/v1'
           CVE_FEED_KEY = credentials('cve-feed-api-key')
           APP_ID = 'your-application-id'
       }
       
       stages {
           stage('Vulnerability Check') {
               steps {
                   script {
                       def response = sh(
                           script: """
                               curl -s -H "X-API-Key: ${CVE_FEED_KEY}" \
                                    "${CVE_FEED_API}/cves/feed?application_id=${APP_ID}&severity=CRITICAL"
                           """,
                           returnStdout: true
                       ).trim()
                       
                       def data = readJSON text: response
                       
                       if (data.total > 0) {
                           echo "⚠️ Found ${data.total} critical vulnerabilities"
                           
                           // Optionally fail the build
                           if (params.FAIL_ON_CRITICAL) {
                               error("Critical vulnerabilities found!")
                           }
                       } else {
                           echo "✅ No critical vulnerabilities found"
                       }
                   }
               }
           }
       }
   }

Best Practices
--------------

Feed Consumption
~~~~~~~~~~~~~~~~

**Regular Monitoring:**
* Check feeds daily for new vulnerabilities
* Focus on high/critical severity levels
* Monitor recently published CVEs

**Efficient Filtering:**
* Use application-specific feeds to reduce noise
* Apply severity filters based on your risk tolerance
* Leverage date filtering for incremental updates

**Pagination Handling:**
* Always check ``has_more`` flag
* Implement proper pagination for large datasets
* Consider batch processing for bulk operations

Alert Management
~~~~~~~~~~~~~~~~

**Severity-Based Alerting:**
* Immediate alerts for critical vulnerabilities
* Daily summaries for high severity
* Weekly reports for medium/low severity

**Context-Aware Notifications:**
* Include application and component information
* Provide direct links to vulnerability details
* Include remediation guidance when available

**Alert Fatigue Prevention:**
* Filter out irrelevant vulnerabilities
* Group related vulnerabilities
* Provide clear action items

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~

**Caching Strategies:**
* Cache feed results for short periods
* Use ETags or last-modified headers when available
* Implement client-side caching for static data

**Efficient Queries:**
* Use specific filters to reduce result sets
* Implement pagination for large datasets
* Consider parallel requests for multiple applications

**Rate Limiting:**
* Respect API rate limits
* Implement exponential backoff for retries
* Use API keys for higher rate limits

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Empty Feed Results**
   * Verify application has components with CPE mappings
   * Check CPE mapping accuracy
   * Ensure CVE data has been imported

**Too Many Results**
   * Apply severity filtering
   * Use date-based filtering
   * Verify CPE mappings aren't too broad

**Missing Expected CVEs**
   * Verify component versions in inventory
   * Check CPE mapping accuracy
   * Confirm CVE data is up to date

**Performance Issues**
   * Implement pagination
   * Use appropriate filters
   * Consider caching strategies

Feed Validation
~~~~~~~~~~~~~~~

**Verify Feed Accuracy:**

.. code-block:: bash

   # 1. Get a known vulnerable component
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/components/{component_id}"

   # 2. Check if expected CVEs appear in feed
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/cves/feed?application_id={app_id}" \
        | jq '.cves[] | select(.cve_id=="CVE-YYYY-NNNN")'

   # 3. Verify CPE applicability
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/cves/CVE-YYYY-NNNN" \
        | jq '.cpe_applicability[]'
