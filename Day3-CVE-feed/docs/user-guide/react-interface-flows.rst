React Interface User Flows
===========================

This guide provides detailed user flow documentation for the React interface, including sequence diagrams showing the interaction between users, frontend components, and backend services.

🎨 **Modern Dark-Mode Interface**
================================

The CVE Feed Service React interface is designed with a **dark-mode-first** approach, optimized for security professionals who often work in low-light environments. The interface provides a comprehensive, intuitive experience for vulnerability management.

.. mermaid::

   graph TB
       subgraph "Interface Layout"
           HEADER[Header Navigation]
           SIDEBAR[Collapsible Sidebar]
           MAIN[Main Content Area]
           FOOTER[Status Footer]
       end

       subgraph "Core Features"
           SEARCH[Global Search]
           FILTER[Advanced Filtering]
           EXPORT[Data Export]
           REALTIME[Real-time Updates]
       end

       subgraph "User Experience"
           DARKMODE[Dark Mode First]
           RESPONSIVE[Mobile Responsive]
           ACCESSIBLE[WCAG 2.1 AA]
           PERFORMANCE[Sub-second Loading]
       end

       HEADER --> SEARCH
       SIDEBAR --> FILTER
       MAIN --> EXPORT
       FOOTER --> REALTIME

       SEARCH --> DARKMODE
       FILTER --> RESPONSIVE
       EXPORT --> ACCESSIBLE
       REALTIME --> PERFORMANCE

       style HEADER fill:#1e293b
       style SIDEBAR fill:#334155
       style MAIN fill:#475569
       style DARKMODE fill:#e1f5fe

📋 **User Flow Categories**
==========================

The React interface provides six main user flow categories, each designed to support specific vulnerability management workflows:

1. **Authentication Flows** - User login, registration, and session management
2. **Dashboard Flows** - Overview, metrics, and global search functionality
3. **Application Management Flows** - CRUD operations for application inventory
4. **CVE Management Flows** - Vulnerability feed consumption and analysis
5. **Component Management Flows** - Software component tracking and CPE mapping
6. **User Management Flows** - Administrative user and permission management

🎯 **Design Principles**
=======================

**Dark Mode First**
   Primary interface optimized for dark environments with high contrast ratios and reduced eye strain.

**Security-Focused UX**
   Information hierarchy prioritizes security-critical data with clear severity indicators and risk scoring.

**Real-time Responsiveness**
   WebSocket integration provides live updates for vulnerability feeds and system status.

**Accessibility Compliance**
   Full WCAG 2.1 AA compliance with keyboard navigation, screen reader support, and focus management.

**Performance Optimized**
   Lazy loading, code splitting, and intelligent caching for sub-second response times.

Authentication Flows
--------------------

Login Flow
~~~~~~~~~~

The login flow handles user authentication with JWT token management and session persistence.

.. mermaid::

   sequenceDiagram
       participant U as User
       participant L as LoginForm
       participant A as AuthAPI
       participant B as Backend
       participant S as AuthStore
       participant R as Router

       U->>L: Enter credentials
       L->>L: Validate form inputs
       L->>A: dispatch(login(credentials))
       A->>B: POST /api/v1/auth/login
       
       alt Successful Authentication
           B->>A: 200 OK + JWT token
           A->>S: Store token & user data
           S->>R: Navigate to /dashboard
           R->>U: Display dashboard
       else Authentication Failed
           B->>A: 401 Unauthorized
           A->>L: Set error state
           L->>U: Display error message
       end

       Note over U,R: Token stored in httpOnly cookie<br/>for security

**Key Components:**
- LoginForm: Handles user input and validation
- AuthAPI: RTK Query API for authentication
- AuthStore: Redux slice for auth state management
- Router: React Router for navigation

Registration Flow
~~~~~~~~~~~~~~~~~

New user account creation with email verification.

.. mermaid::

   sequenceDiagram
       participant U as User
       participant R as RegistrationForm
       participant A as AuthAPI
       participant B as Backend
       participant E as EmailService
       participant V as VerificationPage

       U->>R: Fill registration form
       R->>R: Validate password strength
       R->>R: Check terms acceptance
       R->>A: dispatch(register(userData))
       A->>B: POST /api/v1/auth/register
       
       alt Registration Successful
           B->>E: Send verification email
           B->>A: 201 Created + user data
           A->>V: Navigate to verification page
           V->>U: Show "Check your email" message
           
           Note over U,E: User clicks email link
           U->>B: GET /api/v1/auth/verify/{token}
           B->>A: Account verified
           A->>U: Navigate to login page
       else Registration Failed
           B->>A: 400 Bad Request + errors
           A->>R: Set validation errors
           R->>U: Display field errors
       end

Dashboard Flows
---------------

Main Dashboard Flow
~~~~~~~~~~~~~~~~~~~

Central hub displaying vulnerability metrics and system overview.

.. mermaid::

   sequenceDiagram
       participant U as User
       participant D as Dashboard
       participant M as MetricsAPI
       participant C as CVEsAPI
       participant A as AppsAPI
       participant B as Backend

       U->>D: Navigate to /dashboard
       D->>D: Check authentication
       
       par Fetch Metrics
           D->>M: getMetrics()
           M->>B: GET /api/v1/metrics/summary
           B->>M: Metrics data
           M->>D: Update metrics cards
       and Fetch Recent CVEs
           D->>C: getRecentCVEs(limit: 10)
           C->>B: GET /api/v1/cves?limit=10&sort=published_date
           B->>C: Recent CVE list
           C->>D: Update recent CVEs section
       and Fetch App Status
           D->>A: getApplications(status: true)
           A->>B: GET /api/v1/applications?include_status=true
           B->>A: Applications with health status
           A->>D: Update app status grid
       end

       D->>U: Display complete dashboard

**Dashboard Components:**
- MetricsCards: CVE counts by severity
- RecentCVEs: Latest vulnerability entries
- ApplicationStatus: Health indicators per app
- QuickActions: Shortcut buttons for common tasks

Global Search Flow
~~~~~~~~~~~~~~~~~~

Universal search across all vulnerability data.

.. mermaid::

   sequenceDiagram
       participant U as User
       participant S as SearchBar
       participant API as SearchAPI
       participant B as Backend
       participant R as SearchResults

       U->>S: Type search query
       S->>S: Debounce input (300ms)
       S->>API: searchGlobal(query, filters)
       API->>B: GET /api/v1/search?q={query}&type=all
       
       alt Results Found
           B->>API: Search results + metadata
           API->>R: Display results with pagination
           R->>U: Show categorized results
           
           U->>R: Click result item
           R->>U: Navigate to detail page
       else No Results
           B->>API: Empty result set
           API->>R: Show "No results found"
           R->>U: Display search suggestions
       end

       Note over U,R: Search supports CVEs, Applications,<br/>Components, and Users

Application Management Flows
----------------------------

Application Creation Flow
~~~~~~~~~~~~~~~~~~~~~~~~~

Creating new applications in the inventory system.

.. mermaid::

   sequenceDiagram
       participant U as User
       participant L as AppList
       participant F as AppForm
       participant API as AppsAPI
       participant B as Backend
       participant N as Notifications

       U->>L: Click "Add Application"
       L->>F: Open creation modal
       U->>F: Fill application details
       F->>F: Validate form inputs
       U->>F: Click "Save"
       
       F->>API: createApplication(appData)
       API->>B: POST /api/v1/applications
       
       alt Creation Successful
           B->>API: 201 Created + app data
           API->>L: Refresh application list
           API->>N: Show success notification
           F->>F: Close modal
           L->>U: Display updated list
       else Creation Failed
           B->>API: 400 Bad Request + errors
           API->>F: Set field errors
           F->>U: Display validation errors
       end

       Note over U,N: Form includes environment,<br/>criticality, and description fields

Application Details Flow
~~~~~~~~~~~~~~~~~~~~~~~~

Viewing and managing individual application details.

.. mermaid::

   sequenceDiagram
       participant U as User
       participant L as AppList
       participant D as AppDetails
       participant API as AppsAPI
       participant C as ComponentsAPI
       participant V as CVEsAPI
       participant B as Backend

       U->>L: Click application item
       L->>D: Navigate to /applications/{id}
       
       par Load Application
           D->>API: getApplication(id)
           API->>B: GET /api/v1/applications/{id}
           B->>API: Application data
           API->>D: Update app overview
       and Load Components
           D->>C: getComponents(appId)
           C->>B: GET /api/v1/applications/{id}/components
           B->>C: Component list
           C->>D: Update components section
       and Load CVE Timeline
           D->>V: getCVETimeline(appId)
           V->>B: GET /api/v1/cves/timeline?app_id={id}
           B->>V: CVE timeline data
           V->>D: Update timeline visualization
       end

       D->>U: Display complete application view

       Note over U,D: Includes risk assessment<br/>and remediation tracking

CVE Management Flows
--------------------

CVE Feed Flow
~~~~~~~~~~~~~

Consuming tailored vulnerability feeds based on application components.

.. mermaid::

   sequenceDiagram
       participant U as User
       participant F as CVEFeed
       participant S as FilterSidebar
       participant API as CVEsAPI
       participant B as Backend
       participant E as ExportService

       U->>F: Navigate to /cves
       F->>API: getCVEFeed(defaultFilters)
       API->>B: GET /api/v1/cves/feed?limit=50
       B->>API: CVE feed data
       API->>F: Display CVE list

       U->>S: Apply severity filter
       S->>API: getCVEFeed(updatedFilters)
       API->>B: GET /api/v1/cves/feed?severity=HIGH
       B->>API: Filtered CVE data
       API->>F: Update CVE list

       alt Export Request
           U->>F: Click "Export"
           F->>E: exportCVEs(currentFilters, format)
           E->>B: GET /api/v1/cves/export?format=csv
           B->>E: CSV file data
           E->>U: Download CSV file
       end

       Note over U,E: Supports real-time filtering<br/>and multiple export formats

CVE Details Flow
~~~~~~~~~~~~~~~~

Detailed vulnerability information and impact analysis.

.. mermaid::

   sequenceDiagram
       participant U as User
       participant L as CVEList
       participant D as CVEDetails
       participant API as CVEsAPI
       participant C as ComponentsAPI
       participant B as Backend

       U->>L: Click CVE item
       L->>D: Navigate to /cves/{cve_id}
       
       par Load CVE Details
           D->>API: getCVE(cveId)
           API->>B: GET /api/v1/cves/{cve_id}
           B->>API: Detailed CVE data
           API->>D: Update CVE information
       and Load Affected Components
           D->>C: getAffectedComponents(cveId)
           C->>B: GET /api/v1/cves/{cve_id}/affected-components
           B->>C: Component impact data
           C->>D: Update impact analysis
       end

       D->>U: Display comprehensive CVE view

       Note over U,D: Includes CVSS metrics,<br/>remediation steps, and references

Component Management Flows
---------------------------

Component Inventory Flow
~~~~~~~~~~~~~~~~~~~~~~~~

Managing software component tracking and CPE mapping.

.. mermaid::

   sequenceDiagram
       participant U as User
       participant I as ComponentInventory
       participant F as ComponentForm
       participant M as CPEMapping
       participant API as ComponentsAPI
       participant B as Backend

       U->>I: Navigate to /components
       I->>API: getComponents()
       API->>B: GET /api/v1/components
       B->>API: Component list
       API->>I: Display component table

       U->>I: Click "Add Component"
       I->>F: Open component form
       U->>F: Enter component details
       F->>API: createComponent(componentData)
       API->>B: POST /api/v1/components
       
       alt Component Created
           B->>API: 201 Created + component data
           API->>M: Open CPE mapping dialog
           U->>M: Map to CPE string
           M->>API: createCPEMapping(mapping)
           API->>B: POST /api/v1/components/{id}/cpe-mappings
           B->>API: CPE mapping created
           API->>I: Refresh component list
       end

       Note over U,I: Supports bulk operations<br/>and version tracking

User Management Flows (Admin)
------------------------------

User Administration Flow
~~~~~~~~~~~~~~~~~~~~~~~~

Administrative user and permission management.

.. mermaid::

   sequenceDiagram
       participant A as Admin
       participant U as UserManagement
       participant F as UserForm
       participant R as RoleAssignment
       participant API as AuthAPI
       participant B as Backend

       A->>U: Navigate to /admin/users
       U->>API: getUsers()
       API->>B: GET /api/v1/auth/users
       B->>API: User list with roles
       API->>U: Display user table

       A->>U: Click "Create User"
       U->>F: Open user creation form
       A->>F: Fill user details
       F->>API: createUser(userData)
       API->>B: POST /api/v1/auth/users
       
       alt User Created
           B->>API: 201 Created + user data
           API->>R: Open role assignment
           A->>R: Assign user roles
           R->>API: updateUserRoles(userId, roles)
           API->>B: PATCH /api/v1/auth/users/{id}/roles
           B->>API: Roles updated
           API->>U: Refresh user list
       end

       Note over A,U: Includes audit logging<br/>and API key management

Error Handling Patterns
------------------------

Global Error Handling
~~~~~~~~~~~~~~~~~~~~~~

Centralized error management across all flows.

.. mermaid::

   sequenceDiagram
       participant C as Component
       participant E as ErrorBoundary
       participant API as RTKQuery
       participant N as NotificationSystem
       participant L as Logger

       C->>API: Make API request
       API->>API: Request fails
       
       alt Network Error
           API->>E: Throw network error
           E->>N: Show "Connection lost" notification
           E->>L: Log error details
           E->>C: Display retry button
       else Validation Error
           API->>C: Return validation errors
           C->>C: Display field-specific errors
           C->>N: Show summary notification
       else Server Error
           API->>E: Throw server error
           E->>N: Show "Something went wrong" message
           E->>L: Log error with context
           E->>C: Display fallback UI
       end

       Note over C,L: All errors logged with<br/>user context and request details

Performance Optimization Flows
-------------------------------

Data Loading Patterns
~~~~~~~~~~~~~~~~~~~~~~

Optimized data fetching and caching strategies.

.. mermaid::

   sequenceDiagram
       participant U as User
       participant C as Component
       participant Cache as RTKCache
       participant API as RTKQuery
       participant B as Backend

       U->>C: Navigate to page
       C->>Cache: Check cached data
       
       alt Cache Hit
           Cache->>C: Return cached data
           C->>U: Display data immediately
           
           Note over C,API: Background refresh if stale
           C->>API: Fetch fresh data
           API->>B: GET request
           B->>API: Fresh data
           API->>Cache: Update cache
           Cache->>C: Update UI if changed
       else Cache Miss
           C->>API: Fetch data
           API->>B: GET request
           B->>API: Data response
           API->>Cache: Store in cache
           Cache->>C: Return data
           C->>U: Display data
       end

       Note over U,B: Implements stale-while-revalidate<br/>pattern for optimal UX

Next Steps
----------

**Implementation Priority:**

1. **Phase 1**: Authentication and Dashboard flows
2. **Phase 2**: Application and CVE management flows  
3. **Phase 3**: Component management and admin flows
4. **Phase 4**: Performance optimization and error handling

**Testing Strategy:**

- Each flow includes comprehensive Playwright E2E tests
- Unit tests for individual components and hooks
- Integration tests for API interactions
- Performance tests for data-heavy flows

**Accessibility Considerations:**

- Keyboard navigation support for all flows
- Screen reader compatibility
- High contrast mode support
- Focus management during navigation

For implementation details, see:
* :doc:`react-setup` - Technical setup and configuration
* :doc:`../testing/ux-testing` - User experience testing strategies
* :doc:`../api-reference/index` - Backend API documentation
