Test Configuration
==================

This document covers the comprehensive test configuration setup for the CVE Feed Service, including pytest configuration, test databases, fixtures, and environment management.

Overview
--------

Proper test configuration is essential for reliable, fast, and maintainable tests. Our configuration supports multiple test types, environments, and execution modes while maintaining isolation and reproducibility.

**Configuration Components**:
* pytest configuration and markers
* Test database setup and management
* Environment variable management
* Fixture organization and scoping
* Test data management
* CI/CD integration settings

pytest Configuration
--------------------

pyproject.toml Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: toml

   [tool.pytest.ini_options]
   testpaths = ["tests"]
   python_files = ["test_*.py", "*_test.py"]
   python_classes = ["Test*"]
   python_functions = ["test_*"]
   addopts = [
       "--strict-markers",
       "--strict-config",
       "--cov=src",
       "--cov-report=term-missing",
       "--cov-report=html:htmlcov",
       "--cov-report=xml:coverage.xml",
       "--tb=short",
       "--durations=10",
   ]
   asyncio_mode = "auto"
   markers = [
       "unit: marks tests as unit tests (fast, isolated)",
       "integration: marks tests as integration tests (database required)",
       "e2e: marks tests as end-to-end tests (full system)",
       "ux: marks tests as user experience tests",
       "slow: marks tests as slow (external APIs, large datasets)",
       "security: marks tests as security-focused tests",
       "performance: marks tests as performance tests",
       "external: marks tests that require external services",
   ]
   filterwarnings = [
       "ignore::DeprecationWarning",
       "ignore::PendingDeprecationWarning",
   ]

Test Markers Usage
~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Unit test example
   @pytest.mark.unit
   async def test_application_service_create():
       """Fast, isolated unit test."""
       pass
   
   # Integration test example
   @pytest.mark.integration
   async def test_database_operations():
       """Test with real database."""
       pass
   
   # Slow test example
   @pytest.mark.slow
   @pytest.mark.external
   async def test_nvd_api_integration():
       """Test that requires external API calls."""
       pass
   
   # Multiple markers
   @pytest.mark.integration
   @pytest.mark.performance
   async def test_bulk_operations_performance():
       """Integration test focused on performance."""
       pass

Running Tests with Markers
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run only unit tests (fast)
   pytest -m unit
   
   # Run integration tests
   pytest -m integration
   
   # Skip slow tests
   pytest -m "not slow"
   
   # Run security tests only
   pytest -m security
   
   # Run unit and integration tests, skip slow ones
   pytest -m "unit or integration and not slow"
   
   # Run with coverage
   pytest --cov=src --cov-report=html
   
   # Run specific test file
   pytest tests/unit/services/test_application_service.py
   
   # Run with verbose output
   pytest -v
   
   # Run tests in parallel
   pytest -n auto

Database Configuration
---------------------

Test Database Setup
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/conftest.py
   import pytest
   import asyncio
   from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
   from sqlalchemy.pool import StaticPool
   from sqlalchemy.orm import sessionmaker
   
   from src.cve_feed_service.core.database import Base
   from src.cve_feed_service.core.config import get_settings
   
   
   @pytest.fixture(scope="session")
   def event_loop():
       """Create an instance of the default event loop for the test session."""
       loop = asyncio.new_event_loop()
       asyncio.set_event_loop(loop)
       yield loop
       loop.close()
   
   
   @pytest.fixture(scope="session")
   async def test_engine():
       """Create test database engine."""
       # Use in-memory SQLite for unit tests
       engine = create_async_engine(
           "sqlite+aiosqlite:///:memory:",
           poolclass=StaticPool,
           connect_args={
               "check_same_thread": False,
               "isolation_level": None,
           },
           echo=False,  # Set to True for SQL debugging
       )
       
       # Create all tables
       async with engine.begin() as conn:
           await conn.run_sync(Base.metadata.create_all)
       
       yield engine
       
       await engine.dispose()
   
   
   @pytest.fixture(scope="session")
   def test_session_factory(test_engine):
       """Create session factory for tests."""
       return sessionmaker(
           test_engine,
           class_=AsyncSession,
           expire_on_commit=False,
       )
   
   
   @pytest.fixture
   async def db_session(test_session_factory):
       """Create database session for each test with automatic rollback."""
       async with test_session_factory() as session:
           # Start a transaction
           transaction = await session.begin()
           
           yield session
           
           # Rollback transaction to ensure test isolation
           await transaction.rollback()

PostgreSQL Test Database
~~~~~~~~~~~~~~~~~~~~~~~~

For integration tests that require PostgreSQL-specific features:

.. code-block:: python

   # tests/conftest.py (PostgreSQL variant)
   import pytest
   import asyncio
   from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
   
   
   @pytest.fixture(scope="session")
   async def postgres_test_engine():
       """Create PostgreSQL test database engine."""
       # Use test database
       test_db_url = "postgresql+asyncpg://test_user:test_pass@localhost/test_cve_feed"
       
       engine = create_async_engine(
           test_db_url,
           echo=False,
           pool_pre_ping=True,
       )
       
       # Create all tables
       async with engine.begin() as conn:
           await conn.run_sync(Base.metadata.create_all)
       
       yield engine
       
       # Clean up
       async with engine.begin() as conn:
           await conn.run_sync(Base.metadata.drop_all)
       
       await engine.dispose()
   
   
   @pytest.fixture
   async def postgres_session(postgres_test_engine):
       """Create PostgreSQL session for integration tests."""
       async with AsyncSession(postgres_test_engine) as session:
           yield session
           await session.rollback()

Environment Configuration
-------------------------

Test Environment Variables
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/conftest.py
   import os
   import pytest
   from unittest.mock import patch
   
   
   @pytest.fixture(scope="session", autouse=True)
   def test_environment():
       """Set up test environment variables."""
       test_env = {
           "ENVIRONMENT": "test",
           "DATABASE_URL": "sqlite+aiosqlite:///:memory:",
           "SECRET_KEY": "test-secret-key-not-for-production",
           "NVD_API_KEY": "test-nvd-api-key",
           "LOG_LEVEL": "DEBUG",
           "TESTING": "true",
       }
       
       with patch.dict(os.environ, test_env):
           yield

Settings Override
~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/conftest.py
   import pytest
   from src.cve_feed_service.core.config import Settings
   
   
   @pytest.fixture
   def test_settings():
       """Override settings for testing."""
       return Settings(
           environment="test",
           database_url="sqlite+aiosqlite:///:memory:",
           secret_key="test-secret-key",
           nvd_api_key="test-nvd-api-key",
           log_level="DEBUG",
           testing=True,
           # Disable external services in tests
           enable_nvd_sync=False,
           enable_background_tasks=False,
       )

Fixture Organization
-------------------

Fixture Scoping Strategy
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/conftest.py
   
   # Session-scoped fixtures (expensive setup, shared across all tests)
   @pytest.fixture(scope="session")
   async def test_engine():
       """Database engine - created once per test session."""
       pass
   
   # Module-scoped fixtures (shared within a test module)
   @pytest.fixture(scope="module")
   async def sample_cve_data():
       """Sample CVE data - created once per test module."""
       pass
   
   # Function-scoped fixtures (default, created for each test)
   @pytest.fixture
   async def db_session():
       """Database session - fresh for each test."""
       pass
   
   @pytest.fixture
   async def application_service(db_session):
       """Application service - fresh for each test."""
       return ApplicationService(db_session)

Authentication Fixtures
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/fixtures/auth.py
   import pytest
   from uuid import uuid4
   from src.cve_feed_service.services.auth_service import AuthService
   from src.cve_feed_service.schemas.user import UserCreate
   
   
   @pytest.fixture
   async def test_user(db_session):
       """Create a test user."""
       auth_service = AuthService(db_session)
       
       user_data = UserCreate(
           username=f"testuser_{uuid4().hex[:8]}",
           email=f"test_{uuid4().hex[:8]}@example.com",
           full_name="Test User",
           password="testpassword123",
           role="security_analyst"
       )
       
       return await auth_service.create_user(user_data)
   
   
   @pytest.fixture
   async def test_admin_user(db_session):
       """Create a test admin user."""
       auth_service = AuthService(db_session)
       
       user_data = UserCreate(
           username=f"admin_{uuid4().hex[:8]}",
           email=f"admin_{uuid4().hex[:8]}@example.com",
           full_name="Test Admin",
           password="adminpassword123",
           role="it_admin"
       )
       
       return await auth_service.create_user(user_data)
   
   
   @pytest.fixture
   async def api_key(test_user, db_session):
       """Create API key for test user."""
       auth_service = AuthService(db_session)
       api_key_obj, plain_key = await auth_service.create_api_key(
           test_user.id, "Test API Key"
       )
       return plain_key
   
   
   @pytest.fixture
   async def auth_headers(api_key):
       """Create authentication headers."""
       return {"X-API-Key": api_key}
   
   
   async def create_test_user_with_api_key(db_session):
       """Utility function to create user with API key."""
       auth_service = AuthService(db_session)
       
       user_data = UserCreate(
           username=f"testuser_{uuid4().hex[:8]}",
           email=f"test_{uuid4().hex[:8]}@example.com",
           full_name="Test User",
           password="testpassword123",
           role="security_analyst"
       )
       
       user = await auth_service.create_user(user_data)
       api_key_obj, plain_key = await auth_service.create_api_key(
           user.id, "Test API Key"
       )
       
       return user, plain_key

Test Data Fixtures
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/fixtures/data.py
   import pytest
   from datetime import datetime, timedelta
   from uuid import uuid4
   
   from src.cve_feed_service.models.cve import CVE, CVECPEApplicability
   from src.cve_feed_service.models.application import Application
   from src.cve_feed_service.models.component import Component
   from src.cve_feed_service.models.cpe_mapping import CPEMapping
   
   
   @pytest.fixture
   def sample_application_data():
       """Sample application creation data."""
       return {
           "name": f"Test App {uuid4().hex[:8]}",
           "description": "Test application for testing",
           "environment": "test",
           "criticality": "medium",
           "owner": "Test Team"
       }
   
   
   @pytest.fixture
   def sample_component_data():
       """Sample component creation data."""
       return {
           "name": "nginx",
           "version": "1.20.1",
           "vendor": "nginx",
           "component_type": "web_server",
           "description": "Web server component"
       }
   
   
   @pytest.fixture
   def sample_cpe_mapping_data():
       """Sample CPE mapping creation data."""
       return {
           "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
           "confidence": 1.0,
           "mapping_source": "official"
       }
   
   
   @pytest.fixture
   async def sample_cve_data():
       """Sample CVE data for testing."""
       return {
           "cve_id": "CVE-2023-1234",
           "description": "Test vulnerability description",
           "published_date": datetime.utcnow() - timedelta(days=1),
           "last_modified_date": datetime.utcnow(),
           "cvss_v3_score": 7.5,
           "cvss_v3_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H",
           "cvss_v3_severity": "HIGH",
           "cvss_v2_score": 7.8,
           "cwe_ids": ["CWE-79", "CWE-89"],
           "references": [
               {"url": "https://example.com/advisory", "source": "vendor"},
               {"url": "https://nvd.nist.gov/vuln/detail/CVE-2023-1234", "source": "nvd"}
           ]
       }
   
   
   async def create_test_cves_with_applicability(db_session):
       """Create test CVEs with CPE applicability data."""
       # Create sample CVEs
       cve1 = CVE(
           cve_id="CVE-2023-1234",
           description="Test vulnerability in nginx",
           published_date=datetime.utcnow() - timedelta(days=1),
           last_modified_date=datetime.utcnow(),
           cvss_v3_score=7.5,
           cvss_v3_severity="HIGH"
       )
       
       cve2 = CVE(
           cve_id="CVE-2023-5678",
           description="Test vulnerability in log4j",
           published_date=datetime.utcnow() - timedelta(days=2),
           last_modified_date=datetime.utcnow(),
           cvss_v3_score=9.0,
           cvss_v3_severity="CRITICAL"
       )
       
       db_session.add(cve1)
       db_session.add(cve2)
       
       # Create CPE applicability
       cpe_app1 = CVECPEApplicability(
           cve_id="CVE-2023-1234",
           cpe_string="cpe:2.3:a:nginx:nginx:*:*:*:*:*:*:*:*",
           version_start_including="1.20.0",
           version_end_including="1.20.1",
           vulnerable=True
       )
       
       cpe_app2 = CVECPEApplicability(
           cve_id="CVE-2023-5678",
           cpe_string="cpe:2.3:a:apache:log4j:*:*:*:*:*:*:*:*",
           version_start_including="2.0.0",
           version_end_excluding="2.17.0",
           vulnerable=True
       )
       
       db_session.add(cpe_app1)
       db_session.add(cpe_app2)
       
       await db_session.commit()

HTTP Client Configuration
------------------------

FastAPI Test Client
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/conftest.py
   import pytest
   from httpx import AsyncClient
   from fastapi.testclient import TestClient
   
   from src.cve_feed_service.main import app
   
   
   @pytest.fixture
   def test_client():
       """Synchronous test client for simple tests."""
       return TestClient(app)
   
   
   @pytest.fixture
   async def async_test_client():
       """Async test client for async tests."""
       async with AsyncClient(app=app, base_url="http://test") as client:
           yield client

Mock Configuration
-----------------

External Service Mocking
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/conftest.py
   import pytest
   from unittest.mock import AsyncMock, patch
   
   
   @pytest.fixture
   def mock_nvd_client():
       """Mock NVD API client."""
       with patch('src.cve_feed_service.services.nvd_client.NVDAPIClient') as mock:
           mock_instance = AsyncMock()
           mock.return_value.__aenter__.return_value = mock_instance
           
           # Default mock responses
           mock_instance.get_cves_by_date_range.return_value = []
           mock_instance.get_single_cve.return_value = None
           
           yield mock_instance
   
   
   @pytest.fixture
   def mock_email_service():
       """Mock email service."""
       with patch('src.cve_feed_service.services.email_service.EmailService') as mock:
           mock_instance = AsyncMock()
           mock.return_value = mock_instance
           
           mock_instance.send_email.return_value = True
           
           yield mock_instance

Coverage Configuration
---------------------

Coverage Settings
~~~~~~~~~~~~~~~~

.. code-block:: toml

   [tool.coverage.run]
   source = ["src"]
   omit = [
       "*/tests/*",
       "*/migrations/*",
       "*/__pycache__/*",
       "*/venv/*",
       "*/env/*",
       "setup.py",
   ]
   branch = true
   
   [tool.coverage.report]
   exclude_lines = [
       "pragma: no cover",
       "def __repr__",
       "if self.debug:",
       "if settings.DEBUG",
       "raise AssertionError",
       "raise NotImplementedError",
       "if 0:",
       "if __name__ == .__main__.:",
       "class .*\\bProtocol\\):",
       "@(abc\\.)?abstractmethod",
   ]
   show_missing = true
   skip_covered = false
   precision = 2
   
   [tool.coverage.html]
   directory = "htmlcov"
   
   [tool.coverage.xml]
   output = "coverage.xml"

Performance Configuration
------------------------

Test Performance Settings
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/conftest.py
   import pytest
   
   
   def pytest_configure(config):
       """Configure pytest with performance settings."""
       # Add custom markers
       config.addinivalue_line(
           "markers", "benchmark: mark test as a benchmark test"
       )
   
   
   @pytest.fixture
   def benchmark_settings():
       """Settings for benchmark tests."""
       return {
           "max_time": 5.0,  # Maximum time for operations
           "warmup_rounds": 3,  # Warmup iterations
           "test_rounds": 10,  # Test iterations
       }

Parallel Test Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Install pytest-xdist for parallel execution
   pip install pytest-xdist
   
   # Run tests in parallel
   pytest -n auto  # Auto-detect CPU count
   pytest -n 4     # Use 4 workers
   
   # Parallel execution with coverage
   pytest -n auto --cov=src --cov-report=html

CI/CD Configuration
------------------

GitHub Actions Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: yaml

   # .github/workflows/test.yml
   name: Tests
   
   on:
     push:
       branches: [ main, develop ]
     pull_request:
       branches: [ main ]
   
   jobs:
     test:
       runs-on: ubuntu-latest
       strategy:
         matrix:
           python-version: [3.11, 3.12]
       
       services:
         postgres:
           image: postgres:15
           env:
             POSTGRES_PASSWORD: postgres
             POSTGRES_DB: test_cve_feed
           options: >-
             --health-cmd pg_isready
             --health-interval 10s
             --health-timeout 5s
             --health-retries 5
       
       steps:
       - uses: actions/checkout@v4
       
       - name: Set up Python ${{ matrix.python-version }}
         uses: actions/setup-python@v4
         with:
           python-version: ${{ matrix.python-version }}
       
       - name: Install dependencies
         run: |
           python -m pip install --upgrade pip
           pip install -e ".[dev]"
       
       - name: Run unit tests
         run: |
           pytest -m unit --cov=src --cov-report=xml
       
       - name: Run integration tests
         run: |
           pytest -m integration --cov=src --cov-append --cov-report=xml
         env:
           DATABASE_URL: postgresql+asyncpg://postgres:postgres@localhost/test_cve_feed
       
       - name: Upload coverage to Codecov
         uses: codecov/codecov-action@v3
         with:
           file: ./coverage.xml
           flags: unittests
           name: codecov-umbrella

Local Development Configuration
------------------------------

Development Test Commands
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Create useful aliases for development
   alias test-unit="pytest -m unit -v"
   alias test-integration="pytest -m integration -v"
   alias test-fast="pytest -m 'unit or integration and not slow' -v"
   alias test-all="pytest -v"
   alias test-cov="pytest --cov=src --cov-report=html"
   alias test-watch="pytest-watch -- -m unit"

Pre-commit Configuration
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: yaml

   # .pre-commit-config.yaml
   repos:
     - repo: local
       hooks:
         - id: pytest-unit
           name: pytest-unit
           entry: pytest
           language: system
           args: ["-m", "unit", "--tb=short"]
           pass_filenames: false
           always_run: true

Next Steps
----------

* :doc:`testing-patterns` - Common testing patterns and utilities
* :doc:`continuous-integration` - Advanced CI/CD testing strategies
* :doc:`unit-testing` - Unit testing implementation details
* :doc:`integration-testing` - Integration testing strategies
