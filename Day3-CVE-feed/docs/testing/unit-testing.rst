Unit Testing
=============

Unit testing forms the foundation of our testing strategy in the CVE Feed Service. This document provides comprehensive guidance on writing effective unit tests for services, models, and utilities.

Overview
--------

Unit tests are fast, isolated tests that verify individual components in isolation. They should run quickly, be deterministic, and not depend on external resources like databases or APIs.

**Characteristics of Good Unit Tests**:
* **Fast**: Execute in milliseconds
* **Isolated**: No dependencies on external systems
* **Deterministic**: Same result every time
* **Focused**: Test one specific behavior
* **Readable**: Clear intent and assertions

Unit Test Architecture
---------------------

Test Isolation Strategy
~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[Unit Under Test] --> B[Mock Dependencies]
       A --> C[Test Fixtures]
       A --> D[Assertions]
       
       B --> E[Database Session Mock]
       B --> F[External API Mock]
       B --> G[Service Dependencies Mock]
       
       C --> H[Test Data]
       C --> I[Test Objects]
       
       D --> J[Behavior Verification]
       D --> K[State Verification]

**Isolation Techniques**:
* Mock external dependencies
* Use in-memory data structures
* Inject test doubles
* Avoid database connections
* Mock HTTP clients

Service Layer Testing
--------------------

Application Service Tests
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/unit/services/test_application_service.py
   import pytest
   from unittest.mock import AsyncMock, MagicMock
   from uuid import UUID, uuid4
   
   from src.cve_feed_service.services.application_service import ApplicationService
   from src.cve_feed_service.schemas.application import ApplicationCreate, ApplicationUpdate
   from src.cve_feed_service.models.application import Application
   
   
   class TestApplicationService:
       """Unit tests for ApplicationService."""
       
       @pytest.fixture
       def mock_db(self):
           """Mock database session."""
           return AsyncMock()
       
       @pytest.fixture
       def application_service(self, mock_db):
           """ApplicationService instance with mocked dependencies."""
           return ApplicationService(mock_db)
       
       @pytest.fixture
       def sample_application_data(self):
           """Sample application creation data."""
           return ApplicationCreate(
               name="Test Application",
               description="Test Description",
               environment="test",
               criticality="medium"
           )
       
       async def test_create_application_success(
           self, application_service, mock_db, sample_application_data
       ):
           """Test successful application creation."""
           # Arrange
           expected_app = Application(
               id=uuid4(),
               name=sample_application_data.name,
               description=sample_application_data.description,
               environment=sample_application_data.environment,
               criticality=sample_application_data.criticality
           )
           
           # Mock database operations
           mock_db.execute.return_value.scalar_one_or_none.return_value = None  # No duplicate
           mock_db.add = MagicMock()
           mock_db.commit = AsyncMock()
           mock_db.refresh = AsyncMock()
           
           # Mock the created application
           def mock_refresh(app):
               app.id = expected_app.id
           mock_db.refresh.side_effect = mock_refresh
           
           # Act
           result = await application_service.create_application(sample_application_data)
           
           # Assert
           assert result.name == sample_application_data.name
           assert result.description == sample_application_data.description
           assert result.environment == sample_application_data.environment
           assert result.criticality == sample_application_data.criticality
           
           # Verify database interactions
           mock_db.add.assert_called_once()
           mock_db.commit.assert_called_once()
           mock_db.refresh.assert_called_once()
       
       async def test_create_application_duplicate_name_raises_error(
           self, application_service, mock_db, sample_application_data
       ):
           """Test that creating application with duplicate name raises ValueError."""
           # Arrange
           existing_app = Application(
               id=uuid4(),
               name=sample_application_data.name,
               environment=sample_application_data.environment
           )
           
           # Mock database to return existing application
           mock_db.execute.return_value.scalar_one_or_none.return_value = existing_app
           
           # Act & Assert
           with pytest.raises(ValueError, match="already exists"):
               await application_service.create_application(sample_application_data)
           
           # Verify no database modifications were attempted
           mock_db.add.assert_not_called()
           mock_db.commit.assert_not_called()
       
       async def test_get_application_existing_id_returns_application(
           self, application_service, mock_db
       ):
           """Test retrieving an existing application by ID."""
           # Arrange
           app_id = uuid4()
           expected_app = Application(id=app_id, name="Test App")
           
           mock_db.execute.return_value.scalar_one_or_none.return_value = expected_app
           
           # Act
           result = await application_service.get_application(app_id)
           
           # Assert
           assert result == expected_app
           mock_db.execute.assert_called_once()
       
       async def test_get_application_nonexistent_id_returns_none(
           self, application_service, mock_db
       ):
           """Test retrieving a non-existent application returns None."""
           # Arrange
           app_id = uuid4()
           mock_db.execute.return_value.scalar_one_or_none.return_value = None
           
           # Act
           result = await application_service.get_application(app_id)
           
           # Assert
           assert result is None
           mock_db.execute.assert_called_once()

Auth Service Tests
~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/unit/services/test_auth_service.py
   import pytest
   from unittest.mock import AsyncMock, patch
   from uuid import uuid4
   
   from src.cve_feed_service.services.auth_service import AuthService
   from src.cve_feed_service.schemas.user import UserCreate
   from src.cve_feed_service.models.user import User
   
   
   class TestAuthService:
       """Unit tests for AuthService."""
       
       @pytest.fixture
       def mock_db(self):
           return AsyncMock()
       
       @pytest.fixture
       def auth_service(self, mock_db):
           return AuthService(mock_db)
       
       async def test_authenticate_user_valid_credentials_returns_user(
           self, auth_service, mock_db
       ):
           """Test authentication with valid credentials."""
           # Arrange
           username = "testuser"
           password = "testpass"
           hashed_password = "hashed_testpass"
           
           user = User(
               id=uuid4(),
               username=username,
               hashed_password=hashed_password,
               is_active=True
           )
           
           mock_db.execute.return_value.scalar_one_or_none.return_value = user
           
           # Mock password verification
           with patch('src.cve_feed_service.services.auth_service.verify_password') as mock_verify:
               mock_verify.return_value = True
               
               # Act
               result = await auth_service.authenticate_user(username, password)
               
               # Assert
               assert result == user
               mock_verify.assert_called_once_with(password, hashed_password)
       
       async def test_authenticate_user_invalid_password_returns_none(
           self, auth_service, mock_db
       ):
           """Test authentication with invalid password."""
           # Arrange
           username = "testuser"
           password = "wrongpass"
           
           user = User(
               id=uuid4(),
               username=username,
               hashed_password="hashed_testpass",
               is_active=True
           )
           
           mock_db.execute.return_value.scalar_one_or_none.return_value = user
           
           with patch('src.cve_feed_service.services.auth_service.verify_password') as mock_verify:
               mock_verify.return_value = False
               
               # Act
               result = await auth_service.authenticate_user(username, password)
               
               # Assert
               assert result is None
       
       async def test_create_api_key_generates_unique_key(
           self, auth_service, mock_db
       ):
           """Test API key generation creates unique key."""
           # Arrange
           user_id = uuid4()
           key_name = "Test Key"
           
           mock_db.add = AsyncMock()
           mock_db.commit = AsyncMock()
           mock_db.refresh = AsyncMock()
           
           # Act
           with patch('src.cve_feed_service.services.auth_service.generate_api_key') as mock_gen:
               mock_gen.return_value = "test_api_key_12345"
               
               api_key, plain_key = await auth_service.create_api_key(user_id, key_name)
               
               # Assert
               assert plain_key == "test_api_key_12345"
               assert api_key.name == key_name
               assert api_key.user_id == user_id
               mock_db.add.assert_called_once()
               mock_db.commit.assert_called_once()

Model Testing
-------------

Testing Pydantic Models
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/unit/models/test_application.py
   import pytest
   from pydantic import ValidationError
   from datetime import datetime
   
   from src.cve_feed_service.schemas.application import ApplicationCreate, ApplicationUpdate
   
   
   class TestApplicationSchemas:
       """Unit tests for Application Pydantic schemas."""
       
       def test_application_create_valid_data(self):
           """Test ApplicationCreate with valid data."""
           # Arrange & Act
           app_data = ApplicationCreate(
               name="Test App",
               description="Test Description",
               environment="production",
               criticality="high"
           )
           
           # Assert
           assert app_data.name == "Test App"
           assert app_data.description == "Test Description"
           assert app_data.environment == "production"
           assert app_data.criticality == "high"
       
       def test_application_create_minimal_data(self):
           """Test ApplicationCreate with only required fields."""
           # Arrange & Act
           app_data = ApplicationCreate(name="Minimal App")
           
           # Assert
           assert app_data.name == "Minimal App"
           assert app_data.description is None
           assert app_data.environment is None
           assert app_data.criticality is None
       
       def test_application_create_empty_name_raises_validation_error(self):
           """Test that empty name raises ValidationError."""
           # Act & Assert
           with pytest.raises(ValidationError) as exc_info:
               ApplicationCreate(name="")
           
           assert "name" in str(exc_info.value)
       
       def test_application_create_name_too_long_raises_validation_error(self):
           """Test that overly long name raises ValidationError."""
           # Arrange
           long_name = "x" * 256  # Assuming 255 character limit
           
           # Act & Assert
           with pytest.raises(ValidationError) as exc_info:
               ApplicationCreate(name=long_name)
           
           assert "name" in str(exc_info.value)
       
       def test_application_update_partial_data(self):
           """Test ApplicationUpdate with partial data."""
           # Arrange & Act
           update_data = ApplicationUpdate(
               description="Updated Description",
               criticality="low"
           )
           
           # Assert
           assert update_data.description == "Updated Description"
           assert update_data.criticality == "low"
           assert update_data.name is None  # Not updated
           assert update_data.environment is None  # Not updated

Testing SQLAlchemy Models
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/unit/models/test_application_model.py
   import pytest
   from datetime import datetime
   from uuid import uuid4
   
   from src.cve_feed_service.models.application import Application
   from src.cve_feed_service.models.component import Component
   
   
   class TestApplicationModel:
       """Unit tests for Application SQLAlchemy model."""
       
       def test_application_creation(self):
           """Test creating an Application instance."""
           # Arrange & Act
           app = Application(
               name="Test App",
               description="Test Description",
               environment="test",
               criticality="medium"
           )
           
           # Assert
           assert app.name == "Test App"
           assert app.description == "Test Description"
           assert app.environment == "test"
           assert app.criticality == "medium"
           assert app.deleted_at is None
           assert isinstance(app.created_at, datetime)
           assert isinstance(app.updated_at, datetime)
       
       def test_application_soft_delete(self):
           """Test soft delete functionality."""
           # Arrange
           app = Application(name="Test App")
           assert app.deleted_at is None
           
           # Act
           app.soft_delete()
           
           # Assert
           assert app.deleted_at is not None
           assert isinstance(app.deleted_at, datetime)
       
       def test_application_component_relationship(self):
           """Test application-component relationship."""
           # Arrange
           app = Application(name="Test App")
           component1 = Component(name="Component 1", application=app)
           component2 = Component(name="Component 2", application=app)
           
           # Act
           app.components = [component1, component2]
           
           # Assert
           assert len(app.components) == 2
           assert component1 in app.components
           assert component2 in app.components
           assert component1.application == app
           assert component2.application == app

Utility Function Testing
-----------------------

Testing Pure Functions
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/unit/utils/test_cpe_utils.py
   import pytest
   
   from src.cve_feed_service.utils.cpe_utils import (
       validate_cpe_format,
       parse_cpe_components,
       normalize_cpe_string
   )
   
   
   class TestCPEUtils:
       """Unit tests for CPE utility functions."""
       
       @pytest.mark.parametrize("cpe_string,expected", [
           ("cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*", True),
           ("cpe:2.3:o:microsoft:windows:10:*:*:*:*:*:*:*", True),
           ("cpe:2.3:h:cisco:router:*:*:*:*:*:*:*:*", True),
           ("invalid_cpe_string", False),
           ("cpe:2.3:a:nginx:nginx", False),  # Too few components
           ("cpe:2.2:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*", False),  # Wrong version
       ])
       def test_validate_cpe_format(self, cpe_string, expected):
           """Test CPE format validation with various inputs."""
           # Act
           result = validate_cpe_format(cpe_string)
           
           # Assert
           assert result == expected
       
       def test_parse_cpe_components_valid_cpe(self):
           """Test parsing CPE components from valid CPE string."""
           # Arrange
           cpe_string = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"
           
           # Act
           components = parse_cpe_components(cpe_string)
           
           # Assert
           assert components["part"] == "a"
           assert components["vendor"] == "nginx"
           assert components["product"] == "nginx"
           assert components["version"] == "1.20.1"
       
       def test_parse_cpe_components_invalid_cpe_raises_error(self):
           """Test that invalid CPE string raises ValueError."""
           # Arrange
           invalid_cpe = "invalid_cpe_string"
           
           # Act & Assert
           with pytest.raises(ValueError, match="Invalid CPE format"):
               parse_cpe_components(invalid_cpe)
       
       @pytest.mark.parametrize("input_cpe,expected_cpe", [
           ("cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*", 
            "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"),
           ("CPE:2.3:A:NGINX:NGINX:1.20.1:*:*:*:*:*:*:*", 
            "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"),
           ("cpe:2.3:a:nginx:nginx:1.20.1", 
            "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"),
       ])
       def test_normalize_cpe_string(self, input_cpe, expected_cpe):
           """Test CPE string normalization."""
           # Act
           result = normalize_cpe_string(input_cpe)
           
           # Assert
           assert result == expected_cpe

Testing Security Functions
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/unit/utils/test_security.py
   import pytest
   from unittest.mock import patch
   
   from src.cve_feed_service.utils.security import (
       hash_password,
       verify_password,
       generate_api_key,
       hash_api_key
   )
   
   
   class TestSecurityUtils:
       """Unit tests for security utility functions."""
       
       def test_hash_password_returns_different_hash_for_same_password(self):
           """Test that hashing the same password twice returns different hashes."""
           # Arrange
           password = "test_password"
           
           # Act
           hash1 = hash_password(password)
           hash2 = hash_password(password)
           
           # Assert
           assert hash1 != hash2  # Different salts should produce different hashes
           assert hash1 != password  # Hash should not equal plain password
           assert hash2 != password
       
       def test_verify_password_correct_password_returns_true(self):
           """Test password verification with correct password."""
           # Arrange
           password = "test_password"
           password_hash = hash_password(password)
           
           # Act
           result = verify_password(password, password_hash)
           
           # Assert
           assert result is True
       
       def test_verify_password_incorrect_password_returns_false(self):
           """Test password verification with incorrect password."""
           # Arrange
           correct_password = "test_password"
           incorrect_password = "wrong_password"
           password_hash = hash_password(correct_password)
           
           # Act
           result = verify_password(incorrect_password, password_hash)
           
           # Assert
           assert result is False
       
       def test_generate_api_key_returns_unique_keys(self):
           """Test that API key generation returns unique keys."""
           # Act
           key1 = generate_api_key()
           key2 = generate_api_key()
           
           # Assert
           assert key1 != key2
           assert key1.startswith("cvef_")
           assert key2.startswith("cvef_")
           assert len(key1) > 20  # Reasonable length
           assert len(key2) > 20
       
       def test_hash_api_key_consistent_hash(self):
           """Test that hashing the same API key returns consistent hash."""
           # Arrange
           api_key = "cvef_test_api_key_12345"
           
           # Act
           hash1 = hash_api_key(api_key)
           hash2 = hash_api_key(api_key)
           
           # Assert
           assert hash1 == hash2  # Should be deterministic
           assert hash1 != api_key  # Hash should not equal plain key

Mock Strategies
--------------

Database Mocking
~~~~~~~~~~~~~~~

.. code-block:: python

   @pytest.fixture
   def mock_db_session():
       """Mock database session with common methods."""
       mock_session = AsyncMock()
       
       # Mock query result
       mock_result = AsyncMock()
       mock_result.scalar_one_or_none = AsyncMock()
       mock_result.scalars = AsyncMock()
       mock_session.execute.return_value = mock_result
       
       # Mock transaction methods
       mock_session.add = MagicMock()
       mock_session.commit = AsyncMock()
       mock_session.rollback = AsyncMock()
       mock_session.refresh = AsyncMock()
       
       return mock_session

External API Mocking
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @pytest.fixture
   def mock_nvd_client():
       """Mock NVD API client."""
       with patch('src.cve_feed_service.services.nvd_client.NVDAPIClient') as mock_client:
           mock_instance = AsyncMock()
           mock_client.return_value.__aenter__.return_value = mock_instance
           
           # Mock API responses
           mock_instance.get_cves_by_date_range.return_value = [
               {"cve": {"id": "CVE-2023-1234", "descriptions": []}},
               {"cve": {"id": "CVE-2023-5678", "descriptions": []}}
           ]
           
           yield mock_instance

Test Fixtures and Factories
---------------------------

Reusable Test Data
~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/conftest.py
   import pytest
   from uuid import uuid4
   from datetime import datetime
   
   
   @pytest.fixture
   def sample_application():
       """Sample application for testing."""
       return Application(
           id=uuid4(),
           name="Test Application",
           description="Test Description",
           environment="test",
           criticality="medium",
           created_at=datetime.utcnow(),
           updated_at=datetime.utcnow()
       )
   
   @pytest.fixture
   def sample_cve_data():
       """Sample CVE data for testing."""
       return {
           "cve": {
               "id": "CVE-2023-1234",
               "descriptions": [
                   {"lang": "en", "value": "Test vulnerability description"}
               ],
               "published": "2023-01-01T00:00:00.000Z",
               "lastModified": "2023-01-01T00:00:00.000Z"
           },
           "metrics": {
               "cvssMetricV31": [{
                   "cvssData": {
                       "baseScore": 7.5,
                       "baseSeverity": "HIGH",
                       "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H"
                   }
               }]
           }
       }

Performance Testing
------------------

Unit Test Performance
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import time
   import pytest
   
   
   class TestPerformance:
       """Performance tests for critical functions."""
       
       def test_cpe_validation_performance(self):
           """Test that CPE validation is fast enough."""
           # Arrange
           cpe_strings = [
               f"cpe:2.3:a:vendor{i}:product{i}:1.0.0:*:*:*:*:*:*:*"
               for i in range(1000)
           ]
           
           # Act
           start_time = time.time()
           for cpe in cpe_strings:
               validate_cpe_format(cpe)
           duration = time.time() - start_time
           
           # Assert
           assert duration < 1.0  # Should validate 1000 CPEs in under 1 second
       
       @pytest.mark.parametrize("data_size", [10, 100, 1000])
       def test_bulk_processing_scales_linearly(self, data_size):
           """Test that bulk processing scales linearly."""
           # This test ensures O(n) complexity for bulk operations
           test_data = [f"item_{i}" for i in range(data_size)]
           
           start_time = time.time()
           result = process_bulk_data(test_data)
           duration = time.time() - start_time
           
           # Performance should scale roughly linearly
           expected_max_duration = data_size * 0.001  # 1ms per item
           assert duration < expected_max_duration

Next Steps
----------

* :doc:`integration-testing` - Testing service interactions
* :doc:`testing-patterns` - Common testing patterns and utilities
* :doc:`test-configuration` - Test setup and configuration
* :doc:`continuous-integration` - CI/CD testing pipeline
