UX Testing
==========

User Experience (UX) testing for the CVE Feed Service focuses on API usability, error handling, performance, and developer experience. Since this is primarily an API service, UX testing emphasizes the developer experience of consuming the API.

Overview
--------

UX testing for APIs differs from traditional web application UX testing. It focuses on:

**API Usability**:
* Intuitive endpoint design
* Clear request/response formats
* Consistent error messages
* Comprehensive documentation

**Developer Experience**:
* Easy authentication
* Helpful error messages
* Predictable behavior
* Performance characteristics

**Error Handling**:
* Graceful degradation
* Informative error responses
* Proper HTTP status codes
* Recovery guidance

UX Testing Framework
-------------------

Testing Approach
~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[UX Testing] --> B[API Usability]
       A --> C[Error Experience]
       A --> D[Performance UX]
       A --> E[Documentation UX]
       
       B --> F[Endpoint Design]
       B --> G[Request/Response Format]
       B --> H[Authentication Flow]
       
       C --> I[Error Messages]
       C --> J[Status Codes]
       C --> K[Recovery Guidance]
       
       D --> L[Response Times]
       D --> M[Rate Limiting]
       D --> N[Pagination]
       
       E --> O[API Documentation]
       E --> P[Code Examples]
       E --> Q[Interactive Docs]

API Usability Testing
--------------------

Endpoint Design Testing
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/ux/test_api_usability.py
   import pytest
   from httpx import AsyncClient
   
   from tests.fixtures.auth import create_test_user_with_api_key
   
   
   @pytest.mark.ux
   class TestAPIUsability:
       """Test API usability and developer experience."""
       
       async def test_api_endpoint_consistency(self, test_client, auth_headers):
           """Test that API endpoints follow consistent patterns."""
           # Test consistent resource naming
           endpoints = [
               "/api/v1/applications",
               "/api/v1/components", 
               "/api/v1/cves",
               "/api/v1/users"
           ]
           
           for endpoint in endpoints:
               response = await test_client.get(endpoint, headers=auth_headers)
               # All collection endpoints should support GET
               assert response.status_code in [200, 404]  # 404 if no data
               
               # Response should have consistent structure
               if response.status_code == 200:
                   data = response.json()
                   assert isinstance(data, (list, dict))
                   
                   if isinstance(data, dict):
                       # Paginated response structure
                       assert "items" in data or "total" in data
       
       async def test_resource_creation_returns_created_resource(
           self, test_client, auth_headers
       ):
           """Test that POST requests return the created resource."""
           # Arrange
           app_data = {
               "name": "UX Test App",
               "description": "Testing UX patterns",
               "environment": "test"
           }
           
           # Act
           response = await test_client.post(
               "/api/v1/applications",
               json=app_data,
               headers=auth_headers
           )
           
           # Assert
           assert response.status_code == 201
           created_app = response.json()
           
           # Should return the created resource with ID
           assert "id" in created_app
           assert created_app["name"] == app_data["name"]
           assert created_app["description"] == app_data["description"]
           
           # Should include timestamps
           assert "created_at" in created_app
           assert "updated_at" in created_app
       
       async def test_resource_updates_return_updated_resource(
           self, test_client, auth_headers
       ):
           """Test that PATCH requests return the updated resource."""
           # Arrange - Create resource first
           app_data = {"name": "Original Name", "environment": "test"}
           create_response = await test_client.post(
               "/api/v1/applications",
               json=app_data,
               headers=auth_headers
           )
           app_id = create_response.json()["id"]
           
           # Act - Update resource
           update_data = {"description": "Updated description"}
           response = await test_client.patch(
               f"/api/v1/applications/{app_id}",
               json=update_data,
               headers=auth_headers
           )
           
           # Assert
           assert response.status_code == 200
           updated_app = response.json()
           
           # Should return updated resource
           assert updated_app["id"] == app_id
           assert updated_app["name"] == "Original Name"  # Unchanged
           assert updated_app["description"] == "Updated description"  # Changed
           
           # Updated timestamp should be newer
           assert "updated_at" in updated_app
       
       async def test_list_endpoints_support_pagination(
           self, test_client, auth_headers
       ):
           """Test that list endpoints support pagination parameters."""
           # Test pagination parameters are accepted
           response = await test_client.get(
               "/api/v1/applications?limit=10&offset=0",
               headers=auth_headers
           )
           
           assert response.status_code == 200
           data = response.json()
           
           # Should include pagination metadata
           if isinstance(data, dict):
               # Paginated response
               assert "total" in data or "has_more" in data
           
           # Test limit parameter is respected
           response = await test_client.get(
               "/api/v1/applications?limit=1",
               headers=auth_headers
           )
           
           if response.status_code == 200:
               data = response.json()
               if isinstance(data, list):
                   assert len(data) <= 1
               elif isinstance(data, dict) and "items" in data:
                   assert len(data["items"]) <= 1

Authentication UX Testing
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @pytest.mark.ux
   class TestAuthenticationUX:
       """Test authentication user experience."""
       
       async def test_api_key_authentication_is_straightforward(self, test_client):
           """Test that API key authentication is easy to use."""
           # Test that API key can be provided in header
           response = await test_client.get(
               "/api/v1/applications",
               headers={"X-API-Key": "invalid-key"}
           )
           
           # Should return clear authentication error
           assert response.status_code == 401
           error_data = response.json()
           assert "detail" in error_data
           assert "authentication" in error_data["detail"].lower()
       
       async def test_missing_authentication_provides_clear_guidance(self, test_client):
           """Test that missing authentication provides helpful error."""
           # Act
           response = await test_client.get("/api/v1/applications")
           
           # Assert
           assert response.status_code == 401
           error_data = response.json()
           
           # Should provide clear guidance
           assert "detail" in error_data
           detail = error_data["detail"].lower()
           assert any(word in detail for word in ["authentication", "api key", "token"])
       
       async def test_expired_credentials_provide_clear_error(self, test_client):
           """Test that expired credentials provide clear error message."""
           # This would test with an expired JWT token or deactivated API key
           # Implementation depends on your auth system
           pass

Error Handling UX Testing
-------------------------

Error Message Quality
~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @pytest.mark.ux
   class TestErrorHandlingUX:
       """Test error handling user experience."""
       
       async def test_validation_errors_are_specific_and_helpful(
           self, test_client, auth_headers
       ):
           """Test that validation errors provide specific guidance."""
           # Test missing required field
           invalid_data = {"description": "Missing name field"}
           
           response = await test_client.post(
               "/api/v1/applications",
               json=invalid_data,
               headers=auth_headers
           )
           
           assert response.status_code == 422
           error_data = response.json()
           
           # Should specify which field is missing
           assert "detail" in error_data
           detail = str(error_data["detail"]).lower()
           assert "name" in detail
           assert any(word in detail for word in ["required", "missing"])
       
       async def test_duplicate_resource_error_is_clear(
           self, test_client, auth_headers
       ):
           """Test that duplicate resource errors are clear."""
           # Create first application
           app_data = {"name": "Duplicate Test", "environment": "test"}
           response1 = await test_client.post(
               "/api/v1/applications",
               json=app_data,
               headers=auth_headers
           )
           assert response1.status_code == 201
           
           # Try to create duplicate
           response2 = await test_client.post(
               "/api/v1/applications",
               json=app_data,
               headers=auth_headers
           )
           
           assert response2.status_code == 409  # Conflict
           error_data = response2.json()
           
           # Should clearly indicate the conflict
           assert "detail" in error_data
           detail = error_data["detail"].lower()
           assert any(word in detail for word in ["exists", "duplicate", "conflict"])
           assert "duplicate test" in detail  # Should mention the conflicting name
       
       async def test_not_found_errors_are_helpful(
           self, test_client, auth_headers
       ):
           """Test that not found errors provide helpful context."""
           import uuid
           
           # Try to get non-existent resource
           fake_id = str(uuid.uuid4())
           response = await test_client.get(
               f"/api/v1/applications/{fake_id}",
               headers=auth_headers
           )
           
           assert response.status_code == 404
           error_data = response.json()
           
           # Should indicate what was not found
           assert "detail" in error_data
           detail = error_data["detail"].lower()
           assert "application" in detail
           assert "not found" in detail
       
       async def test_server_errors_dont_expose_internal_details(
           self, test_client, auth_headers
       ):
           """Test that server errors don't expose internal implementation."""
           # This test would simulate a server error condition
           # and verify that the response doesn't include stack traces,
           # database errors, or other internal details
           
           # Example: Test with malformed data that might cause internal error
           malformed_data = {"name": "x" * 1000000}  # Extremely long name
           
           response = await test_client.post(
               "/api/v1/applications",
               json=malformed_data,
               headers=auth_headers
           )
           
           # Should return appropriate error without internal details
           if response.status_code == 500:
               error_data = response.json()
               detail = error_data.get("detail", "").lower()
               
               # Should not contain internal error details
               forbidden_terms = ["traceback", "exception", "database", "sql"]
               for term in forbidden_terms:
                   assert term not in detail

Performance UX Testing
----------------------

Response Time Testing
~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @pytest.mark.ux
   @pytest.mark.performance
   class TestPerformanceUX:
       """Test performance aspects of user experience."""
       
       async def test_list_endpoints_respond_quickly(
           self, test_client, auth_headers
       ):
           """Test that list endpoints respond within acceptable time."""
           import time
           
           # Test application list endpoint
           start_time = time.time()
           response = await test_client.get(
               "/api/v1/applications",
               headers=auth_headers
           )
           duration = time.time() - start_time
           
           assert response.status_code == 200
           assert duration < 2.0  # Should respond within 2 seconds
       
       async def test_search_endpoints_respond_quickly(
           self, test_client, auth_headers
       ):
           """Test that search/filter operations are fast."""
           import time
           
           # Test CVE search with filters
           start_time = time.time()
           response = await test_client.get(
               "/api/v1/cves?severity=HIGH&limit=10",
               headers=auth_headers
           )
           duration = time.time() - start_time
           
           assert response.status_code == 200
           assert duration < 3.0  # Search should complete within 3 seconds
       
       async def test_pagination_provides_performance_hints(
           self, test_client, auth_headers
       ):
           """Test that pagination responses include performance hints."""
           response = await test_client.get(
               "/api/v1/applications?limit=1000",  # Large limit
               headers=auth_headers
           )
           
           # Should either limit the response or provide guidance
           if response.status_code == 200:
               data = response.json()
               if isinstance(data, dict):
                   # Should include total count for pagination
                   assert "total" in data or "has_more" in data
           elif response.status_code == 400:
               # Should explain limit restrictions
               error_data = response.json()
               detail = error_data.get("detail", "").lower()
               assert "limit" in detail

Rate Limiting UX Testing
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @pytest.mark.ux
   class TestRateLimitingUX:
       """Test rate limiting user experience."""
       
       async def test_rate_limit_headers_are_informative(
           self, test_client, auth_headers
       ):
           """Test that rate limit headers provide useful information."""
           response = await test_client.get(
               "/api/v1/applications",
               headers=auth_headers
           )
           
           # Should include rate limit headers
           headers = response.headers
           rate_limit_headers = [
               "x-ratelimit-limit",
               "x-ratelimit-remaining", 
               "x-ratelimit-reset"
           ]
           
           # At least some rate limit information should be present
           present_headers = [h for h in rate_limit_headers if h in headers]
           assert len(present_headers) > 0
       
       async def test_rate_limit_exceeded_provides_clear_guidance(
           self, test_client, auth_headers
       ):
           """Test that rate limit exceeded errors are helpful."""
           # This test would need to actually trigger rate limiting
           # which might be difficult in a test environment
           
           # Simulate rate limit exceeded response
           # In a real test, you'd make many requests quickly
           pass

Documentation UX Testing
------------------------

API Documentation Testing
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @pytest.mark.ux
   class TestDocumentationUX:
       """Test API documentation user experience."""
       
       async def test_openapi_schema_is_comprehensive(self, test_client):
           """Test that OpenAPI schema includes comprehensive information."""
           response = await test_client.get("/api/v1/openapi.json")
           
           assert response.status_code == 200
           schema = response.json()
           
           # Should include basic OpenAPI structure
           assert "openapi" in schema
           assert "info" in schema
           assert "paths" in schema
           
           # Should include API information
           info = schema["info"]
           assert "title" in info
           assert "version" in info
           assert "description" in info
           
           # Should document main endpoints
           paths = schema["paths"]
           expected_paths = [
               "/api/v1/applications",
               "/api/v1/applications/{application_id}",
               "/api/v1/cves",
               "/api/v1/cves/feed"
           ]
           
           for path in expected_paths:
               assert any(p.startswith(path.replace("{", "").replace("}", "")) 
                         for p in paths.keys())
       
       async def test_interactive_docs_are_accessible(self, test_client):
           """Test that interactive API documentation is accessible."""
           # Test Swagger UI
           response = await test_client.get("/api/v1/docs")
           assert response.status_code == 200
           
           # Test ReDoc
           response = await test_client.get("/api/v1/redoc")
           assert response.status_code == 200
       
       async def test_endpoint_examples_are_realistic(self, test_client):
           """Test that API documentation includes realistic examples."""
           response = await test_client.get("/api/v1/openapi.json")
           schema = response.json()
           
           # Check that schemas include examples
           if "components" in schema and "schemas" in schema["components"]:
               schemas = schema["components"]["schemas"]
               
               # Application schema should have example
               if "ApplicationCreate" in schemas:
                   app_schema = schemas["ApplicationCreate"]
                   assert "example" in app_schema or "examples" in app_schema

Accessibility Testing
---------------------

API Accessibility
~~~~~~~~~~~~~~~~~

.. code-block:: python

   @pytest.mark.ux
   class TestAPIAccessibility:
       """Test API accessibility for different user types."""
       
       async def test_api_supports_different_content_types(self, test_client):
           """Test that API supports standard content types."""
           # Test JSON content type (primary)
           response = await test_client.get(
               "/api/v1/applications",
               headers={"Accept": "application/json"}
           )
           
           if response.status_code == 200:
               assert response.headers["content-type"].startswith("application/json")
       
       async def test_cors_headers_support_web_clients(self, test_client):
           """Test that CORS headers support web client access."""
           # Test preflight request
           response = await test_client.options(
               "/api/v1/applications",
               headers={
                   "Origin": "https://example.com",
                   "Access-Control-Request-Method": "GET"
               }
           )
           
           # Should include CORS headers
           headers = response.headers
           cors_headers = [
               "access-control-allow-origin",
               "access-control-allow-methods",
               "access-control-allow-headers"
           ]
           
           # At least some CORS headers should be present
           present_headers = [h for h in cors_headers if h in headers]
           assert len(present_headers) > 0

User Journey Testing
-------------------

Complete Workflow Testing
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @pytest.mark.ux
   class TestUserJourneys:
       """Test complete user workflows for UX."""
       
       async def test_new_user_onboarding_journey(self, test_client, db_session):
           """Test the complete journey for a new user."""
           # This test simulates a new user's first experience
           
           # Step 1: User tries to access API without authentication
           response = await test_client.get("/api/v1/applications")
           assert response.status_code == 401
           
           # Error should guide user to authentication
           error_data = response.json()
           assert "authentication" in error_data["detail"].lower()
           
           # Step 2: User creates account and gets API key
           from tests.fixtures.auth import create_test_user_with_api_key
           user, api_key = await create_test_user_with_api_key(db_session)
           headers = {"X-API-Key": api_key}
           
           # Step 3: User successfully accesses API
           response = await test_client.get("/api/v1/applications", headers=headers)
           assert response.status_code == 200
           
           # Step 4: User creates their first application
           app_data = {
               "name": "My First App",
               "description": "Getting started with CVE Feed Service",
               "environment": "development"
           }
           response = await test_client.post(
               "/api/v1/applications",
               json=app_data,
               headers=headers
           )
           assert response.status_code == 201
           
           # Response should be encouraging and informative
           created_app = response.json()
           assert "id" in created_app
           assert created_app["name"] == app_data["name"]
       
       async def test_vulnerability_discovery_journey(
           self, test_client, db_session
       ):
           """Test the journey of discovering vulnerabilities for an application."""
           # Setup: Create user and application with components
           from tests.fixtures.auth import create_test_user_with_api_key
           user, api_key = await create_test_user_with_api_key(db_session)
           headers = {"X-API-Key": api_key}
           
           # Create application
           app_data = {"name": "Production App", "environment": "production"}
           response = await test_client.post(
               "/api/v1/applications",
               json=app_data,
               headers=headers
           )
           app_id = response.json()["id"]
           
           # Add component
           comp_data = {
               "name": "nginx",
               "version": "1.20.1",
               "vendor": "nginx",
               "component_type": "web_server"
           }
           response = await test_client.post(
               f"/api/v1/applications/{app_id}/components",
               json=comp_data,
               headers=headers
           )
           comp_id = response.json()["id"]
           
           # Add CPE mapping
           cpe_data = {
               "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
               "confidence": 1.0,
               "mapping_source": "official"
           }
           response = await test_client.post(
               f"/api/v1/components/{comp_id}/cpe-mappings",
               json=cpe_data,
               headers=headers
           )
           assert response.status_code == 201
           
           # Get vulnerability feed
           response = await test_client.get(
               f"/api/v1/cves/feed?application_id={app_id}",
               headers=headers
           )
           assert response.status_code == 200
           
           # Response should be structured for easy consumption
           feed_data = response.json()
           assert "cves" in feed_data
           assert "total" in feed_data
           assert isinstance(feed_data["cves"], list)

UX Metrics and Monitoring
-------------------------

UX Performance Metrics
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @pytest.mark.ux
   class TestUXMetrics:
       """Test UX-related metrics and monitoring."""
       
       async def test_response_time_consistency(self, test_client, auth_headers):
           """Test that response times are consistent."""
           import time
           import statistics
           
           # Make multiple requests and measure response times
           response_times = []
           
           for _ in range(10):
               start_time = time.time()
               response = await test_client.get(
                   "/api/v1/applications",
                   headers=auth_headers
               )
               duration = time.time() - start_time
               
               assert response.status_code == 200
               response_times.append(duration)
           
           # Response times should be consistent (low variance)
           avg_time = statistics.mean(response_times)
           std_dev = statistics.stdev(response_times)
           
           # Standard deviation should be less than 50% of average
           assert std_dev < (avg_time * 0.5)
       
       async def test_error_rate_is_acceptable(self, test_client, auth_headers):
           """Test that error rates are within acceptable limits."""
           # Make multiple valid requests
           total_requests = 20
           error_count = 0
           
           for i in range(total_requests):
               response = await test_client.get(
                   "/api/v1/applications",
                   headers=auth_headers
               )
               
               if response.status_code >= 500:
                   error_count += 1
           
           # Error rate should be very low for valid requests
           error_rate = error_count / total_requests
           assert error_rate < 0.05  # Less than 5% error rate

Next Steps
----------

* :doc:`testing-patterns` - Common testing patterns and utilities
* :doc:`test-configuration` - Advanced test configuration
* :doc:`continuous-integration` - CI/CD testing pipeline
* :doc:`../user-guide/index` - User guide for API consumers
