BDD Scenarios Reference
========================

This section provides a comprehensive reference for all BDD scenarios implemented in the CVE Feed Service. Each scenario validates specific business requirements using natural language that stakeholders can understand.

Scenario Organization
---------------------

Scenarios are organized by business domain and tagged for easy execution:

.. list-table:: Scenario Tags
   :header-rows: 1
   :widths: 20 30 50

   * - Tag
     - Purpose
     - Usage
   * - ``@smoke``
     - Critical functionality
     - ``behave --tags=@smoke``
   * - ``@regression``
     - Full regression testing
     - ``behave --tags=@regression``
   * - ``@api``
     - API-focused scenarios
     - ``behave --tags=@api``
   * - ``@performance``
     - Performance testing
     - ``behave --tags=@performance``
   * - ``@security``
     - Security validation
     - ``behave --tags=@security``
   * - ``@slow``
     - Long-running tests
     - ``behave --tags="not @slow"``

Application Management Scenarios
---------------------------------

**Core CRUD Operations**

.. code-block:: gherkin

   Scenario: Create a new application
     Given I am an authenticated user
     When I create an application with the following details:
       | name        | Test Application      |
       | environment | production           |
       | description | A test application   |
       | criticality | high                 |
       | owner       | Security Team        |
     Then the application should be created successfully
     And the response should contain the application details
     And the application should have a unique ID

**Data Validation**

.. code-block:: gherkin

   Scenario: Fail to create application with missing required fields
     Given I am an authenticated user
     When I try to create an application without a name
     Then the request should fail with validation error
     And the error message should indicate missing required field

**Business Logic Validation**

.. code-block:: gherkin

   Scenario: Create duplicate application in same environment
     Given I am an authenticated user
     And an application "Existing App" exists in "production" environment
     When I try to create another application "Existing App" in "production" environment
     Then the request should fail with conflict error
     And the error message should indicate duplicate application

Authentication & Authorization Scenarios
-----------------------------------------

**User Registration**

.. code-block:: gherkin

   Scenario: User registration
     Given I am not authenticated
     When I register a new user with the following details:
       | username  | testuser              |
       | email     | <EMAIL>      |
       | full_name | Test User             |
       | password  | SecurePassword123!    |
       | role      | SECURITY_ANALYST      |
     Then the user should be created successfully
     And the response should contain the user details
     And the password should not be visible in the response

**Role-Based Access Control**

.. code-block:: gherkin

   Scenario: Role-based access control - Security Analyst
     Given I am an authenticated user with role "SECURITY_ANALYST"
     When I try to access CVE information
     Then the request should be successful
     When I try to create an application
     Then the request should be successful
     When I try to access admin functions
     Then the request should fail with forbidden error

CVE Management Scenarios
-------------------------

**CVE Listing and Filtering**

.. code-block:: gherkin

   Scenario: Filter CVEs by severity
     Given I am an authenticated user
     And the following CVEs exist in the system:
       | cve_id          | cvss_v3_severity |
       | CVE-2023-HIGH-1 | HIGH             |
       | CVE-2023-HIGH-2 | HIGH             |
       | CVE-2023-MED-1  | MEDIUM           |
       | CVE-2023-CRIT-1 | CRITICAL         |
     When I request CVEs filtered by "HIGH" severity
     Then I should receive 2 CVEs
     And all returned CVEs should have "HIGH" severity

**Tailored Feeds**

.. code-block:: gherkin

   Scenario: Get tailored CVE feed for specific application
     Given I am an authenticated user
     And an application "Web App" exists with ID stored as "app_id"
     And the application has the following components:
       | name    | version | cpe_string                           |
       | Apache  | 2.4.41  | cpe:2.3:a:apache:http_server:2.4.41 |
       | PHP     | 7.4.3   | cpe:2.3:a:php:php:7.4.3             |
     And CVEs exist that affect these components
     When I request the tailored CVE feed for application "app_id"
     Then I should receive CVEs relevant to the application
     And the CVEs should be related to Apache or PHP components

Component Management Scenarios
-------------------------------

**Component Lifecycle**

.. code-block:: gherkin

   Scenario: Add component to application
     Given an application "Web Portal" exists with ID stored as "app_id"
     When I add a component to the application with details:
       | name         | Apache HTTP Server |
       | version      | 2.4.41            |
       | cpe_string   | cpe:2.3:a:apache:http_server:2.4.41:*:*:*:*:*:*:* |
       | description  | Web server component |
     Then the component should be added successfully
     And the component should be linked to the application
     And the component should have a valid CPE string

**Vulnerability Detection**

.. code-block:: gherkin

   Scenario: Detect vulnerable components
     Given an application "Legacy System" exists with components:
       | name    | version | cpe_string                           |
       | OpenSSL | 1.0.1   | cpe:2.3:a:openssl:openssl:1.0.1:*   |
       | Apache  | 2.2.15  | cpe:2.3:a:apache:http_server:2.2.15 |
     And CVEs exist that affect these components:
       | cve_id          | affected_cpe                         | severity |
       | CVE-2014-0160   | cpe:2.3:a:openssl:openssl:1.0.1:*   | HIGH     |
       | CVE-2012-0883   | cpe:2.3:a:apache:http_server:2.2.15 | MEDIUM   |
     When I request vulnerable components for the application
     Then I should receive 2 vulnerable components
     And each component should show its associated CVEs
     And the components should be ranked by severity

Performance & Scalability Scenarios
------------------------------------

**Load Testing**

.. code-block:: gherkin

   Scenario: Concurrent user load testing
     Given 100 concurrent users are accessing the system
     And each user performs typical operations:
       | operation           | frequency | expected_response_time |
       | List applications   | 40%       | < 200ms               |
       | View CVE details    | 30%       | < 500ms               |
       | Search CVEs         | 20%       | < 1000ms              |
       | Generate reports    | 10%       | < 5000ms              |
     When the load test runs for 10 minutes
     Then 95% of requests should meet response time targets
     And error rate should be less than 0.1%
     And system should remain stable throughout

**Auto-Scaling**

.. code-block:: gherkin

   Scenario: Auto-scaling behavior
     Given auto-scaling is configured:
       | metric          | scale_up_threshold | scale_down_threshold | min_instances | max_instances |
       | CPU utilization | 70%                | 30%                  | 2             | 10            |
       | Memory usage    | 80%                | 40%                  | 2             | 10            |
       | Request queue   | 100 requests       | 10 requests          | 2             | 10            |
     When load increases gradually
     Then new instances should be provisioned automatically
     And load should be distributed evenly
     And scaling should not cause service disruption

Security & Compliance Scenarios
--------------------------------

**NIST Cybersecurity Framework**

.. code-block:: gherkin

   Scenario: NIST Cybersecurity Framework compliance
     Given applications with different security maturity levels:
       | application    | identify | protect | detect | respond | recover |
       | Critical App   | 90%      | 85%     | 80%    | 75%     | 70%     |
       | Standard App   | 70%      | 65%     | 60%    | 55%     | 50%     |
       | Legacy App     | 40%      | 35%     | 30%    | 25%     | 20%     |
     When I generate NIST CSF compliance report
     Then each application should show framework maturity scores
     And improvement recommendations should be provided
     And compliance gaps should be prioritized by risk

**GDPR Privacy Impact Assessment**

.. code-block:: gherkin

   Scenario: GDPR privacy impact assessment
     Given applications processing personal data:
       | application | data_types           | data_subjects | processing_purpose |
       | CRM System  | name, email, phone   | customers     | marketing          |
       | HR Portal   | SSN, salary, address | employees     | payroll            |
       | Analytics   | IP, behavior, prefs  | users         | optimization       |
     When I conduct GDPR privacy impact assessment
     Then data processing should be mapped completely
     And legal basis should be documented
     And privacy risks should be assessed and mitigated

Error Handling & Recovery Scenarios
------------------------------------

**Network Failures**

.. code-block:: gherkin

   Scenario: Network connectivity failures
     Given the system is ingesting CVE data from NVD
     When network connectivity is lost intermittently:
       | failure_type    | duration | frequency | expected_behavior        |
       | Complete outage | 30s      | once      | retry_with_backoff       |
       | Packet loss     | 60s      | periodic  | adaptive_timeout         |
       | DNS failure     | 45s      | once      | fallback_dns             |
       | SSL handshake   | 15s      | sporadic  | certificate_validation   |
     Then the system should handle each failure gracefully
     And data ingestion should resume automatically
     And no data should be lost or corrupted

**Disaster Recovery**

.. code-block:: gherkin

   Scenario: Disaster recovery testing
     Given disaster recovery procedures are documented
     When disaster recovery is tested:
       | disaster_scenario    | rto_target | rpo_target | test_frequency | success_criteria |
       | Primary DC failure   | 1 hour     | 15 minutes | quarterly      | full_functionality |
       | Database corruption  | 30 minutes | 5 minutes  | monthly        | data_integrity     |
       | Security breach      | 2 hours    | 0 minutes  | semi_annually  | secure_operation   |
       | Complete system loss | 4 hours    | 1 hour     | annually       | business_continuity|
     Then recovery should meet RTO/RPO targets
     And all systems should be fully functional
     And lessons learned should be documented

Data Ingestion Scenarios
-------------------------

**Bulk Import**

.. code-block:: gherkin

   Scenario: Successful bulk CVE import
     Given the NVD API returns sample CVE data:
       | cve_id          | cvss_v3_score | severity | description                    |
       | CVE-2023-TEST1  | 9.8           | CRITICAL | Critical test vulnerability    |
       | CVE-2023-TEST2  | 7.5           | HIGH     | High severity test issue       |
       | CVE-2023-TEST3  | 4.3           | MEDIUM   | Medium severity test problem   |
     When I perform a bulk CVE import
     Then all 3 CVEs should be imported successfully
     And the CVEs should be stored in the database
     And each CVE should have complete metadata

**Incremental Updates**

.. code-block:: gherkin

   Scenario: Incremental CVE update
     Given existing CVEs in the database:
       | cve_id          | last_modified   |
       | CVE-2023-OLD    | 2023-01-01      |
       | CVE-2023-UPDATE | 2023-06-01      |
     And the NVD API returns updated CVE data:
       | cve_id          | last_modified   | cvss_v3_score |
       | CVE-2023-UPDATE | 2023-12-01      | 8.5           |
       | CVE-2023-NEW    | 2023-12-15      | 6.2           |
     When I perform an incremental CVE update
     Then the existing CVE "CVE-2023-UPDATE" should be updated
     And the new CVE "CVE-2023-NEW" should be added
     And the unchanged CVE "CVE-2023-OLD" should remain the same

Scenario Execution Patterns
----------------------------

**Data-Driven Testing**

Many scenarios use data tables to test multiple conditions:

.. code-block:: gherkin

   Scenario Outline: CPE string validation
     Given I want to add a component with CPE "<input_cpe>"
     When I validate the CPE string
     Then it should be normalized to "<expected_cpe>"
     And validation should "<result>"

     Examples:
       | input_cpe                                    | expected_cpe                                  | result |
       | cpe:/a:apache:http_server:2.4.41            | cpe:2.3:a:apache:http_server:2.4.41:*:*:*:*:*:*:* | pass   |
       | apache:http_server:2.4.41                   | cpe:2.3:a:apache:http_server:2.4.41:*:*:*:*:*:*:* | pass   |
       | invalid-cpe-format                          | invalid-cpe-format                            | fail   |

**Background Steps**

Common setup is handled in Background sections:

.. code-block:: gherkin

   Background:
     Given the CVE feed service is running
     And I have a clean database
     And I am an authenticated user with role "SECURITY_ANALYST"

This ensures consistent test environment setup across all scenarios in a feature file.
