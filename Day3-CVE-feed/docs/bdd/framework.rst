BDD Framework Architecture
===========================

The CVE Feed Service implements a sophisticated Behavior-Driven Development framework that bridges the gap between business requirements and technical implementation. This section details the framework architecture, components, and implementation patterns.

Framework Components
--------------------

The BDD framework consists of several key components working together:

.. code-block:: text

   BDD Framework Architecture
   ┌─────────────────────────────────────────────────────────────────┐
   │                        Feature Layer                            │
   │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
   │  │ Application     │  │ Security &      │  │ Performance &   │ │
   │  │ Management      │  │ Compliance      │  │ Scalability     │ │
   │  │ (17 scenarios)  │  │ (15 scenarios)  │  │ (15 scenarios)  │ │
   │  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
   │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
   │  │ Authentication  │  │ Component       │  │ Error Handling  │ │
   │  │ & Authorization │  │ Management      │  │ & Recovery      │ │
   │  │ (25 scenarios)  │  │ (15 scenarios)  │  │ (15 scenarios)  │ │
   │  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
   │  ┌─────────────────┐  ┌─────────────────┐                     │
   │  │ CVE Management  │  │ Data Ingestion  │                     │
   │  │ (18 scenarios)  │  │ (15 scenarios)  │                     │
   │  └─────────────────┘  └─────────────────┘                     │
   └─────────────────────────────────────────────────────────────────┘
                                    │
   ┌─────────────────────────────────────────────────────────────────┐
   │                      Step Definition Layer                      │
   │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
   │  │ Given Steps     │  │ When Steps      │  │ Then Steps      │ │
   │  │ (Preconditions) │  │ (Actions)       │  │ (Assertions)    │ │
   │  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
   │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
   │  │ Advanced Steps  │  │ Performance     │  │ Security Steps  │ │
   │  │ (Complex Logic) │  │ Steps           │  │ (Compliance)    │ │
   │  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
   └─────────────────────────────────────────────────────────────────┘
                                    │
   ┌─────────────────────────────────────────────────────────────────┐
   │                    Test Environment Layer                       │
   │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
   │  │ Database Setup  │  │ HTTP Client     │  │ Mock Services   │ │
   │  │ (SQLite Memory) │  │ (AsyncClient)   │  │ (External APIs) │ │
   │  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
   │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
   │  │ Authentication  │  │ Data Fixtures   │  │ Cleanup         │ │
   │  │ (JWT Tokens)    │  │ (Test Data)     │  │ (Teardown)      │ │
   │  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
   └─────────────────────────────────────────────────────────────────┘
                                    │
   ┌─────────────────────────────────────────────────────────────────┐
   │                     Reporting Layer                             │
   │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
   │  │ HTML Reports    │  │ Business Value  │  │ Compliance      │ │
   │  │ (Executive)     │  │ Mapping         │  │ Tracking        │ │
   │  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
   │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
   │  │ Coverage        │  │ Performance     │  │ CI/CD           │ │
   │  │ Analysis        │  │ Metrics         │  │ Integration     │ │
   │  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
   └─────────────────────────────────────────────────────────────────┘

Feature Files Structure
------------------------

Feature files are organized by business domain with consistent structure:

.. code-block:: text

   tests/behave/features/
   ├── application_management.feature    # @api @core @smoke
   ├── authentication.feature           # @security @auth @regression
   ├── cve_management.feature           # @api @cve @smoke
   ├── data_ingestion.feature           # @integration @ingestion
   ├── component_management.feature     # @components @security @regression
   ├── security_compliance.feature     # @security @compliance @slow
   ├── performance_scalability.feature # @performance @scalability @slow
   └── error_handling_recovery.feature # @reliability @error @regression

Each feature file follows the standard Gherkin structure:

.. code-block:: gherkin

   @tags @for @organization
   Feature: Business Domain Name
     As a [role]
     I want to [capability]
     So that I can [business value]

     Background:
       Given common preconditions
       And shared setup steps

     @scenario-specific-tags
     Scenario: Specific business scenario
       Given specific preconditions
       When I perform actions
       Then I should see expected results
       And additional validations

     Scenario Outline: Data-driven scenarios
       Given I have "<parameter>"
       When I perform action with "<input>"
       Then I should get "<expected_result>"

       Examples:
         | parameter | input | expected_result |
         | value1    | data1 | result1         |
         | value2    | data2 | result2         |

Step Definition Patterns
-------------------------

Step definitions follow consistent patterns for maintainability:

**Given Steps (Preconditions)**

.. code-block:: python

   @given('I am an authenticated user')
   def step_authenticated_user(context):
       """Set up an authenticated user with default role."""
       # Implementation details...

   @given('I am an authenticated user with role "{role}"')
   def step_authenticated_user_with_role(context, role):
       """Set up an authenticated user with specific role."""
       # Implementation details...

   @given('the following {entity_type} exist')
   def step_entities_exist(context, entity_type):
       """Create multiple entities from data table."""
       # Implementation details...

**When Steps (Actions)**

.. code-block:: python

   @when('I create an application with the following details')
   def step_create_application_with_details(context):
       """Create application using data table."""
       # Implementation details...

   @when('I request {entity_type} filtered by "{filter_value}" {filter_type}')
   def step_request_filtered_entities(context, entity_type, filter_value, filter_type):
       """Request entities with specific filter."""
       # Implementation details...

   @when('I perform {operation_type} operation')
   def step_perform_operation(context, operation_type):
       """Perform specific operation type."""
       # Implementation details...

**Then Steps (Assertions)**

.. code-block:: python

   @then('the {entity_type} should be created successfully')
   def step_entity_created_successfully(context, entity_type):
       """Verify entity creation success."""
       # Implementation details...

   @then('I should receive {count:d} {entity_type}')
   def step_receive_entity_count(context, count, entity_type):
       """Verify specific count of entities."""
       # Implementation details...

   @then('the request should fail with {error_type} error')
   def step_request_fails_with_error(context, error_type):
       """Verify specific error type."""
       # Implementation details...

Test Environment Management
----------------------------

The BDD framework uses sophisticated environment management:

**Database Isolation**

.. code-block:: python

   def before_scenario(context, scenario):
       """Set up before each scenario."""
       # Create fresh database for each scenario
       context.behave_context.db_engine = create_async_engine(
           "sqlite+aiosqlite:///:memory:",
           poolclass=StaticPool,
           connect_args={"check_same_thread": False},
       )
       
       # Create all tables
       async def create_tables():
           async with context.behave_context.db_engine.begin() as conn:
               await conn.run_sync(Base.metadata.create_all)
       
       context.loop.run_until_complete(create_tables())

**HTTP Client Setup**

.. code-block:: python

   # Create HTTP client with dependency overrides
   context.behave_context.client = AsyncClient(app=app, base_url="http://test")
   
   # Override database dependency
   async def get_test_db():
       async with AsyncSession(context.behave_context.db_engine) as session:
           yield session
   
   app.dependency_overrides[get_db] = get_test_db

**Mock Service Integration**

.. code-block:: python

   @when('I perform a bulk CVE import')
   def step_perform_bulk_import(context):
       """Perform bulk CVE import with mocked data."""
       async def bulk_import():
           with patch('src.cve_feed_service.services.cve_ingestion_service.NVDAPIClient') as mock_client:
               # Mock the NVD client to return test data
               mock_instance = AsyncMock()
               mock_instance.get_cves_by_date_range.return_value = test_data
               mock_client.return_value.__aenter__.return_value = mock_instance
               
               response = await context.client.post("/api/v1/admin/ingest/bulk")
               context.response = response

Advanced Testing Patterns
--------------------------

**Performance Testing Integration**

.. code-block:: python

   @when('the load test runs for {duration:d} minutes')
   def step_run_load_test(context, duration):
       """Simulate load test execution."""
       async def simulate_load_test():
           response_times = []
           error_count = 0
           
           for _ in range(concurrent_users):
               start_time = time.time()
               response = await context.client.get("/api/v1/applications/")
               end_time = time.time()
               
               response_time = (end_time - start_time) * 1000
               response_times.append(response_time)
               
               if response.status_code >= 400:
                   error_count += 1

**Security Testing Integration**

.. code-block:: python

   @when('I generate NIST CSF compliance report')
   def step_generate_nist_csf_report(context):
       """Generate NIST Cybersecurity Framework compliance report."""
       async def generate_compliance_report():
           response = await context.client.get("/api/v1/compliance/nist-csf")
           context.response = response

**Error Injection Testing**

.. code-block:: python

   @when('network connectivity is lost intermittently')
   def step_network_connectivity_lost(context):
       """Simulate network connectivity issues."""
       async def simulate_network_failures():
           for scenario in failure_scenarios:
               with patch('httpx.AsyncClient.get') as mock_get:
                   if scenario['failure_type'] == 'Complete outage':
                       mock_get.side_effect = Exception("Network unreachable")
                   
                   response = await context.client.get("/api/v1/health")
                   context.response = response

Configuration Management
-------------------------

**Behave Configuration (behave.ini)**

.. code-block:: ini

   [behave]
   paths = tests/behave/features
   step_definitions = tests/behave/steps
   format = pretty
   show_timings = true
   show_skipped = false
   logging_level = INFO
   
   # Advanced configuration
   default_tags = ~@wip,~@manual
   junit = true
   junit_directory = reports/junit
   summary = true
   snippets = true

**Tag-Based Execution**

.. code-block:: bash

   # Run smoke tests only
   behave --tags=@smoke
   
   # Run regression tests excluding slow tests
   behave --tags="@regression and not @slow"
   
   # Run API tests for specific domain
   behave --tags="@api and @application"
   
   # Run performance tests
   behave --tags=@performance

**Environment-Specific Configuration**

.. code-block:: python

   # Environment setup based on tags
   def before_all(context):
       if 'performance' in context.config.tags:
           setup_performance_monitoring(context)
       
       if 'security' in context.config.tags:
           setup_security_validation(context)
       
       if 'slow' in context.config.tags:
           increase_timeouts(context)

Reporting and Analytics
-----------------------

**HTML Report Generation**

The framework generates comprehensive HTML reports with:

- Executive summary with key metrics
- Business value mapping for each scenario
- Feature coverage analysis
- Performance metrics and trends
- Compliance tracking and validation
- Interactive dashboards for stakeholders

**CI/CD Integration**

.. code-block:: yaml

   # GitHub Actions workflow
   - name: Run BDD Tests
     run: |
       python -m pytest tests/behave/test_bdd_runner.py -v
       python tests/behave/generate_bdd_report.py
   
   - name: Upload BDD Reports
     uses: actions/upload-artifact@v3
     with:
       name: bdd-reports
       path: reports/

**Metrics Collection**

The framework automatically collects:

- Scenario execution times
- Pass/fail rates by feature
- Business value delivery metrics
- Compliance validation results
- Performance benchmarks
- Error patterns and trends

Best Practices
--------------

**Scenario Design**

1. **Single Responsibility** - Each scenario tests one business capability
2. **Clear Language** - Use business terminology, not technical jargon
3. **Data Independence** - Scenarios should not depend on each other
4. **Realistic Data** - Use realistic test data that reflects actual usage
5. **Comprehensive Coverage** - Cover happy path, edge cases, and error conditions

**Step Definition Design**

1. **Reusability** - Create reusable steps for common operations
2. **Parameterization** - Use parameters to make steps flexible
3. **Clear Assertions** - Provide meaningful error messages
4. **Async Handling** - Properly handle async operations
5. **Resource Cleanup** - Ensure proper cleanup in teardown

**Maintenance**

1. **Regular Updates** - Keep scenarios synchronized with system changes
2. **Refactoring** - Regularly refactor step definitions for maintainability
3. **Documentation** - Document complex business logic and edge cases
4. **Performance** - Monitor and optimize test execution times
5. **Feedback Loop** - Regularly review scenarios with business stakeholders
