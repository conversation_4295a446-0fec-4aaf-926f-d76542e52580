CVE Feed Service Documentation
=================================

.. image:: https://img.shields.io/badge/version-0.1.0-blue.svg
   :alt: Version
   :target: #

.. image:: https://img.shields.io/badge/python-3.11+-blue.svg
   :alt: Python Version
   :target: #

.. image:: https://img.shields.io/badge/license-MIT-green.svg
   :alt: License
   :target: #

.. image:: https://img.shields.io/badge/docs-sphinx-blue.svg
   :alt: Documentation
   :target: #

Welcome to the **CVE Feed Service** - an enterprise-grade vulnerability management platform that provides comprehensive solutions for tracking, assessing, and managing Common Vulnerabilities and Exposures (CVE) data across your organization's applications and infrastructure.

🎨 **NEW: Modern React Interface** - Complete dark-mode-first web interface with comprehensive user flows! See :doc:`PRD_React_Interface` for specifications.

🛡️ **Enterprise Security Platform**
   Complete vulnerability management with real-time CVE feeds, automated risk assessment, and compliance reporting.

🧪 **World-Class Testing Framework**
   Industry-leading testing approach with 72 comprehensive tests including advanced BDD scenarios for business validation.

📊 **Business-Driven Development**
   Natural language scenarios that validate business requirements and ensure stakeholder alignment.

🚀 **Production-Ready Architecture**
   Modern async Python with FastAPI, comprehensive monitoring, and enterprise deployment patterns.

🚀 **Quick Start**
==================

Get up and running in minutes:

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/forkrul/day3-cve-feed.git
   cd day3-cve-feed

   # Install dependencies
   pip install -r requirements.txt

   # Start the service
   python -m uvicorn src.main:app --reload

📋 **What's Inside**
===================

.. grid:: 2
   :gutter: 3

   .. grid-item-card:: 🔍 Real-time CVE Monitoring
      :link: services/cve-ingestion-service
      :link-type: doc

      Automated ingestion and processing of CVE data from multiple sources including NVD, MITRE, and vendor-specific feeds.

   .. grid-item-card:: 🎯 Intelligent Matching
      :link: services/overview
      :link-type: doc

      Advanced algorithms to match vulnerabilities with your applications and components with high accuracy.

   .. grid-item-card:: 📊 Analytics Dashboard
      :link: user-guide/react-interface-flows
      :link-type: doc

      Modern React-based interface with real-time dashboards, reporting, and visualization capabilities.

   .. grid-item-card:: 🔔 Smart Notifications
      :link: user-guide/workflows
      :link-type: doc

      Multi-channel notification system with intelligent filtering and escalation policies.

.. toctree::
   :maxdepth: 2
   :caption: 📚 Getting Started:

   overview
   installation

.. toctree::
   :maxdepth: 2
   :caption: 👥 User Documentation:

   user-guide/index

.. toctree::
   :maxdepth: 2
   :caption: 🔧 Technical Documentation:

   services/index
   api-reference/index

.. toctree::
   :maxdepth: 2
   :caption: 🧪 Testing & Quality:

   testing/index
   bdd/index

.. toctree::
   :maxdepth: 2
   :caption: 🚀 Development & Deployment:

   development/index
   deployment/index
   enterprise/index

.. toctree::
   :maxdepth: 2
   :caption: 🎨 React Interface:

   PRD_React_Interface

Quick Start
-----------

The CVE Feed Service is a FastAPI-based application that:

* Manages application inventories with component tracking
* Ingests CVE data from the National Vulnerability Database (NVD)
* Provides tailored vulnerability feeds based on CPE (Common Platform Enumeration) mappings
* Offers role-based access control for security teams

Key Features
------------

✅ **Enterprise Application Management**
   Complete CRUD operations for applications and their components with advanced lifecycle management

✅ **Intelligent CVE Data Ingestion**
   Automated ingestion from NVD with rate limiting, incremental updates, and bulk processing capabilities

✅ **Tailored Vulnerability Feeds**
   Application-specific CVE feeds based on component CPE mappings with risk prioritization

✅ **Advanced Authentication & Authorization**
   JWT-based auth with role-based access control (RBAC), API keys, and multi-factor authentication support

✅ **CPE Mapping & Validation**
   Support for CPE 2.3 format with validation, normalization, and intelligent matching logic

✅ **Comprehensive Testing Framework**
   72 tests including unit, integration, API, external, and BDD scenarios with 100% pass rate

✅ **Business-Driven Development**
   75+ Gherkin scenarios covering all business workflows with natural language validation

✅ **Security & Compliance**
   Built-in support for NIST CSF, SOC 2, PCI DSS, GDPR, and HIPAA compliance frameworks

✅ **Performance & Scalability**
   Auto-scaling, caching strategies, load testing, and performance monitoring

✅ **Error Handling & Recovery**
   Comprehensive fault tolerance, disaster recovery, and graceful degradation capabilities

Getting Started
---------------

1. **Installation**: Follow the :doc:`installation` guide to set up your environment
2. **User Guide**: Read the :doc:`user-guide/index` for detailed usage instructions
3. **API Reference**: Explore the :doc:`api-reference/index` for technical details
4. **Development**: See :doc:`development/index` for contributing guidelines

🏗️ **Architecture Overview**
============================

The CVE Feed Service follows a modern, microservices architecture designed for scalability and maintainability:

.. mermaid::

   graph TB
       subgraph "External Sources"
           NVD[NVD API]
           MITRE[MITRE CVE]
           VENDOR[Vendor Feeds]
       end

       subgraph "CVE Feed Service"
           INGESTION[CVE Ingestion Service]
           MATCHING[Vulnerability Matching Engine]
           API[REST API Gateway]
           DB[(PostgreSQL Database)]
           CACHE[(Redis Cache)]
       end

       subgraph "Frontend"
           REACT[React Dashboard]
           MOBILE[Mobile App]
       end

       subgraph "Integrations"
           SLACK[Slack]
           EMAIL[Email]
           WEBHOOK[Webhooks]
           SIEM[SIEM Systems]
       end

       NVD --> INGESTION
       MITRE --> INGESTION
       VENDOR --> INGESTION

       INGESTION --> MATCHING
       MATCHING --> DB
       MATCHING --> CACHE

       API --> DB
       API --> CACHE

       REACT --> API
       MOBILE --> API

       API --> SLACK
       API --> EMAIL
       API --> WEBHOOK
       API --> SIEM

       style INGESTION fill:#e1f5fe
       style MATCHING fill:#f3e5f5
       style API fill:#e8f5e8
       style REACT fill:#fff3e0

🔧 **Technology Stack**
======================

.. tabs::

   .. tab:: Backend

      - **Python 3.11+** - Modern Python with type hints
      - **FastAPI** - High-performance async web framework
      - **PostgreSQL** - Robust relational database
      - **Redis** - High-performance caching and pub/sub
      - **SQLAlchemy** - Powerful ORM with async support
      - **Pydantic** - Data validation and serialization

   .. tab:: Frontend

      - **React 18** - Modern React with hooks and context
      - **TypeScript** - Type-safe JavaScript development
      - **Material-UI** - Comprehensive component library
      - **React Query** - Powerful data fetching and caching
      - **React Router** - Client-side routing
      - **Recharts** - Beautiful and responsive charts

   .. tab:: Testing

      - **pytest** - Comprehensive Python testing framework
      - **behave** - BDD testing with Gherkin scenarios
      - **Playwright** - Modern web testing automation
      - **Factory Boy** - Test data generation
      - **pytest-asyncio** - Async testing support

   .. tab:: DevOps

      - **Docker** - Containerization and deployment
      - **GitHub Actions** - CI/CD automation
      - **Sphinx** - Documentation generation
      - **Black** - Code formatting
      - **mypy** - Static type checking

Support
-------

* **Issues**: Report bugs and feature requests on `GitHub Issues <https://github.com/forkrul/day3-cve-feed/issues>`_
* **Documentation**: This documentation is available online and in the repository
* **API Documentation**: Interactive API docs available at ``/api/v1/docs`` when running the service

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
