CVE Ingestion Service
=====================

The CVE Ingestion Service is responsible for processing and storing CVE data from the National Vulnerability Database (NVD). It handles data parsing, normalization, and storage while maintaining data integrity and performance.

Overview
--------

**Module**: ``src.cve_feed_service.services.cve_ingestion_service``

**Primary Class**: ``CVEIngestionService``

**Purpose**: Processes raw CVE data from NVD API, normalizes it into the application's data model, and stores it efficiently in the database with proper error handling and logging.

**Key Features**:
* CVE data parsing and normalization
* CVSS score processing (v3.1 and v2.0)
* CPE applicability extraction and storage
* Bulk import and incremental update support
* Robust error handling and recovery
* Batch processing for performance

Class Architecture
------------------

.. mermaid::

   classDiagram
       class CVEIngestionService {
           -db: AsyncSession
           +__init__(db: AsyncSession)
           +ingest_cve(cve_data: Dict) CVE
           +bulk_ingest_cves(cve_list: List[Dict]) int
           +perform_bulk_import(years: int) int
           +perform_incremental_update(hours: int) int
           -_create_or_update_cve(cve_data: Dict) CVE
           -_process_cpe_applicability(cve: CVE, cve_data: Dict) None
           -_parse_cvss_data(cve_data: Dict) Dict
           -_parse_cwe_data(cve_data: Dict) List[str]
           -_parse_references(cve_data: Dict) List[Dict]
           -_parse_description(cve_data: Dict) str
           -_parse_dates(cve_data: Dict) Tuple[datetime, datetime]
       }
       
       class NVDAPIClient {
           +get_cves_by_date_range(start: datetime, end: datetime) List[Dict]
           +get_single_cve(cve_id: str) Dict
       }
       
       class CVE {
           +cve_id: str
           +description: str
           +cvss_v3_score: float
           +cvss_v3_severity: str
           +cpe_applicability: List[CVECPEApplicability]
       }
       
       CVEIngestionService --> NVDAPIClient
       CVEIngestionService --> CVE

Data Processing Pipeline
------------------------

CVE Ingestion Flow
~~~~~~~~~~~~~~~~~~

.. mermaid::

   flowchart TD
       A[NVD API Data] --> B[Parse CVE Metadata]
       B --> C[Extract CVSS Scores]
       C --> D[Parse CWE Classifications]
       D --> E[Extract References]
       E --> F[Create/Update CVE Record]
       F --> G[Process CPE Applicability]
       G --> H[Store in Database]
       H --> I[Commit Transaction]
       
       subgraph "Error Handling"
           J[Parse Error] --> K[Log Error]
           K --> L[Skip CVE]
           L --> M[Continue Processing]
       end
       
       B --> J
       C --> J
       D --> J
       E --> J

Bulk Import Process
~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant CLI
       participant IngestionService
       participant NVDClient
       participant Database
       
       CLI->>IngestionService: perform_bulk_import(years=2)
       IngestionService->>IngestionService: Calculate date range
       IngestionService->>NVDClient: get_cves_by_date_range()
       
       loop For each batch of CVEs
           NVDClient-->>IngestionService: CVE batch data
           IngestionService->>IngestionService: Process CVE batch
           IngestionService->>Database: Bulk insert/update
           Database-->>IngestionService: Batch committed
       end
       
       IngestionService-->>CLI: Total processed count

Core Methods
------------

ingest_cve()
~~~~~~~~~~~~

Processes a single CVE from NVD data format.

**Signature**:

.. code-block:: python

   async def ingest_cve(self, cve_data: Dict[str, Any]) -> CVE

**Processing Steps**:

.. mermaid::

   graph TD
       A[Raw CVE Data] --> B[Validate CVE ID]
       B --> C[Parse Metadata]
       C --> D[Extract CVSS Scores]
       D --> E[Parse CWE IDs]
       E --> F[Extract References]
       F --> G[Create/Update CVE]
       G --> H[Process CPE Applicability]
       H --> I[Return CVE Object]

bulk_ingest_cves()
~~~~~~~~~~~~~~~~~~

Processes multiple CVEs in batches for performance.

**Signature**:

.. code-block:: python

   async def bulk_ingest_cves(self, cve_list: List[Dict[str, Any]]) -> int

**Batch Processing Logic**:

.. mermaid::

   flowchart TD
       A[CVE List] --> B[Initialize Counter]
       B --> C[Start Processing Loop]
       C --> D[Process Single CVE]
       D --> E[Increment Counter]
       E --> F{Batch Size Reached?}
       F -->|Yes| G[Commit Transaction]
       F -->|No| H{More CVEs?}
       G --> I[Log Batch Progress]
       I --> H
       H -->|Yes| C
       H -->|No| J[Final Commit]
       J --> K[Return Total Count]
       
       subgraph "Error Handling"
           L[CVE Processing Error] --> M[Log Error]
           M --> N[Rollback Transaction]
           N --> O[Continue with Next CVE]
       end
       
       D --> L

Data Parsing Methods
--------------------

_parse_cvss_data()
~~~~~~~~~~~~~~~~~~

Extracts CVSS v3.1 and v2.0 scoring information.

**CVSS v3.1 Processing**:

.. mermaid::

   graph TD
       A[CVE Metrics] --> B[Find cvssMetricV31]
       B --> C[Extract cvssData]
       C --> D[Get baseScore]
       C --> E[Get vectorString]
       C --> F[Get baseSeverity]
       D --> G[Store cvss_v3_score]
       E --> H[Store cvss_v3_vector]
       F --> I[Store cvss_v3_severity]

**Example Data Structure**:

.. code-block:: python

   # Input NVD format
   nvd_metrics = {
       "cvssMetricV31": [{
           "cvssData": {
               "baseScore": 7.5,
               "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H",
               "baseSeverity": "HIGH"
           }
       }]
   }
   
   # Parsed output
   parsed_cvss = {
       "cvss_v3_score": 7.5,
       "cvss_v3_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H",
       "cvss_v3_severity": "HIGH"
   }

_parse_cwe_data()
~~~~~~~~~~~~~~~~~

Extracts Common Weakness Enumeration (CWE) classifications.

**CWE Extraction Logic**:

.. mermaid::

   flowchart TD
       A[CVE Weaknesses] --> B[Iterate Weakness Entries]
       B --> C[Get Description Array]
       C --> D[Filter by Language = 'en']
       D --> E[Extract Value Field]
       E --> F{Starts with 'CWE-'?}
       F -->|Yes| G[Add to CWE List]
       F -->|No| H[Skip Entry]
       G --> I{More Weaknesses?}
       H --> I
       I -->|Yes| B
       I -->|No| J[Return CWE List]

_process_cpe_applicability()
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Processes CPE applicability data for vulnerability correlation.

**CPE Processing Flow**:

.. mermaid::

   sequenceDiagram
       participant Service
       participant Database
       participant CPEParser
       
       Service->>CPEParser: Parse configurations
       CPEParser-->>Service: CPE applicability rules
       
       loop For each CPE rule
           Service->>Service: Extract CPE string
           Service->>Service: Parse version ranges
           Service->>Database: Create/update CPE applicability
       end
       
       Service->>Database: Commit CPE data

**CPE Applicability Structure**:

.. code-block:: python

   cpe_applicability = {
       "cpe_string": "cpe:2.3:a:nginx:nginx:*:*:*:*:*:*:*:*",
       "version_start_including": "1.20.0",
       "version_end_excluding": "1.20.2",
       "vulnerable": True,
       "configuration_id": "config-1"
   }

Performance Optimization
------------------------

Batch Processing Strategy
~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[Large CVE Dataset] --> B[Split into Batches]
       B --> C[Process Batch 1]
       B --> D[Process Batch 2]
       B --> E[Process Batch N]
       
       C --> F[Commit Batch 1]
       D --> G[Commit Batch 2]
       E --> H[Commit Batch N]
       
       F --> I[Log Progress]
       G --> I
       H --> I
       
       I --> J[Continue Processing]

**Batch Size Configuration**:

.. code-block:: python

   # Configurable batch size for performance tuning
   settings.cve_batch_size = 100  # Default batch size
   
   # Memory vs. performance trade-off
   if processed_count % settings.cve_batch_size == 0:
       await self.db.commit()
       logger.info("Processed CVE batch", count=processed_count)

Database Transaction Management
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def bulk_ingest_cves(self, cve_list: List[Dict]) -> int:
       processed_count = 0
       
       try:
           for cve_data in cve_list:
               await self.ingest_cve(cve_data)
               processed_count += 1
               
               # Batch commits for performance
               if processed_count % self.batch_size == 0:
                   await self.db.commit()
                   
       except Exception as e:
           # Rollback on error
           await self.db.rollback()
           logger.error("Bulk ingestion failed", error=str(e))
           raise
       
       # Final commit
       await self.db.commit()
       return processed_count

Error Handling and Recovery
---------------------------

Error Classification
~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[Ingestion Errors] --> B[Data Format Errors]
       A --> C[Database Errors]
       A --> D[Network Errors]
       A --> E[Validation Errors]
       
       B --> F[Skip CVE, Log Error]
       C --> G[Rollback Transaction]
       D --> H[Retry with Backoff]
       E --> I[Skip CVE, Log Warning]

**Error Handling Strategy**:

.. code-block:: python

   async def ingest_cve(self, cve_data: Dict) -> CVE:
       try:
           return await self._create_or_update_cve(cve_data)
       except ValidationError as e:
           logger.warning(
               "CVE validation failed",
               cve_id=cve_data.get("id"),
               error=str(e)
           )
           # Skip this CVE but continue processing
           return None
       except DatabaseError as e:
           logger.error(
               "Database error during CVE ingestion",
               cve_id=cve_data.get("id"),
               error=str(e)
           )
           # Re-raise to trigger transaction rollback
           raise

Recovery Mechanisms
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def perform_incremental_update(self, hours: int = 24) -> int:
       """Incremental update with automatic recovery."""
       try:
           return await self._perform_update(hours)
       except Exception as e:
           logger.error("Incremental update failed", error=str(e))
           
           # Attempt recovery with smaller time window
           if hours > 1:
               logger.info("Attempting recovery with smaller time window")
               return await self.perform_incremental_update(hours // 2)
           else:
               raise

Data Quality Assurance
----------------------

Validation Rules
~~~~~~~~~~~~~~~~

.. mermaid::

   flowchart TD
       A[CVE Data] --> B[Required Fields Check]
       B --> C{CVE ID Present?}
       C -->|No| D[Reject CVE]
       C -->|Yes| E[CVSS Score Validation]
       E --> F{Valid Score Range?}
       F -->|No| G[Log Warning, Use Default]
       F -->|Yes| H[CWE Validation]
       H --> I[CPE Format Validation]
       I --> J[Accept CVE]

**Validation Implementation**:

.. code-block:: python

   def validate_cve_data(self, cve_data: Dict) -> bool:
       """Validate CVE data before processing."""
       # Required fields
       if not cve_data.get("id"):
           return False
       
       # CVSS score validation
       cvss_score = cve_data.get("cvss_v3_score")
       if cvss_score and not (0.0 <= cvss_score <= 10.0):
           logger.warning("Invalid CVSS score", score=cvss_score)
           return False
       
       return True

Monitoring and Metrics
----------------------

Progress Tracking
~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def bulk_ingest_cves(self, cve_list: List[Dict]) -> int:
       total_cves = len(cve_list)
       processed_count = 0
       
       logger.info("Starting bulk CVE ingestion", total_cves=total_cves)
       
       for i, cve_data in enumerate(cve_list):
           await self.ingest_cve(cve_data)
           processed_count += 1
           
           # Progress reporting
           if processed_count % 100 == 0:
               progress = (processed_count / total_cves) * 100
               logger.info(
                   "Ingestion progress",
                   processed=processed_count,
                   total=total_cves,
                   progress_percent=f"{progress:.1f}%"
               )

Performance Metrics
~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[Ingestion Metrics] --> B[Processing Rate]
       A --> C[Error Rate]
       A --> D[Database Performance]
       A --> E[Memory Usage]
       
       B --> F[CVEs per Second]
       C --> G[Failed CVEs / Total CVEs]
       D --> H[Query Duration]
       D --> I[Transaction Size]
       E --> J[Peak Memory Usage]

Integration Patterns
--------------------

CLI Integration
~~~~~~~~~~~~~~~

.. code-block:: python

   # CLI command for bulk import
   @cli.command()
   @click.option("--years", default=2, help="Years of data to import")
   async def bulk_import(years: int):
       async with get_db_session() as db:
           service = CVEIngestionService(db)
           count = await service.perform_bulk_import(years)
           click.echo(f"Imported {count} CVEs")

Scheduled Updates
~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Automated incremental updates
   async def scheduled_cve_update():
       """Scheduled task for CVE updates."""
       async with get_db_session() as db:
           service = CVEIngestionService(db)
           
           try:
               count = await service.perform_incremental_update(hours=6)
               logger.info("Scheduled CVE update completed", count=count)
           except Exception as e:
               logger.error("Scheduled CVE update failed", error=str(e))
               # Send alert to monitoring system
               await send_alert("CVE update failed", str(e))

Testing Considerations
----------------------

Unit Test Example
~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def test_parse_cvss_data():
       service = CVEIngestionService(mock_db)
       
       nvd_data = {
           "metrics": {
               "cvssMetricV31": [{
                   "cvssData": {
                       "baseScore": 7.5,
                       "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H",
                       "baseSeverity": "HIGH"
                   }
               }]
           }
       }
       
       result = service._parse_cvss_data(nvd_data)
       
       assert result["cvss_v3_score"] == 7.5
       assert result["cvss_v3_severity"] == "HIGH"

Integration Test Example
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def test_bulk_ingestion_with_real_data():
       # Test with sample NVD data
       sample_cves = load_sample_nvd_data()
       
       service = CVEIngestionService(test_db)
       count = await service.bulk_ingest_cves(sample_cves)
       
       assert count == len(sample_cves)
       
       # Verify data was stored correctly
       stored_cve = await test_db.execute(
           select(CVE).where(CVE.cve_id == sample_cves[0]["id"])
       )
       assert stored_cve.scalar_one_or_none() is not None

Next Steps
----------

* :doc:`nvd-client` - NVD API integration details
* :doc:`cve-service` - How ingested data is queried
* :doc:`../user-guide/data-management` - User guide for data management
