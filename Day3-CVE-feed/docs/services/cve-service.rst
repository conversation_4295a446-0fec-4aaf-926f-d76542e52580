CVE Service
===========

The CVE Service provides vulnerability data queries and tailored feeds based on application inventories. It correlates CVE data with application components through CPE mappings to deliver relevant vulnerability information.

Overview
--------

**Module**: ``src.cve_feed_service.services.cve_service``

**Primary Class**: ``CVEService``

**Purpose**: Provides comprehensive CVE querying capabilities, including tailored vulnerability feeds based on application component inventories and CPE mappings.

**Key Features**:
* Individual CVE retrieval with full details
* General CVE listing with filtering and pagination
* Tailored vulnerability feeds for applications
* Severity-based filtering
* Date-based filtering
* Performance-optimized queries

Class Architecture
------------------

.. mermaid::

   classDiagram
       class CVEService {
           -db: AsyncSession
           +__init__(db: AsyncSession)
           +get_cve_by_id(cve_id: str) Optional[CVE]
           +list_cves(severity: str, published_after: str, published_before: str, limit: int, offset: int) Tuple[List[CVE], int]
           +get_tailored_cve_feed(app_id: UUID, severity: str, limit: int, offset: int) Tuple[List[CVE], int]
       }
       
       class CVE {
           +id: UUID
           +cve_id: str
           +description: str
           +published_date: datetime
           +last_modified_date: datetime
           +cvss_v3_score: float
           +cvss_v3_vector: str
           +cvss_v3_severity: str
           +cvss_v2_score: float
           +cwe_ids: List[str]
           +references: List[dict]
           +cpe_applicability: List[CVECPEApplicability]
       }
       
       class CVECPEApplicability {
           +id: UUID
           +cve_id: str
           +cpe_string: str
           +version_start_including: str
           +version_start_excluding: str
           +version_end_including: str
           +version_end_excluding: str
           +vulnerable: bool
           +configuration_id: str
       }
       
       CVEService --> CVE
       CVE --> CVECPEApplicability

Vulnerability Feed Architecture
-------------------------------

Tailored Feed Generation
~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   flowchart TD
       A[Application ID] --> B[Get Application Components]
       B --> C[Extract CPE Mappings]
       C --> D[Build CPE Query]
       D --> E[Query CVE Database]
       E --> F[Join with CPE Applicability]
       F --> G[Apply Severity Filter]
       G --> H[Apply Pagination]
       H --> I[Return CVE List]
       
       subgraph "CPE Matching Logic"
           J[Component CPE] --> K[Match CVE CPE Applicability]
           K --> L[Check Version Ranges]
           L --> M[Include if Vulnerable]
       end
       
       D --> J

**Query Optimization Strategy**:

.. mermaid::

   graph TD
       A[Query Strategy] --> B[Join Optimization]
       A --> C[Index Usage]
       A --> D[Filtering Order]
       
       B --> E[Inner Join on CPE Strings]
       B --> F[Distinct CVE Results]
       
       C --> G[CVE ID Index]
       C --> H[CPE String Index]
       C --> I[Severity Index]
       C --> J[Published Date Index]
       
       D --> K[Filter by Application First]
       D --> L[Then by Severity]
       D --> M[Finally by Date Range]

Core Methods
------------

get_cve_by_id()
~~~~~~~~~~~~~~~

Retrieves a single CVE with complete details including CPE applicability.

**Signature**:

.. code-block:: python

   async def get_cve_by_id(self, cve_id: str) -> Optional[CVE]

**Query Structure**:

.. mermaid::

   graph TD
       A[Start Query] --> B[Select CVE by ID]
       B --> C[Filter: Not Deleted]
       C --> D[Eager Load: CPE Applicability]
       D --> E[Execute Query]
       E --> F[Return CVE or None]

**Response Includes**:
* Complete CVE metadata
* CVSS v3.1 and v2.0 scores
* CWE classifications
* Reference links
* CPE applicability rules with version ranges

list_cves()
~~~~~~~~~~~

Lists CVEs with filtering and pagination support.

**Signature**:

.. code-block:: python

   async def list_cves(
       self,
       severity: Optional[str] = None,
       published_after: Optional[str] = None,
       published_before: Optional[str] = None,
       limit: int = 100,
       offset: int = 0,
   ) -> Tuple[List[CVE], int]

**Filtering Logic**:

.. mermaid::

   flowchart TD
       A[Base Query] --> B[Filter: Not Deleted]
       B --> C{Severity Filter?}
       C -->|Yes| D[Add Severity Filter]
       C -->|No| E{Date After Filter?}
       D --> E
       E -->|Yes| F[Add Published After Filter]
       E -->|No| G{Date Before Filter?}
       F --> G
       G -->|Yes| H[Add Published Before Filter]
       G -->|No| I[Count Total Results]
       H --> I
       I --> J[Apply Pagination]
       J --> K[Order by Published Date DESC]
       K --> L[Execute Query]
       L --> M[Return CVEs + Total Count]

get_tailored_cve_feed()
~~~~~~~~~~~~~~~~~~~~~~~

Generates application-specific vulnerability feeds based on component CPE mappings.

**Signature**:

.. code-block:: python

   async def get_tailored_cve_feed(
       self,
       application_id: UUID,
       severity: Optional[str] = None,
       limit: int = 100,
       offset: int = 0,
   ) -> Tuple[List[CVE], int]

**Feed Generation Process**:

.. mermaid::

   sequenceDiagram
       participant Service
       participant Database
       participant CPEMatcher
       
       Service->>Database: Get application components
       Database-->>Service: Components with CPE mappings
       
       Service->>CPEMatcher: Extract CPE strings
       CPEMatcher-->>Service: List of CPE patterns
       
       Service->>Database: Query CVEs by CPE applicability
       Note over Database: Complex join with version matching
       Database-->>Service: Matching CVEs
       
       Service->>Service: Apply severity filter
       Service->>Service: Apply pagination
       Service->>Database: Get total count
       Database-->>Service: Count result
       
       Service-->>Service: Return CVEs + total

CPE Matching Logic
------------------

Version Range Matching
~~~~~~~~~~~~~~~~~~~~~~

CVE CPE applicability includes version ranges that must be matched against component versions:

.. mermaid::

   graph TD
       A[Component Version] --> B{Version Start Including?}
       B -->|Yes| C[Component >= Start Version]
       B -->|No| D{Version Start Excluding?}
       C --> E{Version End Including?}
       D -->|Yes| F[Component > Start Version]
       D -->|No| E
       F --> E
       E -->|Yes| G[Component <= End Version]
       E -->|No| H{Version End Excluding?}
       G --> I[Match Found]
       H -->|Yes| J[Component < End Version]
       H -->|No| I
       J --> I

**Example Version Matching**:

.. code-block:: python

   # CVE affects nginx versions 1.20.0 to 1.20.1 (inclusive)
   cpe_applicability = {
       "cpe_string": "cpe:2.3:a:nginx:nginx:*:*:*:*:*:*:*:*",
       "version_start_including": "1.20.0",
       "version_end_including": "1.20.1",
       "vulnerable": True
   }
   
   # Component versions and match results:
   # nginx 1.19.9 -> No match (below range)
   # nginx 1.20.0 -> Match (start of range)
   # nginx 1.20.1 -> Match (end of range)
   # nginx 1.20.2 -> No match (above range)

CPE String Matching
~~~~~~~~~~~~~~~~~~~

.. mermaid::

   flowchart TD
       A[Component CPE] --> B[Extract Base Pattern]
       B --> C[Compare with CVE CPE]
       C --> D{Exact Match?}
       D -->|Yes| E[Check Version Range]
       D -->|No| F{Wildcard Match?}
       F -->|Yes| E
       F -->|No| G[No Match]
       E --> H{Version in Range?}
       H -->|Yes| I[Vulnerability Applies]
       H -->|No| J[Vulnerability Does Not Apply]

Performance Optimization
------------------------

Query Performance
~~~~~~~~~~~~~~~~~

**Database Indexes**:
* ``cve_id`` - Primary lookup index
* ``cvss_v3_severity`` - Severity filtering
* ``published_date`` - Date range filtering
* ``cpe_string`` - CPE matching
* Composite indexes for common filter combinations

**Query Optimization Techniques**:

.. code-block:: sql

   -- Optimized tailored feed query
   SELECT DISTINCT c.*
   FROM cves c
   INNER JOIN cve_cpe_applicability cca ON c.cve_id = cca.cve_id
   INNER JOIN cpe_mappings cm ON cca.cpe_string LIKE cm.cpe_string
   INNER JOIN components comp ON cm.component_id = comp.id
   WHERE comp.application_id = $1
     AND c.deleted_at IS NULL
     AND comp.deleted_at IS NULL
     AND cm.deleted_at IS NULL
     AND ($2 IS NULL OR c.cvss_v3_severity = $2)
   ORDER BY c.published_date DESC NULLS LAST
   LIMIT $3 OFFSET $4;

Caching Strategy
~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[CVE Feed Request] --> B{Cache Hit?}
       B -->|Yes| C[Return Cached Result]
       B -->|No| D[Execute Database Query]
       D --> E[Cache Result]
       E --> F[Return Result]
       
       G[Cache Invalidation] --> H[Component Update]
       G --> I[CVE Data Update]
       G --> J[Time-based Expiry]

Severity Classification
-----------------------

CVSS v3.1 Severity Mapping
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Severity Levels
   :header-rows: 1
   :widths: 20 20 60

   * - Severity
     - Score Range
     - Description
   * - CRITICAL
     - 9.0 - 10.0
     - Critical vulnerabilities requiring immediate attention
   * - HIGH
     - 7.0 - 8.9
     - High-impact vulnerabilities requiring prompt remediation
   * - MEDIUM
     - 4.0 - 6.9
     - Medium-impact vulnerabilities for planned remediation
   * - LOW
     - 0.1 - 3.9
     - Low-impact vulnerabilities for routine maintenance

Filtering Implementation
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   def apply_severity_filter(query, severity: str):
       """Apply severity filter to CVE query."""
       severity_ranges = {
           "CRITICAL": (9.0, 10.0),
           "HIGH": (7.0, 8.9),
           "MEDIUM": (4.0, 6.9),
           "LOW": (0.1, 3.9)
       }
       
       if severity in severity_ranges:
           min_score, max_score = severity_ranges[severity]
           query = query.where(
               and_(
                   CVE.cvss_v3_score >= min_score,
                   CVE.cvss_v3_score <= max_score
               )
           )
       
       return query

Error Handling
--------------

Query Errors
~~~~~~~~~~~~~

.. code-block:: python

   # CVE not found
   cve = await service.get_cve_by_id("CVE-9999-0000")
   # Returns None, not an exception
   
   # Invalid application ID
   try:
       cves, total = await service.get_tailored_cve_feed(invalid_app_id)
   except ValueError as e:
       # "Application with ID {id} not found"

Performance Errors
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Large result set handling
   if limit > 1000:
       raise ValueError("Limit cannot exceed 1000 results")
   
   # Timeout handling for complex queries
   try:
       result = await asyncio.wait_for(
           db.execute(complex_query), 
           timeout=30.0
       )
   except asyncio.TimeoutError:
       logger.error("CVE query timeout", query_params=params)
       raise HTTPException(status_code=504, detail="Query timeout")

Integration Patterns
--------------------

API Integration
~~~~~~~~~~~~~~~

.. code-block:: python

   @router.get("/cves/feed")
   async def get_vulnerability_feed(
       application_id: UUID,
       severity: Optional[str] = None,
       limit: int = Query(100, le=1000),
       offset: int = Query(0, ge=0),
       cve_service: CVEService = Depends(get_cve_service)
   ):
       cves, total = await cve_service.get_tailored_cve_feed(
           application_id, severity, limit, offset
       )
       
       return {
           "cves": cves,
           "total": total,
           "limit": limit,
           "offset": offset,
           "has_more": offset + len(cves) < total
       }

Monitoring Integration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Metrics collection
   async def get_tailored_cve_feed(self, app_id: UUID, **kwargs):
       start_time = time.time()
       
       try:
           result = await self._execute_feed_query(app_id, **kwargs)
           
           # Record success metrics
           metrics.histogram(
               "cve_feed_query_duration",
               time.time() - start_time,
               tags={"status": "success", "app_id": str(app_id)}
           )
           
           return result
       except Exception as e:
           # Record error metrics
           metrics.increment(
               "cve_feed_query_errors",
               tags={"error_type": type(e).__name__}
           )
           raise

Testing Considerations
----------------------

Unit Test Example
~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def test_get_tailored_feed_with_severity_filter():
       # Setup test data
       app_id = await create_test_application()
       component_id = await create_test_component(app_id)
       await create_test_cpe_mapping(component_id)
       await create_test_cves_with_applicability()
       
       # Execute
       cves, total = await cve_service.get_tailored_cve_feed(
           app_id, severity="HIGH", limit=10, offset=0
       )
       
       # Verify
       assert len(cves) > 0
       assert all(cve.cvss_v3_severity == "HIGH" for cve in cves)
       assert total >= len(cves)

Performance Test Example
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def test_large_feed_performance():
       # Create application with many components
       app_id = await create_large_test_application(100)
       
       start_time = time.time()
       cves, total = await cve_service.get_tailored_cve_feed(app_id)
       duration = time.time() - start_time
       
       # Performance assertions
       assert duration < 5.0  # Should complete within 5 seconds
       assert len(cves) <= 100  # Respects pagination
       assert total > 0  # Returns results

Next Steps
----------

* :doc:`cve-ingestion-service` - How CVE data is populated
* :doc:`component-service` - How components link to vulnerabilities
* :doc:`../user-guide/vulnerability-feeds` - User guide for feeds
