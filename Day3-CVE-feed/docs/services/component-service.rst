Component Service
=================

The Component Service manages software components within applications and their CPE (Common Platform Enumeration) mappings. It provides the critical link between application inventories and vulnerability data.

Overview
--------

**Module**: ``src.cve_feed_service.services.component_service``

**Primary Class**: ``ComponentService``

**Purpose**: Manages software components and their CPE mappings, enabling accurate vulnerability correlation through standardized component identification.

**Key Features**:
* Component CRUD operations within applications
* CPE mapping management and validation
* Component-application relationship management
* Version tracking and updates
* Soft deletion with cascade to CPE mappings

Class Architecture
------------------

.. mermaid::

   classDiagram
       class ComponentService {
           -db: AsyncSession
           +__init__(db: AsyncSession)
           +create_component(app_id: UUID, data: ComponentCreate) Component
           +get_component(id: UUID) Optional[Component]
           +list_components(app_id: UUID, skip: int, limit: int, type: str) List[Component]
           +update_component(id: UUID, data: ComponentUpdate) Component
           +delete_component(id: UUID) bool
           +create_cpe_mapping(comp_id: UUID, data: CPEMappingCreate) CPEMapping
           +update_cpe_mapping(id: UUID, data: CPEMappingUpdate) CPEMapping
           +delete_cpe_mapping(id: UUID) bool
       }
       
       class Component {
           +id: UUID
           +application_id: UUID
           +name: str
           +version: Optional[str]
           +vendor: Optional[str]
           +component_type: Optional[str]
           +description: Optional[str]
           +cpe_mappings: List[CPEMapping]
           +created_at: datetime
           +updated_at: datetime
           +deleted_at: Optional[datetime]
       }
       
       class CPEMapping {
           +id: UUID
           +component_id: UUID
           +cpe_string: str
           +confidence: float
           +mapping_source: str
           +created_at: datetime
           +updated_at: datetime
           +deleted_at: Optional[datetime]
       }
       
       ComponentService --> Component
       ComponentService --> CPEMapping
       Component --> CPEMapping

Component Management Flow
-------------------------

Component Creation Process
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   flowchart TD
       A[Start] --> B[Validate Application Exists]
       B --> C{Application Found?}
       C -->|No| D[Raise ValueError]
       C -->|Yes| E[Check Component Uniqueness]
       E --> F{Name/Version Exists?}
       F -->|Yes| G[Raise ValueError]
       F -->|No| H[Create Component Entity]
       H --> I[Add to Database Session]
       I --> J[Commit Transaction]
       J --> K[Refresh Entity]
       K --> L[Log Success]
       L --> M[Return Component]

**Uniqueness Rule**: Components must be unique by name and version within an application.

Component-CPE Relationship
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[Application] --> B[Component 1: nginx 1.20.1]
       A --> C[Component 2: postgresql 13.8]
       A --> D[Component 3: redis 6.2.6]
       
       B --> E[CPE: cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*]
       C --> F[CPE: cpe:2.3:a:postgresql:postgresql:13.8:*:*:*:*:*:*:*]
       C --> G[CPE: cpe:2.3:a:postgresql:postgresql:*:*:*:*:*:*:*:* - Alternative]
       D --> H[CPE: cpe:2.3:a:redis:redis:6.2.6:*:*:*:*:*:*:*]

Core Methods
------------

create_component()
~~~~~~~~~~~~~~~~~~

Creates a new component within an application.

**Signature**:

.. code-block:: python

   async def create_component(
       self, 
       application_id: UUID, 
       component_data: ComponentCreate
   ) -> Component

**Validation Process**:

.. mermaid::

   sequenceDiagram
       participant Service
       participant Database
       participant Validator
       
       Service->>Database: Check application exists
       Database-->>Service: Application validation
       Service->>Validator: Check component uniqueness
       Validator->>Database: Query existing components
       Database-->>Validator: Uniqueness check result
       Validator-->>Service: Validation result
       Service->>Database: Create component
       Database-->>Service: New component entity

**Business Rules**:
* Application must exist and not be soft-deleted
* Component name + version must be unique within application
* Name is required, other fields are optional

list_components()
~~~~~~~~~~~~~~~~~

Lists components with filtering and eager loading of CPE mappings.

**Signature**:

.. code-block:: python

   async def list_components(
       self,
       application_id: Optional[UUID] = None,
       skip: int = 0,
       limit: int = 100,
       component_type: Optional[str] = None,
   ) -> List[Component]

**Query Optimization**:

.. mermaid::

   graph TD
       A[Base Query] --> B[Filter: Not Deleted]
       B --> C{Application Filter?}
       C -->|Yes| D[Add Application ID Filter]
       C -->|No| E{Component Type Filter?}
       D --> E
       E -->|Yes| F[Add Component Type Filter]
       E -->|No| G[Add selectinload for CPE Mappings]
       F --> G
       G --> H[Apply Pagination]
       H --> I[Order by Created Date]
       I --> J[Execute Query]

CPE Mapping Management
----------------------

create_cpe_mapping()
~~~~~~~~~~~~~~~~~~~~

Creates a CPE mapping for a component.

**Signature**:

.. code-block:: python

   async def create_cpe_mapping(
       self, 
       component_id: UUID, 
       mapping_data: CPEMappingCreate
   ) -> CPEMapping

**CPE Validation Flow**:

.. mermaid::

   flowchart TD
       A[Start] --> B[Validate Component Exists]
       B --> C[Validate CPE Format]
       C --> D{CPE Valid?}
       D -->|No| E[Raise ValidationError]
       D -->|Yes| F[Check Duplicate CPE]
       F --> G{Duplicate Found?}
       G -->|Yes| H[Raise ValueError]
       G -->|No| I[Create CPE Mapping]
       I --> J[Save to Database]
       J --> K[Return Mapping]

**CPE Validation Rules**:

.. code-block:: python

   def validate_cpe_format(cpe_string: str) -> bool:
       """Validate CPE 2.3 format."""
       # Must start with "cpe:2.3:"
       if not cpe_string.startswith("cpe:2.3:"):
           return False
       
       # Must have exactly 13 components
       parts = cpe_string.split(":")
       if len(parts) != 13:
           return False
       
       # Part type must be 'a', 'o', or 'h'
       if parts[2] not in ['a', 'o', 'h']:
           return False
       
       return True

CPE Mapping Confidence Levels
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Confidence Levels
   :header-rows: 1
   :widths: 20 20 60

   * - Confidence
     - Level
     - Description
   * - 1.0
     - Certain
     - Official CPE from vendor or NIST
   * - 0.9
     - High
     - High confidence based on multiple sources
   * - 0.8
     - Good
     - Good match but some uncertainty
   * - 0.7
     - Fair
     - Reasonable guess, needs verification
   * - ≤ 0.6
     - Low
     - Uncertain, requires review

Mapping Source Types
~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[Mapping Sources] --> B[official]
       A --> C[nist_database]
       A --> D[vendor_advisory]
       A --> E[manual_research]
       A --> F[automated_tool]
       A --> G[best_guess]
       
       B --> H[From vendor or NIST CPE dictionary]
       C --> I[Found in NVD CVE database]
       D --> J[From vendor security advisory]
       E --> K[Manual research and verification]
       F --> L[Generated by automated tool]
       G --> M[Educated guess, needs verification]

Component Types
---------------

Standard Component Types
~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[Component Types] --> B[Infrastructure]
       A --> C[Application]
       A --> D[Security]
       A --> E[Operations]
       
       B --> F[web_server]
       B --> G[database]
       B --> H[cache]
       B --> I[load_balancer]
       
       C --> J[framework]
       C --> K[library]
       C --> L[runtime]
       C --> M[container]
       
       D --> N[authentication]
       D --> O[encryption]
       D --> P[firewall]
       
       E --> Q[monitoring]
       E --> R[logging]

Component Lifecycle
-------------------

Version Update Process
~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant User
       participant ComponentService
       participant Database
       participant CPEService
       
       User->>ComponentService: Update component version
       ComponentService->>Database: Update component record
       Database-->>ComponentService: Updated component
       
       Note over ComponentService: CPE mappings may need updates
       ComponentService->>CPEService: Check CPE mapping validity
       CPEService-->>ComponentService: Mapping recommendations
       
       ComponentService-->>User: Component updated + CPE recommendations

Soft Deletion Cascade
~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[Delete Component] --> B[Set component.deleted_at]
       B --> C[Get All CPE Mappings]
       C --> D[Iterate CPE Mappings]
       D --> E[Set cpe_mapping.deleted_at]
       E --> F{More Mappings?}
       F -->|Yes| D
       F -->|No| G[Commit Transaction]
       G --> H[Log Deletion]

Error Handling
--------------

Validation Errors
~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Component uniqueness violation
   raise ValueError(
       f"Component '{name}' version '{version}' already exists"
   )
   
   # Invalid CPE format
   raise ValidationError(
       "Invalid CPE 2.3 format: must have 13 colon-separated components"
   )
   
   # Component not found
   component = await service.get_component(invalid_id)
   # Returns None

Business Logic Errors
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Application not found
   raise ValueError(f"Application with ID {app_id} not found")
   
   # Duplicate CPE mapping
   raise ValueError(
       f"CPE mapping '{cpe_string}' already exists for this component"
   )

Integration Patterns
--------------------

Application Service Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # When deleting an application, cascade to components
   async def delete_application(self, app_id: UUID) -> bool:
       app = await self.get_application(app_id, include_components=True)
       
       # Soft delete all components
       for component in app.components:
           await component_service.delete_component(component.id)
       
       # Soft delete application
       app.soft_delete()
       await self.db.commit()

CVE Service Integration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # CVE Service uses component CPE mappings for vulnerability correlation
   async def get_application_vulnerabilities(self, app_id: UUID):
       # Get application components with CPE mappings
       components = await component_service.list_components(app_id)
       
       # Extract CPE strings
       cpe_strings = []
       for component in components:
           for mapping in component.cpe_mappings:
               cpe_strings.append(mapping.cpe_string)
       
       # Query CVEs by CPE applicability
       return await self.query_cves_by_cpe(cpe_strings)

Performance Optimization
------------------------

Query Optimization
~~~~~~~~~~~~~~~~~~

* Uses ``selectinload`` for CPE mappings to avoid N+1 queries
* Indexes on frequently queried fields (application_id, component_type)
* Efficient pagination for large component lists

Caching Strategies
~~~~~~~~~~~~~~~~~~

* Component data can be cached since it changes infrequently
* CPE mappings are good candidates for caching
* Cache invalidation on component updates

Testing Considerations
----------------------

Unit Test Example
~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def test_create_component_with_cpe_mapping():
       # Create component
       component_data = ComponentCreate(
           name="nginx",
           version="1.20.1",
           vendor="nginx",
           component_type="web_server"
       )
       component = await service.create_component(app_id, component_data)
       
       # Create CPE mapping
       cpe_data = CPEMappingCreate(
           cpe_string="cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
           confidence=1.0,
           mapping_source="official"
       )
       mapping = await service.create_cpe_mapping(component.id, cpe_data)
       
       assert mapping.cpe_string == cpe_data.cpe_string
       assert mapping.confidence == 1.0

Integration Test Example
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def test_component_deletion_cascade():
       # Create component with CPE mappings
       component = await create_test_component()
       mapping1 = await create_test_cpe_mapping(component.id)
       mapping2 = await create_test_cpe_mapping(component.id)
       
       # Delete component
       result = await service.delete_component(component.id)
       assert result is True
       
       # Verify cascade deletion
       deleted_component = await service.get_component(component.id)
       assert deleted_component is None
       
       # Verify CPE mappings are also soft deleted
       # (would need to query with include_deleted=True)

Next Steps
----------

* :doc:`cve-service` - How components link to vulnerabilities
* :doc:`application-service` - Application-component relationships
* :doc:`../user-guide/component-management` - User guide for components
* :doc:`../user-guide/cpe-mapping` - User guide for CPE mapping
