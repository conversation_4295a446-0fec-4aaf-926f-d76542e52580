NVD Client
==========

The NVD Client provides a robust interface to the National Vulnerability Database (NVD) API, handling authentication, rate limiting, error recovery, and data retrieval for CVE information.

Overview
--------

**Module**: ``src.cve_feed_service.services.nvd_client``

**Primary Class**: ``NVDAPIClient``

**Purpose**: Provides a reliable, rate-limited HTTP client for interacting with the NVD CVE API v2.0, with comprehensive error handling and retry logic.

**Key Features**:
* Rate limiting compliance (5/50 requests per 30 seconds)
* Automatic retry with exponential backoff
* API key authentication support
* Date-based filtering and pagination
* Comprehensive error handling and logging
* Async/await support for high performance

Class Architecture
------------------

.. mermaid::

   classDiagram
       class NVDAPIClient {
           -base_url: str
           -api_key: Optional[str]
           -rate_limit: int
           -timeout: int
           -client: httpx.AsyncClient
           -_last_request_time: float
           -_request_interval: float
           +__init__()
           +__aenter__()
           +__aexit__()
           +get_cves(...) Dict[str, Any]
           +get_single_cve(cve_id: str) Dict[str, Any]
           +get_recent_cves(days: int) List[Dict[str, Any]]
           +get_cves_by_date_range(...) List[Dict[str, Any]]
           -_rate_limit() None
           -_make_request(endpoint: str, params: Dict) Dict[str, Any]
       }
       
       class httpx.AsyncClient {
           +get(url: str, params: Dict) Response
           +close() None
       }
       
       NVDAPIClient --> httpx.AsyncClient

API Integration Architecture
----------------------------

Request Flow
~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Client
       participant NVDClient
       participant RateLimiter
       participant HTTPClient
       participant NVDAPI
       
       Client->>NVDClient: get_cves_by_date_range()
       NVDClient->>RateLimiter: Check rate limit
       RateLimiter-->>NVDClient: Wait if needed
       
       loop For each page of results
           NVDClient->>HTTPClient: Make HTTP request
           HTTPClient->>NVDAPI: GET /cves/2.0
           NVDAPI-->>HTTPClient: JSON response
           HTTPClient-->>NVDClient: Parsed data
           NVDClient->>NVDClient: Accumulate results
       end
       
       NVDClient-->>Client: Complete CVE list

Rate Limiting Strategy
~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   flowchart TD
       A[API Request] --> B[Check Last Request Time]
       B --> C[Calculate Time Since Last Request]
       C --> D{Enough Time Passed?}
       D -->|No| E[Calculate Sleep Duration]
       E --> F[Sleep/Wait]
       F --> G[Update Last Request Time]
       D -->|Yes| G
       G --> H[Make HTTP Request]
       H --> I[Return Response]

**Rate Limiting Implementation**:

.. code-block:: python

   async def _rate_limit(self) -> None:
       """Enforce rate limiting between requests."""
       current_time = time.time()
       time_since_last = current_time - self._last_request_time
       
       if time_since_last < self._request_interval:
           sleep_time = self._request_interval - time_since_last
           logger.debug("Rate limiting", sleep_time=sleep_time)
           await asyncio.sleep(sleep_time)
       
       self._last_request_time = time.time()

Core Methods
------------

get_cves()
~~~~~~~~~~

Core method for retrieving CVEs with flexible filtering options.

**Signature**:

.. code-block:: python

   async def get_cves(
       self,
       start_index: int = 0,
       results_per_page: int = 100,
       pub_start_date: Optional[datetime] = None,
       pub_end_date: Optional[datetime] = None,
       last_mod_start_date: Optional[datetime] = None,
       last_mod_end_date: Optional[datetime] = None,
       cve_id: Optional[str] = None,
   ) -> Dict[str, Any]

**Parameter Processing**:

.. mermaid::

   graph TD
       A[Input Parameters] --> B[Build Query Parameters]
       B --> C[Format Dates to ISO 8601]
       C --> D[Apply Results Per Page Limit]
       D --> E[Add API Key if Available]
       E --> F[Make HTTP Request]
       F --> G[Return JSON Response]

get_cves_by_date_range()
~~~~~~~~~~~~~~~~~~~~~~~~

High-level method for retrieving all CVEs within a date range with automatic pagination.

**Signature**:

.. code-block:: python

   async def get_cves_by_date_range(
       self,
       start_date: datetime,
       end_date: datetime,
       use_last_modified: bool = False,
   ) -> List[Dict[str, Any]]

**Pagination Logic**:

.. mermaid::

   flowchart TD
       A[Start Date Range Query] --> B[Initialize Variables]
       B --> C[Make First Request]
       C --> D[Get Total Results Count]
       D --> E[Extract Vulnerabilities]
       E --> F[Add to Results List]
       F --> G{More Results Available?}
       G -->|Yes| H[Increment Start Index]
       H --> I[Make Next Request]
       I --> E
       G -->|No| J[Return Complete List]

**Example Usage**:

.. code-block:: python

   async with NVDAPIClient() as client:
       # Get CVEs published in last 7 days
       end_date = datetime.utcnow()
       start_date = end_date - timedelta(days=7)
       
       cves = await client.get_cves_by_date_range(start_date, end_date)
       print(f"Retrieved {len(cves)} CVEs")

get_single_cve()
~~~~~~~~~~~~~~~~

Retrieves a specific CVE by its identifier.

**Signature**:

.. code-block:: python

   async def get_single_cve(self, cve_id: str) -> Dict[str, Any]

**Error Handling**:

.. mermaid::

   graph TD
       A[CVE ID Request] --> B[Validate CVE ID Format]
       B --> C{Valid Format?}
       C -->|No| D[Raise ValueError]
       C -->|Yes| E[Make API Request]
       E --> F{CVE Found?}
       F -->|No| G[Raise NotFoundError]
       F -->|Yes| H[Return CVE Data]

Error Handling and Recovery
---------------------------

Retry Strategy
~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[HTTP Request] --> B{Request Successful?}
       B -->|Yes| C[Return Response]
       B -->|No| D[Check Error Type]
       D --> E{Rate Limited?}
       D --> F{Network Error?}
       D --> G{Server Error?}
       
       E -->|Yes| H[Wait and Retry]
       F -->|Yes| I[Exponential Backoff]
       G -->|Yes| I
       
       H --> J{Max Retries Reached?}
       I --> J
       J -->|No| A
       J -->|Yes| K[Raise Exception]

**Retry Implementation**:

.. code-block:: python

   @retry(
       stop=stop_after_attempt(3),
       wait=wait_exponential(multiplier=1, min=4, max=10),
       retry=retry_if_exception_type((httpx.RequestError, httpx.HTTPStatusError))
   )
   async def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
       await self._rate_limit()
       
       url = f"{self.base_url}/{endpoint}"
       response = await self.client.get(url, params=params)
       response.raise_for_status()
       
       return response.json()

Error Classification
~~~~~~~~~~~~~~~~~~~~

.. list-table:: Error Types and Responses
   :header-rows: 1
   :widths: 20 30 50

   * - Error Type
     - HTTP Status
     - Action
   * - Rate Limited
     - 429
     - Wait and retry with backoff
   * - Not Found
     - 404
     - Return empty result or raise NotFoundError
   * - Server Error
     - 5xx
     - Retry with exponential backoff
   * - Client Error
     - 4xx (except 404, 429)
     - Log error and raise exception
   * - Network Error
     - Connection issues
     - Retry with exponential backoff

Configuration Management
------------------------

Environment Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   class Settings:
       # NVD API Configuration
       nvd_api_base_url: str = "https://services.nvd.nist.gov/rest/json"
       nvd_api_key: Optional[str] = None
       nvd_request_timeout: int = 30
       nvd_rate_limit_per_minute: int = 10  # 5 without API key, 50 with key
       
       @property
       def nvd_rate_limit_per_minute(self) -> int:
           """Dynamic rate limit based on API key availability."""
           return 50 if self.nvd_api_key else 5

API Key Benefits
~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[API Key Status] --> B{API Key Present?}
       B -->|Yes| C[Enhanced Limits]
       B -->|No| D[Standard Limits]
       
       C --> E[50 requests per 30 seconds]
       C --> F[100,000 requests per day]
       C --> G[Priority processing]
       
       D --> H[5 requests per 30 seconds]
       D --> I[10,000 requests per day]
       D --> J[Standard processing]

**Configuration Example**:

.. code-block:: python

   # With API key (recommended for production)
   export NVD_API_KEY="your-api-key-here"
   
   # Client automatically detects and uses API key
   async with NVDAPIClient() as client:
       # Higher rate limits automatically applied
       cves = await client.get_recent_cves(days=30)

Performance Optimization
------------------------

Concurrent Requests
~~~~~~~~~~~~~~~~~~~

While the NVD API has rate limits, the client can be optimized for batch operations:

.. code-block:: python

   async def get_multiple_cve_ranges(date_ranges: List[Tuple[datetime, datetime]]):
       """Get CVEs for multiple date ranges efficiently."""
       async with NVDAPIClient() as client:
           tasks = []
           for start_date, end_date in date_ranges:
               task = client.get_cves_by_date_range(start_date, end_date)
               tasks.append(task)
           
           # Process with rate limiting
           results = []
           for task in tasks:
               result = await task
               results.extend(result)
           
           return results

Memory Management
~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[Large Date Range] --> B[Split into Chunks]
       B --> C[Process Chunk 1]
       B --> D[Process Chunk 2]
       B --> E[Process Chunk N]
       
       C --> F[Yield Results]
       D --> F
       E --> F
       
       F --> G[Avoid Memory Accumulation]

**Streaming Implementation**:

.. code-block:: python

   async def stream_cves_by_date_range(
       self, 
       start_date: datetime, 
       end_date: datetime,
       chunk_days: int = 7
   ) -> AsyncGenerator[List[Dict], None]:
       """Stream CVEs in chunks to manage memory usage."""
       current_start = start_date
       
       while current_start < end_date:
           chunk_end = min(current_start + timedelta(days=chunk_days), end_date)
           chunk_cves = await self.get_cves_by_date_range(current_start, chunk_end)
           yield chunk_cves
           current_start = chunk_end

Monitoring and Logging
----------------------

Request Logging
~~~~~~~~~~~~~~~

.. code-block:: python

   async def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
       start_time = time.time()
       
       logger.debug(
           "Making NVD API request",
           endpoint=endpoint,
           params=params
       )
       
       try:
           response = await self.client.get(url, params=params)
           duration = time.time() - start_time
           
           logger.info(
               "NVD API request successful",
               endpoint=endpoint,
               status_code=response.status_code,
               duration_ms=int(duration * 1000),
               total_results=response.json().get("totalResults", 0)
           )
           
           return response.json()
           
       except Exception as e:
           duration = time.time() - start_time
           logger.error(
               "NVD API request failed",
               endpoint=endpoint,
               error=str(e),
               duration_ms=int(duration * 1000)
           )
           raise

Metrics Collection
~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[NVD Client Metrics] --> B[Request Rate]
       A --> C[Response Times]
       A --> D[Error Rates]
       A --> E[Rate Limit Usage]
       
       B --> F[Requests per Minute]
       C --> G[Average Response Time]
       C --> H[95th Percentile Response Time]
       D --> I[Error Rate Percentage]
       D --> J[Error Types Distribution]
       E --> K[Rate Limit Utilization]

Integration Patterns
--------------------

Context Manager Usage
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Recommended usage pattern
   async with NVDAPIClient() as client:
       cves = await client.get_recent_cves(days=7)
       # Client automatically closed when exiting context

Service Integration
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   class CVEIngestionService:
       async def perform_bulk_import(self, years: int) -> int:
           end_date = datetime.utcnow()
           start_date = end_date - timedelta(days=years * 365)
           
           async with NVDAPIClient() as client:
               cve_list = await client.get_cves_by_date_range(start_date, end_date)
               return await self.bulk_ingest_cves(cve_list)

Testing Considerations
----------------------

Mock Testing
~~~~~~~~~~~~

.. code-block:: python

   import pytest
   from unittest.mock import AsyncMock, patch
   
   @pytest.fixture
   async def mock_nvd_client():
       with patch('httpx.AsyncClient') as mock_client:
           mock_response = AsyncMock()
           mock_response.json.return_value = {
               "vulnerabilities": [{"cve": {"id": "CVE-2023-1234"}}],
               "totalResults": 1
           }
           mock_client.return_value.get.return_value = mock_response
           
           client = NVDAPIClient()
           yield client

Integration Testing
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def test_real_nvd_api_integration():
       """Test with real NVD API (requires network)."""
       async with NVDAPIClient() as client:
           # Test with a known CVE
           cve_data = await client.get_single_cve("CVE-2021-44228")
           
           assert cve_data["cve"]["id"] == "CVE-2021-44228"
           assert "descriptions" in cve_data["cve"]

Rate Limit Testing
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def test_rate_limiting():
       """Test that rate limiting is properly enforced."""
       async with NVDAPIClient() as client:
           start_time = time.time()
           
           # Make multiple requests
           for _ in range(3):
               await client.get_cves(results_per_page=1)
           
           duration = time.time() - start_time
           
           # Should take at least 2 * request_interval seconds
           expected_min_duration = 2 * client._request_interval
           assert duration >= expected_min_duration

Next Steps
----------

* :doc:`cve-ingestion-service` - How the client is used for data ingestion
* :doc:`service-interactions` - Integration with other services
* :doc:`../user-guide/data-management` - User guide for CVE data management
