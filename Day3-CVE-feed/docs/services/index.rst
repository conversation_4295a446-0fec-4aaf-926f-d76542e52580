Services Architecture
====================

The CVE Feed Service is built with a layered architecture that separates concerns and provides clear boundaries between different functional areas. This section provides comprehensive documentation of all service components, their interactions, and architectural patterns.

.. toctree::
   :maxdepth: 2

   overview
   application-service
   auth-service
   component-service
   cve-service
   cve-ingestion-service
   nvd-client
   service-interactions

Overview
--------

The service layer implements the business logic of the CVE Feed Service, providing a clean abstraction between the API endpoints and the data models. Each service is responsible for a specific domain and follows consistent patterns for error handling, logging, and database interactions.

**Service Architecture Principles:**

* **Single Responsibility**: Each service handles one specific domain
* **Dependency Injection**: Services receive database sessions and dependencies
* **Async/Await**: All services use async patterns for database and API operations
* **Error Handling**: Consistent error handling with structured logging
* **Transaction Management**: Proper database transaction handling
* **Soft Deletes**: All services support soft deletion patterns

Service Layer Architecture
--------------------------

.. mermaid::

   graph TB
       subgraph "API Layer"
           API[FastAPI Endpoints]
       end
       
       subgraph "Service Layer"
           AS[Application Service]
           AUS[Auth Service]
           CS[Component Service]
           CVS[CVE Service]
           CIS[CVE Ingestion Service]
           NC[NVD Client]
       end
       
       subgraph "Data Layer"
           DB[(PostgreSQL Database)]
           NVD[NVD API]
       end
       
       API --> AS
       API --> AUS
       API --> CS
       API --> CVS
       
       AS --> DB
       AUS --> DB
       CS --> DB
       CVS --> DB
       
       CIS --> DB
       CIS --> NC
       NC --> NVD
       
       AS -.-> CS
       CS -.-> AS
       CVS -.-> AS
       CVS -.-> CS

Core Service Patterns
---------------------

Database Session Management
~~~~~~~~~~~~~~~~~~~~~~~~~~~

All services follow a consistent pattern for database session management:

.. code-block:: python

   class ExampleService:
       def __init__(self, db: AsyncSession) -> None:
           self.db = db
       
       async def create_entity(self, data: CreateSchema) -> Entity:
           # Business logic
           entity = Entity(**data.model_dump())
           self.db.add(entity)
           await self.db.commit()
           await self.db.refresh(entity)
           return entity

Error Handling and Logging
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Services use structured logging and consistent error handling:

.. code-block:: python

   import structlog
   
   logger = structlog.get_logger(__name__)
   
   async def service_method(self, param: str) -> Result:
       logger.info("Starting operation", param=param)
       
       try:
           # Business logic
           result = await self._perform_operation(param)
           logger.info("Operation completed", result_id=result.id)
           return result
       except Exception as e:
           logger.error("Operation failed", param=param, error=str(e))
           raise

Soft Delete Pattern
~~~~~~~~~~~~~~~~~~~

All services implement soft deletion for audit trails:

.. code-block:: python

   async def delete_entity(self, entity_id: UUID) -> bool:
       entity = await self.get_entity(entity_id)
       if not entity:
           return False
       
       entity.soft_delete()
       await self.db.commit()
       logger.info("Entity soft deleted", entity_id=entity_id)
       return True

Service Dependencies
--------------------

The services have the following dependency relationships:

**Direct Dependencies:**
* All services depend on AsyncSession for database access
* CVE Ingestion Service depends on NVD Client for external API access
* NVD Client depends on configuration settings

**Logical Dependencies:**
* Application Service manages Components through Component Service
* CVE Service queries data created by CVE Ingestion Service
* Auth Service manages users and API keys for all other services

**Data Flow Dependencies:**
* CVE data flows from NVD API → NVD Client → CVE Ingestion Service → Database
* Application inventory flows from API → Application/Component Services → Database
* Vulnerability feeds flow from Database → CVE Service → API

Service Initialization
----------------------

Services are initialized through dependency injection in the API layer:

.. code-block:: python

   from sqlalchemy.ext.asyncio import AsyncSession
   from fastapi import Depends
   
   from ..core.dependencies import get_db
   from ..services.application_service import ApplicationService
   
   async def get_application_service(
       db: AsyncSession = Depends(get_db)
   ) -> ApplicationService:
       return ApplicationService(db)

This pattern ensures:
* Clean separation of concerns
* Easy testing with mock dependencies
* Consistent database session management
* Proper resource cleanup

Performance Considerations
--------------------------

**Database Query Optimization:**
* Services use SQLAlchemy's selectinload for eager loading
* Queries include appropriate filters and indexes
* Pagination is implemented for large result sets

**Async Operations:**
* All database operations are async
* External API calls use async HTTP clients
* Proper connection pooling and resource management

**Caching Strategy:**
* Services are stateless to support horizontal scaling
* Database-level caching through PostgreSQL
* Application-level caching can be added at the API layer

Testing Strategy
----------------

Each service is designed for comprehensive testing:

**Unit Testing:**
* Mock database sessions for isolated testing
* Test business logic without database dependencies
* Validate error handling and edge cases

**Integration Testing:**
* Test services with real database connections
* Validate database transactions and constraints
* Test service interactions and data flow

**Example Test Pattern:**

.. code-block:: python

   import pytest
   from unittest.mock import AsyncMock
   
   from ..services.application_service import ApplicationService
   from ..schemas.application import ApplicationCreate
   
   @pytest.fixture
   async def mock_db():
       return AsyncMock()
   
   @pytest.fixture
   async def application_service(mock_db):
       return ApplicationService(mock_db)
   
   async def test_create_application(application_service, mock_db):
       # Test implementation
       pass

Next Steps
----------

Explore the detailed documentation for each service:

* :doc:`application-service` - Application inventory management
* :doc:`auth-service` - Authentication and authorization
* :doc:`component-service` - Software component tracking
* :doc:`cve-service` - Vulnerability data queries and feeds
* :doc:`cve-ingestion-service` - CVE data processing and storage
* :doc:`nvd-client` - NVD API integration
* :doc:`service-interactions` - Inter-service communication patterns
