Service Interactions
====================

This document describes how the various services in the CVE Feed Service interact with each other, including data flow patterns, dependency relationships, and integration scenarios.

Overview
--------

The CVE Feed Service follows a layered architecture where services interact through well-defined interfaces. Understanding these interactions is crucial for system maintenance, debugging, and extending functionality.

**Interaction Types**:
* **Direct Dependencies**: Services that directly call other services
* **Data Dependencies**: Services that depend on data created by other services
* **Event-Driven**: Services that react to events or changes
* **Shared Resources**: Services that share database sessions or external APIs

Service Dependency Graph
------------------------

.. mermaid::

   graph TD
       subgraph "External APIs"
           NVD[NVD API]
       end
       
       subgraph "Client Layer"
           NC[NVD Client]
       end
       
       subgraph "Business Logic Layer"
           AS[Application Service]
           CS[Component Service]
           CVS[CVE Service]
           CIS[CVE Ingestion Service]
           AUS[Auth Service]
       end
       
       subgraph "Data Layer"
           DB[(Database)]
       end
       
       subgraph "API Layer"
           API[FastAPI Endpoints]
       end
       
       NVD --> NC
       NC --> CIS
       CIS --> DB
       
       AS --> DB
       CS --> DB
       CVS --> DB
       AUS --> DB
       
       API --> AS
       API --> CS
       API --> CVS
       API --> AUS
       
       CVS -.-> AS
       CVS -.-> CS
       AS -.-> CS

Core Interaction Patterns
--------------------------

Authentication Flow
~~~~~~~~~~~~~~~~~~~

All services integrate with the Auth Service for user validation:

.. mermaid::

   sequenceDiagram
       participant Client
       participant API
       participant AuthService
       participant BusinessService
       participant Database
       
       Client->>API: Request with credentials
       API->>AuthService: Validate credentials
       AuthService->>Database: Check user/API key
       Database-->>AuthService: User data
       AuthService-->>API: User object
       
       API->>BusinessService: Execute business logic
       BusinessService->>Database: Perform operations
       Database-->>BusinessService: Results
       BusinessService-->>API: Response data
       API-->>Client: Final response

Application-Component Relationship
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The Application Service and Component Service work together to manage inventory:

.. mermaid::

   sequenceDiagram
       participant API
       participant ApplicationService
       participant ComponentService
       participant Database
       
       Note over API: Delete Application Request
       API->>ApplicationService: delete_application(app_id)
       ApplicationService->>Database: Get application with components
       Database-->>ApplicationService: Application + components
       
       loop For each component
           ApplicationService->>ComponentService: delete_component(comp_id)
           ComponentService->>Database: Soft delete component + CPE mappings
       end
       
       ApplicationService->>Database: Soft delete application
       ApplicationService-->>API: Deletion complete

CVE Feed Generation
~~~~~~~~~~~~~~~~~~~

The CVE Service coordinates with Application and Component services to generate tailored feeds:

.. mermaid::

   sequenceDiagram
       participant API
       participant CVEService
       participant ApplicationService
       participant ComponentService
       participant Database
       
       API->>CVEService: get_tailored_cve_feed(app_id)
       CVEService->>ApplicationService: get_application(app_id, include_components=True)
       ApplicationService->>Database: Query application with components
       Database-->>ApplicationService: Application data
       ApplicationService-->>CVEService: Application with components
       
       CVEService->>CVEService: Extract CPE mappings
       CVEService->>Database: Query CVEs by CPE applicability
       Database-->>CVEService: Matching CVEs
       CVEService-->>API: Tailored CVE feed

Data Ingestion Pipeline
~~~~~~~~~~~~~~~~~~~~~~~

The CVE Ingestion Service uses the NVD Client to populate the database:

.. mermaid::

   sequenceDiagram
       participant CLI
       participant CVEIngestionService
       participant NVDClient
       participant Database
       participant NVD_API
       
       CLI->>CVEIngestionService: perform_bulk_import(years=2)
       CVEIngestionService->>NVDClient: get_cves_by_date_range(start, end)
       
       loop Paginated requests
           NVDClient->>NVD_API: GET /cves/2.0
           NVD_API-->>NVDClient: CVE batch
       end
       
       NVDClient-->>CVEIngestionService: Complete CVE list
       
       loop Process CVE batches
           CVEIngestionService->>Database: Bulk insert/update CVEs
           CVEIngestionService->>Database: Process CPE applicability
       end
       
       CVEIngestionService-->>CLI: Import complete

Cross-Service Data Flow
-----------------------

Application Lifecycle
~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   flowchart TD
       A[Create Application] --> B[Application Service]
       B --> C[Store in Database]
       C --> D[Add Components]
       D --> E[Component Service]
       E --> F[Create CPE Mappings]
       F --> G[Component Service]
       G --> H[Query Vulnerabilities]
       H --> I[CVE Service]
       I --> J[Generate Tailored Feed]

**Data Flow Steps**:

1. **Application Creation**: Application Service validates and stores application metadata
2. **Component Addition**: Component Service manages software inventory within applications
3. **CPE Mapping**: Component Service links components to standardized vulnerability identifiers
4. **Vulnerability Correlation**: CVE Service uses CPE mappings to find relevant vulnerabilities
5. **Feed Generation**: CVE Service provides tailored vulnerability information

Component Update Cascade
~~~~~~~~~~~~~~~~~~~~~~~~

When components are updated, it affects vulnerability feeds:

.. mermaid::

   graph TD
       A[Component Version Update] --> B[Component Service]
       B --> C[Update Component Record]
       C --> D[Check CPE Mapping Validity]
       D --> E{CPE Needs Update?}
       E -->|Yes| F[Update CPE Mapping]
       E -->|No| G[CVE Feed Automatically Updated]
       F --> G
       G --> H[Next API Request Gets New Results]

Shared Resource Management
--------------------------

Database Session Sharing
~~~~~~~~~~~~~~~~~~~~~~~~

All services share database sessions through dependency injection:

.. code-block:: python

   # Dependency injection pattern
   async def get_application_service(
       db: AsyncSession = Depends(get_db)
   ) -> ApplicationService:
       return ApplicationService(db)
   
   async def get_component_service(
       db: AsyncSession = Depends(get_db)
   ) -> ComponentService:
       return ComponentService(db)
   
   # Services share the same database session within a request
   @router.delete("/applications/{app_id}")
   async def delete_application(
       app_id: UUID,
       app_service: ApplicationService = Depends(get_application_service),
       comp_service: ComponentService = Depends(get_component_service)
   ):
       # Both services use the same database session
       # Ensuring transactional consistency
       return await app_service.delete_application(app_id)

Configuration Sharing
~~~~~~~~~~~~~~~~~~~~~

Services share configuration through a centralized settings object:

.. code-block:: python

   from ..core.config import get_settings
   
   class NVDAPIClient:
       def __init__(self):
           settings = get_settings()
           self.api_key = settings.nvd_api_key
           self.rate_limit = settings.nvd_rate_limit_per_minute
   
   class CVEIngestionService:
       def __init__(self, db: AsyncSession):
           self.db = db
           settings = get_settings()
           self.batch_size = settings.cve_batch_size

Error Propagation Patterns
--------------------------

Service Error Handling
~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant API
       participant ServiceA
       participant ServiceB
       participant Database
       
       API->>ServiceA: Business operation
       ServiceA->>ServiceB: Dependent operation
       ServiceB->>Database: Database operation
       Database-->>ServiceB: Database error
       ServiceB-->>ServiceA: Propagate error
       ServiceA->>ServiceA: Log error with context
       ServiceA-->>API: Raise appropriate exception
       API->>API: Convert to HTTP response
       API-->>Client: Error response

**Error Handling Strategy**:

.. code-block:: python

   class ApplicationService:
       async def delete_application(self, app_id: UUID) -> bool:
           try:
               # Get application with components
               app = await self.get_application(app_id, include_components=True)
               if not app:
                   return False
               
               # Delete components through Component Service
               for component in app.components:
                   success = await component_service.delete_component(component.id)
                   if not success:
                       logger.warning("Failed to delete component", component_id=component.id)
               
               # Delete application
               app.soft_delete()
               await self.db.commit()
               return True
               
           except Exception as e:
               logger.error("Application deletion failed", app_id=app_id, error=str(e))
               await self.db.rollback()
               raise

Transaction Management
~~~~~~~~~~~~~~~~~~~~~~

Services coordinate database transactions for consistency:

.. code-block:: python

   async def complex_operation_with_multiple_services():
       async with database.transaction():
           try:
               # All operations within the same transaction
               app = await application_service.create_application(app_data)
               component = await component_service.create_component(app.id, comp_data)
               mapping = await component_service.create_cpe_mapping(component.id, cpe_data)
               
               # Transaction commits automatically if no exceptions
               return app
               
           except Exception:
               # Transaction rolls back automatically on exception
               raise

Performance Optimization Patterns
---------------------------------

Eager Loading Coordination
~~~~~~~~~~~~~~~~~~~~~~~~~~

Services coordinate to minimize database queries:

.. code-block:: python

   # Application Service loads components and CPE mappings together
   async def get_application(self, app_id: UUID, include_components: bool = False):
       query = select(Application).where(Application.id == app_id)
       
       if include_components:
           # Eager load components and their CPE mappings
           query = query.options(
               selectinload(Application.components).selectinload(
                   Component.cpe_mappings
               )
           )
       
       result = await self.db.execute(query)
       return result.scalar_one_or_none()

Caching Coordination
~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[API Request] --> B{Cache Hit?}
       B -->|Yes| C[Return Cached Data]
       B -->|No| D[Query Services]
       D --> E[Application Service]
       D --> F[Component Service]
       D --> G[CVE Service]
       E --> H[Combine Results]
       F --> H
       G --> H
       H --> I[Cache Results]
       I --> J[Return to Client]

Testing Service Interactions
----------------------------

Integration Test Patterns
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def test_application_deletion_cascade():
       """Test that deleting an application cascades to components and CPE mappings."""
       # Setup: Create application with components and CPE mappings
       app = await application_service.create_application(test_app_data)
       component = await component_service.create_component(app.id, test_comp_data)
       cpe_mapping = await component_service.create_cpe_mapping(component.id, test_cpe_data)
       
       # Execute: Delete application
       result = await application_service.delete_application(app.id)
       assert result is True
       
       # Verify: All related entities are soft deleted
       deleted_app = await application_service.get_application(app.id)
       assert deleted_app is None
       
       deleted_component = await component_service.get_component(component.id)
       assert deleted_component is None

Mock Service Dependencies
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @pytest.fixture
   async def mock_component_service():
       mock_service = AsyncMock(spec=ComponentService)
       mock_service.delete_component.return_value = True
       return mock_service
   
   async def test_application_service_with_mock_dependencies(mock_component_service):
       """Test application service with mocked component service."""
       # Inject mock dependency
       app_service = ApplicationService(mock_db)
       app_service.component_service = mock_component_service
       
       # Test business logic without component service complexity
       result = await app_service.delete_application(test_app_id)
       
       # Verify interactions
       mock_component_service.delete_component.assert_called()

Monitoring Service Interactions
-------------------------------

Distributed Tracing
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import structlog
   from uuid import uuid4
   
   async def create_application_with_components(app_data, components_data):
       trace_id = str(uuid4())
       logger = structlog.get_logger().bind(trace_id=trace_id)
       
       logger.info("Starting application creation workflow")
       
       try:
           # Create application
           logger.info("Creating application")
           app = await application_service.create_application(app_data)
           
           # Create components
           for comp_data in components_data:
               logger.info("Creating component", component_name=comp_data.name)
               component = await component_service.create_component(app.id, comp_data)
           
           logger.info("Application creation workflow completed", app_id=app.id)
           return app
           
       except Exception as e:
           logger.error("Application creation workflow failed", error=str(e))
           raise

Performance Metrics
~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[Service Interaction Metrics] --> B[Request Duration]
       A --> C[Cross-Service Call Count]
       A --> D[Database Query Count]
       A --> E[Error Rates]
       
       B --> F[Total Request Time]
       B --> G[Service-to-Service Time]
       C --> H[Service Dependency Calls]
       D --> I[N+1 Query Detection]
       E --> J[Service Error Correlation]

Best Practices
--------------

Service Design Principles
~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Loose Coupling**: Services should minimize direct dependencies
2. **High Cohesion**: Related functionality should be grouped together
3. **Clear Interfaces**: Service contracts should be well-defined
4. **Error Isolation**: Errors in one service shouldn't cascade unnecessarily
5. **Transaction Boundaries**: Understand where transactions begin and end

Integration Guidelines
~~~~~~~~~~~~~~~~~~~~~

1. **Use Dependency Injection**: Inject services rather than creating them directly
2. **Handle Errors Gracefully**: Don't let service errors propagate without context
3. **Log Service Interactions**: Include trace IDs for debugging
4. **Test Integration Points**: Focus testing on service boundaries
5. **Monitor Performance**: Track cross-service call patterns

Next Steps
----------

* :doc:`overview` - High-level service architecture
* :doc:`../development/index` - Development guidelines
* :doc:`../api-reference/index` - API endpoint documentation
