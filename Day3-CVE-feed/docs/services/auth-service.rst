Auth Service
============

The Auth Service handles all authentication and authorization functionality in the CVE Feed Service. It manages user accounts, password authentication, API key generation, and role-based access control.

Overview
--------

**Module**: ``src.cve_feed_service.services.auth_service``

**Primary Class**: ``AuthService``

**Purpose**: Provides comprehensive authentication and authorization services including user management, password verification, API key management, and role-based access control.

**Key Features**:
* User authentication with username/password
* API key generation and validation
* User lifecycle management (CRUD)
* Role-based access control (RBAC)
* Secure password hashing
* API key hashing and validation

Class Architecture
------------------

.. mermaid::

   classDiagram
       class AuthService {
           -db: AsyncSession
           +__init__(db: AsyncSession)
           +authenticate_user(username: str, password: str) Optional[User]
           +authenticate_api_key(api_key: str) Optional[User]
           +create_user(data: UserCreate) User
           +get_user_by_id(id: UUID) Optional[User]
           +get_user_by_username(username: str) Optional[User]
           +list_users(skip: int, limit: int) List[User]
           +update_user(id: UUID, data: UserUpdate) User
           +delete_user(id: UUID) bool
           +change_password(id: UUID, old_password: str, new_password: str) bool
           +create_api_key(user_id: UUID, name: str) Tuple[APIKey, str]
           +list_user_api_keys(user_id: UUID) List[APIKey]
           +delete_api_key(api_key_id: UUID) bool
       }
       
       class User {
           +id: UUID
           +username: str
           +email: str
           +full_name: str
           +hashed_password: str
           +role: str
           +is_active: bool
           +api_keys: List[APIKey]
           +created_at: datetime
           +updated_at: datetime
           +deleted_at: Optional[datetime]
       }
       
       class APIKey {
           +id: UUID
           +user_id: str
           +name: str
           +key_hash: str
           +is_active: bool
           +last_used_at: Optional[datetime]
           +created_at: datetime
           +updated_at: datetime
           +deleted_at: Optional[datetime]
       }
       
       AuthService --> User
       AuthService --> APIKey
       User --> APIKey

Authentication Flow
-------------------

User Authentication
~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Client
       participant API
       participant AuthService
       participant Database
       participant PasswordHash
       
       Client->>API: POST /auth/login {username, password}
       API->>AuthService: authenticate_user(username, password)
       AuthService->>Database: Query user by username
       Database-->>AuthService: User record
       AuthService->>PasswordHash: verify_password(password, hash)
       PasswordHash-->>AuthService: Verification result
       
       alt Password Valid
           AuthService-->>API: User object
           API->>API: Generate JWT token
           API-->>Client: {access_token, token_type}
       else Password Invalid
           AuthService-->>API: None
           API-->>Client: 401 Unauthorized
       end

API Key Authentication
~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Client
       participant API
       participant AuthService
       participant Database
       participant KeyHash
       
       Client->>API: Request with X-API-Key header
       API->>AuthService: authenticate_api_key(api_key)
       AuthService->>KeyHash: hash_api_key(api_key)
       KeyHash-->>AuthService: Key hash
       AuthService->>Database: Query by key hash
       Database-->>AuthService: APIKey record
       AuthService->>Database: Get associated user
       Database-->>AuthService: User record
       AuthService->>Database: Update last_used_at
       
       alt Valid API Key
           AuthService-->>API: User object
           API->>API: Set user context
           API-->>Client: Process request
       else Invalid API Key
           AuthService-->>API: None
           API-->>Client: 401 Unauthorized
       end

Core Methods
------------

authenticate_user()
~~~~~~~~~~~~~~~~~~~

Authenticates a user with username and password.

**Signature**:

.. code-block:: python

   async def authenticate_user(
       self, 
       username: str, 
       password: str
   ) -> Optional[User]

**Authentication Process**:

.. mermaid::

   flowchart TD
       A[Start] --> B[Query User by Username]
       B --> C{User Found?}
       C -->|No| D[Return None]
       C -->|Yes| E{User Active?}
       E -->|No| D
       E -->|Yes| F[Verify Password]
       F --> G{Password Valid?}
       G -->|No| D
       G -->|Yes| H[Return User]

**Security Features**:
* Constant-time password verification
* Active user validation
* Soft-deleted user exclusion

create_user()
~~~~~~~~~~~~~

Creates a new user account with role assignment.

**Signature**:

.. code-block:: python

   async def create_user(self, user_data: UserCreate) -> User

**User Creation Flow**:

.. mermaid::

   graph TD
       A[Validate Input] --> B[Check Username Uniqueness]
       B --> C{Username Exists?}
       C -->|Yes| D[Raise ValueError]
       C -->|No| E[Check Email Uniqueness]
       E --> F{Email Exists?}
       F -->|Yes| G[Raise ValueError]
       F -->|No| H[Hash Password]
       H --> I[Create User Entity]
       I --> J[Save to Database]
       J --> K[Return User]

**Validation Rules**:
* Username must be unique
* Email must be unique
* Password is hashed using bcrypt
* Role must be valid (it_admin or security_analyst)

API Key Management
------------------

create_api_key()
~~~~~~~~~~~~~~~~

Generates a new API key for a user.

**Signature**:

.. code-block:: python

   async def create_api_key(
       self, 
       user_id: UUID, 
       name: str
   ) -> Tuple[APIKey, str]

**API Key Generation**:

.. mermaid::

   flowchart TD
       A[Validate User Exists] --> B[Check Name Uniqueness]
       B --> C[Generate Random Key]
       C --> D[Hash API Key]
       D --> E[Create APIKey Entity]
       E --> F[Save to Database]
       F --> G[Return APIKey + Plain Key]

**Security Features**:
* Cryptographically secure random generation
* Key hashing before storage
* Plain key returned only once
* Name uniqueness per user

authenticate_api_key()
~~~~~~~~~~~~~~~~~~~~~~

Validates an API key and returns the associated user.

**Signature**:

.. code-block:: python

   async def authenticate_api_key(self, api_key: str) -> Optional[User]

**Validation Process**:

.. mermaid::

   graph TD
       A[Hash Provided Key] --> B[Query by Hash]
       B --> C{API Key Found?}
       C -->|No| D[Return None]
       C -->|Yes| E{Key Active?}
       E -->|No| D
       E -->|Yes| F[Get Associated User]
       F --> G{User Active?}
       G -->|No| D
       G -->|Yes| H[Update Last Used]
       H --> I[Return User]

Role-Based Access Control
-------------------------

User Roles
~~~~~~~~~~

The system supports two primary roles:

.. mermaid::

   graph TD
       A[User Roles] --> B[IT Administrator]
       A --> C[Security Analyst]
       
       B --> D[User Management]
       B --> E[System Configuration]
       B --> F[All Application Operations]
       B --> G[CVE Data Management]
       
       C --> H[Application Management]
       C --> I[Component Management]
       C --> J[Vulnerability Feeds]
       C --> K[Own Profile Management]

Permission Matrix
~~~~~~~~~~~~~~~~~

.. list-table:: Role Permissions
   :header-rows: 1
   :widths: 30 20 20

   * - Operation
     - IT Admin
     - Security Analyst
   * - Create/Update/Delete Users
     - ✅
     - ❌
   * - Manage Applications
     - ✅
     - ✅
   * - Manage Components
     - ✅
     - ✅
   * - Access CVE Feeds
     - ✅
     - ✅
   * - Import CVE Data
     - ✅
     - ❌
   * - System Configuration
     - ✅
     - ❌
   * - Own API Key Management
     - ✅
     - ✅
   * - Change Own Password
     - ✅
     - ✅

Security Implementation
-----------------------

Password Security
~~~~~~~~~~~~~~~~~

.. code-block:: python

   from passlib.context import CryptContext
   
   pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
   
   def get_password_hash(password: str) -> str:
       return pwd_context.hash(password)
   
   def verify_password(plain_password: str, hashed_password: str) -> bool:
       return pwd_context.verify(plain_password, hashed_password)

**Security Features**:
* bcrypt hashing with automatic salt generation
* Configurable work factor for future-proofing
* Constant-time verification to prevent timing attacks

API Key Security
~~~~~~~~~~~~~~~~

.. code-block:: python

   import secrets
   import hashlib
   
   def generate_api_key() -> str:
       # Generate 32 bytes of random data
       random_bytes = secrets.token_bytes(32)
       # Encode as base64 with prefix
       return f"cvef_{secrets.token_urlsafe(32)}"
   
   def hash_api_key(api_key: str) -> str:
       return hashlib.sha256(api_key.encode()).hexdigest()

**Security Features**:
* Cryptographically secure random generation
* SHA-256 hashing for storage
* Prefixed format for easy identification
* URL-safe encoding

Error Handling
--------------

Authentication Errors
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Invalid credentials
   user = await auth_service.authenticate_user("invalid", "password")
   # Returns None, not an exception
   
   # Duplicate username
   try:
       await auth_service.create_user(user_data)
   except ValueError as e:
       # "Username 'existing_user' already exists"

Security Logging
~~~~~~~~~~~~~~~~

.. code-block:: python

   # Successful authentication
   logger.info(
       "User authenticated",
       username=username,
       method="password"
   )
   
   # Failed authentication (no sensitive data logged)
   logger.warning(
       "Authentication failed",
       username=username,
       method="password",
       reason="invalid_credentials"
   )

Integration Patterns
--------------------

FastAPI Integration
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from fastapi import Depends, HTTPException
   from fastapi.security import HTTPBearer
   
   security = HTTPBearer()
   
   async def get_current_user(
       token: str = Depends(security),
       auth_service: AuthService = Depends(get_auth_service)
   ) -> User:
       # Validate JWT token or API key
       user = await auth_service.authenticate_api_key(token.credentials)
       if not user:
           raise HTTPException(status_code=401, detail="Invalid authentication")
       return user

Middleware Integration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @app.middleware("http")
   async def auth_middleware(request: Request, call_next):
       # Extract authentication from headers
       auth_header = request.headers.get("authorization")
       api_key_header = request.headers.get("x-api-key")
       
       if api_key_header:
           user = await auth_service.authenticate_api_key(api_key_header)
           request.state.user = user
       
       response = await call_next(request)
       return response

Testing Considerations
----------------------

Unit Test Example
~~~~~~~~~~~~~~~~~

.. code-block:: python

   import pytest
   from unittest.mock import AsyncMock, patch
   
   @pytest.fixture
   async def auth_service(mock_db):
       return AuthService(mock_db)
   
   async def test_authenticate_user_success(auth_service, mock_db):
       # Setup
       mock_user = User(username="test", hashed_password="hashed")
       mock_db.execute.return_value.scalar_one_or_none.return_value = mock_user
       
       with patch('auth_service.verify_password', return_value=True):
           result = await auth_service.authenticate_user("test", "password")
       
       assert result == mock_user

Performance Considerations
--------------------------

* Password hashing is CPU-intensive but necessary for security
* API key lookups use indexed database queries
* User sessions can be cached to reduce database load
* Rate limiting should be implemented at the API layer

Next Steps
----------

* :doc:`application-service` - How applications integrate with auth
* :doc:`service-interactions` - Auth service integration patterns
* :doc:`../user-guide/authentication` - User guide for authentication
