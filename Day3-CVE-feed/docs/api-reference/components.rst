Components API
==============

The components API manages software components within applications and their CPE (Common Platform Enumeration) mappings. Components represent individual software packages, libraries, or services that make up an application.

Overview
--------

**Base Paths**: 
* ``/api/v1/applications/{application_id}/components`` (application-scoped)
* ``/api/v1/components`` (global component operations)

**Authentication**: Required (JWT Bearer token or API key)

**Permissions**: 
* ``security_analyst``: Full access to component management
* ``it_admin``: Full access to component management

Component Lifecycle
-------------------

.. mermaid::

   graph TD
       A[Create Component] --> B[Add CPE Mappings]
       B --> C[Validate CPE Format]
       C --> D[Link to Vulnerabilities]
       D --> E[Monitor & Update]
       E --> F[Update Version]
       F --> G[Update CPE Mappings]
       G --> D
       
       E --> H[Archive/Delete]

Component Endpoints
------------------

Create Component
~~~~~~~~~~~~~~~

POST /applications/{application_id}/components
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Create a new component within an application.

**Path Parameters**:
* ``application_id`` (UUID): Application ID

**Request Body**:

.. code-block:: json

   {
     "name": "nginx",
     "version": "1.20.1",
     "vendor": "nginx",
     "component_type": "web_server",
     "description": "High-performance web server and reverse proxy"
   }

**Required Fields**:
* ``name`` (string): Component name

**Optional Fields**:
* ``version`` (string): Component version
* ``vendor`` (string): Component vendor/publisher
* ``component_type`` (string): Type of component
* ``description`` (string): Component description

**Response** (201 Created):

.. code-block:: json

   {
     "id": "770e8400-e29b-41d4-a716-************",
     "application_id": "550e8400-e29b-41d4-a716-************",
     "name": "nginx",
     "version": "1.20.1",
     "vendor": "nginx",
     "component_type": "web_server",
     "description": "High-performance web server and reverse proxy",
     "created_at": "2025-01-15T11:00:00Z",
     "updated_at": "2025-01-15T11:00:00Z"
   }

**Example**:

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/applications/550e8400-e29b-41d4-a716-************/components" \
        -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "nginx",
          "version": "1.20.1",
          "vendor": "nginx",
          "component_type": "web_server"
        }'

**Error Responses**:
* ``400 Bad Request``: Component name/version already exists in application
* ``404 Not Found``: Application not found
* ``422 Unprocessable Entity``: Validation errors

List Application Components
~~~~~~~~~~~~~~~~~~~~~~~~~~

GET /applications/{application_id}/components
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

List all components for a specific application.

**Path Parameters**:
* ``application_id`` (UUID): Application ID

**Query Parameters**:
* ``skip`` (int, optional): Number of records to skip (default: 0)
* ``limit`` (int, optional): Maximum records to return (default: 100, max: 1000)
* ``component_type`` (string, optional): Filter by component type

**Response** (200 OK):

.. code-block:: json

   [
     {
       "id": "770e8400-e29b-41d4-a716-************",
       "application_id": "550e8400-e29b-41d4-a716-************",
       "name": "nginx",
       "version": "1.20.1",
       "vendor": "nginx",
       "component_type": "web_server",
       "description": "High-performance web server and reverse proxy",
       "created_at": "2025-01-15T11:00:00Z",
       "updated_at": "2025-01-15T11:00:00Z",
       "cpe_mappings": [
         {
           "id": "880e8400-e29b-41d4-a716-************",
           "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
           "confidence": 1.0,
           "mapping_source": "official",
           "created_at": "2025-01-15T11:05:00Z",
           "updated_at": "2025-01-15T11:05:00Z"
         }
       ]
     }
   ]

**Examples**:

.. code-block:: bash

   # List all components
   curl -H "X-API-Key: YOUR_API_KEY" \
        "http://localhost:8000/api/v1/applications/550e8400-e29b-41d4-a716-************/components"
   
   # Filter by component type
   curl -H "X-API-Key: YOUR_API_KEY" \
        "http://localhost:8000/api/v1/applications/550e8400-e29b-41d4-a716-************/components?component_type=web_server"

Get Component
~~~~~~~~~~~~

GET /components/{component_id}
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Retrieve a specific component with its CPE mappings.

**Path Parameters**:
* ``component_id`` (UUID): Component ID

**Response** (200 OK):

.. code-block:: json

   {
     "id": "770e8400-e29b-41d4-a716-************",
     "application_id": "550e8400-e29b-41d4-a716-************",
     "name": "nginx",
     "version": "1.20.1",
     "vendor": "nginx",
     "component_type": "web_server",
     "description": "High-performance web server and reverse proxy",
     "created_at": "2025-01-15T11:00:00Z",
     "updated_at": "2025-01-15T11:00:00Z",
     "cpe_mappings": [
       {
         "id": "880e8400-e29b-41d4-a716-************",
         "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
         "confidence": 1.0,
         "mapping_source": "official",
         "created_at": "2025-01-15T11:05:00Z",
         "updated_at": "2025-01-15T11:05:00Z"
       }
     ]
   }

**Example**:

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        "http://localhost:8000/api/v1/components/770e8400-e29b-41d4-a716-************"

**Error Responses**:
* ``404 Not Found``: Component not found

Update Component
~~~~~~~~~~~~~~~

PATCH /components/{component_id}
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Update an existing component. All fields are optional.

**Path Parameters**:
* ``component_id`` (UUID): Component ID

**Request Body** (all fields optional):

.. code-block:: json

   {
     "version": "1.20.2",
     "description": "Updated web server with security patches",
     "component_type": "reverse_proxy"
   }

**Response** (200 OK):

.. code-block:: json

   {
     "id": "770e8400-e29b-41d4-a716-************",
     "application_id": "550e8400-e29b-41d4-a716-************",
     "name": "nginx",
     "version": "1.20.2",
     "vendor": "nginx",
     "component_type": "reverse_proxy",
     "description": "Updated web server with security patches",
     "created_at": "2025-01-15T11:00:00Z",
     "updated_at": "2025-01-15T16:30:00Z"
   }

**Example**:

.. code-block:: bash

   curl -X PATCH "http://localhost:8000/api/v1/components/770e8400-e29b-41d4-a716-************" \
        -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "version": "1.20.2",
          "description": "Updated with security patches"
        }'

**Error Responses**:
* ``400 Bad Request``: Name/version conflict
* ``404 Not Found``: Component not found
* ``422 Unprocessable Entity``: Validation errors

Delete Component
~~~~~~~~~~~~~~~

DELETE /components/{component_id}
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Soft delete a component and all its CPE mappings.

**Path Parameters**:
* ``component_id`` (UUID): Component ID

**Response** (204 No Content): Empty response body

**Example**:

.. code-block:: bash

   curl -X DELETE "http://localhost:8000/api/v1/components/770e8400-e29b-41d4-a716-************" \
        -H "Authorization: Bearer YOUR_JWT_TOKEN"

**Error Responses**:
* ``404 Not Found``: Component not found

CPE Mapping Endpoints
--------------------

Create CPE Mapping
~~~~~~~~~~~~~~~~~

POST /components/{component_id}/cpe-mappings
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Create a CPE mapping for a component to link it to vulnerability data.

**Path Parameters**:
* ``component_id`` (UUID): Component ID

**Request Body**:

.. code-block:: json

   {
     "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
     "confidence": 1.0,
     "mapping_source": "official"
   }

**Required Fields**:
* ``cpe_string`` (string): Valid CPE 2.3 format string
* ``confidence`` (float): Confidence level (0.0 to 1.0)
* ``mapping_source`` (string): Source of the mapping

**Response** (201 Created):

.. code-block:: json

   {
     "id": "880e8400-e29b-41d4-a716-************",
     "component_id": "770e8400-e29b-41d4-a716-************",
     "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
     "confidence": 1.0,
     "mapping_source": "official",
     "created_at": "2025-01-15T11:05:00Z",
     "updated_at": "2025-01-15T11:05:00Z"
   }

**Example**:

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/components/770e8400-e29b-41d4-a716-************/cpe-mappings" \
        -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
          "confidence": 1.0,
          "mapping_source": "official"
        }'

**Error Responses**:
* ``400 Bad Request``: Invalid CPE format or duplicate mapping
* ``404 Not Found``: Component not found
* ``422 Unprocessable Entity``: Validation errors

Update CPE Mapping
~~~~~~~~~~~~~~~~~

PATCH /cpe-mappings/{cpe_mapping_id}
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Update an existing CPE mapping.

**Path Parameters**:
* ``cpe_mapping_id`` (UUID): CPE mapping ID

**Request Body** (all fields optional):

.. code-block:: json

   {
     "confidence": 0.9,
     "mapping_source": "vendor_advisory"
   }

**Response** (200 OK):

.. code-block:: json

   {
     "id": "880e8400-e29b-41d4-a716-************",
     "component_id": "770e8400-e29b-41d4-a716-************",
     "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
     "confidence": 0.9,
     "mapping_source": "vendor_advisory",
     "created_at": "2025-01-15T11:05:00Z",
     "updated_at": "2025-01-15T17:00:00Z"
   }

**Example**:

.. code-block:: bash

   curl -X PATCH "http://localhost:8000/api/v1/cpe-mappings/880e8400-e29b-41d4-a716-************" \
        -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "confidence": 0.9,
          "mapping_source": "vendor_advisory"
        }'

Delete CPE Mapping
~~~~~~~~~~~~~~~~~

DELETE /cpe-mappings/{cpe_mapping_id}
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Delete a CPE mapping.

**Path Parameters**:
* ``cpe_mapping_id`` (UUID): CPE mapping ID

**Response** (204 No Content): Empty response body

**Example**:

.. code-block:: bash

   curl -X DELETE "http://localhost:8000/api/v1/cpe-mappings/880e8400-e29b-41d4-a716-************" \
        -H "Authorization: Bearer YOUR_JWT_TOKEN"

Component Types
--------------

Standard Component Types
~~~~~~~~~~~~~~~~~~~~~~~

Recommended component types (not enforced):

**Infrastructure**:
* ``web_server`` - Web servers (nginx, Apache)
* ``database`` - Database systems (PostgreSQL, MySQL)
* ``cache`` - Caching systems (Redis, Memcached)
* ``load_balancer`` - Load balancers (HAProxy, F5)
* ``message_queue`` - Message queues (RabbitMQ, Kafka)

**Application**:
* ``framework`` - Application frameworks (Django, Spring)
* ``library`` - Software libraries (jQuery, Lodash)
* ``runtime`` - Runtime environments (Node.js, Python)
* ``container`` - Container images (Docker images)

**Security**:
* ``authentication`` - Auth systems (OAuth, LDAP)
* ``encryption`` - Encryption libraries (OpenSSL)
* ``firewall`` - Firewall software

**Operations**:
* ``monitoring`` - Monitoring tools (Prometheus, Grafana)
* ``logging`` - Logging systems (ELK Stack)

CPE Mapping Guidelines
---------------------

CPE Format Validation
~~~~~~~~~~~~~~~~~~~~

CPE strings must follow CPE 2.3 format:

.. code-block:: text

   cpe:2.3:part:vendor:product:version:update:edition:language:sw_edition:target_sw:target_hw:other

**Example Valid CPE Strings**:

.. code-block:: text

   cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*
   cpe:2.3:a:postgresql:postgresql:13.8:*:*:*:*:*:*:*
   cpe:2.3:a:apache:log4j:2.17.0:*:*:*:*:*:*:*

Confidence Levels
~~~~~~~~~~~~~~~~

.. list-table:: Confidence Level Guidelines
   :header-rows: 1
   :widths: 20 20 60

   * - Confidence
     - Level
     - Description
   * - 1.0
     - Certain
     - Official CPE from vendor or NIST
   * - 0.9
     - High
     - High confidence based on multiple sources
   * - 0.8
     - Good
     - Good match but some uncertainty
   * - 0.7
     - Fair
     - Reasonable guess, needs verification
   * - ≤ 0.6
     - Low
     - Uncertain, requires review

Mapping Sources
~~~~~~~~~~~~~~

.. list-table:: Mapping Source Types
   :header-rows: 1
   :widths: 30 70

   * - Source
     - Description
   * - ``official``
     - From vendor or NIST CPE dictionary
   * - ``nist_database``
     - Found in NVD CVE database
   * - ``vendor_advisory``
     - From vendor security advisory
   * - ``manual_research``
     - Manual research and verification
   * - ``automated_tool``
     - Generated by automated tool
   * - ``best_guess``
     - Educated guess, needs verification

Business Rules
-------------

Component Uniqueness
~~~~~~~~~~~~~~~~~~~

* Components must be unique by name and version within an application
* Names are case-sensitive
* Empty versions are allowed for components without versioning

CPE Uniqueness
~~~~~~~~~~~~~

* CPE strings must be unique within a component
* Multiple CPE mappings per component are allowed for different confidence levels

Version Handling
~~~~~~~~~~~~~~~

* Supports semantic versioning (1.2.3)
* Supports date-based versioning (2025.01.15)
* Supports custom versioning schemes
* Version comparison is string-based

Integration Patterns
-------------------

Automated Component Discovery
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Example: Scan container image for components
   def scan_container_components(image_name, application_id):
       scanner = ContainerScanner()
       components = scanner.scan_image(image_name)
       
       for comp in components:
           component_data = {
               "name": comp.name,
               "version": comp.version,
               "vendor": comp.vendor,
               "component_type": comp.type
           }
           
           # Create component
           response = requests.post(
               f"http://localhost:8000/api/v1/applications/{application_id}/components",
               json=component_data,
               headers={"X-API-Key": api_key}
           )
           
           if response.status_code == 201:
               component_id = response.json()["id"]
               
               # Add CPE mapping if available
               if comp.cpe:
                   cpe_data = {
                       "cpe_string": comp.cpe,
                       "confidence": 0.8,
                       "mapping_source": "automated_tool"
                   }
                   
                   requests.post(
                       f"http://localhost:8000/api/v1/components/{component_id}/cpe-mappings",
                       json=cpe_data,
                       headers={"X-API-Key": api_key}
                   )

SBOM Integration
~~~~~~~~~~~~~~~

.. code-block:: python

   # Example: Import from SPDX SBOM
   def import_from_sbom(sbom_file, application_id):
       sbom = parse_spdx_file(sbom_file)
       
       for package in sbom.packages:
           component_data = {
               "name": package.name,
               "version": package.version,
               "vendor": package.supplier,
               "component_type": "library"
           }
           
           # Create component
           response = create_component(application_id, component_data)
           
           # Add CPE if available in SBOM
           if package.external_refs:
               for ref in package.external_refs:
                   if ref.type == "cpe23Type":
                       create_cpe_mapping(response["id"], ref.locator)

Next Steps
----------

* :doc:`vulnerabilities` - Querying vulnerabilities for components
* :doc:`schemas` - Complete schema reference
* :doc:`../user-guide/component-management` - User guide for component management
* :doc:`../user-guide/cpe-mapping` - User guide for CPE mapping
