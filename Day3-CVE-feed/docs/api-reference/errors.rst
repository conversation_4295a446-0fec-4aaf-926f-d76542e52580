Error Handling
==============

The CVE Feed Service API uses standard HTTP status codes and provides detailed error information to help developers understand and resolve issues.

Overview
--------

**Error Response Format**: All errors return JSON with consistent structure
**HTTP Status Codes**: Standard codes indicate error categories
**Error Types**: Specific error types for programmatic handling
**Localization**: Error messages are in English

Standard Error Response
----------------------

Basic Error Structure
~~~~~~~~~~~~~~~~~~~~

.. code-block:: json

   {
     "detail": "Human-readable error message",
     "type": "error_type_identifier",
     "field": "field_name_for_validation_errors"
   }

**Fields**:
* ``detail`` (string, required): Human-readable error description
* ``type`` (string, optional): Machine-readable error type identifier
* ``field`` (string, optional): Field name for validation errors

Validation Error Structure
~~~~~~~~~~~~~~~~~~~~~~~~~

For input validation errors (HTTP 422):

.. code-block:: json

   {
     "detail": [
       {
         "loc": ["field_name"],
         "msg": "Field-specific error message",
         "type": "validation_error_type"
       },
       {
         "loc": ["nested", "field"],
         "msg": "Nested field error message", 
         "type": "validation_error_type"
       }
     ]
   }

**Validation Error Fields**:
* ``loc`` (array): Field location path
* ``msg`` (string): Specific validation error message
* ``type`` (string): Validation error type

HTTP Status Codes
-----------------

Client Errors (4xx)
~~~~~~~~~~~~~~~~~~

400 Bad Request
^^^^^^^^^^^^^^

Invalid request that cannot be processed due to client error.

**Common Causes**:
* Malformed JSON in request body
* Invalid query parameter values
* Business logic violations
* Resource conflicts

**Example**:

.. code-block:: json

   {
     "detail": "Application 'Customer Portal' already exists in environment 'production'",
     "type": "conflict_error"
   }

401 Unauthorized
^^^^^^^^^^^^^^^

Authentication required or authentication failed.

**Common Causes**:
* Missing authentication credentials
* Invalid JWT token
* Expired JWT token
* Invalid API key

**Examples**:

.. code-block:: json

   {
     "detail": "Authentication credentials required",
     "type": "authentication_error"
   }

.. code-block:: json

   {
     "detail": "Invalid or expired authentication token",
     "type": "authentication_error"
   }

403 Forbidden
^^^^^^^^^^^^^

Authentication succeeded but insufficient permissions.

**Common Causes**:
* User role lacks required permissions
* Account deactivated
* Resource access restricted

**Example**:

.. code-block:: json

   {
     "detail": "Insufficient permissions to access user management",
     "type": "authorization_error"
   }

404 Not Found
^^^^^^^^^^^^^

Requested resource does not exist.

**Common Causes**:
* Invalid resource ID
* Resource has been deleted
* Incorrect endpoint URL

**Examples**:

.. code-block:: json

   {
     "detail": "Application with ID 550e8400-e29b-41d4-a716-************ not found",
     "type": "not_found_error"
   }

.. code-block:: json

   {
     "detail": "CVE-2023-9999 not found",
     "type": "not_found_error"
   }

409 Conflict
^^^^^^^^^^^^

Request conflicts with current resource state.

**Common Causes**:
* Duplicate resource creation
* Concurrent modification conflicts
* Business rule violations

**Example**:

.. code-block:: json

   {
     "detail": "Username 'existing_user' already exists",
     "type": "conflict_error"
   }

422 Unprocessable Entity
^^^^^^^^^^^^^^^^^^^^^^^

Request is well-formed but contains semantic errors.

**Common Causes**:
* Input validation failures
* Invalid field values
* Missing required fields
* Field format errors

**Example**:

.. code-block:: json

   {
     "detail": [
       {
         "loc": ["name"],
         "msg": "Field required",
         "type": "missing"
       },
       {
         "loc": ["email"],
         "msg": "Invalid email format",
         "type": "value_error.email"
       }
     ]
   }

429 Too Many Requests
^^^^^^^^^^^^^^^^^^^^

Rate limit exceeded.

**Common Causes**:
* API rate limit exceeded
* Authentication attempt limit exceeded
* Resource-specific rate limits

**Example**:

.. code-block:: json

   {
     "detail": "Rate limit exceeded. Try again in 60 seconds",
     "type": "rate_limit_error"
   }

**Headers**:
* ``Retry-After``: Seconds until rate limit resets
* ``X-RateLimit-Limit``: Rate limit threshold
* ``X-RateLimit-Remaining``: Remaining requests
* ``X-RateLimit-Reset``: Rate limit reset time

Server Errors (5xx)
~~~~~~~~~~~~~~~~~~

500 Internal Server Error
^^^^^^^^^^^^^^^^^^^^^^^^

Unexpected server error occurred.

**Common Causes**:
* Database connection failures
* Unhandled exceptions
* External service failures
* Configuration errors

**Example**:

.. code-block:: json

   {
     "detail": "An internal server error occurred. Please try again later",
     "type": "internal_server_error"
   }

502 Bad Gateway
^^^^^^^^^^^^^^

Error communicating with upstream services.

**Common Causes**:
* Database server unavailable
* External API failures
* Network connectivity issues

**Example**:

.. code-block:: json

   {
     "detail": "Service temporarily unavailable. Please try again later",
     "type": "service_unavailable_error"
   }

503 Service Unavailable
^^^^^^^^^^^^^^^^^^^^^^

Service temporarily unavailable.

**Common Causes**:
* Maintenance mode
* System overload
* Dependency failures

**Example**:

.. code-block:: json

   {
     "detail": "Service temporarily unavailable for maintenance",
     "type": "service_unavailable_error"
   }

Error Types
----------

Authentication Errors
~~~~~~~~~~~~~~~~~~~~

authentication_error
^^^^^^^^^^^^^^^^^^^^

Authentication failed or required.

**HTTP Status**: 401
**Common Scenarios**:
* Missing credentials
* Invalid credentials
* Expired tokens

authorization_error
^^^^^^^^^^^^^^^^^^

Insufficient permissions for requested action.

**HTTP Status**: 403
**Common Scenarios**:
* Role-based access control violations
* Deactivated accounts
* Resource-specific permissions

Validation Errors
~~~~~~~~~~~~~~~~

validation_error
^^^^^^^^^^^^^^^^

Input validation failed.

**HTTP Status**: 422
**Common Scenarios**:
* Required fields missing
* Invalid field formats
* Value out of range

**Validation Error Subtypes**:
* ``missing``: Required field not provided
* ``value_error.email``: Invalid email format
* ``value_error.uuid``: Invalid UUID format
* ``value_error.datetime``: Invalid datetime format
* ``value_error.url``: Invalid URL format

Resource Errors
~~~~~~~~~~~~~~

not_found_error
^^^^^^^^^^^^^^

Requested resource does not exist.

**HTTP Status**: 404
**Common Scenarios**:
* Invalid resource IDs
* Deleted resources
* Incorrect endpoints

conflict_error
^^^^^^^^^^^^^

Resource conflict detected.

**HTTP Status**: 409
**Common Scenarios**:
* Duplicate resource creation
* Unique constraint violations
* Concurrent modifications

Rate Limiting Errors
~~~~~~~~~~~~~~~~~~~

rate_limit_error
^^^^^^^^^^^^^^^

Rate limit exceeded.

**HTTP Status**: 429
**Common Scenarios**:
* API call frequency limits
* Authentication attempt limits
* Resource-specific limits

System Errors
~~~~~~~~~~~~~

internal_server_error
^^^^^^^^^^^^^^^^^^^^

Unexpected server error.

**HTTP Status**: 500
**Common Scenarios**:
* Unhandled exceptions
* Database errors
* Configuration issues

service_unavailable_error
^^^^^^^^^^^^^^^^^^^^^^^^

Service temporarily unavailable.

**HTTP Status**: 503
**Common Scenarios**:
* Maintenance mode
* System overload
* Dependency failures

Field-Specific Errors
--------------------

Application Fields
~~~~~~~~~~~~~~~~~

name
^^^^

* **Required**: Application name is required
* **Length**: Must be 1-255 characters
* **Uniqueness**: Must be unique within environment
* **Format**: Trimmed of whitespace

**Error Examples**:

.. code-block:: json

   {
     "detail": [
       {
         "loc": ["name"],
         "msg": "Field required",
         "type": "missing"
       }
     ]
   }

.. code-block:: json

   {
     "detail": [
       {
         "loc": ["name"],
         "msg": "String should have at most 255 characters",
         "type": "string_too_long"
       }
     ]
   }

environment
^^^^^^^^^^

* **Optional**: Environment is optional
* **Length**: Maximum 50 characters
* **Format**: Trimmed of whitespace

Component Fields
~~~~~~~~~~~~~~~

cpe_string
^^^^^^^^^

* **Required**: CPE string is required for CPE mappings
* **Format**: Must be valid CPE 2.3 format
* **Components**: Must have exactly 13 colon-separated parts

**Error Examples**:

.. code-block:: json

   {
     "detail": [
       {
         "loc": ["cpe_string"],
         "msg": "Invalid CPE 2.3 format: must have 13 colon-separated components",
         "type": "value_error.cpe_format"
       }
     ]
   }

confidence
^^^^^^^^^

* **Required**: Confidence level is required
* **Range**: Must be between 0.0 and 1.0 inclusive
* **Type**: Must be a float

**Error Examples**:

.. code-block:: json

   {
     "detail": [
       {
         "loc": ["confidence"],
         "msg": "Value must be between 0.0 and 1.0",
         "type": "value_error.number.not_ge"
       }
     ]
   }

User Fields
~~~~~~~~~~

password
^^^^^^^

* **Required**: Password is required for user creation
* **Length**: Minimum 8 characters
* **Complexity**: Must meet complexity requirements

**Error Examples**:

.. code-block:: json

   {
     "detail": [
       {
         "loc": ["password"],
         "msg": "Password must be at least 8 characters long",
         "type": "value_error.password.too_short"
       }
     ]
   }

.. code-block:: json

   {
     "detail": [
       {
         "loc": ["password"],
         "msg": "Password must contain uppercase, lowercase, number, and special character",
         "type": "value_error.password.complexity"
       }
     ]
   }

Error Handling Best Practices
-----------------------------

Client-Side Handling
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import httpx
   
   async def handle_api_request(url, data, headers):
       """Example of proper error handling."""
       try:
           async with httpx.AsyncClient() as client:
               response = await client.post(url, json=data, headers=headers)
               
               if response.status_code == 200:
                   return response.json()
               elif response.status_code == 400:
                   error = response.json()
                   print(f"Bad request: {error['detail']}")
                   return None
               elif response.status_code == 401:
                   print("Authentication required")
                   # Redirect to login
                   return None
               elif response.status_code == 403:
                   print("Insufficient permissions")
                   return None
               elif response.status_code == 404:
                   print("Resource not found")
                   return None
               elif response.status_code == 422:
                   error = response.json()
                   print("Validation errors:")
                   for field_error in error['detail']:
                       field = '.'.join(field_error['loc'])
                       message = field_error['msg']
                       print(f"  {field}: {message}")
                   return None
               elif response.status_code == 429:
                   retry_after = response.headers.get('Retry-After', '60')
                   print(f"Rate limited. Retry after {retry_after} seconds")
                   return None
               elif response.status_code >= 500:
                   print("Server error. Please try again later")
                   return None
               else:
                   print(f"Unexpected status code: {response.status_code}")
                   return None
                   
       except httpx.RequestError as e:
           print(f"Network error: {e}")
           return None

Retry Logic
~~~~~~~~~~

.. code-block:: python

   import asyncio
   import random
   
   async def api_request_with_retry(url, data, headers, max_retries=3):
       """API request with exponential backoff retry."""
       for attempt in range(max_retries + 1):
           try:
               response = await make_api_request(url, data, headers)
               
               if response.status_code == 429:
                   # Rate limited - respect Retry-After header
                   retry_after = int(response.headers.get('Retry-After', '60'))
                   await asyncio.sleep(retry_after)
                   continue
               elif response.status_code >= 500:
                   # Server error - exponential backoff
                   if attempt < max_retries:
                       delay = (2 ** attempt) + random.uniform(0, 1)
                       await asyncio.sleep(delay)
                       continue
               
               return response
               
           except httpx.RequestError:
               if attempt < max_retries:
                   delay = (2 ** attempt) + random.uniform(0, 1)
                   await asyncio.sleep(delay)
                   continue
               raise
       
       raise Exception("Max retries exceeded")

Logging and Monitoring
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import logging
   
   logger = logging.getLogger(__name__)
   
   def log_api_error(response, context):
       """Log API errors for monitoring."""
       error_data = response.json() if response.content else {}
       
       logger.error(
           "API request failed",
           extra={
               "status_code": response.status_code,
               "error_type": error_data.get("type"),
               "error_detail": error_data.get("detail"),
               "url": str(response.url),
               "context": context
           }
       )

Common Error Scenarios
---------------------

Authentication Issues
~~~~~~~~~~~~~~~~~~~

**Scenario**: API key authentication fails

.. code-block:: bash

   curl -H "X-API-Key: invalid_key" \
        "http://localhost:8000/api/v1/applications"

**Response**:

.. code-block:: json

   {
     "detail": "Invalid API key",
     "type": "authentication_error"
   }

**Resolution**: Verify API key is correct and active

Validation Failures
~~~~~~~~~~~~~~~~~~

**Scenario**: Creating application with invalid data

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/applications" \
        -H "X-API-Key: valid_key" \
        -H "Content-Type: application/json" \
        -d '{"name": "", "environment": "x" * 100}'

**Response**:

.. code-block:: json

   {
     "detail": [
       {
         "loc": ["name"],
         "msg": "String should have at least 1 character",
         "type": "string_too_short"
       },
       {
         "loc": ["environment"],
         "msg": "String should have at most 50 characters",
         "type": "string_too_long"
       }
     ]
   }

**Resolution**: Fix validation errors and retry

Resource Conflicts
~~~~~~~~~~~~~~~~~

**Scenario**: Creating duplicate application

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/applications" \
        -H "X-API-Key: valid_key" \
        -H "Content-Type: application/json" \
        -d '{"name": "Existing App", "environment": "production"}'

**Response**:

.. code-block:: json

   {
     "detail": "Application 'Existing App' already exists in environment 'production'",
     "type": "conflict_error"
   }

**Resolution**: Use different name or update existing application

Next Steps
----------

* :doc:`authentication` - Authentication endpoint details
* :doc:`schemas` - Complete schema reference
* :doc:`../user-guide/index` - User guide for API consumers
* :doc:`../testing/ux-testing` - UX testing for error scenarios
