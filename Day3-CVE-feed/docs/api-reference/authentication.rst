Authentication API
==================

The authentication API provides endpoints for user management, login, and API key management. All endpoints except login require authentication.

Overview
--------

**Base Path**: ``/api/v1/auth``

**Authentication Methods**:
* JWT Bearer tokens (for user sessions)
* API keys (for programmatic access)

**User Roles**:
* ``security_analyst``: Can manage applications and access vulnerability feeds
* ``it_admin``: Full administrative access including user management

Authentication Flow
-------------------

.. mermaid::

   sequenceDiagram
       participant Client
       participant API
       participant AuthService
       participant Database
       
       Client->>API: POST /auth/login
       API->>AuthService: authenticate_user()
       AuthService->>Database: Verify credentials
       Database-->>AuthService: User data
       AuthService-->>API: User object
       API->>API: Generate JWT token
       API-->>Client: JWT token + expiry
       
       Note over Client: Store token for subsequent requests
       
       Client->>API: GET /auth/me (with Bear<PERSON> token)
       API->>API: Validate JWT token
       API-->>Client: User profile data

Endpoints
---------

User Authentication
~~~~~~~~~~~~~~~~~~~

POST /auth/login
^^^^^^^^^^^^^^^^

Authenticate user and receive JWT token.

**Request Body**:

.. code-block:: json

   {
     "username": "string",
     "password": "string"
   }

**Response** (200 OK):

.. code-block:: json

   {
     "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "token_type": "bearer",
     "expires_in": 3600
   }

**Example**:

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
          "username": "<EMAIL>",
          "password": "secure_password"
        }'

**Error Responses**:
* ``401 Unauthorized``: Invalid credentials
* ``422 Unprocessable Entity``: Invalid request format

GET /auth/me
^^^^^^^^^^^^

Get current user information including API keys.

**Authentication**: Required (JWT or API key)

**Response** (200 OK):

.. code-block:: json

   {
     "id": "550e8400-e29b-41d4-a716-************",
     "username": "<EMAIL>",
     "email": "<EMAIL>",
     "full_name": "Security Analyst",
     "role": "security_analyst",
     "is_active": true,
     "created_at": "2025-01-01T00:00:00Z",
     "updated_at": "2025-01-01T00:00:00Z",
     "api_keys": [
       {
         "id": "660e8400-e29b-41d4-a716-************",
         "name": "Production API Key",
         "is_active": true,
         "last_used_at": "2025-01-15T10:30:00Z",
         "created_at": "2025-01-01T00:00:00Z"
       }
     ]
   }

**Example**:

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        "http://localhost:8000/api/v1/auth/me"

POST /auth/change-password
^^^^^^^^^^^^^^^^^^^^^^^^^^

Change current user's password.

**Authentication**: Required (JWT or API key)

**Request Body**:

.. code-block:: json

   {
     "current_password": "old_password",
     "new_password": "new_secure_password"
   }

**Response** (200 OK):

.. code-block:: json

   {
     "message": "Password changed successfully"
   }

**Example**:

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/auth/change-password" \
        -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "current_password": "old_password",
          "new_password": "new_secure_password"
        }'

**Error Responses**:
* ``400 Bad Request``: Current password incorrect
* ``422 Unprocessable Entity``: Password validation failed

User Management (Admin Only)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

POST /auth/users
^^^^^^^^^^^^^^^^

Create a new user account.

**Authentication**: Required (Admin role)

**Request Body**:

.. code-block:: json

   {
     "username": "<EMAIL>",
     "email": "<EMAIL>",
     "full_name": "New User",
     "password": "secure_password",
     "role": "security_analyst"
   }

**Response** (201 Created):

.. code-block:: json

   {
     "id": "770e8400-e29b-41d4-a716-************",
     "username": "<EMAIL>",
     "email": "<EMAIL>",
     "full_name": "New User",
     "role": "security_analyst",
     "is_active": true,
     "created_at": "2025-01-15T12:00:00Z",
     "updated_at": "2025-01-15T12:00:00Z"
   }

**Example**:

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/auth/users" \
        -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "username": "<EMAIL>",
          "email": "<EMAIL>",
          "full_name": "New User",
          "password": "secure_password",
          "role": "security_analyst"
        }'

**Error Responses**:
* ``400 Bad Request``: Username/email already exists
* ``403 Forbidden``: Insufficient permissions
* ``422 Unprocessable Entity``: Validation errors

GET /auth/users
^^^^^^^^^^^^^^^

List all users.

**Authentication**: Required (Admin role)

**Query Parameters**:
* ``skip`` (int, optional): Number of records to skip (default: 0)
* ``limit`` (int, optional): Maximum records to return (default: 100, max: 1000)

**Response** (200 OK):

.. code-block:: json

   [
     {
       "id": "550e8400-e29b-41d4-a716-************",
       "username": "<EMAIL>",
       "email": "<EMAIL>",
       "full_name": "Security Analyst",
       "role": "security_analyst",
       "is_active": true,
       "created_at": "2025-01-01T00:00:00Z",
       "updated_at": "2025-01-01T00:00:00Z"
     }
   ]

**Example**:

.. code-block:: bash

   curl -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
        "http://localhost:8000/api/v1/auth/users?limit=50"

PATCH /auth/users/{user_id}
^^^^^^^^^^^^^^^^^^^^^^^^^^^

Update user information.

**Authentication**: Required (Admin role)

**Path Parameters**:
* ``user_id`` (UUID): User ID to update

**Request Body** (all fields optional):

.. code-block:: json

   {
     "email": "<EMAIL>",
     "full_name": "Updated Name",
     "role": "it_admin",
     "is_active": false
   }

**Response** (200 OK):

.. code-block:: json

   {
     "id": "550e8400-e29b-41d4-a716-************",
     "username": "<EMAIL>",
     "email": "<EMAIL>",
     "full_name": "Updated Name",
     "role": "it_admin",
     "is_active": false,
     "created_at": "2025-01-01T00:00:00Z",
     "updated_at": "2025-01-15T12:30:00Z"
   }

**Example**:

.. code-block:: bash

   curl -X PATCH "http://localhost:8000/api/v1/auth/users/550e8400-e29b-41d4-a716-************" \
        -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "role": "it_admin",
          "is_active": true
        }'

API Key Management
~~~~~~~~~~~~~~~~~

POST /auth/api-keys
^^^^^^^^^^^^^^^^^^^

Create a new API key for the current user.

**Authentication**: Required (JWT or API key)

**Request Body**:

.. code-block:: json

   {
     "name": "Production Integration Key"
   }

**Response** (201 Created):

.. code-block:: json

   {
     "id": "880e8400-e29b-41d4-a716-************",
     "name": "Production Integration Key",
     "api_key": "cvef_abc123def456ghi789jkl012mno345pqr678stu901vwx234yz",
     "is_active": true,
     "created_at": "2025-01-15T13:00:00Z"
   }

.. warning::
   The ``api_key`` value is only returned once during creation. Store it securely as it cannot be retrieved again.

**Example**:

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/auth/api-keys" \
        -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "Production Integration Key"
        }'

GET /auth/api-keys
^^^^^^^^^^^^^^^^^^

List current user's API keys.

**Authentication**: Required (JWT or API key)

**Response** (200 OK):

.. code-block:: json

   [
     {
       "id": "880e8400-e29b-41d4-a716-************",
       "name": "Production Integration Key",
       "is_active": true,
       "last_used_at": "2025-01-15T14:30:00Z",
       "created_at": "2025-01-15T13:00:00Z"
     }
   ]

**Example**:

.. code-block:: bash

   curl -H "X-API-Key: YOUR_API_KEY" \
        "http://localhost:8000/api/v1/auth/api-keys"

DELETE /auth/api-keys/{api_key_id}
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Delete an API key.

**Authentication**: Required (JWT or API key)

**Path Parameters**:
* ``api_key_id`` (UUID): API key ID to delete

**Response** (204 No Content): Empty response body

**Example**:

.. code-block:: bash

   curl -X DELETE "http://localhost:8000/api/v1/auth/api-keys/880e8400-e29b-41d4-a716-************" \
        -H "Authorization: Bearer YOUR_JWT_TOKEN"

**Error Responses**:
* ``404 Not Found``: API key not found or doesn't belong to user

Authentication Headers
---------------------

JWT Bearer Token
~~~~~~~~~~~~~~~~

Include JWT token in the Authorization header:

.. code-block:: text

   Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

API Key
~~~~~~~

Include API key in the X-API-Key header:

.. code-block:: text

   X-API-Key: cvef_abc123def456ghi789jkl012mno345pqr678stu901vwx234yz

Security Considerations
----------------------

Password Requirements
~~~~~~~~~~~~~~~~~~~~

* Minimum 8 characters
* Must contain at least one uppercase letter
* Must contain at least one lowercase letter
* Must contain at least one number
* Must contain at least one special character

JWT Token Security
~~~~~~~~~~~~~~~~~

* Tokens expire after 60 minutes by default
* Tokens are signed with HS256 algorithm
* Include user role for authorization
* Should be stored securely on client side

API Key Security
~~~~~~~~~~~~~~~

* API keys are hashed before storage
* Keys have ``cvef_`` prefix for identification
* Keys should be rotated regularly
* Deactivated keys are immediately invalid
* Track last usage for security monitoring

Rate Limiting
~~~~~~~~~~~~

Authentication endpoints have specific rate limits:

* Login attempts: 5 per minute per IP
* Password changes: 3 per hour per user
* API key creation: 10 per hour per user

Error Handling
-------------

Authentication errors follow standard HTTP status codes:

**401 Unauthorized**:
* Invalid credentials
* Expired or invalid JWT token
* Invalid API key

**403 Forbidden**:
* Insufficient permissions for admin endpoints
* Account deactivated

**422 Unprocessable Entity**:
* Invalid request format
* Password validation failures
* Username/email format errors

**429 Too Many Requests**:
* Rate limit exceeded
* Includes ``Retry-After`` header

Example error response:

.. code-block:: json

   {
     "detail": "Invalid authentication credentials",
     "type": "authentication_error"
   }

Next Steps
----------

* :doc:`applications` - Application management endpoints
* :doc:`components` - Component and CPE mapping endpoints
* :doc:`vulnerabilities` - CVE and vulnerability feed endpoints
* :doc:`schemas` - Complete schema reference
