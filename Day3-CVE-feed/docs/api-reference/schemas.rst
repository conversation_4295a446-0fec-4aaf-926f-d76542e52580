API Schemas
===========

This document provides comprehensive reference for all request and response schemas used in the CVE Feed Service API.

Overview
--------

All API endpoints use JSON for request and response bodies. Schemas are defined using Pydantic models with automatic validation and serialization.

**Schema Categories**:
* Authentication schemas (users, API keys)
* Application management schemas
* Component and CPE mapping schemas
* Vulnerability and CVE schemas
* Common utility schemas

Authentication Schemas
---------------------

User Schemas
~~~~~~~~~~~

UserCreate
^^^^^^^^^^

Schema for creating new users.

.. code-block:: json

   {
     "username": "string (required, 1-255 chars, unique)",
     "email": "string (required, valid email, unique)",
     "full_name": "string (required, 1-255 chars)",
     "password": "string (required, min 8 chars, complexity rules)",
     "role": "string (required, enum: security_analyst|it_admin)"
   }

**Validation Rules**:
* Username: alphanumeric, dots, hyphens, underscores
* Email: valid email format
* Password: minimum 8 characters, must contain uppercase, lowercase, number, special character
* Role: must be one of the defined roles

UserUpdate
^^^^^^^^^^

Schema for updating existing users.

.. code-block:: json

   {
     "email": "string (optional, valid email, unique)",
     "full_name": "string (optional, 1-255 chars)",
     "role": "string (optional, enum: security_analyst|it_admin)",
     "is_active": "boolean (optional)"
   }

UserResponse
^^^^^^^^^^^^

Schema for user data in responses.

.. code-block:: json

   {
     "id": "UUID",
     "username": "string",
     "email": "string",
     "full_name": "string",
     "role": "string",
     "is_active": "boolean",
     "created_at": "datetime (ISO 8601)",
     "updated_at": "datetime (ISO 8601)",
     "api_keys": "array of APIKeyResponse (optional)"
   }

Login Schemas
~~~~~~~~~~~~

LoginRequest
^^^^^^^^^^^^

.. code-block:: json

   {
     "username": "string (required)",
     "password": "string (required)"
   }

LoginResponse
^^^^^^^^^^^^^

.. code-block:: json

   {
     "access_token": "string (JWT token)",
     "token_type": "string (always 'bearer')",
     "expires_in": "integer (seconds until expiration)"
   }

ChangePasswordRequest
^^^^^^^^^^^^^^^^^^^^

.. code-block:: json

   {
     "current_password": "string (required)",
     "new_password": "string (required, min 8 chars, complexity rules)"
   }

API Key Schemas
~~~~~~~~~~~~~~

APIKeyCreate
^^^^^^^^^^^^

.. code-block:: json

   {
     "name": "string (required, 1-255 chars, unique per user)"
   }

APIKeyResponse
^^^^^^^^^^^^^^

.. code-block:: json

   {
     "id": "UUID",
     "name": "string",
     "is_active": "boolean",
     "last_used_at": "datetime (ISO 8601, nullable)",
     "created_at": "datetime (ISO 8601)",
     "updated_at": "datetime (ISO 8601)"
   }

APIKeyCreateResponse
^^^^^^^^^^^^^^^^^^^

.. code-block:: json

   {
     "id": "UUID",
     "name": "string",
     "api_key": "string (plain text key, only returned once)",
     "is_active": "boolean",
     "created_at": "datetime (ISO 8601)"
   }

Application Schemas
------------------

ApplicationCreate
~~~~~~~~~~~~~~~~

Schema for creating new applications.

.. code-block:: json

   {
     "name": "string (required, 1-255 chars)",
     "description": "string (optional, max 1000 chars)",
     "version": "string (optional, max 100 chars)",
     "owner": "string (optional, max 255 chars)",
     "environment": "string (optional, max 50 chars)",
     "criticality": "string (optional, max 20 chars)"
   }

**Validation Rules**:
* Name: required, trimmed, unique within environment
* Description: optional, trimmed
* Version: optional, supports semantic versioning
* Owner: optional, can be team name or email
* Environment: optional, common values: dev, staging, production
* Criticality: optional, common values: low, medium, high, critical

ApplicationUpdate
~~~~~~~~~~~~~~~~~

Schema for updating existing applications.

.. code-block:: json

   {
     "name": "string (optional, 1-255 chars)",
     "description": "string (optional, max 1000 chars)",
     "version": "string (optional, max 100 chars)",
     "owner": "string (optional, max 255 chars)",
     "environment": "string (optional, max 50 chars)",
     "criticality": "string (optional, max 20 chars)"
   }

ApplicationResponse
~~~~~~~~~~~~~~~~~~

Schema for application data in responses.

.. code-block:: json

   {
     "id": "UUID",
     "name": "string",
     "description": "string (nullable)",
     "version": "string (nullable)",
     "owner": "string (nullable)",
     "environment": "string (nullable)",
     "criticality": "string (nullable)",
     "created_at": "datetime (ISO 8601)",
     "updated_at": "datetime (ISO 8601)",
     "components": "array of ComponentResponse (optional)"
   }

Component Schemas
----------------

ComponentCreate
~~~~~~~~~~~~~~

Schema for creating new components.

.. code-block:: json

   {
     "name": "string (required, 1-255 chars)",
     "version": "string (optional, max 100 chars)",
     "vendor": "string (optional, max 255 chars)",
     "component_type": "string (optional, max 50 chars)",
     "description": "string (optional, max 1000 chars)"
   }

**Validation Rules**:
* Name: required, trimmed, unique within application (with version)
* Version: optional, supports various versioning schemes
* Vendor: optional, component publisher/maintainer
* Component type: optional, categorizes component
* Description: optional, component details

ComponentUpdate
~~~~~~~~~~~~~~

Schema for updating existing components.

.. code-block:: json

   {
     "name": "string (optional, 1-255 chars)",
     "version": "string (optional, max 100 chars)",
     "vendor": "string (optional, max 255 chars)",
     "component_type": "string (optional, max 50 chars)",
     "description": "string (optional, max 1000 chars)"
   }

ComponentResponse
~~~~~~~~~~~~~~~~

Schema for component data in responses.

.. code-block:: json

   {
     "id": "UUID",
     "application_id": "UUID",
     "name": "string",
     "version": "string (nullable)",
     "vendor": "string (nullable)",
     "component_type": "string (nullable)",
     "description": "string (nullable)",
     "created_at": "datetime (ISO 8601)",
     "updated_at": "datetime (ISO 8601)",
     "cpe_mappings": "array of CPEMappingResponse (optional)"
   }

CPE Mapping Schemas
------------------

CPEMappingCreate
~~~~~~~~~~~~~~~

Schema for creating CPE mappings.

.. code-block:: json

   {
     "cpe_string": "string (required, valid CPE 2.3 format)",
     "confidence": "float (required, 0.0-1.0)",
     "mapping_source": "string (required, max 50 chars)"
   }

**Validation Rules**:
* CPE string: must be valid CPE 2.3 format (13 colon-separated components)
* Confidence: float between 0.0 and 1.0 inclusive
* Mapping source: describes origin of mapping

**Valid CPE Format**:

.. code-block:: text

   cpe:2.3:part:vendor:product:version:update:edition:language:sw_edition:target_sw:target_hw:other

**Example**:

.. code-block:: text

   cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*

CPEMappingUpdate
~~~~~~~~~~~~~~~

Schema for updating CPE mappings.

.. code-block:: json

   {
     "confidence": "float (optional, 0.0-1.0)",
     "mapping_source": "string (optional, max 50 chars)"
   }

CPEMappingResponse
~~~~~~~~~~~~~~~~~

Schema for CPE mapping data in responses.

.. code-block:: json

   {
     "id": "UUID",
     "component_id": "UUID",
     "cpe_string": "string",
     "confidence": "float",
     "mapping_source": "string",
     "created_at": "datetime (ISO 8601)",
     "updated_at": "datetime (ISO 8601)"
   }

Vulnerability Schemas
--------------------

CVEResponse
~~~~~~~~~~

Schema for CVE data in responses.

.. code-block:: json

   {
     "id": "UUID",
     "cve_id": "string (CVE-YYYY-NNNN format)",
     "description": "string",
     "published_date": "datetime (ISO 8601)",
     "last_modified_date": "datetime (ISO 8601)",
     "cvss_v3_score": "float (0.0-10.0, nullable)",
     "cvss_v3_vector": "string (CVSS v3.1 vector, nullable)",
     "cvss_v3_severity": "string (LOW|MEDIUM|HIGH|CRITICAL, nullable)",
     "cvss_v2_score": "float (0.0-10.0, nullable)",
     "cwe_ids": "array of strings (CWE-XXX format)",
     "references": "array of ReferenceResponse",
     "cpe_applicability": "array of CPEApplicabilityResponse (optional)"
   }

ReferenceResponse
~~~~~~~~~~~~~~~~

Schema for CVE reference data.

.. code-block:: json

   {
     "url": "string (valid URL)",
     "source": "string (vendor|nvd|patch|advisory|other)"
   }

CPEApplicabilityResponse
~~~~~~~~~~~~~~~~~~~~~~~

Schema for CVE CPE applicability data.

.. code-block:: json

   {
     "id": "UUID",
     "cve_id": "string",
     "cpe_string": "string (CPE 2.3 format)",
     "version_start_including": "string (nullable)",
     "version_end_including": "string (nullable)",
     "version_start_excluding": "string (nullable)",
     "version_end_excluding": "string (nullable)",
     "vulnerable": "boolean",
     "configuration_id": "string"
   }

VulnerabilityFeedResponse
~~~~~~~~~~~~~~~~~~~~~~~~

Schema for tailored vulnerability feed responses.

.. code-block:: json

   {
     "cves": "array of CVEResponse",
     "total": "integer (total matching CVEs)",
     "limit": "integer (requested limit)",
     "offset": "integer (requested offset)",
     "has_more": "boolean (more results available)",
     "application_id": "UUID"
   }

CVEListResponse
~~~~~~~~~~~~~~

Schema for general CVE list responses.

.. code-block:: json

   {
     "cves": "array of CVEResponse",
     "total": "integer (total matching CVEs)",
     "limit": "integer (requested limit)",
     "offset": "integer (requested offset)",
     "has_more": "boolean (more results available)"
   }

Common Schemas
-------------

ErrorResponse
~~~~~~~~~~~~

Standard error response schema.

.. code-block:: json

   {
     "detail": "string (error message)",
     "type": "string (error type, optional)",
     "field": "string (field name for validation errors, optional)"
   }

**Error Types**:
* ``validation_error``: Input validation failed
* ``authentication_error``: Authentication failed
* ``authorization_error``: Insufficient permissions
* ``not_found_error``: Resource not found
* ``conflict_error``: Resource conflict (duplicates)
* ``rate_limit_error``: Rate limit exceeded

ValidationErrorResponse
~~~~~~~~~~~~~~~~~~~~~~

Detailed validation error response.

.. code-block:: json

   {
     "detail": [
       {
         "loc": ["field_name"],
         "msg": "error message",
         "type": "error_type"
       }
     ]
   }

PaginationParams
~~~~~~~~~~~~~~~

Common pagination parameters.

.. code-block:: json

   {
     "skip": "integer (optional, default: 0, min: 0)",
     "limit": "integer (optional, default: 100, min: 1, max: 1000)"
   }

DateTimeRange
~~~~~~~~~~~~

Date range filtering parameters.

.. code-block:: json

   {
     "start_date": "datetime (ISO 8601, optional)",
     "end_date": "datetime (ISO 8601, optional)"
   }

Data Types and Formats
---------------------

UUID Format
~~~~~~~~~~

All UUIDs follow RFC 4122 format:

.. code-block:: text

   550e8400-e29b-41d4-a716-************

DateTime Format
~~~~~~~~~~~~~~

All datetimes use ISO 8601 format with UTC timezone:

.. code-block:: text

   2025-01-15T10:30:00Z
   2025-01-15T10:30:00.123Z  (with milliseconds)

CVE ID Format
~~~~~~~~~~~~

CVE identifiers follow standard format:

.. code-block:: text

   CVE-YYYY-NNNN
   CVE-YYYY-NNNNN (for years 2005+)

Examples:
* CVE-2023-1234
* CVE-2023-12345

CVSS Vector Format
~~~~~~~~~~~~~~~~~

CVSS v3.1 vectors follow standard format:

.. code-block:: text

   CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H

CWE ID Format
~~~~~~~~~~~~

Common Weakness Enumeration IDs:

.. code-block:: text

   CWE-XXX

Examples:
* CWE-79 (Cross-site Scripting)
* CWE-89 (SQL Injection)
* CWE-400 (Resource Exhaustion)

Validation Rules
---------------

String Validation
~~~~~~~~~~~~~~~~~

* All strings are trimmed of leading/trailing whitespace
* Empty strings are converted to null for optional fields
* Maximum lengths are enforced as specified
* Unicode characters are supported

Email Validation
~~~~~~~~~~~~~~~

* Must be valid email format
* Maximum length: 254 characters
* Case-insensitive uniqueness

Password Validation
~~~~~~~~~~~~~~~~~~

* Minimum 8 characters
* Must contain at least one uppercase letter (A-Z)
* Must contain at least one lowercase letter (a-z)
* Must contain at least one digit (0-9)
* Must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)

CPE Validation
~~~~~~~~~~~~~

* Must start with "cpe:2.3:"
* Must have exactly 13 colon-separated components
* Part field (3rd component) must be 'a', 'o', or 'h'
* Wildcards (*) are allowed in appropriate positions

Version Validation
~~~~~~~~~~~~~~~~~

* Supports semantic versioning (1.2.3)
* Supports date-based versioning (2025.01.15)
* Supports custom schemes
* Maximum length: 100 characters

Next Steps
----------

* :doc:`errors` - Detailed error handling reference
* :doc:`authentication` - Authentication endpoint details
* :doc:`applications` - Application management endpoints
* :doc:`components` - Component management endpoints
* :doc:`vulnerabilities` - Vulnerability query endpoints
