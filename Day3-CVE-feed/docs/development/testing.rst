Testing in Development
======================

This document covers testing practices, tools, and workflows specifically for developers working on the CVE Feed Service. It complements the comprehensive testing documentation with development-focused guidance.

Overview
--------

Testing is integral to the development process, with multiple testing strategies employed to ensure code quality, reliability, and maintainability.

**Testing Philosophy**:
* **Test-Driven Development (TDD)**: Write tests before implementation
* **Comprehensive Coverage**: Aim for >90% code coverage
* **Fast Feedback**: Unit tests run in milliseconds
* **Realistic Testing**: Integration tests use real databases
* **Continuous Testing**: Tests run on every commit

**Testing Pyramid**:

.. mermaid::

   graph TD
       A[Unit Tests - 70%] --> B[Integration Tests - 20%]
       B --> C[End-to-End Tests - 10%]
       
       style A fill:#90EE90
       style B fill:#FFE4B5
       style C fill:#FFB6C1

Development Testing Workflow
---------------------------

TDD Cycle in Practice
~~~~~~~~~~~~~~~~~~~~

**Red-Green-Refactor Workflow**:

.. code-block:: bash

   # 1. RED: Write failing test
   pytest tests/unit/services/test_application_service.py::test_create_application_with_duplicate_name -v
   # FAILED - function not implemented
   
   # 2. GREEN: Make test pass with minimal code
   # Implement just enough to pass the test
   
   # 3. REFACTOR: Improve code while keeping tests green
   pytest tests/unit/services/test_application_service.py -v
   # All tests pass

**Example TDD Session**:

.. code-block:: python

   # Step 1: Write failing test
   async def test_create_application_with_duplicate_name_raises_error():
       """Test that duplicate application names raise ValueError."""
       # Arrange
       app_service = ApplicationService(mock_db)
       app_data = ApplicationCreate(name="Duplicate App", environment="prod")
       
       # Create first application
       await app_service.create_application(app_data)
       
       # Act & Assert
       with pytest.raises(ValueError, match="already exists"):
           await app_service.create_application(app_data)
   
   # Step 2: Implement minimal code to pass
   async def create_application(self, data: ApplicationCreate) -> Application:
       # Check for duplicates
       existing = await self._get_by_name_and_env(data.name, data.environment)
       if existing:
           raise ValueError(f"Application '{data.name}' already exists")
       
       # Create application
       application = Application(**data.model_dump())
       self.db.add(application)
       await self.db.commit()
       return application

Pre-commit Testing
~~~~~~~~~~~~~~~~~

**Setup Pre-commit Hooks**:

.. code-block:: bash

   # Install pre-commit
   pip install pre-commit
   
   # Install hooks
   pre-commit install
   
   # Configure .pre-commit-config.yaml
   repos:
     - repo: local
       hooks:
         - id: pytest-unit
           name: Run unit tests
           entry: pytest
           language: system
           args: ["-m", "unit", "--tb=short"]
           pass_filenames: false

**Manual Pre-commit Testing**:

.. code-block:: bash

   # Run unit tests before commit
   pytest -m unit --tb=short
   
   # Run linting
   flake8 src/ tests/
   
   # Run type checking
   mypy src/
   
   # Format code
   black src/ tests/
   isort src/ tests/

Development Test Categories
--------------------------

Unit Tests for Development
~~~~~~~~~~~~~~~~~~~~~~~~~

**Focus Areas**:
* Business logic validation
* Error handling
* Edge cases
* Input validation

**Example Unit Test Structure**:

.. code-block:: python

   # tests/unit/services/test_application_service.py
   import pytest
   from unittest.mock import AsyncMock, MagicMock
   
   from src.cve_feed_service.services.application_service import ApplicationService
   from src.cve_feed_service.schemas.application import ApplicationCreate
   
   
   class TestApplicationService:
       """Unit tests for ApplicationService."""
       
       @pytest.fixture
       def mock_db(self):
           """Mock database session."""
           return AsyncMock()
       
       @pytest.fixture
       def application_service(self, mock_db):
           """ApplicationService with mocked dependencies."""
           return ApplicationService(mock_db)
       
       async def test_create_application_success(self, application_service, mock_db):
           """Test successful application creation."""
           # Arrange
           app_data = ApplicationCreate(name="Test App", environment="test")
           mock_db.execute.return_value.scalar_one_or_none.return_value = None
           
           # Act
           result = await application_service.create_application(app_data)
           
           # Assert
           assert result.name == "Test App"
           mock_db.add.assert_called_once()
           mock_db.commit.assert_called_once()

**Running Unit Tests**:

.. code-block:: bash

   # Run all unit tests
   pytest -m unit
   
   # Run specific test file
   pytest tests/unit/services/test_application_service.py
   
   # Run with coverage
   pytest -m unit --cov=src/cve_feed_service --cov-report=term-missing

Integration Tests for Development
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Focus Areas**:
* Database operations
* Service interactions
* API endpoint testing
* External service integration

**Example Integration Test**:

.. code-block:: python

   # tests/integration/test_application_crud.py
   import pytest
   from sqlalchemy.ext.asyncio import AsyncSession
   
   from src.cve_feed_service.services.application_service import ApplicationService
   from src.cve_feed_service.schemas.application import ApplicationCreate
   
   
   @pytest.mark.integration
   class TestApplicationCRUD:
       """Integration tests for application CRUD operations."""
       
       async def test_create_and_retrieve_application(self, db_session: AsyncSession):
           """Test creating and retrieving an application."""
           # Arrange
           service = ApplicationService(db_session)
           app_data = ApplicationCreate(
               name="Integration Test App",
               environment="test",
               criticality="medium"
           )
           
           # Act - Create
           created_app = await service.create_application(app_data)
           
           # Act - Retrieve
           retrieved_app = await service.get_application(created_app.id)
           
           # Assert
           assert retrieved_app is not None
           assert retrieved_app.name == app_data.name
           assert retrieved_app.environment == app_data.environment

**Running Integration Tests**:

.. code-block:: bash

   # Run integration tests
   pytest -m integration
   
   # Run with test database
   TEST_DATABASE_URL=postgresql+asyncpg://user:pass@localhost/test_db pytest -m integration

API Testing for Development
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Focus Areas**:
* Endpoint functionality
* Request/response validation
* Authentication/authorization
* Error handling

**Example API Test**:

.. code-block:: python

   # tests/integration/test_api_applications.py
   import pytest
   from httpx import AsyncClient
   
   
   @pytest.mark.integration
   class TestApplicationsAPI:
       """Integration tests for applications API."""
       
       async def test_create_application_endpoint(
           self, async_client: AsyncClient, auth_headers: dict
       ):
           """Test application creation endpoint."""
           # Arrange
           app_data = {
               "name": "API Test App",
               "environment": "test",
               "criticality": "low"
           }
           
           # Act
           response = await async_client.post(
               "/api/v1/applications",
               json=app_data,
               headers=auth_headers
           )
           
           # Assert
           assert response.status_code == 201
           data = response.json()
           assert data["name"] == app_data["name"]
           assert "id" in data

Test Data Management
-------------------

Test Fixtures
~~~~~~~~~~~~

**Shared Fixtures**:

.. code-block:: python

   # tests/conftest.py
   import pytest
   import asyncio
   from uuid import uuid4
   from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
   
   from src.cve_feed_service.core.database import Base
   
   
   @pytest.fixture(scope="session")
   def event_loop():
       """Create event loop for async tests."""
       loop = asyncio.new_event_loop()
       yield loop
       loop.close()
   
   
   @pytest.fixture(scope="session")
   async def test_engine():
       """Create test database engine."""
       engine = create_async_engine("sqlite+aiosqlite:///:memory:")
       
       async with engine.begin() as conn:
           await conn.run_sync(Base.metadata.create_all)
       
       yield engine
       await engine.dispose()
   
   
   @pytest.fixture
   async def db_session(test_engine):
       """Create database session for each test."""
       async with AsyncSession(test_engine) as session:
           yield session
           await session.rollback()

**Factory Fixtures**:

.. code-block:: python

   # tests/factories.py
   import factory
   from factory import Faker
   from uuid import uuid4
   
   from src.cve_feed_service.models.application import Application
   
   
   class ApplicationFactory(factory.Factory):
       """Factory for creating test applications."""
       
       class Meta:
           model = Application
       
       id = factory.LazyFunction(uuid4)
       name = Faker('company')
       environment = Faker('random_element', elements=['dev', 'staging', 'prod'])
       criticality = Faker('random_element', elements=['low', 'medium', 'high'])

Mock Strategies
~~~~~~~~~~~~~~

**Service Mocking**:

.. code-block:: python

   # tests/mocks.py
   from unittest.mock import AsyncMock
   
   
   class MockNVDClient:
       """Mock NVD API client for testing."""
       
       def __init__(self):
           self.get_cves_by_date_range = AsyncMock()
           self.get_single_cve = AsyncMock()
       
       async def __aenter__(self):
           return self
       
       async def __aexit__(self, exc_type, exc_val, exc_tb):
           pass

**Database Mocking**:

.. code-block:: python

   @pytest.fixture
   def mock_db_session():
       """Mock database session."""
       mock_session = AsyncMock()
       
       # Configure common mock behaviors
       mock_result = AsyncMock()
       mock_result.scalar_one_or_none = AsyncMock()
       mock_session.execute.return_value = mock_result
       
       return mock_session

Test-Driven Development Examples
-------------------------------

Feature Development with TDD
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Scenario**: Adding CPE validation to components

**Step 1: Write the test**:

.. code-block:: python

   # tests/unit/utils/test_cpe_utils.py
   import pytest
   from src.cve_feed_service.utils.cpe_utils import validate_cpe_format
   
   
   def test_validate_cpe_format_valid_cpe_returns_true():
       """Test that valid CPE format returns True."""
       # Arrange
       valid_cpe = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"
       
       # Act
       result = validate_cpe_format(valid_cpe)
       
       # Assert
       assert result is True
   
   
   def test_validate_cpe_format_invalid_cpe_returns_false():
       """Test that invalid CPE format returns False."""
       # Arrange
       invalid_cpe = "invalid_cpe_string"
       
       # Act
       result = validate_cpe_format(invalid_cpe)
       
       # Assert
       assert result is False

**Step 2: Run the test (should fail)**:

.. code-block:: bash

   pytest tests/unit/utils/test_cpe_utils.py::test_validate_cpe_format_valid_cpe_returns_true -v
   # ImportError: cannot import name 'validate_cpe_format'

**Step 3: Implement minimal code**:

.. code-block:: python

   # src/cve_feed_service/utils/cpe_utils.py
   def validate_cpe_format(cpe_string: str) -> bool:
       """Validate CPE 2.3 format."""
       if not cpe_string.startswith("cpe:2.3:"):
           return False
       
       parts = cpe_string.split(":")
       return len(parts) == 13

**Step 4: Run tests (should pass)**:

.. code-block:: bash

   pytest tests/unit/utils/test_cpe_utils.py -v
   # All tests pass

**Step 5: Refactor and add more tests**:

.. code-block:: python

   # Add more comprehensive tests
   @pytest.mark.parametrize("cpe_string,expected", [
       ("cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*", True),
       ("cpe:2.3:o:microsoft:windows:10:*:*:*:*:*:*:*", True),
       ("cpe:2.2:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*", False),  # Wrong version
       ("invalid_string", False),
       ("cpe:2.3:a:nginx:nginx", False),  # Too few parts
   ])
   def test_validate_cpe_format_various_inputs(cpe_string, expected):
       """Test CPE validation with various inputs."""
       assert validate_cpe_format(cpe_string) == expected

Bug Fix with TDD
~~~~~~~~~~~~~~~~

**Scenario**: Fix bug where application deletion doesn't cascade to components

**Step 1: Write test that reproduces the bug**:

.. code-block:: python

   async def test_delete_application_cascades_to_components():
       """Test that deleting application also deletes components."""
       # Arrange
       app_service = ApplicationService(db_session)
       comp_service = ComponentService(db_session)
       
       # Create application and component
       app = await app_service.create_application(app_data)
       component = await comp_service.create_component(app.id, comp_data)
       
       # Act
       await app_service.delete_application(app.id)
       
       # Assert
       deleted_app = await app_service.get_application(app.id)
       deleted_comp = await comp_service.get_component(component.id)
       
       assert deleted_app is None
       assert deleted_comp is None  # This should pass but currently fails

**Step 2: Run test to confirm bug**:

.. code-block:: bash

   pytest tests/integration/test_application_deletion.py::test_delete_application_cascades_to_components -v
   # FAILED - Component still exists after application deletion

**Step 3: Fix the bug**:

.. code-block:: python

   # src/cve_feed_service/services/application_service.py
   async def delete_application(self, app_id: UUID) -> bool:
       """Delete application and cascade to components."""
       app = await self.get_application(app_id, include_components=True)
       if not app:
           return False
       
       # Delete components first
       for component in app.components:
           component.soft_delete()
       
       # Delete application
       app.soft_delete()
       await self.db.commit()
       return True

**Step 4: Verify fix**:

.. code-block:: bash

   pytest tests/integration/test_application_deletion.py::test_delete_application_cascades_to_components -v
   # PASSED

Performance Testing in Development
---------------------------------

Benchmark Tests
~~~~~~~~~~~~~~

.. code-block:: python

   # tests/performance/test_benchmarks.py
   import pytest
   import time
   from src.cve_feed_service.services.cve_service import CVEService
   
   
   @pytest.mark.performance
   class TestCVEServicePerformance:
       """Performance tests for CVE service."""
       
       async def test_tailored_feed_performance(self, db_session, sample_application):
           """Test that tailored feed generation is fast enough."""
           # Arrange
           service = CVEService(db_session)
           
           # Act
           start_time = time.time()
           cves, total = await service.get_tailored_cve_feed(sample_application.id)
           duration = time.time() - start_time
           
           # Assert
           assert duration < 2.0  # Should complete within 2 seconds
           assert isinstance(cves, list)

Memory Usage Tests
~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/performance/test_memory.py
   import pytest
   import psutil
   import os
   
   
   @pytest.mark.performance
   def test_bulk_import_memory_usage():
       """Test that bulk import doesn't consume excessive memory."""
       # Arrange
       process = psutil.Process(os.getpid())
       initial_memory = process.memory_info().rss
       
       # Act
       # Perform bulk import operation
       
       # Assert
       final_memory = process.memory_info().rss
       memory_increase = final_memory - initial_memory
       
       # Should not increase memory by more than 100MB
       assert memory_increase < 100 * 1024 * 1024

Debugging Tests
--------------

Test Debugging Techniques
~~~~~~~~~~~~~~~~~~~~~~~~~

**Using pytest debugger**:

.. code-block:: bash

   # Drop into debugger on failure
   pytest --pdb tests/unit/services/test_application_service.py
   
   # Drop into debugger on first failure
   pytest --pdb -x tests/
   
   # Use pdbpp for better debugging experience
   pip install pdbpp

**Verbose test output**:

.. code-block:: bash

   # Show detailed test output
   pytest -v -s tests/
   
   # Show local variables on failure
   pytest --tb=long tests/
   
   # Show only failed tests
   pytest --tb=short --no-header -q tests/

**Test isolation debugging**:

.. code-block:: python

   # Add debugging prints
   async def test_application_creation():
       print(f"Database session: {db_session}")
       print(f"Application data: {app_data}")
       
       result = await service.create_application(app_data)
       
       print(f"Created application: {result}")
       assert result.name == app_data.name

Continuous Integration Testing
-----------------------------

GitHub Actions Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: yaml

   # .github/workflows/test.yml
   name: Tests
   
   on: [push, pull_request]
   
   jobs:
     test:
       runs-on: ubuntu-latest
       
       services:
         postgres:
           image: postgres:15
           env:
             POSTGRES_PASSWORD: postgres
           options: >-
             --health-cmd pg_isready
             --health-interval 10s
             --health-timeout 5s
             --health-retries 5
       
       steps:
       - uses: actions/checkout@v4
       
       - name: Set up Python
         uses: actions/setup-python@v4
         with:
           python-version: '3.11'
       
       - name: Install dependencies
         run: |
           pip install -e ".[dev]"
       
       - name: Run unit tests
         run: |
           pytest -m unit --cov=src --cov-report=xml
       
       - name: Run integration tests
         run: |
           pytest -m integration --cov=src --cov-append --cov-report=xml
         env:
           DATABASE_URL: postgresql+asyncpg://postgres:postgres@localhost/postgres

Local CI Simulation
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Simulate CI environment locally
   docker run --rm -it \
     -v $(pwd):/app \
     -w /app \
     python:3.11 \
     bash -c "pip install -e '.[dev]' && pytest"

Test Coverage Analysis
---------------------

Coverage Reporting
~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Generate coverage report
   pytest --cov=src/cve_feed_service --cov-report=html --cov-report=term-missing
   
   # Open HTML coverage report
   open htmlcov/index.html
   
   # Generate XML for CI
   pytest --cov=src/cve_feed_service --cov-report=xml

Coverage Targets
~~~~~~~~~~~~~~~

.. code-block:: bash

   # Fail if coverage below threshold
   pytest --cov=src/cve_feed_service --cov-fail-under=90
   
   # Show missing lines
   pytest --cov=src/cve_feed_service --cov-report=term-missing

Next Steps
----------

* :doc:`contributing` - Contributing guidelines and code review process
* :doc:`../testing/index` - Comprehensive testing framework documentation
* :doc:`../testing/tdd-practices` - Detailed TDD methodology
* :doc:`../testing/unit-testing` - Unit testing patterns and examples
