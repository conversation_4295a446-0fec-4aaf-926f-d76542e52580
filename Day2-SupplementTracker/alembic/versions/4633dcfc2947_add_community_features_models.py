"""Add community features models

Revision ID: 4633dcfc2947
Revises: 
Create Date: 2025-06-17 22:06:44.313384

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4633dcfc2947'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade database schema."""
    # Create users table
    op.create_table(
        'users',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('email', sa.String(length=255), nullable=False),
        sa.Column('username', sa.String(length=50), nullable=False),
        sa.Column('hashed_password', sa.String(length=255), nullable=False),
        sa.Column('full_name', sa.String(length=255), nullable=True),
        sa.Column('bio', sa.Text(), nullable=True),
        sa.Column('is_active', sa.<PERSON>(), nullable=False, server_default=sa.text('true')),
        sa.Column('is_superuser', sa.Boolean(), nullable=False, server_default=sa.text('false')),
        sa.Column('is_verified', sa.Boolean(), nullable=False, server_default=sa.text('false')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('last_login_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_users')),
        sa.UniqueConstraint('email', name=op.f('uq_users_email')),
        sa.UniqueConstraint('username', name=op.f('uq_users_username'))
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=False)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=False)

    # Create user_sessions table
    op.create_table(
        'user_sessions',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('session_token', sa.String(length=255), nullable=False),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default=sa.text('true')),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_user_sessions')),
        sa.UniqueConstraint('session_token', name=op.f('uq_user_sessions_session_token'))
    )
    op.create_index(op.f('ix_user_sessions_id'), 'user_sessions', ['id'], unique=False)
    op.create_index(op.f('ix_user_sessions_user_id'), 'user_sessions', ['user_id'], unique=False)

    # Create supplements table
    op.create_table(
        'supplements',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('brand', sa.String(length=255), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('category', sa.String(length=100), nullable=False),
        sa.Column('form', sa.String(length=50), nullable=False),
        sa.Column('serving_size', sa.Numeric(precision=10, scale=3), nullable=True),
        sa.Column('serving_unit', sa.String(length=20), nullable=True),
        sa.Column('ingredients', sa.Text(), nullable=True),
        sa.Column('barcode', sa.String(length=50), nullable=True),
        sa.Column('is_verified', sa.Boolean(), nullable=False, server_default=sa.text('false')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_by_user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=True),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_supplements')),
        sa.UniqueConstraint('barcode', name=op.f('uq_supplements_barcode'))
    )
    op.create_index(op.f('ix_supplements_barcode'), 'supplements', ['barcode'], unique=False)
    op.create_index(op.f('ix_supplements_category'), 'supplements', ['category'], unique=False)
    op.create_index(op.f('ix_supplements_created_by_user_id'), 'supplements', ['created_by_user_id'], unique=False)
    op.create_index(op.f('ix_supplements_id'), 'supplements', ['id'], unique=False)
    op.create_index(op.f('ix_supplements_name'), 'supplements', ['name'], unique=False)

    # Create supplement_intakes table
    op.create_table(
        'supplement_intakes',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('supplement_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('dosage', sa.Numeric(precision=10, scale=3), nullable=False),
        sa.Column('dosage_unit', sa.String(length=20), nullable=False),
        sa.Column('taken_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('mood_before', sa.Integer(), nullable=True),
        sa.Column('mood_after', sa.Integer(), nullable=True),
        sa.Column('energy_before', sa.Integer(), nullable=True),
        sa.Column('energy_after', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['supplement_id'], ['supplements.id'], name=op.f('fk_supplement_intakes_supplement_id_supplements')),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_supplement_intakes'))
    )
    op.create_index(op.f('ix_supplement_intakes_id'), 'supplement_intakes', ['id'], unique=False)
    op.create_index(op.f('ix_supplement_intakes_supplement_id'), 'supplement_intakes', ['supplement_id'], unique=False)
    op.create_index(op.f('ix_supplement_intakes_taken_at'), 'supplement_intakes', ['taken_at'], unique=False)
    op.create_index(op.f('ix_supplement_intakes_user_id'), 'supplement_intakes', ['user_id'], unique=False)

    # Create community_groups table
    op.create_table(
        'community_groups',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('category', sa.String(length=50), nullable=False),
        sa.Column('is_public', sa.Boolean(), nullable=False, server_default=sa.text('true')),
        sa.Column('is_moderated', sa.Boolean(), nullable=False, server_default=sa.text('false')),
        sa.Column('member_count', sa.Integer(), nullable=False, server_default=sa.text('0')),
        sa.Column('created_by_user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_community_groups'))
    )
    op.create_index(op.f('ix_community_groups_category'), 'community_groups', ['category'], unique=False)
    op.create_index(op.f('ix_community_groups_created_by_user_id'), 'community_groups', ['created_by_user_id'], unique=False)
    op.create_index(op.f('ix_community_groups_id'), 'community_groups', ['id'], unique=False)
    op.create_index(op.f('ix_community_groups_name'), 'community_groups', ['name'], unique=False)

    # Create community_posts table
    op.create_table(
        'community_posts',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('title', sa.String(length=500), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('post_type', sa.String(length=50), nullable=False, server_default=sa.text("'discussion'")),
        sa.Column('group_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('author_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('is_pinned', sa.Boolean(), nullable=False, server_default=sa.text('false')),
        sa.Column('is_locked', sa.Boolean(), nullable=False, server_default=sa.text('false')),
        sa.Column('view_count', sa.Integer(), nullable=False, server_default=sa.text('0')),
        sa.Column('like_count', sa.Integer(), nullable=False, server_default=sa.text('0')),
        sa.Column('comment_count', sa.Integer(), nullable=False, server_default=sa.text('0')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['group_id'], ['community_groups.id'], name=op.f('fk_community_posts_group_id_community_groups')),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_community_posts'))
    )
    op.create_index(op.f('ix_community_posts_author_id'), 'community_posts', ['author_id'], unique=False)
    op.create_index(op.f('ix_community_posts_created_at'), 'community_posts', ['created_at'], unique=False)
    op.create_index(op.f('ix_community_posts_group_id'), 'community_posts', ['group_id'], unique=False)
    op.create_index(op.f('ix_community_posts_id'), 'community_posts', ['id'], unique=False)
    op.create_index(op.f('ix_community_posts_post_type'), 'community_posts', ['post_type'], unique=False)
    op.create_index(op.f('ix_community_posts_title'), 'community_posts', ['title'], unique=False)

    # Create user_follows table
    op.create_table(
        'user_follows',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('follower_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('followed_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_user_follows')),
        sa.UniqueConstraint('follower_id', 'followed_id', name='uq_user_follow')
    )
    op.create_index(op.f('ix_user_follows_followed_id'), 'user_follows', ['followed_id'], unique=False)
    op.create_index(op.f('ix_user_follows_follower_id'), 'user_follows', ['follower_id'], unique=False)
    op.create_index(op.f('ix_user_follows_id'), 'user_follows', ['id'], unique=False)

    # Create post_comments table
    op.create_table(
        'post_comments',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('post_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('author_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('parent_comment_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('like_count', sa.Integer(), nullable=False, server_default=sa.text('0')),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default=sa.text('false')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['parent_comment_id'], ['post_comments.id'], name=op.f('fk_post_comments_parent_comment_id_post_comments')),
        sa.ForeignKeyConstraint(['post_id'], ['community_posts.id'], name=op.f('fk_post_comments_post_id_community_posts')),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_post_comments'))
    )
    op.create_index(op.f('ix_post_comments_author_id'), 'post_comments', ['author_id'], unique=False)
    op.create_index(op.f('ix_post_comments_created_at'), 'post_comments', ['created_at'], unique=False)
    op.create_index(op.f('ix_post_comments_id'), 'post_comments', ['id'], unique=False)
    op.create_index(op.f('ix_post_comments_parent_comment_id'), 'post_comments', ['parent_comment_id'], unique=False)
    op.create_index(op.f('ix_post_comments_post_id'), 'post_comments', ['post_id'], unique=False)

    # Create peer_reviews table
    op.create_table(
        'peer_reviews',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('content_type', sa.String(length=50), nullable=False),
        sa.Column('content_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('reviewer_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=False, server_default=sa.text("'pending'")),
        sa.Column('score', sa.Integer(), nullable=True),
        sa.Column('feedback', sa.Text(), nullable=True),
        sa.Column('expertise_level', sa.String(length=20), nullable=False, server_default=sa.text("'general'")),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_peer_reviews')),
        sa.UniqueConstraint('content_type', 'content_id', 'reviewer_id', name='uq_peer_review')
    )
    op.create_index(op.f('ix_peer_reviews_content_id'), 'peer_reviews', ['content_id'], unique=False)
    op.create_index(op.f('ix_peer_reviews_content_type'), 'peer_reviews', ['content_type'], unique=False)
    op.create_index(op.f('ix_peer_reviews_id'), 'peer_reviews', ['id'], unique=False)
    op.create_index(op.f('ix_peer_reviews_reviewer_id'), 'peer_reviews', ['reviewer_id'], unique=False)
    op.create_index(op.f('ix_peer_reviews_status'), 'peer_reviews', ['status'], unique=False)

    # Create notifications table
    op.create_table(
        'notifications',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('type', sa.String(length=50), nullable=False),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('message', sa.Text(), nullable=False),
        sa.Column('related_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('related_type', sa.String(length=50), nullable=True),
        sa.Column('is_read', sa.Boolean(), nullable=False, server_default=sa.text('false')),
        sa.Column('is_sent', sa.Boolean(), nullable=False, server_default=sa.text('false')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_notifications'))
    )
    op.create_index(op.f('ix_notifications_created_at'), 'notifications', ['created_at'], unique=False)
    op.create_index(op.f('ix_notifications_id'), 'notifications', ['id'], unique=False)
    op.create_index(op.f('ix_notifications_is_read'), 'notifications', ['is_read'], unique=False)
    op.create_index(op.f('ix_notifications_related_id'), 'notifications', ['related_id'], unique=False)
    op.create_index(op.f('ix_notifications_type'), 'notifications', ['type'], unique=False)
    op.create_index(op.f('ix_notifications_user_id'), 'notifications', ['user_id'], unique=False)


def downgrade() -> None:
    """Downgrade database schema."""
    # Drop tables in reverse order to handle foreign key constraints
    op.drop_table('notifications')
    op.drop_table('peer_reviews')
    op.drop_table('post_comments')
    op.drop_table('user_follows')
    op.drop_table('community_posts')
    op.drop_table('community_groups')
    op.drop_table('supplement_intakes')
    op.drop_table('supplements')
    op.drop_table('user_sessions')
    op.drop_table('users')
