"""Add compliance tables for legal and regulatory compliance

Revision ID: 004
Revises: 003
Create Date: 2024-12-17 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '004'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade():
    """Add compliance tables for consent management and user rights."""
    
    # Create consent_records table
    op.create_table(
        'consent_records',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('consent_type', sa.String(length=50), nullable=False),
        sa.Column('consent_status', sa.String(length=20), nullable=False),
        sa.Column('consent_version', sa.String(length=20), nullable=False),
        sa.Column('consent_text', sa.Text(), nullable=True),
        sa.Column('consent_metadata', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('given_at', sa.DateTime(), nullable=True),
        sa.Column('withdrawn_at', sa.DateTime(), nullable=True),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.String(length=500), nullable=True),
        sa.Column('consent_method', sa.String(length=50), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for consent_records
    op.create_index('ix_consent_records_id', 'consent_records', ['id'])
    op.create_index('ix_consent_records_user_id', 'consent_records', ['user_id'])
    op.create_index('ix_consent_records_consent_type', 'consent_records', ['consent_type'])
    op.create_index('ix_consent_records_consent_status', 'consent_records', ['consent_status'])
    op.create_index('ix_consent_records_created_at', 'consent_records', ['created_at'])
    
    # Create user_rights_requests table
    op.create_table(
        'user_rights_requests',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('request_type', sa.String(length=50), nullable=False),
        sa.Column('request_status', sa.String(length=50), nullable=False),
        sa.Column('request_description', sa.Text(), nullable=True),
        sa.Column('request_data', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('processed_by', sa.Integer(), nullable=True),
        sa.Column('processing_notes', sa.Text(), nullable=True),
        sa.Column('completion_data', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('requested_at', sa.DateTime(), nullable=True),
        sa.Column('due_date', sa.DateTime(), nullable=False),
        sa.Column('processed_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.String(length=500), nullable=True),
        sa.ForeignKeyConstraint(['processed_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for user_rights_requests
    op.create_index('ix_user_rights_requests_id', 'user_rights_requests', ['id'])
    op.create_index('ix_user_rights_requests_user_id', 'user_rights_requests', ['user_id'])
    op.create_index('ix_user_rights_requests_request_type', 'user_rights_requests', ['request_type'])
    op.create_index('ix_user_rights_requests_request_status', 'user_rights_requests', ['request_status'])
    op.create_index('ix_user_rights_requests_requested_at', 'user_rights_requests', ['requested_at'])
    op.create_index('ix_user_rights_requests_due_date', 'user_rights_requests', ['due_date'])
    
    # Create data_processing_logs table for audit trail
    op.create_table(
        'data_processing_logs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('processing_type', sa.String(length=50), nullable=False),
        sa.Column('data_category', sa.String(length=50), nullable=False),
        sa.Column('processing_purpose', sa.String(length=100), nullable=False),
        sa.Column('legal_basis', sa.String(length=50), nullable=False),
        sa.Column('data_source', sa.String(length=100), nullable=True),
        sa.Column('data_destination', sa.String(length=100), nullable=True),
        sa.Column('processing_details', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('processed_at', sa.DateTime(), nullable=True),
        sa.Column('processed_by', sa.Integer(), nullable=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.String(length=500), nullable=True),
        sa.ForeignKeyConstraint(['processed_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for data_processing_logs
    op.create_index('ix_data_processing_logs_id', 'data_processing_logs', ['id'])
    op.create_index('ix_data_processing_logs_user_id', 'data_processing_logs', ['user_id'])
    op.create_index('ix_data_processing_logs_processing_type', 'data_processing_logs', ['processing_type'])
    op.create_index('ix_data_processing_logs_processed_at', 'data_processing_logs', ['processed_at'])
    
    # Create compliance_settings table for platform-wide compliance configuration
    op.create_table(
        'compliance_settings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('setting_key', sa.String(length=100), nullable=False),
        sa.Column('setting_value', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('setting_description', sa.Text(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('updated_by', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('setting_key')
    )
    
    # Create indexes for compliance_settings
    op.create_index('ix_compliance_settings_id', 'compliance_settings', ['id'])
    op.create_index('ix_compliance_settings_setting_key', 'compliance_settings', ['setting_key'])
    op.create_index('ix_compliance_settings_is_active', 'compliance_settings', ['is_active'])
    
    # Insert default compliance settings
    op.execute("""
        INSERT INTO compliance_settings (setting_key, setting_value, setting_description, is_active, created_at)
        VALUES 
        ('privacy_policy_version', '"1.0"', 'Current privacy policy version', true, NOW()),
        ('terms_of_service_version', '"1.0"', 'Current terms of service version', true, NOW()),
        ('consent_retention_period', '2555', 'Consent record retention period in days (7 years)', true, NOW()),
        ('data_retention_period', '2555', 'Default data retention period in days (7 years)', true, NOW()),
        ('gdpr_enabled', 'true', 'Enable GDPR compliance features', true, NOW()),
        ('ccpa_enabled', 'true', 'Enable CCPA compliance features', true, NOW()),
        ('hipaa_enabled', 'true', 'Enable HIPAA compliance features', true, NOW()),
        ('auto_process_access_requests', 'true', 'Automatically process data access requests', true, NOW()),
        ('require_explicit_consent', 'true', 'Require explicit consent for data processing', true, NOW()),
        ('enable_consent_withdrawal', 'true', 'Allow users to withdraw consent', true, NOW())
    """)


def downgrade():
    """Remove compliance tables."""
    
    # Drop tables in reverse order
    op.drop_table('compliance_settings')
    op.drop_table('data_processing_logs')
    op.drop_table('user_rights_requests')
    op.drop_table('consent_records')
