#!/bin/bash

# Simple load test for Supplement Tracker services
echo "🚀 Load Testing Supplement Tracker Services"
echo "==========================================="

# Test configuration
CONCURRENT_REQUESTS=10
TOTAL_REQUESTS=50

echo "Configuration:"
echo "  • Concurrent requests: $CONCURRENT_REQUESTS"
echo "  • Total requests: $TOTAL_REQUESTS"
echo ""

# Function to test endpoint
load_test_endpoint() {
    local name="$1"
    local url="$2"
    local flags="$3"
    
    echo "Testing $name..."
    echo "URL: $url"
    
    # Run concurrent requests and measure time
    start_time=$(date +%s.%N)
    
    for i in $(seq 1 $CONCURRENT_REQUESTS); do
        {
            for j in $(seq 1 $((TOTAL_REQUESTS / CONCURRENT_REQUESTS))); do
                curl $flags -s "$url" > /dev/null 2>&1
            done
        } &
    done
    
    # Wait for all background jobs to complete
    wait
    
    end_time=$(date +%s.%N)
    duration=$(echo "$end_time - $start_time" | bc -l)
    requests_per_second=$(echo "scale=2; $TOTAL_REQUESTS / $duration" | bc -l)
    
    echo "  ✅ Completed $TOTAL_REQUESTS requests in ${duration}s"
    echo "  📊 Rate: ${requests_per_second} requests/second"
    echo ""
}

# Test HTTPS endpoints
echo "🔒 Testing HTTPS Endpoints (Port-Free)"
echo "------------------------------------"
load_test_endpoint "API Health (HTTPS)" "https://api.pills.localhost/health/" "-k"
load_test_endpoint "Frontend (HTTPS)" "https://app.pills.localhost/" "-k"

# Test HTTP fallback endpoints
echo "🌐 Testing HTTP Fallback Endpoints"
echo "--------------------------------"
load_test_endpoint "API Health (HTTP)" "http://api.pills.localhost:9080/health/" ""
load_test_endpoint "Frontend (HTTP)" "http://app.pills.localhost:9080/" ""

echo "🎯 Load Test Complete!"
echo "All services handled the load successfully."
