/**
 * Protocol Builder Component
 * 
 * Step-by-step research protocol creation interface.
 */

import React, { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { useAppDispatch } from '@/store';
import { createResearchProtocol } from '@/store/slices/researchSlice';
import { showSuccessNotification, showErrorNotification } from '@/store/slices/uiSlice';
import styled from 'styled-components';

// Components
import Card from '@/components/common/Card';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import LoadingSpinner from '@/components/common/LoadingSpinner';

// Icons
const PlusIcon = () => <span>+</span>;
const MinusIcon = () => <span>−</span>;
const NextIcon = () => <span>→</span>;
const PrevIcon = () => <span>←</span>;
const SaveIcon = () => <span>💾</span>;

// Styled components
const BuilderContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const StepIndicator = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  
  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    margin-bottom: 1rem;
  }
`;

const Step = styled.div<{ $active: boolean; $completed: boolean }>`
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: ${props => props.theme.borderRadius.full};
  font-size: 0.9rem;
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  margin: 0 0.5rem;
  
  ${props => {
    if (props.$active) {
      return `
        background: ${props.theme.colors.primary};
        color: white;
      `;
    } else if (props.$completed) {
      return `
        background: ${props.theme.colors.success};
        color: white;
      `;
    } else {
      return `
        background: ${props.theme.colors.backgroundSecondary};
        color: ${props.theme.colors.textSecondary};
      `;
    }
  }}
  
  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    margin: 0 0.25rem;
  }
`;

const StepContent = styled(Card)`
  padding: 2rem;
  margin-bottom: 2rem;
  
  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    padding: 1rem;
  }
`;

const StepTitle = styled.h2`
  color: ${props => props.theme.colors.text};
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
`;

const StepDescription = styled.p`
  color: ${props => props.theme.colors.textSecondary};
  margin: 0 0 2rem 0;
  line-height: 1.5;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
`;

const TextArea = styled.textarea`
  padding: 0.75rem 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.9375rem;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
  
  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
  }
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
`;

const ErrorMessage = styled.div`
  color: ${props => props.theme.colors.error};
  font-size: 0.8rem;
  margin-top: 0.25rem;
`;

const ArrayField = styled.div`
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  padding: 1rem;
  background: ${props => props.theme.colors.backgroundSecondary};
`;

const ArrayItem = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  align-items: flex-start;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const ArrayInput = styled(Input)`
  flex: 1;
`;

const RemoveButton = styled(Button)`
  flex-shrink: 0;
  margin-top: 0;
`;

const AddButton = styled(Button)`
  margin-top: 0.5rem;
`;

const Navigation = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  
  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    flex-direction: column;
  }
`;

const NavGroup = styled.div`
  display: flex;
  gap: 1rem;
`;

// Form data interface
interface ProtocolFormData {
  title: string;
  description: string;
  objectives: { value: string }[];
  inclusion_criteria: { value: string }[];
  exclusion_criteria: { value: string }[];
  duration_weeks: number;
  max_participants?: number;
  supplements: { value: string }[];
  data_collection_schedule: string;
  primary_endpoints: { value: string }[];
  secondary_endpoints: { value: string }[];
  ethical_considerations: string;
  consent_form_url?: string;
}

// Steps configuration
const STEPS = [
  {
    id: 'basic',
    title: 'Basic Information',
    description: 'Define the core details of your research protocol',
  },
  {
    id: 'objectives',
    title: 'Objectives & Endpoints',
    description: 'Specify research objectives and measurement endpoints',
  },
  {
    id: 'participants',
    title: 'Participant Criteria',
    description: 'Define inclusion and exclusion criteria for participants',
  },
  {
    id: 'methodology',
    title: 'Methodology',
    description: 'Outline study methodology and data collection',
  },
  {
    id: 'ethics',
    title: 'Ethics & Consent',
    description: 'Address ethical considerations and consent procedures',
  },
  {
    id: 'review',
    title: 'Review & Submit',
    description: 'Review your protocol and submit for approval',
  },
];

interface ProtocolBuilderProps {
  onComplete?: (protocolId: string) => void;
  onCancel?: () => void;
}

const ProtocolBuilder: React.FC<ProtocolBuilderProps> = ({
  onComplete,
  onCancel,
}) => {
  const dispatch = useAppDispatch();
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
    watch,
    trigger,
    getValues,
  } = useForm<ProtocolFormData>({
    defaultValues: {
      objectives: [{ value: '' }],
      inclusion_criteria: [{ value: '' }],
      exclusion_criteria: [{ value: '' }],
      supplements: [{ value: '' }],
      primary_endpoints: [{ value: '' }],
      secondary_endpoints: [{ value: '' }],
    },
  });

  const {
    fields: objectiveFields,
    append: appendObjective,
    remove: removeObjective,
  } = useFieldArray({
    control,
    name: 'objectives',
  });

  const {
    fields: inclusionFields,
    append: appendInclusion,
    remove: removeInclusion,
  } = useFieldArray({
    control,
    name: 'inclusion_criteria',
  });

  const {
    fields: exclusionFields,
    append: appendExclusion,
    remove: removeExclusion,
  } = useFieldArray({
    control,
    name: 'exclusion_criteria',
  });

  const {
    fields: supplementFields,
    append: appendSupplement,
    remove: removeSupplement,
  } = useFieldArray({
    control,
    name: 'supplements',
  });

  const {
    fields: primaryEndpointFields,
    append: appendPrimaryEndpoint,
    remove: removePrimaryEndpoint,
  } = useFieldArray({
    control,
    name: 'primary_endpoints',
  });

  const {
    fields: secondaryEndpointFields,
    append: appendSecondaryEndpoint,
    remove: removeSecondaryEndpoint,
  } = useFieldArray({
    control,
    name: 'secondary_endpoints',
  });

  const handleNext = async () => {
    const isValid = await trigger();
    if (isValid && currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const onSubmit = async (data: ProtocolFormData) => {
    setIsSubmitting(true);
    
    try {
      // Transform form data to API format
      const protocolData = {
        title: data.title,
        description: data.description,
        objectives: data.objectives.map(obj => obj.value).filter(Boolean),
        inclusion_criteria: data.inclusion_criteria.map(crit => crit.value).filter(Boolean),
        exclusion_criteria: data.exclusion_criteria.map(crit => crit.value).filter(Boolean),
        duration_weeks: data.duration_weeks,
        max_participants: data.max_participants,
        supplements: data.supplements.map(supp => supp.value).filter(Boolean),
        data_collection_schedule: data.data_collection_schedule,
        primary_endpoints: data.primary_endpoints.map(ep => ep.value).filter(Boolean),
        secondary_endpoints: data.secondary_endpoints.map(ep => ep.value).filter(Boolean),
        ethical_considerations: data.ethical_considerations,
        consent_form_url: data.consent_form_url,
        status: 'draft',
      };

      const result = await dispatch(createResearchProtocol(protocolData)).unwrap();
      
      dispatch(showSuccessNotification(
        'Research protocol created successfully',
        'Protocol Created'
      ));
      
      if (onComplete) {
        onComplete(result.id);
      }
    } catch (error: any) {
      dispatch(showErrorNotification(
        error.message || 'Failed to create research protocol',
        'Creation Error'
      ));
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    const step = STEPS[currentStep];
    
    switch (step.id) {
      case 'basic':
        return (
          <Form>
            <FormGroup>
              <Label htmlFor="title">Protocol Title *</Label>
              <Input
                id="title"
                {...register('title', { required: 'Title is required' })}
                placeholder="Enter a descriptive title for your research protocol"
                error={!!errors.title}
              />
              {errors.title && <ErrorMessage>{errors.title.message}</ErrorMessage>}
            </FormGroup>

            <FormGroup>
              <Label htmlFor="description">Description *</Label>
              <TextArea
                id="description"
                {...register('description', { required: 'Description is required' })}
                placeholder="Provide a comprehensive description of your research protocol, including background, rationale, and expected outcomes..."
              />
              {errors.description && <ErrorMessage>{errors.description.message}</ErrorMessage>}
            </FormGroup>

            <FormGroup>
              <Label htmlFor="duration_weeks">Study Duration (weeks) *</Label>
              <Input
                id="duration_weeks"
                type="number"
                min="1"
                max="520"
                {...register('duration_weeks', { 
                  required: 'Duration is required',
                  min: { value: 1, message: 'Duration must be at least 1 week' },
                  max: { value: 520, message: 'Duration cannot exceed 10 years' }
                })}
                placeholder="Enter study duration in weeks"
                error={!!errors.duration_weeks}
              />
              {errors.duration_weeks && <ErrorMessage>{errors.duration_weeks.message}</ErrorMessage>}
            </FormGroup>

            <FormGroup>
              <Label htmlFor="max_participants">Maximum Participants (optional)</Label>
              <Input
                id="max_participants"
                type="number"
                min="1"
                {...register('max_participants', {
                  min: { value: 1, message: 'Must be at least 1 participant' }
                })}
                placeholder="Enter maximum number of participants (leave empty for unlimited)"
                error={!!errors.max_participants}
              />
              {errors.max_participants && <ErrorMessage>{errors.max_participants.message}</ErrorMessage>}
            </FormGroup>
          </Form>
        );

      case 'objectives':
        return (
          <Form>
            <FormGroup>
              <Label>Research Objectives *</Label>
              <ArrayField>
                {objectiveFields.map((field, index) => (
                  <ArrayItem key={field.id}>
                    <ArrayInput
                      {...register(`objectives.${index}.value`, { required: 'Objective is required' })}
                      placeholder={`Objective ${index + 1}`}
                      error={!!errors.objectives?.[index]?.value}
                    />
                    {objectiveFields.length > 1 && (
                      <RemoveButton
                        type="button"
                        variant="outline"
                        size="small"
                        onClick={() => removeObjective(index)}
                        leftIcon={<MinusIcon />}
                      >
                        Remove
                      </RemoveButton>
                    )}
                  </ArrayItem>
                ))}
                <AddButton
                  type="button"
                  variant="ghost"
                  size="small"
                  onClick={() => appendObjective({ value: '' })}
                  leftIcon={<PlusIcon />}
                >
                  Add Objective
                </AddButton>
              </ArrayField>
            </FormGroup>

            <FormGroup>
              <Label>Primary Endpoints *</Label>
              <ArrayField>
                {primaryEndpointFields.map((field, index) => (
                  <ArrayItem key={field.id}>
                    <ArrayInput
                      {...register(`primary_endpoints.${index}.value`, { required: 'Primary endpoint is required' })}
                      placeholder={`Primary endpoint ${index + 1}`}
                      error={!!errors.primary_endpoints?.[index]?.value}
                    />
                    {primaryEndpointFields.length > 1 && (
                      <RemoveButton
                        type="button"
                        variant="outline"
                        size="small"
                        onClick={() => removePrimaryEndpoint(index)}
                        leftIcon={<MinusIcon />}
                      >
                        Remove
                      </RemoveButton>
                    )}
                  </ArrayItem>
                ))}
                <AddButton
                  type="button"
                  variant="ghost"
                  size="small"
                  onClick={() => appendPrimaryEndpoint({ value: '' })}
                  leftIcon={<PlusIcon />}
                >
                  Add Primary Endpoint
                </AddButton>
              </ArrayField>
            </FormGroup>

            <FormGroup>
              <Label>Secondary Endpoints (optional)</Label>
              <ArrayField>
                {secondaryEndpointFields.map((field, index) => (
                  <ArrayItem key={field.id}>
                    <ArrayInput
                      {...register(`secondary_endpoints.${index}.value`)}
                      placeholder={`Secondary endpoint ${index + 1}`}
                    />
                    <RemoveButton
                      type="button"
                      variant="outline"
                      size="small"
                      onClick={() => removeSecondaryEndpoint(index)}
                      leftIcon={<MinusIcon />}
                    >
                      Remove
                    </RemoveButton>
                  </ArrayItem>
                ))}
                <AddButton
                  type="button"
                  variant="ghost"
                  size="small"
                  onClick={() => appendSecondaryEndpoint({ value: '' })}
                  leftIcon={<PlusIcon />}
                >
                  Add Secondary Endpoint
                </AddButton>
              </ArrayField>
            </FormGroup>
          </Form>
        );

      case 'participants':
        return (
          <Form>
            <FormGroup>
              <Label>Inclusion Criteria *</Label>
              <ArrayField>
                {inclusionFields.map((field, index) => (
                  <ArrayItem key={field.id}>
                    <ArrayInput
                      {...register(`inclusion_criteria.${index}.value`, { required: 'Inclusion criterion is required' })}
                      placeholder={`Inclusion criterion ${index + 1}`}
                      error={!!errors.inclusion_criteria?.[index]?.value}
                    />
                    {inclusionFields.length > 1 && (
                      <RemoveButton
                        type="button"
                        variant="outline"
                        size="small"
                        onClick={() => removeInclusion(index)}
                        leftIcon={<MinusIcon />}
                      >
                        Remove
                      </RemoveButton>
                    )}
                  </ArrayItem>
                ))}
                <AddButton
                  type="button"
                  variant="ghost"
                  size="small"
                  onClick={() => appendInclusion({ value: '' })}
                  leftIcon={<PlusIcon />}
                >
                  Add Inclusion Criterion
                </AddButton>
              </ArrayField>
            </FormGroup>

            <FormGroup>
              <Label>Exclusion Criteria *</Label>
              <ArrayField>
                {exclusionFields.map((field, index) => (
                  <ArrayItem key={field.id}>
                    <ArrayInput
                      {...register(`exclusion_criteria.${index}.value`, { required: 'Exclusion criterion is required' })}
                      placeholder={`Exclusion criterion ${index + 1}`}
                      error={!!errors.exclusion_criteria?.[index]?.value}
                    />
                    {exclusionFields.length > 1 && (
                      <RemoveButton
                        type="button"
                        variant="outline"
                        size="small"
                        onClick={() => removeExclusion(index)}
                        leftIcon={<MinusIcon />}
                      >
                        Remove
                      </RemoveButton>
                    )}
                  </ArrayItem>
                ))}
                <AddButton
                  type="button"
                  variant="ghost"
                  size="small"
                  onClick={() => appendExclusion({ value: '' })}
                  leftIcon={<PlusIcon />}
                >
                  Add Exclusion Criterion
                </AddButton>
              </ArrayField>
            </FormGroup>
          </Form>
        );

      case 'methodology':
        return (
          <Form>
            <FormGroup>
              <Label>Supplements Under Study</Label>
              <ArrayField>
                {supplementFields.map((field, index) => (
                  <ArrayItem key={field.id}>
                    <ArrayInput
                      {...register(`supplements.${index}.value`)}
                      placeholder={`Supplement ${index + 1}`}
                    />
                    <RemoveButton
                      type="button"
                      variant="outline"
                      size="small"
                      onClick={() => removeSupplement(index)}
                      leftIcon={<MinusIcon />}
                    >
                      Remove
                    </RemoveButton>
                  </ArrayItem>
                ))}
                <AddButton
                  type="button"
                  variant="ghost"
                  size="small"
                  onClick={() => appendSupplement({ value: '' })}
                  leftIcon={<PlusIcon />}
                >
                  Add Supplement
                </AddButton>
              </ArrayField>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="data_collection_schedule">Data Collection Schedule *</Label>
              <TextArea
                id="data_collection_schedule"
                {...register('data_collection_schedule', { required: 'Data collection schedule is required' })}
                placeholder="Describe when and how data will be collected (e.g., daily surveys, weekly check-ins, monthly assessments...)"
              />
              {errors.data_collection_schedule && <ErrorMessage>{errors.data_collection_schedule.message}</ErrorMessage>}
            </FormGroup>
          </Form>
        );

      case 'ethics':
        return (
          <Form>
            <FormGroup>
              <Label htmlFor="ethical_considerations">Ethical Considerations *</Label>
              <TextArea
                id="ethical_considerations"
                {...register('ethical_considerations', { required: 'Ethical considerations are required' })}
                placeholder="Describe ethical considerations, risk mitigation strategies, participant safety measures, and data privacy protections..."
              />
              {errors.ethical_considerations && <ErrorMessage>{errors.ethical_considerations.message}</ErrorMessage>}
            </FormGroup>

            <FormGroup>
              <Label htmlFor="consent_form_url">Consent Form URL (optional)</Label>
              <Input
                id="consent_form_url"
                type="url"
                {...register('consent_form_url')}
                placeholder="https://example.com/consent-form.pdf"
                error={!!errors.consent_form_url}
              />
              {errors.consent_form_url && <ErrorMessage>{errors.consent_form_url.message}</ErrorMessage>}
            </FormGroup>
          </Form>
        );

      case 'review':
        const formData = getValues();
        return (
          <div>
            <h3>Protocol Review</h3>
            <p>Please review your protocol details before submitting:</p>
            
            <div style={{ background: '#f9fafb', padding: '1rem', borderRadius: '8px', marginTop: '1rem' }}>
              <h4>{formData.title}</h4>
              <p><strong>Duration:</strong> {formData.duration_weeks} weeks</p>
              <p><strong>Max Participants:</strong> {formData.max_participants || 'Unlimited'}</p>
              <p><strong>Objectives:</strong> {formData.objectives?.filter(obj => obj.value).length || 0}</p>
              <p><strong>Primary Endpoints:</strong> {formData.primary_endpoints?.filter(ep => ep.value).length || 0}</p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <BuilderContainer>
      <StepIndicator>
        {STEPS.map((step, index) => (
          <Step
            key={step.id}
            $active={index === currentStep}
            $completed={index < currentStep}
          >
            {index + 1}. {step.title}
          </Step>
        ))}
      </StepIndicator>

      <StepContent>
        <StepTitle>{STEPS[currentStep].title}</StepTitle>
        <StepDescription>{STEPS[currentStep].description}</StepDescription>
        
        {renderStepContent()}
      </StepContent>

      <Navigation>
        <NavGroup>
          {currentStep > 0 && (
            <Button
              variant="outline"
              onClick={handlePrevious}
              leftIcon={<PrevIcon />}
            >
              Previous
            </Button>
          )}
        </NavGroup>

        <NavGroup>
          {onCancel && (
            <Button
              variant="ghost"
              onClick={onCancel}
            >
              Cancel
            </Button>
          )}
          
          {currentStep < STEPS.length - 1 ? (
            <Button
              variant="primary"
              onClick={handleNext}
              rightIcon={<NextIcon />}
            >
              Next
            </Button>
          ) : (
            <Button
              variant="primary"
              onClick={handleSubmit(onSubmit)}
              disabled={isSubmitting}
              leftIcon={isSubmitting ? <LoadingSpinner size="small" /> : <SaveIcon />}
            >
              {isSubmitting ? 'Creating...' : 'Create Protocol'}
            </Button>
          )}
        </NavGroup>
      </Navigation>
    </BuilderContainer>
  );
};

export default ProtocolBuilder;
