/**
 * Performance Monitor Component
 * 
 * Real-time monitoring of API performance, response times, and system health
 */

import React, { useState, useEffect, useRef } from 'react';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { 
  Activity, 
  Zap, 
  Clock, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  BarChart3
} from 'lucide-react';

interface PerformanceMetric {
  endpoint: string;
  responseTime: number;
  status: 'success' | 'error' | 'timeout';
  timestamp: Date;
  size?: number;
}

interface SystemHealth {
  apiHealth: 'healthy' | 'degraded' | 'down';
  averageResponseTime: number;
  successRate: number;
  totalRequests: number;
  errorCount: number;
  lastCheck: Date;
}

const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    apiHealth: 'healthy',
    averageResponseTime: 0,
    successRate: 100,
    totalRequests: 0,
    errorCount: 0,
    lastCheck: new Date()
  });
  const [isMonitoring, setIsMonitoring] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout>();

  const API_ENDPOINTS = [
    { name: 'Health Check', url: 'http://api.pills.localhost:9080/health/' },
    { name: 'Supplements', url: 'http://api.pills.localhost:9080/api/v1/supplements/catalog' },
    { name: 'Dashboard Stats', url: 'http://api.pills.localhost:9080/api/v1/supplements/dashboard/stats' },
    { name: 'Research Studies', url: 'http://api.pills.localhost:9080/api/v1/research/studies' }
  ];

  const measureApiPerformance = async (endpoint: { name: string; url: string }): Promise<PerformanceMetric> => {
    const startTime = performance.now();
    
    try {
      const response = await fetch(endpoint.url);
      const endTime = performance.now();
      const responseTime = Math.round(endTime - startTime);
      
      // Get response size if available
      const contentLength = response.headers.get('content-length');
      const size = contentLength ? parseInt(contentLength) : undefined;
      
      return {
        endpoint: endpoint.name,
        responseTime,
        status: response.ok ? 'success' : 'error',
        timestamp: new Date(),
        size
      };
    } catch (error) {
      const endTime = performance.now();
      const responseTime = Math.round(endTime - startTime);
      
      return {
        endpoint: endpoint.name,
        responseTime,
        status: responseTime > 10000 ? 'timeout' : 'error',
        timestamp: new Date()
      };
    }
  };

  const runPerformanceTest = async () => {
    const newMetrics: PerformanceMetric[] = [];
    
    for (const endpoint of API_ENDPOINTS) {
      const metric = await measureApiPerformance(endpoint);
      newMetrics.push(metric);
    }
    
    setMetrics(prev => [...newMetrics, ...prev].slice(0, 100)); // Keep last 100 metrics
    
    // Update system health
    const allMetrics = [...newMetrics, ...metrics].slice(0, 50); // Last 50 for health calculation
    const successfulRequests = allMetrics.filter(m => m.status === 'success').length;
    const totalRequests = allMetrics.length;
    const successRate = totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 100;
    const averageResponseTime = allMetrics.length > 0 
      ? allMetrics.reduce((sum, m) => sum + m.responseTime, 0) / allMetrics.length 
      : 0;
    
    let apiHealth: 'healthy' | 'degraded' | 'down' = 'healthy';
    if (successRate < 50) apiHealth = 'down';
    else if (successRate < 90 || averageResponseTime > 2000) apiHealth = 'degraded';
    
    setSystemHealth({
      apiHealth,
      averageResponseTime: Math.round(averageResponseTime),
      successRate: Math.round(successRate),
      totalRequests,
      errorCount: totalRequests - successfulRequests,
      lastCheck: new Date()
    });
  };

  const startMonitoring = () => {
    setIsMonitoring(true);
    runPerformanceTest(); // Run immediately
    intervalRef.current = setInterval(runPerformanceTest, 10000); // Every 10 seconds
  };

  const stopMonitoring = () => {
    setIsMonitoring(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'degraded': return 'text-yellow-600 bg-yellow-100';
      case 'down': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'timeout': return <Clock className="h-4 w-4 text-orange-500" />;
      default: return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getResponseTimeColor = (time: number) => {
    if (time < 200) return 'text-green-600';
    if (time < 1000) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Performance Monitor</h2>
          <p className="text-gray-600 dark:text-gray-300">Real-time API performance and system health monitoring</p>
        </div>
        <div className="flex space-x-2">
          {!isMonitoring ? (
            <Button onClick={startMonitoring}>
              <Activity className="h-4 w-4 mr-2" />
              Start Monitoring
            </Button>
          ) : (
            <Button variant="outline" onClick={stopMonitoring}>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Stop Monitoring
            </Button>
          )}
        </div>
      </div>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">API Health</p>
              <p className={`text-lg font-semibold capitalize ${getHealthColor(systemHealth.apiHealth).split(' ')[0]}`}>
                {systemHealth.apiHealth}
              </p>
            </div>
            <Badge className={getHealthColor(systemHealth.apiHealth)}>
              {systemHealth.apiHealth}
            </Badge>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Response Time</p>
              <p className={`text-lg font-semibold ${getResponseTimeColor(systemHealth.averageResponseTime)}`}>
                {systemHealth.averageResponseTime}ms
              </p>
            </div>
            <Zap className="h-6 w-6 text-blue-500" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Success Rate</p>
              <p className={`text-lg font-semibold ${systemHealth.successRate >= 95 ? 'text-green-600' : systemHealth.successRate >= 80 ? 'text-yellow-600' : 'text-red-600'}`}>
                {systemHealth.successRate}%
              </p>
            </div>
            {systemHealth.successRate >= 95 ? (
              <TrendingUp className="h-6 w-6 text-green-500" />
            ) : (
              <TrendingDown className="h-6 w-6 text-red-500" />
            )}
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Requests</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {systemHealth.totalRequests}
              </p>
            </div>
            <BarChart3 className="h-6 w-6 text-purple-500" />
          </div>
        </Card>
      </div>

      {/* Recent Metrics */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent API Calls</h3>
        
        {metrics.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No metrics available. Start monitoring to see real-time data.
          </div>
        ) : (
          <div className="space-y-3">
            {metrics.slice(0, 10).map((metric, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(metric.status)}
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">{metric.endpoint}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {metric.timestamp.toLocaleTimeString()}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4 text-sm">
                  <span className={`font-medium ${getResponseTimeColor(metric.responseTime)}`}>
                    {metric.responseTime}ms
                  </span>
                  {metric.size && (
                    <span className="text-gray-500">
                      {formatBytes(metric.size)}
                    </span>
                  )}
                  <Badge variant={metric.status === 'success' ? 'default' : 'destructive'}>
                    {metric.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>

      {/* Performance Insights */}
      {metrics.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Insights</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Fastest Endpoints</h4>
              <div className="space-y-2">
                {API_ENDPOINTS.map(endpoint => {
                  const endpointMetrics = metrics.filter(m => m.endpoint === endpoint.name && m.status === 'success');
                  const avgTime = endpointMetrics.length > 0 
                    ? endpointMetrics.reduce((sum, m) => sum + m.responseTime, 0) / endpointMetrics.length 
                    : 0;
                  
                  return (
                    <div key={endpoint.name} className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 dark:text-gray-400">{endpoint.name}</span>
                      <span className={`text-sm font-medium ${getResponseTimeColor(avgTime)}`}>
                        {Math.round(avgTime)}ms
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Reliability</h4>
              <div className="space-y-2">
                {API_ENDPOINTS.map(endpoint => {
                  const endpointMetrics = metrics.filter(m => m.endpoint === endpoint.name);
                  const successRate = endpointMetrics.length > 0 
                    ? (endpointMetrics.filter(m => m.status === 'success').length / endpointMetrics.length) * 100 
                    : 100;
                  
                  return (
                    <div key={endpoint.name} className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 dark:text-gray-400">{endpoint.name}</span>
                      <span className={`text-sm font-medium ${successRate >= 95 ? 'text-green-600' : successRate >= 80 ? 'text-yellow-600' : 'text-red-600'}`}>
                        {Math.round(successRate)}%
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default PerformanceMonitor;
