/**
 * Intake History Component
 * 
 * Displays user's supplement intake history with filtering and analytics.
 */

import React, { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { 
  fetchIntakeHistory, 
  selectIntakeHistory, 
  selectSupplementsLoading 
} from '@/store/slices/supplementSlice';
import { format, parseISO, isToday, isYesterday, startOfWeek, endOfWeek } from 'date-fns';
import styled from 'styled-components';

// Components
import Card from '@/components/common/Card';
import Button from '@/components/common/Button';
import LoadingSpinner from '@/components/common/LoadingSpinner';

// Icons
const CalendarIcon = () => <span>📅</span>;
const FilterIcon = () => <span>🔽</span>;
const ExportIcon = () => <span>📊</span>;

// Styled components
const HistoryContainer = styled(Card)`
  padding: 1.5rem;
`;

const HistoryHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  
  h3 {
    color: ${props => props.theme.colors.text};
    margin: 0;
    font-size: 1.3rem;
  }
  
  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
`;

const HeaderActions = styled.div`
  display: flex;
  gap: 0.5rem;
  
  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    justify-content: space-between;
  }
`;

const FilterRow = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: center;
  flex-wrap: wrap;
`;

const FilterSelect = styled.select`
  padding: 0.5rem 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
  min-width: 120px;
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
`;

const IntakeList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const IntakeGroup = styled.div`
  border-bottom: 1px solid ${props => props.theme.colors.border};
  padding-bottom: 1rem;
  
  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
`;

const GroupHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  
  h4 {
    color: ${props => props.theme.colors.text};
    margin: 0;
    font-size: 1rem;
    font-weight: ${props => props.theme.typography.fontWeight.semibold};
  }
  
  span {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 0.9rem;
  }
`;

const IntakeItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: ${props => props.theme.colors.backgroundSecondary};
  border-radius: ${props => props.theme.borderRadius.medium};
  margin-bottom: 0.5rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const IntakeInfo = styled.div`
  flex: 1;
  
  .supplement-name {
    font-weight: ${props => props.theme.typography.fontWeight.medium};
    color: ${props => props.theme.colors.text};
    margin-bottom: 0.25rem;
  }
  
  .dosage {
    color: ${props => props.theme.colors.primary};
    font-weight: ${props => props.theme.typography.fontWeight.semibold};
    margin-bottom: 0.25rem;
  }
  
  .notes {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 0.9rem;
    font-style: italic;
  }
`;

const IntakeTime = styled.div`
  text-align: right;
  color: ${props => props.theme.colors.textSecondary};
  font-size: 0.9rem;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem 1rem;
  color: ${props => props.theme.colors.textSecondary};
  
  h4 {
    color: ${props => props.theme.colors.text};
    margin-bottom: 0.5rem;
  }
  
  p {
    margin-bottom: 1.5rem;
  }
`;

const StatsRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const StatCard = styled.div`
  background: ${props => props.theme.colors.backgroundSecondary};
  padding: 1rem;
  border-radius: ${props => props.theme.borderRadius.medium};
  text-align: center;
  
  .stat-value {
    font-size: 1.5rem;
    font-weight: ${props => props.theme.typography.fontWeight.bold};
    color: ${props => props.theme.colors.primary};
    margin-bottom: 0.25rem;
  }
  
  .stat-label {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 0.9rem;
  }
`;

// Filter options
const TIME_FILTERS = [
  { value: 'all', label: 'All Time' },
  { value: 'today', label: 'Today' },
  { value: 'yesterday', label: 'Yesterday' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
];

interface IntakeHistoryProps {
  supplementId?: string;
  showStats?: boolean;
  compact?: boolean;
}

const IntakeHistory: React.FC<IntakeHistoryProps> = ({
  supplementId,
  showStats = true,
  compact = false,
}) => {
  const dispatch = useAppDispatch();
  const intakeHistory = useAppSelector(selectIntakeHistory);
  const isLoading = useAppSelector(selectSupplementsLoading);
  
  const [timeFilter, setTimeFilter] = useState('week');
  const [showFilters, setShowFilters] = useState(!compact);

  useEffect(() => {
    dispatch(fetchIntakeHistory({
      supplement_id: supplementId,
      limit: 50,
    }));
  }, [dispatch, supplementId]);

  const formatDateGroup = (dateString: string) => {
    const date = parseISO(dateString);
    
    if (isToday(date)) return 'Today';
    if (isYesterday(date)) return 'Yesterday';
    
    return format(date, 'EEEE, MMMM d, yyyy');
  };

  const formatTime = (dateString: string) => {
    return format(parseISO(dateString), 'h:mm a');
  };

  const filterIntakesByTime = (intakes: typeof intakeHistory) => {
    if (timeFilter === 'all') return intakes;
    
    const now = new Date();
    
    return intakes.filter(intake => {
      const intakeDate = parseISO(intake.taken_at);
      
      switch (timeFilter) {
        case 'today':
          return isToday(intakeDate);
        case 'yesterday':
          return isYesterday(intakeDate);
        case 'week':
          return intakeDate >= startOfWeek(now) && intakeDate <= endOfWeek(now);
        case 'month':
          return intakeDate.getMonth() === now.getMonth() && 
                 intakeDate.getFullYear() === now.getFullYear();
        default:
          return true;
      }
    });
  };

  const groupIntakesByDate = (intakes: typeof intakeHistory) => {
    const groups: Record<string, typeof intakeHistory> = {};
    
    intakes.forEach(intake => {
      const dateKey = format(parseISO(intake.taken_at), 'yyyy-MM-dd');
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(intake);
    });
    
    return groups;
  };

  const calculateStats = (intakes: typeof intakeHistory) => {
    const totalIntakes = intakes.length;
    const uniqueSupplements = new Set(intakes.map(i => i.supplement_id)).size;
    const avgPerDay = totalIntakes / 7; // Assuming week view
    
    return {
      totalIntakes,
      uniqueSupplements,
      avgPerDay: Math.round(avgPerDay * 10) / 10,
    };
  };

  const filteredIntakes = filterIntakesByTime(intakeHistory);
  const groupedIntakes = groupIntakesByDate(filteredIntakes);
  const stats = calculateStats(filteredIntakes);

  if (isLoading && intakeHistory.length === 0) {
    return (
      <HistoryContainer>
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <LoadingSpinner size="large" />
        </div>
      </HistoryContainer>
    );
  }

  return (
    <HistoryContainer>
      <HistoryHeader>
        <h3>
          {supplementId ? 'Intake History' : 'Recent Intakes'}
        </h3>
        <HeaderActions>
          {compact && (
            <Button
              variant="ghost"
              size="small"
              onClick={() => setShowFilters(!showFilters)}
              leftIcon={<FilterIcon />}
            >
              Filter
            </Button>
          )}
          <Button
            variant="ghost"
            size="small"
            leftIcon={<ExportIcon />}
          >
            Export
          </Button>
        </HeaderActions>
      </HistoryHeader>

      {showFilters && (
        <FilterRow>
          <FilterSelect
            value={timeFilter}
            onChange={(e) => setTimeFilter(e.target.value)}
          >
            {TIME_FILTERS.map(filter => (
              <option key={filter.value} value={filter.value}>
                {filter.label}
              </option>
            ))}
          </FilterSelect>
        </FilterRow>
      )}

      {showStats && filteredIntakes.length > 0 && (
        <StatsRow>
          <StatCard>
            <div className="stat-value">{stats.totalIntakes}</div>
            <div className="stat-label">Total Intakes</div>
          </StatCard>
          <StatCard>
            <div className="stat-value">{stats.uniqueSupplements}</div>
            <div className="stat-label">Supplements</div>
          </StatCard>
          <StatCard>
            <div className="stat-value">{stats.avgPerDay}</div>
            <div className="stat-label">Avg/Day</div>
          </StatCard>
        </StatsRow>
      )}

      {filteredIntakes.length === 0 ? (
        <EmptyState>
          <h4>No intake records found</h4>
          <p>
            {timeFilter === 'all' 
              ? 'Start tracking your supplements to see your history here'
              : 'No intakes recorded for the selected time period'
            }
          </p>
        </EmptyState>
      ) : (
        <IntakeList>
          {Object.entries(groupedIntakes)
            .sort(([a], [b]) => b.localeCompare(a))
            .map(([date, intakes]) => (
              <IntakeGroup key={date}>
                <GroupHeader>
                  <h4>{formatDateGroup(intakes[0].taken_at)}</h4>
                  <span>{intakes.length} intake{intakes.length !== 1 ? 's' : ''}</span>
                </GroupHeader>
                
                {intakes
                  .sort((a, b) => new Date(b.taken_at).getTime() - new Date(a.taken_at).getTime())
                  .map(intake => (
                    <IntakeItem key={intake.id}>
                      <IntakeInfo>
                        <div className="supplement-name">
                          {intake.supplement.name}
                        </div>
                        <div className="dosage">
                          {intake.dosage} {intake.dosage_unit}
                        </div>
                        {intake.notes && (
                          <div className="notes">
                            {intake.notes}
                          </div>
                        )}
                      </IntakeInfo>
                      <IntakeTime>
                        {formatTime(intake.taken_at)}
                      </IntakeTime>
                    </IntakeItem>
                  ))}
              </IntakeGroup>
            ))}
        </IntakeList>
      )}
    </HistoryContainer>
  );
};

export default IntakeHistory;
