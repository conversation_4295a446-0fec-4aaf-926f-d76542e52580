/**
 * Quick Add Widget Component
 * 
 * Sidebar widget for quickly adding frequently used supplements.
 */

import React, { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { 
  trackSupplementIntake, 
  selectTrackingIntake,
  selectRecentIntakes 
} from '@/store/slices/supplementSlice';
import { showSuccessNotification, showErrorNotification } from '@/store/slices/uiSlice';
import styled from 'styled-components';

// Components
import Button from '@/components/common/Button';
import LoadingSpinner from '@/components/common/LoadingSpinner';

// Icons
const PlusIcon = () => <span>+</span>;
const ClockIcon = () => <span>🕐</span>;

// Styled components
const WidgetContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const Section = styled.div`
  h4 {
    color: ${props => props.theme.colors.text};
    margin: 0 0 0.75rem 0;
    font-size: 1rem;
    font-weight: ${props => props.theme.typography.fontWeight.semibold};
  }
`;

const QuickAddList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const QuickAddItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: ${props => props.theme.colors.backgroundSecondary};
  border-radius: ${props => props.theme.borderRadius.medium};
  border: 1px solid ${props => props.theme.colors.border};
`;

const ItemInfo = styled.div`
  flex: 1;
  
  .name {
    font-weight: ${props => props.theme.typography.fontWeight.medium};
    color: ${props => props.theme.colors.text};
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
  }
  
  .dosage {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 0.8rem;
  }
`;

const AddButton = styled(Button)`
  padding: 0.5rem;
  min-width: auto;
`;

const RecentItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  
  &:last-child {
    border-bottom: none;
  }
`;

const RecentInfo = styled.div`
  flex: 1;
  
  .name {
    font-weight: ${props => props.theme.typography.fontWeight.medium};
    color: ${props => props.theme.colors.text};
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
  }
  
  .time {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 0.8rem;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 1rem;
  color: ${props => props.theme.colors.textSecondary};
  font-size: 0.9rem;
`;

// Mock frequently used supplements (would come from user preferences/history)
const FREQUENT_SUPPLEMENTS = [
  {
    id: '1',
    name: 'Vitamin D3',
    defaultDosage: 2000,
    defaultUnit: 'IU',
  },
  {
    id: '2',
    name: 'Omega-3',
    defaultDosage: 1000,
    defaultUnit: 'mg',
  },
  {
    id: '3',
    name: 'Magnesium',
    defaultDosage: 400,
    defaultUnit: 'mg',
  },
  {
    id: '4',
    name: 'Vitamin B12',
    defaultDosage: 1000,
    defaultUnit: 'mcg',
  },
];

const QuickAddWidget: React.FC = () => {
  const dispatch = useAppDispatch();
  const isTracking = useAppSelector(selectTrackingIntake);
  const recentIntakes = useAppSelector(selectRecentIntakes);
  const [trackingId, setTrackingId] = useState<string | null>(null);

  const handleQuickAdd = async (supplementId: string, dosage: number, unit: string, name: string) => {
    setTrackingId(supplementId);
    
    try {
      await dispatch(trackSupplementIntake({
        supplementId,
        intakeData: {
          dosage,
          dosage_unit: unit,
          taken_at: new Date().toISOString(),
        },
      })).unwrap();

      dispatch(showSuccessNotification(
        `Successfully logged ${dosage}${unit} of ${name}`,
        'Quick Add Complete'
      ));
    } catch (error: any) {
      dispatch(showErrorNotification(
        error.message || 'Failed to log supplement intake',
        'Quick Add Failed'
      ));
    } finally {
      setTrackingId(null);
    }
  };

  const formatRecentTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <WidgetContainer>
      <Section>
        <h4>Frequently Used</h4>
        <QuickAddList>
          {FREQUENT_SUPPLEMENTS.map(supplement => (
            <QuickAddItem key={supplement.id}>
              <ItemInfo>
                <div className="name">{supplement.name}</div>
                <div className="dosage">
                  {supplement.defaultDosage} {supplement.defaultUnit}
                </div>
              </ItemInfo>
              <AddButton
                variant="outline"
                size="small"
                onClick={() => handleQuickAdd(
                  supplement.id,
                  supplement.defaultDosage,
                  supplement.defaultUnit,
                  supplement.name
                )}
                disabled={isTracking}
                leftIcon={
                  trackingId === supplement.id ? 
                    <LoadingSpinner size="small" /> : 
                    <PlusIcon />
                }
              >
                {trackingId === supplement.id ? '' : 'Add'}
              </AddButton>
            </QuickAddItem>
          ))}
        </QuickAddList>
      </Section>

      <Section>
        <h4>Recent Activity</h4>
        {recentIntakes.length === 0 ? (
          <EmptyState>
            No recent activity
          </EmptyState>
        ) : (
          <div>
            {recentIntakes.slice(0, 5).map(intake => (
              <RecentItem key={intake.id}>
                <RecentInfo>
                  <div className="name">{intake.supplement.name}</div>
                  <div className="time">
                    <ClockIcon /> {formatRecentTime(intake.taken_at)}
                  </div>
                </RecentInfo>
              </RecentItem>
            ))}
          </div>
        )}
      </Section>
    </WidgetContainer>
  );
};

export default QuickAddWidget;
