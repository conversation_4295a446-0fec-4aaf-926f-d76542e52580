/**
 * Supplement Card Component
 * 
 * Individual supplement display card with quick actions.
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/store';
import { trackSupplementIntake, selectTrackingIntake } from '@/store/slices/supplementSlice';
import { showSuccessNotification, showErrorNotification } from '@/store/slices/uiSlice';
import { Supplement } from '@/types/api';
import styled from 'styled-components';

// Components
import Card from '@/components/common/Card';
import Button from '@/components/common/Button';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import QuickTrackModal from './QuickTrackModal';

// Icons (placeholder)
const PlusIcon = () => <span>+</span>;
const InfoIcon = () => <span>ℹ️</span>;
const StarIcon = () => <span>⭐</span>;

// Styled components
const SupplementCardContainer = styled(Card)`
  position: relative;
  transition: all 0.2s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px ${props => props.theme.colors.shadow}20;
  }
`;

const SupplementImage = styled.div<{ $imageUrl?: string }>`
  width: 100%;
  height: 120px;
  background: ${props => 
    props.$imageUrl 
      ? `url(${props.$imageUrl}) center/cover` 
      : `linear-gradient(135deg, ${props.theme.colors.primary}20, ${props.theme.colors.secondary}20)`
  };
  border-radius: ${props => props.theme.borderRadius.medium};
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.theme.colors.textSecondary};
  font-size: 2rem;
`;

const SupplementHeader = styled.div`
  margin-bottom: 1rem;
`;

const SupplementName = styled.h3`
  font-size: 1.1rem;
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const SupplementBrand = styled.p`
  font-size: 0.9rem;
  color: ${props => props.theme.colors.textSecondary};
  margin: 0 0 0.5rem 0;
`;

const SupplementCategory = styled.span`
  display: inline-block;
  background: ${props => props.theme.colors.primary}15;
  color: ${props => props.theme.colors.primary};
  padding: 0.25rem 0.5rem;
  border-radius: ${props => props.theme.borderRadius.small};
  font-size: 0.8rem;
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const SupplementDescription = styled.p`
  font-size: 0.9rem;
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.4;
  margin: 0.75rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const SupplementMeta = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 0.75rem 0;
  font-size: 0.8rem;
  color: ${props => props.theme.colors.textSecondary};
`;

const ServingInfo = styled.span`
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const Actions = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid ${props => props.theme.colors.border};
`;

const QuickAddButton = styled(Button)`
  flex: 1;
`;

const DetailsButton = styled(Button)`
  flex: 1;
`;

interface SupplementCardProps {
  supplement: Supplement;
  onSelect?: (supplementId: string) => void;
  showQuickAdd?: boolean;
  compact?: boolean;
}

const SupplementCard: React.FC<SupplementCardProps> = ({
  supplement,
  onSelect,
  showQuickAdd = true,
  compact = false,
}) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const isTracking = useAppSelector(selectTrackingIntake);
  const [showQuickTrack, setShowQuickTrack] = useState(false);

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't trigger if clicking on buttons
    if ((e.target as HTMLElement).closest('button')) {
      return;
    }

    if (onSelect) {
      onSelect(supplement.id);
    } else {
      navigate(`/supplements/${supplement.id}`);
    }
  };

  const handleQuickAdd = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowQuickTrack(true);
  };

  const handleViewDetails = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/supplements/${supplement.id}`);
  };

  const handleQuickTrack = async (dosage: number, dosageUnit: string, notes?: string) => {
    try {
      await dispatch(trackSupplementIntake({
        supplementId: supplement.id,
        intakeData: {
          dosage,
          dosage_unit: dosageUnit,
          taken_at: new Date().toISOString(),
          notes,
        },
      })).unwrap();

      dispatch(showSuccessNotification(
        `Successfully logged ${dosage}${dosageUnit} of ${supplement.name}`,
        'Intake Recorded'
      ));
      setShowQuickTrack(false);
    } catch (error: any) {
      dispatch(showErrorNotification(
        error.message || 'Failed to log supplement intake',
        'Tracking Error'
      ));
    }
  };

  const formatCategory = (category: string) => {
    return category
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <>
      <SupplementCardContainer
        onClick={handleCardClick}
        padding={compact ? 'small' : 'medium'}
        hover
      >
        {!compact && (
          <SupplementImage $imageUrl={supplement.image_url}>
            {!supplement.image_url && '💊'}
          </SupplementImage>
        )}

        <SupplementHeader>
          <SupplementName>{supplement.name}</SupplementName>
          {supplement.brand && (
            <SupplementBrand>by {supplement.brand}</SupplementBrand>
          )}
          <SupplementCategory>
            {formatCategory(supplement.category)}
          </SupplementCategory>
        </SupplementHeader>

        {!compact && supplement.description && (
          <SupplementDescription>
            {supplement.description}
          </SupplementDescription>
        )}

        {supplement.serving_size && supplement.serving_unit && (
          <SupplementMeta>
            <ServingInfo>
              <span>📏</span>
              Serving: {supplement.serving_size} {supplement.serving_unit}
            </ServingInfo>
          </SupplementMeta>
        )}

        <Actions>
          {showQuickAdd && (
            <QuickAddButton
              variant="primary"
              size="small"
              onClick={handleQuickAdd}
              disabled={isTracking}
              leftIcon={isTracking ? <LoadingSpinner size="small" /> : <PlusIcon />}
            >
              {isTracking ? 'Adding...' : 'Quick Add'}
            </QuickAddButton>
          )}
          
          <DetailsButton
            variant="outline"
            size="small"
            onClick={handleViewDetails}
            leftIcon={<InfoIcon />}
          >
            Details
          </DetailsButton>
        </Actions>
      </SupplementCardContainer>

      {showQuickTrack && (
        <QuickTrackModal
          supplement={supplement}
          onTrack={handleQuickTrack}
          onClose={() => setShowQuickTrack(false)}
          isLoading={isTracking}
        />
      )}
    </>
  );
};

export default SupplementCard;
