/**
 * Main Layout Component
 * 
 * Primary layout wrapper for authenticated pages.
 */

import React from 'react';
import { Outlet } from 'react-router-dom';
import styled from 'styled-components';

// Placeholder layout - will be expanded later
const LayoutContainer = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: ${props => props.theme.colors.background};
`;

const Header = styled.header`
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  padding: 1rem 2rem;
  
  h1 {
    color: ${props => props.theme.colors.primary};
    margin: 0;
    font-size: 1.5rem;
  }
`;

const Main = styled.main`
  flex: 1;
  padding: 2rem;
`;

const Layout: React.FC = () => {
  return (
    <LayoutContainer>
      <Header>
        <h1>Supplement Tracker</h1>
      </Header>
      <Main>
        <Outlet />
      </Main>
    </LayoutContainer>
  );
};

export default Layout;
