/**
 * Data Integration Demo Component
 * 
 * Demonstrates the integration between UX components and backend API
 */

import React, { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { apiClient } from '../services/api';
import { 
  Activity, 
  Database, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  Wifi,
  WifiOff,
  Server,
  Globe
} from 'lucide-react';

interface ApiStatus {
  health: boolean;
  supplements: boolean;
  research: boolean;
  analytics: boolean;
}

const DataIntegrationDemo: React.FC = () => {
  const [apiStatus, setApiStatus] = useState<ApiStatus>({
    health: false,
    supplements: false,
    research: false,
    analytics: false
  });
  const [loading, setLoading] = useState(true);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [sampleData, setSampleData] = useState<any>({});

  const checkApiEndpoints = async () => {
    setLoading(true);
    const status: ApiStatus = {
      health: false,
      supplements: false,
      research: false,
      analytics: false
    };

    try {
      // Test health endpoint
      try {
        await apiClient.healthCheck();
        status.health = true;
      } catch (error) {
        console.log('Health check failed:', error);
      }

      // Test supplements endpoint
      try {
        const supplements = await apiClient.getSupplements({ limit: 1 });
        status.supplements = true;
        setSampleData(prev => ({ ...prev, supplements }));
      } catch (error) {
        console.log('Supplements endpoint failed:', error);
      }

      // Test research endpoint
      try {
        const research = await apiClient.getResearchProtocols({ limit: 1 });
        status.research = true;
        setSampleData(prev => ({ ...prev, research }));
      } catch (error) {
        console.log('Research endpoint failed:', error);
      }

      // Test analytics endpoint
      try {
        const analytics = await apiClient.getAnalyticsData({ timeRange: 'week' });
        status.analytics = true;
        setSampleData(prev => ({ ...prev, analytics }));
      } catch (error) {
        console.log('Analytics endpoint failed:', error);
      }

    } catch (error) {
      console.error('API check failed:', error);
    }

    setApiStatus(status);
    setLastChecked(new Date());
    setLoading(false);
  };

  useEffect(() => {
    checkApiEndpoints();
  }, []);

  const getStatusIcon = (isWorking: boolean) => {
    return isWorking ? (
      <CheckCircle className="h-5 w-5 text-green-500" />
    ) : (
      <AlertCircle className="h-5 w-5 text-red-500" />
    );
  };

  const getStatusBadge = (isWorking: boolean) => {
    return (
      <Badge variant={isWorking ? "default" : "destructive"}>
        {isWorking ? "Working" : "Offline"}
      </Badge>
    );
  };

  const overallStatus = Object.values(apiStatus).some(status => status);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
              <Database className="h-8 w-8 mr-3 text-blue-600" />
              Data Integration Demo
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mt-1">
              Testing backend API connectivity and data integration
            </p>
          </div>
          <Button onClick={checkApiEndpoints} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh Status
          </Button>
        </div>

        {/* Overall Status */}
        <Card className="p-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {overallStatus ? (
                <Wifi className="h-8 w-8 text-green-500 mr-4" />
              ) : (
                <WifiOff className="h-8 w-8 text-red-500 mr-4" />
              )}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Backend Connection Status
                </h2>
                <p className="text-gray-600 dark:text-gray-300">
                  {overallStatus 
                    ? "Connected to backend services" 
                    : "Backend services unavailable - using mock data"
                  }
                </p>
              </div>
            </div>
            <div className="text-right">
              {getStatusBadge(overallStatus)}
              {lastChecked && (
                <p className="text-sm text-gray-500 mt-1">
                  Last checked: {lastChecked.toLocaleTimeString()}
                </p>
              )}
            </div>
          </div>
        </Card>

        {/* API Endpoints Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Server className="h-6 w-6 text-blue-600 mr-3" />
                <h3 className="font-semibold">Health Check</h3>
              </div>
              {getStatusIcon(apiStatus.health)}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Basic API connectivity test
            </p>
            {getStatusBadge(apiStatus.health)}
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Activity className="h-6 w-6 text-green-600 mr-3" />
                <h3 className="font-semibold">Supplements</h3>
              </div>
              {getStatusIcon(apiStatus.supplements)}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Supplement catalog and tracking
            </p>
            {getStatusBadge(apiStatus.supplements)}
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Globe className="h-6 w-6 text-purple-600 mr-3" />
                <h3 className="font-semibold">Research</h3>
              </div>
              {getStatusIcon(apiStatus.research)}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Research protocols and studies
            </p>
            {getStatusBadge(apiStatus.research)}
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Database className="h-6 w-6 text-orange-600 mr-3" />
                <h3 className="font-semibold">Analytics</h3>
              </div>
              {getStatusIcon(apiStatus.analytics)}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Data analysis and insights
            </p>
            {getStatusBadge(apiStatus.analytics)}
          </Card>
        </div>

        {/* Data Integration Status */}
        <Card className="p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Data Integration Status
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">Dashboard Component</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {apiStatus.health ? "Loading real-time data from API" : "Using fallback mock data"}
                </p>
              </div>
              {getStatusIcon(apiStatus.health)}
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">Supplement Tracker</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {apiStatus.supplements ? "Connected to supplement database" : "Using sample supplement data"}
                </p>
              </div>
              {getStatusIcon(apiStatus.supplements)}
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">Research Hub</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {apiStatus.research ? "Connected to research database" : "Using sample research data"}
                </p>
              </div>
              {getStatusIcon(apiStatus.research)}
            </div>
          </div>
        </Card>

        {/* Sample Data Preview */}
        {Object.keys(sampleData).length > 0 && (
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Sample API Data
            </h3>
            <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
              <pre className="text-sm text-gray-700 dark:text-gray-300 overflow-auto">
                {JSON.stringify(sampleData, null, 2)}
              </pre>
            </div>
          </Card>
        )}

        {/* Integration Benefits */}
        <Card className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            🎉 Integration Benefits
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">✅ Real-Time Data</h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Components automatically load and display live data from the backend API
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">🔄 Graceful Fallback</h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                When API is unavailable, components seamlessly switch to mock data
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">⚡ Performance</h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Efficient data loading with proper loading states and error handling
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">🔧 Development Ready</h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Full TypeScript integration with proper type safety and error handling
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default DataIntegrationDemo;
