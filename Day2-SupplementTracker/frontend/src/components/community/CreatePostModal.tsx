/**
 * Create Post Modal Component
 * 
 * Modal for creating new community posts with rich content options.
 */

import React, { useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { useAppDispatch, useAppSelector } from '@/store';
import { selectUser } from '@/store/slices/authSlice';
import { createCommunityPost } from '@/store/slices/communitySlice';
import { closeModal, showSuccessNotification, showErrorNotification } from '@/store/slices/uiSlice';
import styled from 'styled-components';

// Components
import Modal from '@/components/common/Modal';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Avatar from '@/components/common/Avatar';
import LoadingSpinner from '@/components/common/LoadingSpinner';

// Icons
const ImageIcon = () => <span>🖼️</span>;
const EmojiIcon = () => <span>😊</span>;
const TagIcon = () => <span>#</span>;
const CloseIcon = () => <span>✕</span>;
const PostIcon = () => <span>📝</span>;

// Styled components
const ModalContent = styled.div`
  padding: 1.5rem;
  max-width: 600px;
  width: 100%;
`;

const ModalHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const ModalTitle = styled.h2`
  color: ${props => props.theme.colors.text};
  margin: 0;
  font-size: 1.25rem;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.textSecondary};
  cursor: pointer;
  padding: 0.5rem;
  border-radius: ${props => props.theme.borderRadius.medium};
  
  &:hover {
    background: ${props => props.theme.colors.backgroundSecondary};
    color: ${props => props.theme.colors.text};
  }
`;

const PostForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const AuthorSection = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
`;

const AuthorInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
`;

const AuthorName = styled.span`
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  color: ${props => props.theme.colors.text};
`;

const PostTypeSelect = styled.select`
  padding: 0.5rem 0.75rem;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.875rem;
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
`;

const TextArea = styled.textarea`
  padding: 0.75rem 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.9375rem;
  font-family: inherit;
  resize: vertical;
  min-height: 120px;
  
  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
  }
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
`;

const CharacterCount = styled.div<{ $isNearLimit: boolean }>`
  text-align: right;
  font-size: 0.8rem;
  color: ${props => props.$isNearLimit ? props.theme.colors.warning : props.theme.colors.textSecondary};
`;

const TagsInput = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  background: ${props => props.theme.colors.background};
  min-height: 44px;
  
  &:focus-within {
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
`;

const Tag = styled.span`
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: ${props => props.theme.colors.primary}15;
  color: ${props => props.theme.colors.primary};
  border-radius: ${props => props.theme.borderRadius.full};
  font-size: 0.8rem;
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const TagRemoveButton = styled.button`
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0;
  font-size: 0.7rem;
  
  &:hover {
    opacity: 0.7;
  }
`;

const TagInput = styled.input`
  background: none;
  border: none;
  outline: none;
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
  flex: 1;
  min-width: 100px;
  
  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
  }
`;

const PostActions = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 1rem;
  border-top: 1px solid ${props => props.theme.colors.border};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const ActionButton = styled(Button)`
  padding: 0.5rem;
  min-width: auto;
`;

const SubmitActions = styled.div`
  display: flex;
  gap: 0.75rem;
`;

const ErrorMessage = styled.div`
  color: ${props => props.theme.colors.error};
  font-size: 0.8rem;
  margin-top: 0.25rem;
`;

// Form data interface
interface PostFormData {
  title?: string;
  content: string;
  post_type: 'supplement_review' | 'research_finding' | 'experience_share' | 'general';
  tags: string[];
}

// Post type options
const POST_TYPE_OPTIONS = [
  { value: 'general', label: 'General Discussion' },
  { value: 'supplement_review', label: 'Supplement Review' },
  { value: 'research_finding', label: 'Research Finding' },
  { value: 'experience_share', label: 'Experience Share' },
];

interface CreatePostModalProps {
  isOpen: boolean;
  onClose?: () => void;
}

const CreatePostModal: React.FC<CreatePostModalProps> = ({
  isOpen,
  onClose,
}) => {
  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(selectUser);
  
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    control,
    watch,
    reset,
    formState: { errors },
  } = useForm<PostFormData>({
    defaultValues: {
      post_type: 'general',
      content: '',
      tags: [],
    },
  });

  const contentValue = watch('content');
  const postType = watch('post_type');
  const maxContentLength = 2000;
  const isNearLimit = contentValue?.length > maxContentLength * 0.8;

  const handleClose = () => {
    reset();
    setTags([]);
    setTagInput('');
    if (onClose) {
      onClose();
    } else {
      dispatch(closeModal());
    }
  };

  const handleAddTag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      const newTag = tagInput.trim().toLowerCase();
      
      if (newTag && !tags.includes(newTag) && tags.length < 5) {
        setTags([...tags, newTag]);
        setTagInput('');
      }
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const onSubmit = async (data: PostFormData) => {
    if (!currentUser) return;

    setIsSubmitting(true);
    
    try {
      const postData = {
        ...data,
        tags,
        title: data.title?.trim() || undefined,
      };

      await dispatch(createCommunityPost(postData)).unwrap();
      
      dispatch(showSuccessNotification(
        'Post created successfully',
        'Community Post'
      ));
      
      handleClose();
    } catch (error: any) {
      dispatch(showErrorNotification(
        error.message || 'Failed to create post',
        'Post Creation Error'
      ));
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!currentUser) return null;

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="large">
      <ModalContent>
        <ModalHeader>
          <ModalTitle>Create New Post</ModalTitle>
          <CloseButton onClick={handleClose}>
            <CloseIcon />
          </CloseButton>
        </ModalHeader>

        <AuthorSection>
          <Avatar
            src={currentUser.avatar_url}
            alt={currentUser.full_name}
            size="medium"
          />
          <AuthorInfo>
            <AuthorName>{currentUser.full_name}</AuthorName>
            <PostTypeSelect
              {...register('post_type', { required: true })}
            >
              {POST_TYPE_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </PostTypeSelect>
          </AuthorInfo>
        </AuthorSection>

        <PostForm onSubmit={handleSubmit(onSubmit)}>
          {(postType === 'supplement_review' || postType === 'research_finding') && (
            <FormGroup>
              <Label htmlFor="title">Title (optional)</Label>
              <Input
                id="title"
                {...register('title')}
                placeholder="Give your post a descriptive title..."
                error={!!errors.title}
              />
              {errors.title && <ErrorMessage>{errors.title.message}</ErrorMessage>}
            </FormGroup>
          )}

          <FormGroup>
            <Label htmlFor="content">
              What's on your mind? *
            </Label>
            <Controller
              name="content"
              control={control}
              rules={{ 
                required: 'Content is required',
                maxLength: { 
                  value: maxContentLength, 
                  message: `Content must be less than ${maxContentLength} characters` 
                }
              }}
              render={({ field }) => (
                <>
                  <TextArea
                    {...field}
                    id="content"
                    placeholder={
                      postType === 'supplement_review' 
                        ? 'Share your experience with a supplement...'
                        : postType === 'research_finding'
                        ? 'Share an interesting research finding...'
                        : postType === 'experience_share'
                        ? 'Tell us about your supplement journey...'
                        : 'Share your thoughts with the community...'
                    }
                  />
                  <CharacterCount $isNearLimit={isNearLimit}>
                    {field.value?.length || 0} / {maxContentLength}
                  </CharacterCount>
                </>
              )}
            />
            {errors.content && <ErrorMessage>{errors.content.message}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label>Tags (optional)</Label>
            <TagsInput>
              {tags.map((tag, index) => (
                <Tag key={index}>
                  #{tag}
                  <TagRemoveButton
                    type="button"
                    onClick={() => handleRemoveTag(tag)}
                  >
                    <CloseIcon />
                  </TagRemoveButton>
                </Tag>
              ))}
              <TagInput
                type="text"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyDown={handleAddTag}
                placeholder={tags.length === 0 ? "Add tags (press Enter or comma to add)" : ""}
                disabled={tags.length >= 5}
              />
            </TagsInput>
            {tags.length >= 5 && (
              <ErrorMessage>Maximum 5 tags allowed</ErrorMessage>
            )}
          </FormGroup>

          <PostActions>
            <ActionButtons>
              <ActionButton
                type="button"
                variant="ghost"
                size="small"
                leftIcon={<ImageIcon />}
                disabled
              >
                Image
              </ActionButton>
              <ActionButton
                type="button"
                variant="ghost"
                size="small"
                leftIcon={<EmojiIcon />}
                disabled
              >
                Emoji
              </ActionButton>
            </ActionButtons>

            <SubmitActions>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={isSubmitting || !contentValue?.trim()}
                leftIcon={isSubmitting ? <LoadingSpinner size="small" /> : <PostIcon />}
              >
                {isSubmitting ? 'Posting...' : 'Post'}
              </Button>
            </SubmitActions>
          </PostActions>
        </PostForm>
      </ModalContent>
    </Modal>
  );
};

export default CreatePostModal;
