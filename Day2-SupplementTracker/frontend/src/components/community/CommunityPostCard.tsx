/**
 * Community Post Card Component
 * 
 * Displays individual community posts with social interactions.
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/store';
import { selectUser } from '@/store/slices/authSlice';
import { likePost, unlikePost, sharePost } from '@/store/slices/communitySlice';
import { showSuccessNotification, showErrorNotification } from '@/store/slices/uiSlice';
import { CommunityPost } from '@/types/api';
import styled from 'styled-components';
import { formatDistanceToNow, parseISO } from 'date-fns';

// Components
import Card from '@/components/common/Card';
import Button from '@/components/common/Button';
import Avatar from '@/components/common/Avatar';

// Icons (placeholder)
const HeartIcon = ({ filled }: { filled?: boolean }) => (
  <span style={{ color: filled ? '#ef4444' : 'inherit' }}>❤️</span>
);
const CommentIcon = () => <span>💬</span>;
const ShareIcon = () => <span>🔄</span>;
const MoreIcon = () => <span>⋯</span>;
const SupplementIcon = () => <span>💊</span>;
const ResearchIcon = () => <span>🔬</span>;
const ExperienceIcon = () => <span>📝</span>;

// Styled components
const PostCardContainer = styled(Card)`
  margin-bottom: 1.5rem;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px ${props => props.theme.colors.shadow}15;
  }
`;

const PostHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
`;

const AuthorInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  
  &:hover .author-name {
    color: ${props => props.theme.colors.primary};
  }
`;

const AuthorDetails = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
`;

const AuthorName = styled.span`
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  color: ${props => props.theme.colors.text};
  transition: color 0.2s ease;
`;

const PostMeta = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: ${props => props.theme.colors.textSecondary};
`;

const PostTypeTag = styled.span<{ $type: string }>`
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: ${props => props.theme.borderRadius.full};
  font-size: 0.75rem;
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  text-transform: uppercase;
  letter-spacing: 0.5px;
  
  ${props => {
    switch (props.$type) {
      case 'supplement_review':
        return `
          background: ${props.theme.colors.primary}15;
          color: ${props.theme.colors.primary};
        `;
      case 'research_finding':
        return `
          background: ${props.theme.colors.success}15;
          color: ${props.theme.colors.success};
        `;
      case 'experience_share':
        return `
          background: ${props.theme.colors.warning}15;
          color: ${props.theme.colors.warning};
        `;
      default:
        return `
          background: ${props.theme.colors.backgroundSecondary};
          color: ${props.theme.colors.textSecondary};
        `;
    }
  }}
`;

const PostActions = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const ActionButton = styled(Button)`
  padding: 0.25rem;
  min-width: auto;
  border-radius: ${props => props.theme.borderRadius.full};
`;

const PostContent = styled.div`
  margin-bottom: 1.5rem;
`;

const PostTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
  cursor: pointer;
  
  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const PostText = styled.p`
  color: ${props => props.theme.colors.text};
  line-height: 1.6;
  margin: 0 0 1rem 0;
  
  &.truncated {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`;

const ReadMoreButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.primary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  cursor: pointer;
  padding: 0;
  
  &:hover {
    text-decoration: underline;
  }
`;

const PostTags = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

const Tag = styled.span`
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background: ${props => props.theme.colors.backgroundSecondary};
  color: ${props => props.theme.colors.textSecondary};
  border-radius: ${props => props.theme.borderRadius.full};
  font-size: 0.8rem;
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const PostStats = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 1rem;
  border-top: 1px solid ${props => props.theme.colors.border};
`;

const StatGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const StatButton = styled.button<{ $active?: boolean }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.textSecondary};
  font-size: 0.875rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: ${props => props.theme.borderRadius.medium};
  transition: all 0.2s ease;
  
  &:hover {
    background: ${props => props.theme.colors.backgroundSecondary};
    color: ${props => props.theme.colors.primary};
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const StatCount = styled.span`
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const TimeStamp = styled.span`
  font-size: 0.8rem;
  color: ${props => props.theme.colors.textSecondary};
`;

interface CommunityPostCardProps {
  post: CommunityPost;
  onCommentClick?: (postId: string) => void;
  showFullContent?: boolean;
}

const CommunityPostCard: React.FC<CommunityPostCardProps> = ({
  post,
  onCommentClick,
  showFullContent = false,
}) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(selectUser);
  
  const [isExpanded, setIsExpanded] = useState(showFullContent);
  const [isLiking, setIsLiking] = useState(false);
  const [isSharing, setIsSharing] = useState(false);

  const handleAuthorClick = () => {
    navigate(`/users/${post.author.id}`);
  };

  const handlePostClick = () => {
    navigate(`/community/posts/${post.id}`);
  };

  const handleLikeToggle = async () => {
    if (!currentUser || isLiking) return;

    setIsLiking(true);
    
    try {
      if (post.is_liked) {
        await dispatch(unlikePost(post.id)).unwrap();
      } else {
        await dispatch(likePost(post.id)).unwrap();
      }
    } catch (error: any) {
      dispatch(showErrorNotification(
        error.message || 'Failed to update like',
        'Social Action Error'
      ));
    } finally {
      setIsLiking(false);
    }
  };

  const handleCommentClick = () => {
    if (onCommentClick) {
      onCommentClick(post.id);
    } else {
      navigate(`/community/posts/${post.id}#comments`);
    }
  };

  const handleShare = async () => {
    if (!currentUser || isSharing) return;

    setIsSharing(true);
    
    try {
      await dispatch(sharePost(post.id)).unwrap();
      dispatch(showSuccessNotification(
        'Post shared successfully',
        'Social Action'
      ));
    } catch (error: any) {
      dispatch(showErrorNotification(
        error.message || 'Failed to share post',
        'Social Action Error'
      ));
    } finally {
      setIsSharing(false);
    }
  };

  const getPostTypeIcon = (type: string) => {
    switch (type) {
      case 'supplement_review':
        return <SupplementIcon />;
      case 'research_finding':
        return <ResearchIcon />;
      case 'experience_share':
        return <ExperienceIcon />;
      default:
        return <ExperienceIcon />;
    }
  };

  const getPostTypeLabel = (type: string) => {
    switch (type) {
      case 'supplement_review':
        return 'Supplement Review';
      case 'research_finding':
        return 'Research Finding';
      case 'experience_share':
        return 'Experience';
      default:
        return 'Post';
    }
  };

  const shouldTruncate = !isExpanded && post.content.length > 300;
  const displayContent = shouldTruncate 
    ? post.content.substring(0, 300) + '...'
    : post.content;

  return (
    <PostCardContainer>
      <PostHeader>
        <AuthorInfo onClick={handleAuthorClick}>
          <Avatar
            src={post.author.avatar_url}
            alt={post.author.full_name}
            size="medium"
          />
          <AuthorDetails>
            <AuthorName className="author-name">
              {post.author.full_name}
            </AuthorName>
            <PostMeta>
              <PostTypeTag $type={post.post_type}>
                {getPostTypeIcon(post.post_type)}
                {getPostTypeLabel(post.post_type)}
              </PostTypeTag>
              <TimeStamp>
                {formatDistanceToNow(parseISO(post.created_at), { addSuffix: true })}
              </TimeStamp>
            </PostMeta>
          </AuthorDetails>
        </AuthorInfo>

        <PostActions>
          <ActionButton
            variant="ghost"
            size="small"
            leftIcon={<MoreIcon />}
            aria-label="More options"
          />
        </PostActions>
      </PostHeader>

      <PostContent>
        {post.title && (
          <PostTitle onClick={handlePostClick}>
            {post.title}
          </PostTitle>
        )}

        <PostText className={shouldTruncate ? 'truncated' : ''}>
          {displayContent}
        </PostText>

        {shouldTruncate && (
          <ReadMoreButton onClick={() => setIsExpanded(true)}>
            Read more
          </ReadMoreButton>
        )}

        {post.tags && post.tags.length > 0 && (
          <PostTags>
            {post.tags.map((tag, index) => (
              <Tag key={index}>#{tag}</Tag>
            ))}
          </PostTags>
        )}
      </PostContent>

      <PostStats>
        <StatGroup>
          <StatButton
            $active={post.is_liked}
            onClick={handleLikeToggle}
            disabled={!currentUser || isLiking}
          >
            <HeartIcon filled={post.is_liked} />
            <StatCount>{post.like_count}</StatCount>
          </StatButton>

          <StatButton onClick={handleCommentClick}>
            <CommentIcon />
            <StatCount>{post.comment_count}</StatCount>
          </StatButton>

          <StatButton
            onClick={handleShare}
            disabled={!currentUser || isSharing}
          >
            <ShareIcon />
            <StatCount>{post.share_count}</StatCount>
          </StatButton>
        </StatGroup>

        <TimeStamp>
          {formatDistanceToNow(parseISO(post.created_at), { addSuffix: true })}
        </TimeStamp>
      </PostStats>
    </PostCardContainer>
  );
};

export default CommunityPostCard;
