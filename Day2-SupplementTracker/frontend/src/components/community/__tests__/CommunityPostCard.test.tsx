/**
 * CommunityPostCard Component Tests
 * 
 * Test-driven development tests for the CommunityPostCard component.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { configureStore } from '@reduxjs/toolkit';

// Components and store
import CommunityPostCard from '../CommunityPostCard';
import authReducer from '@/store/slices/authSlice';
import communityReducer from '@/store/slices/communitySlice';
import uiReducer from '@/store/slices/uiSlice';
import { lightTheme } from '@/styles/theme';

// Test data
const mockPost = {
  id: '1',
  title: 'My Experience with Vitamin D3',
  content: 'I\'ve been taking Vitamin D3 for 6 months now and have noticed significant improvements in my energy levels and mood. Started with 2000 IU daily and had my levels tested after 3 months.',
  post_type: 'supplement_review' as const,
  tags: ['vitamin-d3', 'energy', 'mood'],
  author: {
    id: '2',
    email: '<EMAIL>',
    full_name: '<PERSON>',
    avatar_url: 'https://example.com/avatar.jpg',
    is_active: true,
    is_superuser: false,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  like_count: 15,
  comment_count: 8,
  share_count: 3,
  is_liked: false,
  created_at: '2024-01-15T10:30:00Z',
  updated_at: '2024-01-15T10:30:00Z',
};

const mockLikedPost = {
  ...mockPost,
  id: '2',
  is_liked: true,
  like_count: 16,
};

// Test store setup
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authReducer,
      community: communityReducer,
      ui: uiReducer,
    },
    preloadedState: {
      auth: {
        user: {
          id: '1',
          email: '<EMAIL>',
          full_name: 'Test User',
          is_active: true,
          is_superuser: false,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        token: 'test-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
      community: {
        posts: [],
        currentPost: null,
        comments: {},
        groups: [],
        following: [],
        followers: [],
        userProfiles: {},
        isLoading: false,
        isPostingComment: false,
        isCreatingPost: false,
        isLikingPost: {},
        isSharingPost: {},
        isFollowingUser: {},
        commentsLoading: {},
        profilesLoading: {},
        error: null,
        feedFilters: {
          post_type: '',
          search: '',
          sort: 'recent',
        },
        feedPagination: {
          total: 0,
          page: 1,
          size: 10,
          hasMore: false,
        },
        filters: {
          post_type: '',
          group_id: '',
          search: '',
        },
      },
      ui: {
        theme: 'light',
        sidebarOpen: true,
        notifications: [],
        modals: [],
        globalLoading: false,
        pageTitle: 'Community',
        breadcrumbs: [],
        searchQuery: '',
        mobileMenuOpen: false,
      },
      ...initialState,
    },
  });
};

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode; store?: any }> = ({ 
  children, 
  store = createTestStore() 
}) => (
  <Provider store={store}>
    <BrowserRouter>
      <ThemeProvider theme={lightTheme}>
        {children}
      </ThemeProvider>
    </BrowserRouter>
  </Provider>
);

describe('CommunityPostCard', () => {
  const mockOnCommentClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render post information correctly', () => {
      render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      expect(screen.getByText('My Experience with Vitamin D3')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('Supplement Review')).toBeInTheDocument();
      expect(screen.getByText(/6 months now and have noticed/)).toBeInTheDocument();
    });

    it('should render post metadata correctly', () => {
      render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      expect(screen.getByText('15')).toBeInTheDocument(); // Like count
      expect(screen.getByText('8')).toBeInTheDocument(); // Comment count
      expect(screen.getByText('3')).toBeInTheDocument(); // Share count
    });

    it('should render post tags', () => {
      render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      expect(screen.getByText('#vitamin-d3')).toBeInTheDocument();
      expect(screen.getByText('#energy')).toBeInTheDocument();
      expect(screen.getByText('#mood')).toBeInTheDocument();
    });

    it('should render post type badge with correct styling', () => {
      render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      const badge = screen.getByText('Supplement Review');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveStyle('text-transform: uppercase');
    });

    it('should render compact mode correctly', () => {
      const { rerender } = render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      // Full content should be visible
      expect(screen.getByText(/6 months now and have noticed/)).toBeInTheDocument();

      rerender(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} showFullContent={false} />
        </TestWrapper>
      );

      // Content should be truncated if long enough
      const content = screen.getByText(/6 months now and have noticed/);
      expect(content).toBeInTheDocument();
    });
  });

  describe('Post Type Display', () => {
    it('should display supplement review type correctly', () => {
      render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      expect(screen.getByText('Supplement Review')).toBeInTheDocument();
    });

    it('should display research finding type correctly', () => {
      const researchPost = { ...mockPost, post_type: 'research_finding' as const };
      
      render(
        <TestWrapper>
          <CommunityPostCard post={researchPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      expect(screen.getByText('Research Finding')).toBeInTheDocument();
    });

    it('should display experience share type correctly', () => {
      const experiencePost = { ...mockPost, post_type: 'experience_share' as const };
      
      render(
        <TestWrapper>
          <CommunityPostCard post={experiencePost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      expect(screen.getByText('Experience')).toBeInTheDocument();
    });
  });

  describe('Social Interactions', () => {
    it('should show like button with correct state', () => {
      render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      const likeButton = screen.getByRole('button', { name: /15/ });
      expect(likeButton).toBeInTheDocument();
      expect(likeButton).not.toBeDisabled();
    });

    it('should show liked state correctly', () => {
      render(
        <TestWrapper>
          <CommunityPostCard post={mockLikedPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      const likeButton = screen.getByRole('button', { name: /16/ });
      expect(likeButton).toBeInTheDocument();
      // Would check for active styling in real implementation
    });

    it('should handle like button click', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      const likeButton = screen.getByRole('button', { name: /15/ });
      await user.click(likeButton);

      // Would verify dispatch call in real implementation
    });

    it('should handle comment button click', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      const commentButton = screen.getByRole('button', { name: /8/ });
      await user.click(commentButton);

      expect(mockOnCommentClick).toHaveBeenCalledWith('1');
    });

    it('should handle share button click', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      const shareButton = screen.getByRole('button', { name: /3/ });
      await user.click(shareButton);

      // Would verify dispatch call in real implementation
    });

    it('should disable interactions when not authenticated', () => {
      const storeWithoutUser = createTestStore({
        auth: {
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        },
      });

      render(
        <TestWrapper store={storeWithoutUser}>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      const likeButton = screen.getByRole('button', { name: /15/ });
      const shareButton = screen.getByRole('button', { name: /3/ });
      
      expect(likeButton).toBeDisabled();
      expect(shareButton).toBeDisabled();
    });
  });

  describe('Navigation', () => {
    it('should navigate to author profile when author is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      const authorName = screen.getByText('Jane Smith');
      await user.click(authorName);

      // Would check navigation in real implementation
    });

    it('should navigate to post details when title is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      const title = screen.getByText('My Experience with Vitamin D3');
      await user.click(title);

      // Would check navigation in real implementation
    });

    it('should navigate to post details when card is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      // Click on card content area (not buttons)
      const content = screen.getByText(/6 months now and have noticed/);
      await user.click(content);

      // Would check navigation in real implementation
    });
  });

  describe('Content Expansion', () => {
    const longPost = {
      ...mockPost,
      content: 'This is a very long post content that should be truncated when displayed in the card view. '.repeat(10),
    };

    it('should show read more button for long content', () => {
      render(
        <TestWrapper>
          <CommunityPostCard post={longPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      expect(screen.getByText('Read more')).toBeInTheDocument();
    });

    it('should expand content when read more is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CommunityPostCard post={longPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      const readMoreButton = screen.getByText('Read more');
      await user.click(readMoreButton);

      expect(screen.queryByText('Read more')).not.toBeInTheDocument();
    });

    it('should show full content when showFullContent is true', () => {
      render(
        <TestWrapper>
          <CommunityPostCard post={longPost} onCommentClick={mockOnCommentClick} showFullContent />
        </TestWrapper>
      );

      expect(screen.queryByText('Read more')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper button roles and labels', () => {
      render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      expect(screen.getByRole('button', { name: /15/ })).toBeInTheDocument(); // Like button
      expect(screen.getByRole('button', { name: /8/ })).toBeInTheDocument(); // Comment button
      expect(screen.getByRole('button', { name: /3/ })).toBeInTheDocument(); // Share button
    });

    it('should be keyboard navigable', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      // Tab through interactive elements
      await user.tab();
      // Would check focus states in real implementation
    });

    it('should have proper ARIA attributes', () => {
      render(
        <TestWrapper>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      // Check for proper accessibility attributes
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toHaveAttribute('type', 'button');
      });
    });
  });

  describe('Loading States', () => {
    it('should show loading state when liking post', async () => {
      const storeWithLoading = createTestStore({
        community: {
          isLikingPost: { '1': true },
        },
      });

      render(
        <TestWrapper store={storeWithLoading}>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      const likeButton = screen.getByRole('button', { name: /15/ });
      expect(likeButton).toBeDisabled();
    });

    it('should show loading state when sharing post', async () => {
      const storeWithLoading = createTestStore({
        community: {
          isSharingPost: { '1': true },
        },
      });

      render(
        <TestWrapper store={storeWithLoading}>
          <CommunityPostCard post={mockPost} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      const shareButton = screen.getByRole('button', { name: /3/ });
      expect(shareButton).toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    it('should handle missing author gracefully', () => {
      const postWithoutAuthor = {
        ...mockPost,
        author: {
          ...mockPost.author,
          full_name: '',
        },
      };

      render(
        <TestWrapper>
          <CommunityPostCard post={postWithoutAuthor} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      // Should still render the card
      expect(screen.getByText('My Experience with Vitamin D3')).toBeInTheDocument();
    });

    it('should handle missing tags gracefully', () => {
      const postWithoutTags = {
        ...mockPost,
        tags: [],
      };

      render(
        <TestWrapper>
          <CommunityPostCard post={postWithoutTags} onCommentClick={mockOnCommentClick} />
        </TestWrapper>
      );

      // Should still render the card
      expect(screen.getByText('My Experience with Vitamin D3')).toBeInTheDocument();
      expect(screen.queryByText('#vitamin-d3')).not.toBeInTheDocument();
    });
  });
});
