/**
 * Analytics Dashboard Component
 * 
 * Comprehensive analytics dashboard with supplement tracking insights and data visualization.
 */

import React, { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { 
  fetchAnalyticsData, 
  updateAnalyticsFilters,
  selectAnalyticsData, 
  selectAnalyticsLoading, 
  selectAnalyticsFilters 
} from '@/store/slices/analyticsSlice';
import { selectUser } from '@/store/slices/authSlice';
import styled from 'styled-components';
import { subDays, format, parseISO } from 'date-fns';

// Components
import LineChart from './charts/LineChart';
import BarChart from './charts/BarChart';
import PieChart from './charts/PieChart';
import Card from '@/components/common/Card';
import Button from '@/components/common/Button';
import LoadingSpinner from '@/components/common/LoadingSpinner';

// Icons
const TrendUpIcon = () => <span>📈</span>;
const TrendDownIcon = () => <span>📉</span>;
const CalendarIcon = () => <span>📅</span>;
const DownloadIcon = () => <span>⬇️</span>;
const FilterIcon = () => <span>🔽</span>;
const InsightIcon = () => <span>💡</span>;

// Styled components
const DashboardContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
`;

const DashboardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
`;

const HeaderInfo = styled.div`
  h1 {
    color: ${props => props.theme.colors.text};
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
  }
  
  p {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 1.1rem;
    margin: 0;
  }
`;

const HeaderActions = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  
  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    flex-direction: column;
    width: 100%;
  }
`;

const FilterControls = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;
  flex-wrap: wrap;
  
  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const FilterSelect = styled.select`
  padding: 0.75rem 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.9375rem;
  min-width: 150px;
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
`;

const DateRangeInput = styled.input`
  padding: 0.75rem 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.9375rem;
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const MetricCard = styled(Card)`
  padding: 1.5rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${props => props.theme.colors.primary};
  }
`;

const MetricValue = styled.div`
  font-size: 2.5rem;
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  color: ${props => props.theme.colors.primary};
  margin-bottom: 0.5rem;
  line-height: 1;
`;

const MetricLabel = styled.div`
  font-size: 0.9rem;
  color: ${props => props.theme.colors.textSecondary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.75rem;
`;

const MetricChange = styled.div<{ $positive: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: ${props => props.$positive ? props.theme.colors.success : props.theme.colors.error};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const ChartsGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
  
  @media (max-width: ${props => props.theme.breakpoints.desktop}) {
    grid-template-columns: 1fr;
  }
`;

const ChartSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const InsightsSection = styled.div`
  margin-bottom: 2rem;
`;

const InsightsHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  
  h3 {
    color: ${props => props.theme.colors.text};
    margin: 0;
    font-size: 1.25rem;
  }
`;

const InsightsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const InsightCard = styled(Card)`
  padding: 1rem 1.5rem;
  border-left: 4px solid ${props => props.theme.colors.primary};
`;

const InsightText = styled.p`
  color: ${props => props.theme.colors.text};
  margin: 0;
  line-height: 1.5;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 4rem 2rem;
  
  h3 {
    color: ${props => props.theme.colors.text};
    margin-bottom: 1rem;
  }
  
  p {
    color: ${props => props.theme.colors.textSecondary};
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }
`;

// Time range options
const TIME_RANGE_OPTIONS = [
  { value: '7d', label: 'Last 7 days' },
  { value: '30d', label: 'Last 30 days' },
  { value: '90d', label: 'Last 3 months' },
  { value: '1y', label: 'Last year' },
  { value: 'custom', label: 'Custom range' },
];

const AnalyticsDashboard: React.FC = () => {
  const dispatch = useAppDispatch();
  const analyticsData = useAppSelector(selectAnalyticsData);
  const isLoading = useAppSelector(selectAnalyticsLoading);
  const filters = useAppSelector(selectAnalyticsFilters);
  const currentUser = useAppSelector(selectUser);
  
  const [customDateRange, setCustomDateRange] = useState({
    start: format(subDays(new Date(), 30), 'yyyy-MM-dd'),
    end: format(new Date(), 'yyyy-MM-dd'),
  });

  useEffect(() => {
    if (currentUser) {
      dispatch(fetchAnalyticsData({
        timeRange: filters.timeRange,
        supplementIds: filters.supplementIds,
        startDate: filters.startDate,
        endDate: filters.endDate,
      }));
    }
  }, [filters, currentUser, dispatch]);

  const handleTimeRangeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const timeRange = e.target.value;
    
    if (timeRange === 'custom') {
      dispatch(updateAnalyticsFilters({
        timeRange,
        startDate: customDateRange.start,
        endDate: customDateRange.end,
      }));
    } else {
      dispatch(updateAnalyticsFilters({
        timeRange,
        startDate: undefined,
        endDate: undefined,
      }));
    }
  };

  const handleCustomDateChange = (field: 'start' | 'end', value: string) => {
    const newRange = { ...customDateRange, [field]: value };
    setCustomDateRange(newRange);
    
    if (filters.timeRange === 'custom') {
      dispatch(updateAnalyticsFilters({
        startDate: newRange.start,
        endDate: newRange.end,
      }));
    }
  };

  const handleExportData = () => {
    // This would trigger data export functionality
    console.log('Exporting analytics data...');
  };

  if (!currentUser) {
    return (
      <DashboardContainer>
        <EmptyState>
          <h3>Please log in to view analytics</h3>
          <p>Sign in to access your personal supplement tracking analytics and insights.</p>
        </EmptyState>
      </DashboardContainer>
    );
  }

  if (isLoading && !analyticsData) {
    return (
      <DashboardContainer>
        <LoadingContainer>
          <LoadingSpinner size="large" />
        </LoadingContainer>
      </DashboardContainer>
    );
  }

  if (!analyticsData || !analyticsData.metrics) {
    return (
      <DashboardContainer>
        <EmptyState>
          <h3>No data available</h3>
          <p>Start tracking your supplements to see analytics and insights here.</p>
        </EmptyState>
      </DashboardContainer>
    );
  }

  const { metrics, trends, distribution, insights } = analyticsData;

  // Prepare chart data
  const adherenceData = trends.adherence?.map(point => ({
    date: parseISO(point.date),
    value: point.value,
    label: 'Adherence %',
  })) || [];

  const supplementDistribution = distribution.supplements?.map(item => ({
    label: item.name,
    value: item.count,
    color: item.color,
  })) || [];

  const categoryDistribution = distribution.categories?.map(item => ({
    label: item.name,
    value: item.count,
  })) || [];

  return (
    <DashboardContainer>
      <DashboardHeader>
        <HeaderInfo>
          <h1>Analytics Dashboard</h1>
          <p>Track your supplement journey with detailed insights and trends</p>
        </HeaderInfo>
        <HeaderActions>
          <Button
            variant="outline"
            leftIcon={<DownloadIcon />}
            onClick={handleExportData}
          >
            Export Data
          </Button>
        </HeaderActions>
      </DashboardHeader>

      <FilterControls>
        <FilterSelect
          value={filters.timeRange}
          onChange={handleTimeRangeChange}
        >
          {TIME_RANGE_OPTIONS.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </FilterSelect>

        {filters.timeRange === 'custom' && (
          <>
            <DateRangeInput
              type="date"
              value={customDateRange.start}
              onChange={(e) => handleCustomDateChange('start', e.target.value)}
            />
            <DateRangeInput
              type="date"
              value={customDateRange.end}
              onChange={(e) => handleCustomDateChange('end', e.target.value)}
            />
          </>
        )}
      </FilterControls>

      <MetricsGrid>
        <MetricCard>
          <MetricValue>{metrics.totalSupplements}</MetricValue>
          <MetricLabel>Total Supplements</MetricLabel>
          <MetricChange $positive={metrics.supplementsChange >= 0}>
            {metrics.supplementsChange >= 0 ? <TrendUpIcon /> : <TrendDownIcon />}
            {Math.abs(metrics.supplementsChange)}% vs last period
          </MetricChange>
        </MetricCard>

        <MetricCard>
          <MetricValue>{metrics.adherenceRate}%</MetricValue>
          <MetricLabel>Adherence Rate</MetricLabel>
          <MetricChange $positive={metrics.adherenceChange >= 0}>
            {metrics.adherenceChange >= 0 ? <TrendUpIcon /> : <TrendDownIcon />}
            {Math.abs(metrics.adherenceChange)}% vs last period
          </MetricChange>
        </MetricCard>

        <MetricCard>
          <MetricValue>{metrics.totalIntakes}</MetricValue>
          <MetricLabel>Total Intakes</MetricLabel>
          <MetricChange $positive={metrics.intakesChange >= 0}>
            {metrics.intakesChange >= 0 ? <TrendUpIcon /> : <TrendDownIcon />}
            {Math.abs(metrics.intakesChange)}% vs last period
          </MetricChange>
        </MetricCard>

        <MetricCard>
          <MetricValue>{metrics.streakDays}</MetricValue>
          <MetricLabel>Current Streak</MetricLabel>
          <MetricChange $positive={metrics.streakChange >= 0}>
            {metrics.streakChange >= 0 ? <TrendUpIcon /> : <TrendDownIcon />}
            {Math.abs(metrics.streakChange)} days vs last period
          </MetricChange>
        </MetricCard>
      </MetricsGrid>

      <ChartsGrid>
        <ChartSection>
          {adherenceData.length > 0 && (
            <LineChart
              data={adherenceData}
              title="Adherence Trend"
              xAxisLabel="Date"
              yAxisLabel="Adherence (%)"
              height={300}
              showDots={true}
              animate={true}
            />
          )}

          {categoryDistribution.length > 0 && (
            <BarChart
              data={categoryDistribution}
              title="Supplements by Category"
              xAxisLabel="Category"
              yAxisLabel="Count"
              height={300}
              animate={true}
            />
          )}
        </ChartSection>

        <ChartSection>
          {supplementDistribution.length > 0 && (
            <PieChart
              data={supplementDistribution}
              title="Supplement Distribution"
              width={350}
              height={350}
              showLegend={true}
              animate={true}
            />
          )}
        </ChartSection>
      </ChartsGrid>

      {insights && insights.length > 0 && (
        <InsightsSection>
          <InsightsHeader>
            <InsightIcon />
            <h3>AI-Powered Insights</h3>
          </InsightsHeader>
          <InsightsList>
            {insights.map((insight, index) => (
              <InsightCard key={index}>
                <InsightText>{insight.text}</InsightText>
              </InsightCard>
            ))}
          </InsightsList>
        </InsightsSection>
      )}
    </DashboardContainer>
  );
};

export default AnalyticsDashboard;
