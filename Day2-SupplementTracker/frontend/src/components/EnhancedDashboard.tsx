/**
 * Enhanced Dashboard Component with Real-Time Data
 * 
 * Features automatic refresh, caching, and improved error handling
 */

import React from 'react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { useDashboardStats, useSupplements, useResearchStudies, useHealthCheck } from '../hooks/useApiData';
import { 
  Activity, 
  Plus, 
  Calendar, 
  TrendingUp, 
  Users, 
  Search,
  Bell,
  Settings,
  BarChart3,
  Pill,
  FlaskConical,
  RefreshCw,
  Wifi,
  WifiOff,
  Clock,
  AlertTriangle
} from 'lucide-react';

interface EnhancedDashboardProps {
  user?: {
    name: string;
    email: string;
    joinDate: string;
  };
}

const EnhancedDashboard: React.FC<EnhancedDashboardProps> = ({ user }) => {
  const healthCheck = useHealthCheck();
  const dashboardStats = useDashboardStats();
  const supplements = useSupplements();
  const researchStudies = useResearchStudies();

  const isOnline = !healthCheck.error;
  const hasData = dashboardStats.data || supplements.data || researchStudies.data;

  const getConnectionStatus = () => {
    if (healthCheck.loading) return { icon: RefreshCw, color: 'text-yellow-500', text: 'Connecting...', spinning: true };
    if (isOnline) return { icon: Wifi, color: 'text-green-500', text: 'Online', spinning: false };
    return { icon: WifiOff, color: 'text-red-500', text: 'Offline', spinning: false };
  };

  const connectionStatus = getConnectionStatus();
  const ConnectionIcon = connectionStatus.icon;

  const refreshAll = async () => {
    await Promise.all([
      healthCheck.refresh(),
      dashboardStats.refresh(),
      supplements.refresh(),
      researchStudies.refresh()
    ]);
  };

  const formatLastUpdated = (date: Date | null) => {
    if (!date) return 'Never';
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                Enhanced Dashboard
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Connection Status */}
              <div className="flex items-center space-x-2">
                <ConnectionIcon className={`h-4 w-4 ${connectionStatus.color} ${connectionStatus.spinning ? 'animate-spin' : ''}`} />
                <span className={`text-sm ${connectionStatus.color}`}>
                  {connectionStatus.text}
                </span>
              </div>
              
              {/* Refresh Button */}
              <Button 
                variant="outline" 
                size="sm" 
                onClick={refreshAll}
                disabled={healthCheck.loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${healthCheck.loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              
              <Button variant="outline" size="sm">
                <Bell className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Welcome back, {user?.name || 'User'}!
          </h2>
          <div className="flex items-center space-x-4 text-gray-600 dark:text-gray-300">
            <span>Real-time supplement tracking and research platform</span>
            {dashboardStats.lastUpdated && (
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span className="text-sm">Updated {formatLastUpdated(dashboardStats.lastUpdated)}</span>
              </div>
            )}
          </div>
        </div>

        {/* Data Status Banner */}
        {!isOnline && (
          <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  Using Cached Data
                </h3>
                <p className="text-sm text-yellow-700 dark:text-yellow-300">
                  Backend services are unavailable. Displaying last known data with automatic retry.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Pill className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Today's Intakes</p>
                <div className="flex items-center space-x-2">
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {dashboardStats.loading ? '...' : dashboardStats.data?.today_intakes || 0}
                  </p>
                  {dashboardStats.isStale && (
                    <Badge variant="outline" className="text-xs">Cached</Badge>
                  )}
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <FlaskConical className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Studies</p>
                <div className="flex items-center space-x-2">
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {researchStudies.loading ? '...' : researchStudies.data?.filter((s: any) => s.status === 'recruiting').length || 0}
                  </p>
                  {researchStudies.isStale && (
                    <Badge variant="outline" className="text-xs">Cached</Badge>
                  )}
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Weekly Progress</p>
                <div className="flex items-center space-x-2">
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {dashboardStats.loading ? '...' : `${dashboardStats.data?.weekly_compliance || 0}%`}
                  </p>
                  {dashboardStats.isStale && (
                    <Badge variant="outline" className="text-xs">Cached</Badge>
                  )}
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                <Users className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Supplements</p>
                <div className="flex items-center space-x-2">
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {supplements.loading ? '...' : supplements.data?.length || 0}
                  </p>
                  {supplements.isStale && (
                    <Badge variant="outline" className="text-xs">Cached</Badge>
                  )}
                </div>
              </div>
            </div>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Today's Supplements */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Available Supplements
              </h3>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Intake
              </Button>
            </div>

            <div className="space-y-4">
              {supplements.loading ? (
                <div className="text-center py-4">
                  <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2 text-gray-400" />
                  <p className="text-gray-500">Loading supplements...</p>
                </div>
              ) : supplements.data?.slice(0, 3).map((supplement: any) => (
                <div key={supplement.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full mr-3 bg-blue-500" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">{supplement.name}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {supplement.default_dosage} {supplement.dosage_unit} • {supplement.brand}
                      </p>
                    </div>
                  </div>
                  <Badge variant="outline">
                    {supplement.evidence_level}
                  </Badge>
                </div>
              )) || (
                <div className="text-center py-4 text-gray-500">
                  No supplements available
                </div>
              )}
            </div>

            <div className="mt-6">
              <Button variant="outline" className="w-full">
                <Calendar className="h-4 w-4 mr-2" />
                View All Supplements
              </Button>
            </div>
          </Card>

          {/* Active Research */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Research Studies
              </h3>
              <Button size="sm" variant="outline">
                <Search className="h-4 w-4 mr-2" />
                Browse Studies
              </Button>
            </div>

            <div className="space-y-4">
              {researchStudies.loading ? (
                <div className="text-center py-4">
                  <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2 text-gray-400" />
                  <p className="text-gray-500">Loading studies...</p>
                </div>
              ) : researchStudies.data?.slice(0, 2).map((study: any) => (
                <div key={study.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900 dark:text-white">{study.title}</h4>
                    <Badge variant="outline">{study.current_participants} participants</Badge>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mb-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${(study.current_participants / study.max_participants) * 100}%` }}
                    />
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {Math.round((study.current_participants / study.max_participants) * 100)}% enrolled
                  </p>
                </div>
              )) || (
                <div className="text-center py-4 text-gray-500">
                  No studies available
                </div>
              )}
            </div>

            <div className="mt-6">
              <Button variant="outline" className="w-full">
                <BarChart3 className="h-4 w-4 mr-2" />
                View Research Dashboard
              </Button>
            </div>
          </Card>
        </div>

        {/* Error Display */}
        {(dashboardStats.error || supplements.error || researchStudies.error) && (
          <Card className="mt-8 p-6 border-red-200 bg-red-50 dark:bg-red-900/20">
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
              Connection Issues
            </h3>
            <div className="space-y-1 text-sm text-red-700 dark:text-red-300">
              {dashboardStats.error && <p>Dashboard: {dashboardStats.error}</p>}
              {supplements.error && <p>Supplements: {supplements.error}</p>}
              {researchStudies.error && <p>Research: {researchStudies.error}</p>}
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default EnhancedDashboard;
