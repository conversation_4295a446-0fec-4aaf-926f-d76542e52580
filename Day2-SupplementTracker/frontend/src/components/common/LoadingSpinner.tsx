/**
 * Loading Spinner Component
 * 
 * Reusable loading spinner with different sizes and colors.
 */

import React from 'react';
import styled, { keyframes, css } from 'styled-components';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  className?: string;
}

// Spinning animation
const spin = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;

// Styled spinner component
const StyledSpinner = styled.div<{
  $size: LoadingSpinnerProps['size'];
  $color?: string;
}>`
  display: inline-block;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top-color: ${props => props.$color || props.theme.colors.primary};
  border-right-color: ${props => props.$color || props.theme.colors.primary};
  animation: ${spin} 0.8s linear infinite;
  
  ${props => {
    switch (props.$size) {
      case 'small':
        return css`
          width: 1rem;
          height: 1rem;
          border-width: 2px;
        `;
      case 'large':
        return css`
          width: 2.5rem;
          height: 2.5rem;
          border-width: 3px;
        `;
      default: // medium
        return css`
          width: 1.5rem;
          height: 1.5rem;
          border-width: 2px;
        `;
    }
  }}
`;

// Container for centering
const SpinnerContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  color,
  className,
}) => {
  return (
    <SpinnerContainer className={className}>
      <StyledSpinner $size={size} $color={color} />
    </SpinnerContainer>
  );
};

export default LoadingSpinner;
