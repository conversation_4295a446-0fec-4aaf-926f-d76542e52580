/**
 * Input Component
 * 
 * Reusable input component with validation states and various types.
 */

import React, { forwardRef } from 'react';
import styled, { css } from 'styled-components';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
  success?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
}

// Styled input wrapper
const InputWrapper = styled.div<{ $fullWidth: boolean }>`
  position: relative;
  display: inline-flex;
  align-items: center;
  
  ${props => props.$fullWidth && css`
    width: 100%;
  `}
`;

// Styled input component
const StyledInput = styled.input<{
  $error: boolean;
  $success: boolean;
  $hasLeftIcon: boolean;
  $hasRightIcon: boolean;
}>`
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  font-size: 0.9375rem;
  font-family: inherit;
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  transition: all 0.2s ease;
  
  ${props => props.$hasLeftIcon && css`
    padding-left: 2.75rem;
  `}
  
  ${props => props.$hasRightIcon && css`
    padding-right: 2.75rem;
  `}
  
  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
  }
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
  
  &:disabled {
    background: ${props => props.theme.colors.backgroundSecondary};
    color: ${props => props.theme.colors.textSecondary};
    cursor: not-allowed;
    opacity: 0.6;
  }
  
  /* Error state */
  ${props => props.$error && css`
    border-color: ${props.theme.colors.error};
    
    &:focus {
      border-color: ${props.theme.colors.error};
      box-shadow: 0 0 0 2px ${props.theme.colors.error}20;
    }
  `}
  
  /* Success state */
  ${props => props.$success && css`
    border-color: ${props.theme.colors.success};
    
    &:focus {
      border-color: ${props.theme.colors.success};
      box-shadow: 0 0 0 2px ${props.theme.colors.success}20;
    }
  `}
  
  /* Hover state */
  &:hover:not(:disabled):not(:focus) {
    border-color: ${props => props.theme.colors.primary}80;
  }
`;

// Icon wrapper
const IconWrapper = styled.div<{ $position: 'left' | 'right' }>`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  color: ${props => props.theme.colors.textSecondary};
  pointer-events: none;
  z-index: 1;
  
  ${props => props.$position === 'left' && css`
    left: 0.75rem;
  `}
  
  ${props => props.$position === 'right' && css`
    right: 0.75rem;
  `}
  
  svg {
    width: 100%;
    height: 100%;
  }
`;

const Input = forwardRef<HTMLInputElement, InputProps>(({
  error = false,
  success = false,
  leftIcon,
  rightIcon,
  fullWidth = false,
  className,
  ...props
}, ref) => {
  return (
    <InputWrapper $fullWidth={fullWidth} className={className}>
      {leftIcon && (
        <IconWrapper $position="left">
          {leftIcon}
        </IconWrapper>
      )}
      
      <StyledInput
        ref={ref}
        $error={error}
        $success={success}
        $hasLeftIcon={!!leftIcon}
        $hasRightIcon={!!rightIcon}
        {...props}
      />
      
      {rightIcon && (
        <IconWrapper $position="right">
          {rightIcon}
        </IconWrapper>
      )}
    </InputWrapper>
  );
});

Input.displayName = 'Input';

export default Input;
