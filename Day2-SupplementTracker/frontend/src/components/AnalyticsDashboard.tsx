/**
 * Advanced Analytics Dashboard
 * 
 * Comprehensive data visualization and insights for supplement tracking
 */

import React, { useState, useEffect } from 'react';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { useSupplements, useDashboardStats, useResearchStudies } from '../hooks/useApiData';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Calendar,
  Users,
  Target,
  Award,
  Activity,
  Pill,
  FlaskConical,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react';

interface AnalyticsData {
  supplementUsage: { name: string; count: number; category: string }[];
  complianceRate: number;
  streakData: { current: number; longest: number; average: number };
  researchParticipation: { active: number; completed: number; total: number };
  categoryBreakdown: { category: string; count: number; percentage: number }[];
  timeSeriesData: { date: string; intakes: number; compliance: number }[];
}

const AnalyticsDashboard: React.FC = () => {
  const supplements = useSupplements();
  const dashboardStats = useDashboardStats();
  const researchStudies = useResearchStudies();
  
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter'>('month');
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);

  // Generate mock analytics data based on real API data
  useEffect(() => {
    if (supplements.data && dashboardStats.data && researchStudies.data) {
      const supplementUsage = supplements.data.slice(0, 6).map((supp: any, index: number) => ({
        name: supp.name,
        count: Math.floor(Math.random() * 30) + 10,
        category: supp.category
      }));

      const categoryBreakdown = supplements.data.reduce((acc: any, supp: any) => {
        const existing = acc.find((item: any) => item.category === supp.category);
        if (existing) {
          existing.count++;
        } else {
          acc.push({ category: supp.category, count: 1, percentage: 0 });
        }
        return acc;
      }, []).map((item: any) => ({
        ...item,
        percentage: Math.round((item.count / supplements.data.length) * 100)
      }));

      const timeSeriesData = Array.from({ length: 30 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (29 - i));
        return {
          date: date.toISOString().split('T')[0],
          intakes: Math.floor(Math.random() * 5) + 2,
          compliance: Math.floor(Math.random() * 30) + 70
        };
      });

      setAnalyticsData({
        supplementUsage,
        complianceRate: dashboardStats.data.weekly_compliance || 85,
        streakData: {
          current: dashboardStats.data.current_streak || 15,
          longest: 28,
          average: 12
        },
        researchParticipation: {
          active: researchStudies.data.filter((s: any) => s.status === 'recruiting').length,
          completed: 2,
          total: researchStudies.data.length
        },
        categoryBreakdown,
        timeSeriesData
      });
    }
  }, [supplements.data, dashboardStats.data, researchStudies.data]);

  const getComplianceColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600 bg-green-100';
    if (rate >= 70) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (current < previous) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <Activity className="h-4 w-4 text-gray-500" />;
  };

  if (!analyticsData) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
        <span className="ml-2 text-gray-600">Loading analytics...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Analytics Dashboard</h2>
          <p className="text-gray-600 dark:text-gray-300">Comprehensive insights into your supplement journey</p>
        </div>
        <div className="flex space-x-2">
          <select 
            value={timeRange} 
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="week">Last Week</option>
            <option value="month">Last Month</option>
            <option value="quarter">Last Quarter</option>
          </select>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Compliance Rate</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{analyticsData.complianceRate}%</p>
              <p className="text-sm text-gray-500 mt-1">+5% from last month</p>
            </div>
            <Badge className={getComplianceColor(analyticsData.complianceRate)}>
              {analyticsData.complianceRate >= 90 ? 'Excellent' : analyticsData.complianceRate >= 70 ? 'Good' : 'Needs Work'}
            </Badge>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Streak</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{analyticsData.streakData.current} days</p>
              <p className="text-sm text-gray-500 mt-1">Longest: {analyticsData.streakData.longest} days</p>
            </div>
            <Target className="h-8 w-8 text-blue-500" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Research</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{analyticsData.researchParticipation.active}</p>
              <p className="text-sm text-gray-500 mt-1">{analyticsData.researchParticipation.completed} completed</p>
            </div>
            <FlaskConical className="h-8 w-8 text-purple-500" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Supplements</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{supplements.data?.length || 0}</p>
              <p className="text-sm text-gray-500 mt-1">Across {analyticsData.categoryBreakdown.length} categories</p>
            </div>
            <Pill className="h-8 w-8 text-green-500" />
          </div>
        </Card>
      </div>

      {/* Charts and Visualizations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Supplement Usage Chart */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Most Used Supplements</h3>
          <div className="space-y-4">
            {analyticsData.supplementUsage.map((item, index) => (
              <div key={item.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">{item.name}</p>
                    <p className="text-sm text-gray-500">{item.category}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium text-gray-900 dark:text-white">{item.count}</p>
                  <p className="text-sm text-gray-500">intakes</p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Category Breakdown */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Category Distribution</h3>
          <div className="space-y-4">
            {analyticsData.categoryBreakdown.map((category, index) => (
              <div key={category.category} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{category.category}</span>
                  <span className="text-sm text-gray-500">{category.percentage}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${
                      index % 4 === 0 ? 'bg-blue-500' : 
                      index % 4 === 1 ? 'bg-green-500' : 
                      index % 4 === 2 ? 'bg-purple-500' : 'bg-orange-500'
                    }`}
                    style={{ width: `${category.percentage}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Compliance Trends */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Compliance Trends (Last 30 Days)</h3>
        <div className="h-64 flex items-end space-x-1">
          {analyticsData.timeSeriesData.map((day, index) => (
            <div key={day.date} className="flex-1 flex flex-col items-center">
              <div 
                className="w-full bg-blue-500 rounded-t"
                style={{ height: `${(day.compliance / 100) * 200}px` }}
                title={`${day.date}: ${day.compliance}% compliance`}
              />
              {index % 5 === 0 && (
                <span className="text-xs text-gray-500 mt-1 transform rotate-45">
                  {new Date(day.date).getDate()}
                </span>
              )}
            </div>
          ))}
        </div>
        <div className="flex justify-between items-center mt-4 text-sm text-gray-500">
          <span>0%</span>
          <span>Compliance Rate</span>
          <span>100%</span>
        </div>
      </Card>

      {/* Achievements and Goals */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Achievements</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Award className="h-6 w-6 text-green-600" />
              <div>
                <p className="font-medium text-green-800 dark:text-green-200">15-Day Streak!</p>
                <p className="text-sm text-green-600 dark:text-green-300">Consistent daily supplement intake</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <Users className="h-6 w-6 text-blue-600" />
              <div>
                <p className="font-medium text-blue-800 dark:text-blue-200">Research Participant</p>
                <p className="text-sm text-blue-600 dark:text-blue-300">Joined 2 active studies</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <BarChart3 className="h-6 w-6 text-purple-600" />
              <div>
                <p className="font-medium text-purple-800 dark:text-purple-200">Data Contributor</p>
                <p className="text-sm text-purple-600 dark:text-purple-300">100+ data points logged</p>
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Goals & Targets</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Monthly Compliance</span>
                <span className="text-sm text-gray-500">85% / 90%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full" style={{ width: '94%' }} />
              </div>
            </div>
            
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Research Participation</span>
                <span className="text-sm text-gray-500">2 / 3</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-blue-500 h-2 rounded-full" style={{ width: '67%' }} />
              </div>
            </div>
            
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Streak Target</span>
                <span className="text-sm text-gray-500">15 / 30 days</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-purple-500 h-2 rounded-full" style={{ width: '50%' }} />
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
