/**
 * Supplement Tracker Component
 * 
 * Interface for logging and tracking supplement intake
 */

import React, { useState } from 'react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { 
  Plus, 
  Search, 
  Clock, 
  Calendar,
  Pill,
  Edit,
  Trash2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface Supplement {
  id: number;
  name: string;
  brand: string;
  dosage: string;
  frequency: string;
  category: string;
  lastTaken?: string;
  nextDue?: string;
  streak: number;
}

interface IntakeLog {
  id: number;
  supplementId: number;
  supplementName: string;
  dosage: string;
  timestamp: string;
  notes?: string;
}

const SupplementTracker: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'today' | 'schedule' | 'history'>('today');
  const [showAddModal, setShowAddModal] = useState(false);

  // Mock data
  const supplements: Supplement[] = [
    {
      id: 1,
      name: 'Vitamin D3',
      brand: 'Nature Made',
      dosage: '2000 IU',
      frequency: 'Daily',
      category: 'Vitamins',
      lastTaken: '2024-06-20 08:00',
      nextDue: '2024-06-21 08:00',
      streak: 15
    },
    {
      id: 2,
      name: 'Omega-3 Fish Oil',
      brand: 'Nordic Naturals',
      dosage: '1000mg',
      frequency: 'Daily',
      category: 'Fatty Acids',
      lastTaken: '2024-06-20 08:00',
      nextDue: '2024-06-21 08:00',
      streak: 12
    },
    {
      id: 3,
      name: 'Magnesium Glycinate',
      brand: 'Thorne',
      dosage: '400mg',
      frequency: 'Evening',
      category: 'Minerals',
      nextDue: '2024-06-20 21:00',
      streak: 8
    }
  ];

  const todayIntakes: IntakeLog[] = [
    {
      id: 1,
      supplementId: 1,
      supplementName: 'Vitamin D3',
      dosage: '2000 IU',
      timestamp: '2024-06-20 08:00',
      notes: 'With breakfast'
    },
    {
      id: 2,
      supplementId: 2,
      supplementName: 'Omega-3 Fish Oil',
      dosage: '1000mg',
      timestamp: '2024-06-20 08:05'
    }
  ];

  const handleLogIntake = (supplementId: number) => {
    console.log('Logging intake for supplement:', supplementId);
    // Implementation for logging intake
  };

  const handleQuickAdd = () => {
    setShowAddModal(true);
  };

  const isOverdue = (nextDue: string) => {
    return new Date(nextDue) < new Date();
  };

  const isDueToday = (nextDue: string) => {
    const due = new Date(nextDue);
    const today = new Date();
    return due.toDateString() === today.toDateString();
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Supplement Tracker
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mt-1">
              Track your daily supplement intake and build healthy habits
            </p>
          </div>
          <Button onClick={handleQuickAdd}>
            <Plus className="h-4 w-4 mr-2" />
            Add Supplement
          </Button>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 mb-6 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          {[
            { key: 'today', label: 'Today', icon: Calendar },
            { key: 'schedule', label: 'Schedule', icon: Clock },
            { key: 'history', label: 'History', icon: CheckCircle }
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setActiveTab(key as any)}
              className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === key
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <Icon className="h-4 w-4 mr-2" />
              {label}
            </button>
          ))}
        </div>

        {/* Today's View */}
        {activeTab === 'today' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Due Today */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Due Today
              </h3>
              <div className="space-y-4">
                {supplements
                  .filter(s => s.nextDue && isDueToday(s.nextDue))
                  .map((supplement) => (
                    <div key={supplement.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center">
                        <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg mr-4">
                          <Pill className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {supplement.name}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {supplement.dosage} • {supplement.brand}
                          </p>
                          <div className="flex items-center mt-1">
                            <Badge variant="outline" className="text-xs">
                              {supplement.streak} day streak
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {isOverdue(supplement.nextDue!) && (
                          <AlertCircle className="h-5 w-5 text-red-500" />
                        )}
                        <Button 
                          size="sm"
                          onClick={() => handleLogIntake(supplement.id)}
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Take
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </Card>

            {/* Today's Intake Log */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Today's Intake
              </h3>
              <div className="space-y-4">
                {todayIntakes.map((intake) => (
                  <div key={intake.id} className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mr-3" />
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {intake.supplementName}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {intake.dosage} at {new Date(intake.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </p>
                        {intake.notes && (
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {intake.notes}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        )}

        {/* Schedule View */}
        {activeTab === 'schedule' && (
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Supplement Schedule
              </h3>
              <Button variant="outline">
                <Search className="h-4 w-4 mr-2" />
                Search Supplements
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {supplements.map((supplement) => (
                <Card key={supplement.id} className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {supplement.name}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {supplement.brand}
                      </p>
                    </div>
                    <Badge variant="outline">{supplement.category}</Badge>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Dosage:</span>
                      <span className="font-medium">{supplement.dosage}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Frequency:</span>
                      <span className="font-medium">{supplement.frequency}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Streak:</span>
                      <span className="font-medium">{supplement.streak} days</span>
                    </div>
                  </div>
                  
                  <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex space-x-2">
                      <Button size="sm" className="flex-1">
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Log Intake
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </Card>
        )}

        {/* History View */}
        {activeTab === 'history' && (
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
              Intake History
            </h3>
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                History view coming soon...
              </p>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default SupplementTracker;
