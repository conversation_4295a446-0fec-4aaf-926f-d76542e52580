/**
 * HealthDashboard Component Tests
 * 
 * Test-driven development tests for the HealthDashboard component.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { configureStore } from '@reduxjs/toolkit';

// Components and store
import HealthDashboard from '../HealthDashboard';
import authReducer from '@/store/slices/authSlice';
import healthReducer from '@/store/slices/healthSlice';
import uiReducer from '@/store/slices/uiSlice';
import { lightTheme } from '@/styles/theme';

// Test data
const mockHealthMetrics = [
  {
    id: '1',
    name: 'Heart Rate',
    category: 'vitals',
    unit: 'bpm',
    current_value: 72,
    target_min: 60,
    target_max: 100,
    trend: 0.02,
    status: 'optimal' as const,
    recorded_at: '2024-01-05T08:00:00Z',
    data_source: 'Fitbit',
    is_synced: true,
    supplement_correlation: 0.3,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-05T08:00:00Z',
  },
  {
    id: '2',
    name: 'Sleep Hours',
    category: 'sleep',
    unit: 'hours',
    current_value: 7.5,
    target_min: 7,
    target_max: 9,
    trend: -0.1,
    status: 'warning' as const,
    recorded_at: '2024-01-05T07:00:00Z',
    data_source: 'Apple Health',
    is_synced: true,
    supplement_correlation: 0.6,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-05T07:00:00Z',
  },
  {
    id: '3',
    name: 'Weight',
    category: 'fitness',
    unit: 'kg',
    current_value: 70.5,
    target_min: 65,
    target_max: 75,
    trend: 0.05,
    status: 'optimal' as const,
    recorded_at: '2024-01-05T06:00:00Z',
    data_source: 'Manual',
    is_synced: false,
    supplement_correlation: 0.1,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-05T06:00:00Z',
  },
];

const mockConnectedDevices = [
  {
    id: '1',
    device_id: 'fitbit_123',
    device_name: 'Fitbit Charge 5',
    device_type: 'fitness_tracker',
    is_connected: true,
    last_sync: '2024-01-05T08:00:00Z',
    sync_frequency: 'hourly',
    metrics_synced: 5,
    data_points: 1250,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-05T08:00:00Z',
  },
  {
    id: '2',
    device_id: 'apple_health_456',
    device_name: 'Apple Health',
    device_type: 'health_platform',
    is_connected: true,
    last_sync: '2024-01-05T07:30:00Z',
    sync_frequency: 'daily',
    metrics_synced: 8,
    data_points: 2100,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-05T07:30:00Z',
  },
];

const mockCorrelations = [
  {
    id: '1',
    health_metric: 'Sleep Hours',
    supplement: 'Magnesium Glycinate',
    strength: 0.75,
    confidence: 0.9,
    description: 'Strong positive correlation between magnesium intake and sleep duration',
    time_period: '30 days',
    data_points: 30,
  },
  {
    id: '2',
    health_metric: 'Heart Rate',
    supplement: 'Omega-3',
    strength: -0.4,
    confidence: 0.7,
    description: 'Moderate negative correlation between omega-3 intake and resting heart rate',
    time_period: '30 days',
    data_points: 28,
  },
];

// Test store setup
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authReducer,
      health: healthReducer,
      ui: uiReducer,
    },
    preloadedState: {
      auth: {
        user: {
          id: '1',
          email: '<EMAIL>',
          full_name: 'Test User',
          is_active: true,
          is_superuser: false,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        token: 'test-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
      health: {
        metrics: mockHealthMetrics,
        connectedDevices: mockConnectedDevices,
        correlations: mockCorrelations,
        biomarkers: [],
        isLoading: false,
        isSyncing: false,
        deviceSyncStatus: {},
        error: null,
        filters: {
          category: '',
          timeRange: '30d',
        },
        lastUpdated: '2024-01-05T12:00:00Z',
      },
      ui: {
        theme: 'light',
        sidebarOpen: true,
        notifications: [],
        modals: [],
        globalLoading: false,
        pageTitle: 'Health Metrics',
        breadcrumbs: [],
        searchQuery: '',
        mobileMenuOpen: false,
      },
      ...initialState,
    },
  });
};

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode; store?: any }> = ({ 
  children, 
  store = createTestStore() 
}) => (
  <Provider store={store}>
    <BrowserRouter>
      <ThemeProvider theme={lightTheme}>
        {children}
      </ThemeProvider>
    </BrowserRouter>
  </Provider>
);

describe('HealthDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render dashboard header correctly', () => {
      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('Health Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Track your health metrics and discover correlations with your supplements')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /add metric/i })).toBeInTheDocument();
    });

    it('should render filter controls', () => {
      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      expect(screen.getByDisplayValue('All Categories')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Last 30 days')).toBeInTheDocument();
    });

    it('should render tab navigation', () => {
      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('Overview')).toBeInTheDocument();
      expect(screen.getByText('Health Metrics')).toBeInTheDocument();
      expect(screen.getByText('Correlations')).toBeInTheDocument();
      expect(screen.getByText('Connected Devices')).toBeInTheDocument();
    });

    it('should render health metrics in overview tab', () => {
      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('Heart Rate')).toBeInTheDocument();
      expect(screen.getByText('Sleep Hours')).toBeInTheDocument();
      expect(screen.getByText('Weight')).toBeInTheDocument();
    });

    it('should render charts in overview tab', () => {
      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('Health Metrics Trends')).toBeInTheDocument();
      expect(screen.getByText('Metrics by Category')).toBeInTheDocument();
    });
  });

  describe('Tab Navigation', () => {
    it('should switch to metrics tab', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      const metricsTab = screen.getByText('Health Metrics');
      await user.click(metricsTab);

      // Should show all metrics
      expect(screen.getAllByText('Heart Rate')).toHaveLength(1);
      expect(screen.getAllByText('Sleep Hours')).toHaveLength(1);
      expect(screen.getAllByText('Weight')).toHaveLength(1);
    });

    it('should switch to correlations tab', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      const correlationsTab = screen.getByText('Correlations');
      await user.click(correlationsTab);

      expect(screen.getByText('Health-Supplement Correlations')).toBeInTheDocument();
      expect(screen.getByText('Sleep Hours ↔ Magnesium Glycinate')).toBeInTheDocument();
      expect(screen.getByText('Heart Rate ↔ Omega-3')).toBeInTheDocument();
    });

    it('should switch to devices tab', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      const devicesTab = screen.getByText('Connected Devices');
      await user.click(devicesTab);

      expect(screen.getByText('Wearable Device Integration')).toBeInTheDocument();
      expect(screen.getByText('Fitbit')).toBeInTheDocument();
      expect(screen.getByText('Apple Health')).toBeInTheDocument();
    });
  });

  describe('Filter Interactions', () => {
    it('should handle category filter changes', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      const categorySelect = screen.getByDisplayValue('All Categories');
      await user.selectOptions(categorySelect, 'Vital Signs');

      expect(categorySelect).toHaveValue('vitals');
    });

    it('should handle time range filter changes', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      const timeRangeSelect = screen.getByDisplayValue('Last 30 days');
      await user.selectOptions(timeRangeSelect, 'Last 7 days');

      expect(timeRangeSelect).toHaveValue('7d');
    });
  });

  describe('Metric Management', () => {
    it('should handle add metric button click', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      const addButton = screen.getByRole('button', { name: /add metric/i });
      await user.click(addButton);

      // Would verify modal opening or navigation in real implementation
    });
  });

  describe('Loading States', () => {
    it('should show loading spinner when data is loading', () => {
      const storeWithLoading = createTestStore({
        health: {
          metrics: [],
          connectedDevices: [],
          correlations: [],
          biomarkers: [],
          isLoading: true,
          isSyncing: false,
          deviceSyncStatus: {},
          error: null,
          filters: {
            category: '',
            timeRange: '30d',
          },
          lastUpdated: null,
        },
      });

      render(
        <TestWrapper store={storeWithLoading}>
          <HealthDashboard />
        </TestWrapper>
      );

      expect(screen.getByRole('status')).toBeInTheDocument(); // Loading spinner
    });

    it('should show empty state when no metrics are available', () => {
      const storeWithoutData = createTestStore({
        health: {
          metrics: [],
          connectedDevices: [],
          correlations: [],
          biomarkers: [],
          isLoading: false,
          isSyncing: false,
          deviceSyncStatus: {},
          error: null,
          filters: {
            category: '',
            timeRange: '30d',
          },
          lastUpdated: null,
        },
      });

      render(
        <TestWrapper store={storeWithoutData}>
          <HealthDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('No health metrics yet')).toBeInTheDocument();
      expect(screen.getByText('Start tracking your health metrics to see insights and correlations with your supplements.')).toBeInTheDocument();
    });

    it('should show login prompt when user is not authenticated', () => {
      const storeWithoutUser = createTestStore({
        auth: {
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        },
      });

      render(
        <TestWrapper store={storeWithoutUser}>
          <HealthDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('Please log in to view health dashboard')).toBeInTheDocument();
      expect(screen.getByText('Sign in to access your personal health metrics and supplement correlations.')).toBeInTheDocument();
    });
  });

  describe('Correlations Display', () => {
    it('should display correlation strength correctly', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      const correlationsTab = screen.getByText('Correlations');
      await user.click(correlationsTab);

      expect(screen.getByText('75.0%')).toBeInTheDocument(); // Strong correlation
      expect(screen.getByText('-40.0%')).toBeInTheDocument(); // Moderate negative correlation
    });

    it('should show empty correlations state', async () => {
      const user = userEvent.setup();
      const storeWithoutCorrelations = createTestStore({
        health: {
          ...createTestStore().getState().health,
          correlations: [],
        },
      });

      render(
        <TestWrapper store={storeWithoutCorrelations}>
          <HealthDashboard />
        </TestWrapper>
      );

      const correlationsTab = screen.getByText('Correlations');
      await user.click(correlationsTab);

      expect(screen.getByText('No correlations found')).toBeInTheDocument();
      expect(screen.getByText('We need more data to identify correlations between your health metrics and supplements.')).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('should handle mobile layout', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      // Should still render main content
      expect(screen.getByText('Health Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Heart Rate')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle health data errors gracefully', () => {
      const storeWithError = createTestStore({
        health: {
          metrics: [],
          connectedDevices: [],
          correlations: [],
          biomarkers: [],
          isLoading: false,
          isSyncing: false,
          deviceSyncStatus: {},
          error: 'Failed to load health data',
          filters: {
            category: '',
            timeRange: '30d',
          },
          lastUpdated: null,
        },
      });

      render(
        <TestWrapper store={storeWithError}>
          <HealthDashboard />
        </TestWrapper>
      );

      // Should show empty state when there's an error
      expect(screen.getByText('No health metrics yet')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper heading structure', () => {
      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Health Dashboard');
    });

    it('should have accessible form controls', () => {
      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      const categorySelect = screen.getByDisplayValue('All Categories');
      const timeRangeSelect = screen.getByDisplayValue('Last 30 days');
      
      expect(categorySelect).toBeInTheDocument();
      expect(timeRangeSelect).toBeInTheDocument();
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <HealthDashboard />
        </TestWrapper>
      );

      // Tab through interactive elements
      await user.tab();
      expect(screen.getByDisplayValue('All Categories')).toHaveFocus();

      await user.tab();
      expect(screen.getByDisplayValue('Last 30 days')).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('button', { name: /add metric/i })).toHaveFocus();
    });
  });
});
