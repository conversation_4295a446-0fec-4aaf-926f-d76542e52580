/**
 * Health Page Component
 * 
 * Main health metrics page with comprehensive health tracking and correlation analysis.
 */

import React, { useEffect } from 'react';
import { useAppDispatch } from '@/store';
import { setPageTitle, setBreadcrumbs } from '@/store/slices/uiSlice';
import styled from 'styled-components';

// Components
import HealthDashboard from '@/components/health/HealthDashboard';

// Styled components
const PageContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
`;

const HealthPage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setPageTitle('Health Metrics'));
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', href: '/dashboard' },
      { label: 'Health Metrics' },
    ]));
  }, [dispatch]);

  return (
    <PageContainer>
      <HealthDashboard />
    </PageContainer>
  );
};

export default HealthPage;
