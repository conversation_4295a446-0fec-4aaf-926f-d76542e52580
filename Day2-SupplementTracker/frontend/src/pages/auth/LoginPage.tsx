/**
 * Login Page Component
 * 
 * User authentication page with login form and validation.
 */

import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import styled from 'styled-components';
import { useAppDispatch, useAppSelector } from '@/store';
import { loginUser, clearError, selectAuthLoading, selectAuthError } from '@/store/slices/authSlice';
import { showErrorNotification } from '@/store/slices/uiSlice';
import { LoginRequest } from '@/types/api';

// Components
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Card from '@/components/common/Card';
import LoadingSpinner from '@/components/common/LoadingSpinner';

// Styled components
const LoginContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary}20, ${props => props.theme.colors.secondary}20);
  padding: 1rem;
`;

const LoginCard = styled(Card)`
  width: 100%;
  max-width: 400px;
  padding: 2rem;
`;

const Logo = styled.div`
  text-align: center;
  margin-bottom: 2rem;
  
  h1 {
    color: ${props => props.theme.colors.primary};
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 0.9rem;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
`;

const ErrorMessage = styled.div`
  color: ${props => props.theme.colors.error};
  font-size: 0.8rem;
  margin-top: 0.25rem;
`;

const Footer = styled.div`
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid ${props => props.theme.colors.border};
  
  p {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 0.9rem;
    
    a {
      color: ${props => props.theme.colors.primary};
      text-decoration: none;
      font-weight: 500;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
`;

const ForgotPassword = styled.div`
  text-align: right;
  margin-top: 0.5rem;
  
  a {
    color: ${props => props.theme.colors.primary};
    text-decoration: none;
    font-size: 0.9rem;
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

// Form validation rules
const validationRules = {
  email: {
    required: 'Email is required',
    pattern: {
      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
      message: 'Invalid email address',
    },
  },
  password: {
    required: 'Password is required',
    minLength: {
      value: 6,
      message: 'Password must be at least 6 characters',
    },
  },
};

const LoginPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const isLoading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setFocus,
  } = useForm<LoginRequest>();

  // Clear errors on component mount
  useEffect(() => {
    dispatch(clearError());
    setFocus('email');
  }, [dispatch, setFocus]);

  // Show error notification when error changes
  useEffect(() => {
    if (error) {
      dispatch(showErrorNotification(error, 'Login Failed'));
    }
  }, [error, dispatch]);

  const onSubmit = async (data: LoginRequest) => {
    try {
      const result = await dispatch(loginUser(data)).unwrap();
      
      // Redirect to intended page or dashboard
      const from = location.state?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    } catch (error) {
      // Error is handled by the slice and notification
      console.error('Login failed:', error);
    }
  };

  return (
    <LoginContainer>
      <LoginCard>
        <Logo>
          <h1>Supplement Tracker</h1>
          <p>Evidence-based supplement research platform</p>
        </Logo>

        <Form onSubmit={handleSubmit(onSubmit)}>
          <FormGroup>
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              {...register('email', validationRules.email)}
              error={!!errors.email}
              disabled={isLoading}
            />
            {errors.email && (
              <ErrorMessage>{errors.email.message}</ErrorMessage>
            )}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              placeholder="Enter your password"
              {...register('password', validationRules.password)}
              error={!!errors.password}
              disabled={isLoading}
            />
            {errors.password && (
              <ErrorMessage>{errors.password.message}</ErrorMessage>
            )}
            <ForgotPassword>
              <Link to="/forgot-password">Forgot your password?</Link>
            </ForgotPassword>
          </FormGroup>

          <Button
            type="submit"
            variant="primary"
            size="large"
            disabled={isLoading}
            fullWidth
          >
            {isLoading ? <LoadingSpinner size="small" /> : 'Sign In'}
          </Button>
        </Form>

        <Footer>
          <p>
            Don't have an account?{' '}
            <Link to="/register">Create one here</Link>
          </p>
        </Footer>
      </LoginCard>
    </LoginContainer>
  );
};

export default LoginPage;
