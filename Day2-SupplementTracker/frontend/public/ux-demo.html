<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#2563eb" />
    <meta name="description" content="Supplement Tracker UX Demo" />
    <title>Supplement Tracker - UX Demo</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
      .btn {
        @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50;
      }
      
      .btn-primary {
        @apply bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2;
      }
      
      .btn-secondary {
        @apply bg-gray-200 text-gray-900 hover:bg-gray-300 h-10 px-4 py-2;
      }
      
      .btn-outline {
        @apply border border-gray-300 bg-transparent hover:bg-gray-100 h-10 px-4 py-2;
      }
      
      .card {
        @apply rounded-lg border bg-white shadow-sm p-6;
      }
      
      .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
      }
      
      .badge-primary {
        @apply bg-blue-100 text-blue-800;
      }
      
      .badge-secondary {
        @apply bg-gray-100 text-gray-800;
      }
      
      .badge-success {
        @apply bg-green-100 text-green-800;
      }
      
      .demo-section {
        @apply mb-12 p-8 bg-white rounded-lg shadow-sm border;
      }
      
      .feature-grid {
        @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
      }
      
      .stat-card {
        @apply bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg text-center;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <div class="min-h-screen">
      <!-- Header -->
      <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between items-center h-16">
            <div class="flex items-center">
              <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center text-white text-lg mr-3">🏥</div>
              <h1 class="text-xl font-semibold text-gray-900">Supplement Tracker - UX Demo</h1>
            </div>
            <a href="/" class="btn btn-outline">← Back to Home</a>
          </div>
        </div>
      </header>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Hero Section -->
        <div class="text-center mb-12">
          <h1 class="text-4xl font-bold text-gray-900 mb-4">
            🎨 UX Implementation Showcase
          </h1>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-6">
            Experience the modern, responsive user interface built with MVP template components. 
            This demo showcases the core user flows for supplement tracking and research participation.
          </p>
          <div class="flex items-center justify-center space-x-2">
            <span class="badge badge-success">✓ UX Complete</span>
            <span class="badge badge-secondary">React + TypeScript</span>
            <span class="badge badge-primary">MVP Components</span>
          </div>
        </div>

        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <div class="stat-card">
            <div class="text-3xl font-bold text-blue-600 mb-2">4</div>
            <div class="text-sm text-gray-600">Core UX Components</div>
          </div>
          <div class="stat-card">
            <div class="text-3xl font-bold text-green-600 mb-2">50+</div>
            <div class="text-sm text-gray-600">MVP UI Components</div>
          </div>
          <div class="stat-card">
            <div class="text-3xl font-bold text-purple-600 mb-2">100%</div>
            <div class="text-sm text-gray-600">Responsive Design</div>
          </div>
          <div class="stat-card">
            <div class="text-3xl font-bold text-orange-600 mb-2">✅</div>
            <div class="text-sm text-gray-600">API Integration</div>
          </div>
        </div>

        <!-- Dashboard Demo -->
        <div class="demo-section">
          <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <div class="h-8 w-8 bg-blue-500 rounded-lg flex items-center justify-center text-white mr-3">📊</div>
            Dashboard Component
          </h2>
          <p class="text-gray-600 mb-6">
            Central hub showing quick stats, today's supplements, active research, and quick actions.
          </p>
          
          <div class="bg-gray-50 p-6 rounded-lg mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div class="bg-white p-4 rounded-lg shadow-sm">
                <div class="flex items-center">
                  <div class="p-2 bg-blue-100 rounded-lg mr-3">💊</div>
                  <div>
                    <p class="text-sm text-gray-600">Today's Intakes</p>
                    <p class="text-2xl font-bold text-gray-900">3</p>
                  </div>
                </div>
              </div>
              <div class="bg-white p-4 rounded-lg shadow-sm">
                <div class="flex items-center">
                  <div class="p-2 bg-green-100 rounded-lg mr-3">🧪</div>
                  <div>
                    <p class="text-sm text-gray-600">Active Studies</p>
                    <p class="text-2xl font-bold text-gray-900">2</p>
                  </div>
                </div>
              </div>
              <div class="bg-white p-4 rounded-lg shadow-sm">
                <div class="flex items-center">
                  <div class="p-2 bg-purple-100 rounded-lg mr-3">📈</div>
                  <div>
                    <p class="text-sm text-gray-600">Weekly Progress</p>
                    <p class="text-2xl font-bold text-gray-900">85%</p>
                  </div>
                </div>
              </div>
              <div class="bg-white p-4 rounded-lg shadow-sm">
                <div class="flex items-center">
                  <div class="p-2 bg-orange-100 rounded-lg mr-3">👥</div>
                  <div>
                    <p class="text-sm text-gray-600">Community Rank</p>
                    <p class="text-2xl font-bold text-gray-900">#42</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="bg-white p-6 rounded-lg shadow-sm">
                <h3 class="text-lg font-semibold mb-4">Today's Supplements</h3>
                <div class="space-y-3">
                  <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                      <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                      <div>
                        <p class="font-medium">Vitamin D3</p>
                        <p class="text-sm text-gray-600">2000 IU at 8:00 AM</p>
                      </div>
                    </div>
                    <span class="badge badge-success">Taken</span>
                  </div>
                  <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                      <div class="w-3 h-3 bg-gray-300 rounded-full mr-3"></div>
                      <div>
                        <p class="font-medium">Magnesium</p>
                        <p class="text-sm text-gray-600">400mg at 9:00 PM</p>
                      </div>
                    </div>
                    <span class="badge badge-secondary">Pending</span>
                  </div>
                </div>
              </div>
              
              <div class="bg-white p-6 rounded-lg shadow-sm">
                <h3 class="text-lg font-semibold mb-4">Active Research</h3>
                <div class="space-y-4">
                  <div class="p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-medium mb-2">Vitamin D and Sleep Quality</h4>
                    <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                      <div class="bg-blue-600 h-2 rounded-full" style="width: 60%"></div>
                    </div>
                    <p class="text-sm text-gray-600">60% complete</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="flex flex-wrap gap-2">
            <span class="badge badge-success">✓ Quick Stats</span>
            <span class="badge badge-success">✓ Today's Schedule</span>
            <span class="badge badge-success">✓ Research Progress</span>
            <span class="badge badge-success">✓ Quick Actions</span>
          </div>
        </div>

        <!-- Supplement Tracker Demo -->
        <div class="demo-section">
          <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <div class="h-8 w-8 bg-green-500 rounded-lg flex items-center justify-center text-white mr-3">💊</div>
            Supplement Tracker Component
          </h2>
          <p class="text-gray-600 mb-6">
            Comprehensive interface for logging supplement intake, managing schedules, and tracking history.
          </p>
          
          <div class="bg-gray-50 p-6 rounded-lg mb-6">
            <div class="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg w-fit">
              <button class="px-4 py-2 bg-white text-gray-900 rounded-md text-sm font-medium shadow-sm">Today</button>
              <button class="px-4 py-2 text-gray-600 rounded-md text-sm font-medium">Schedule</button>
              <button class="px-4 py-2 text-gray-600 rounded-md text-sm font-medium">History</button>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="bg-white p-6 rounded-lg shadow-sm">
                <h3 class="text-lg font-semibold mb-4">Due Today</h3>
                <div class="space-y-4">
                  <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                      <div class="p-2 bg-blue-100 rounded-lg mr-4">💊</div>
                      <div>
                        <h4 class="font-medium">Vitamin D3</h4>
                        <p class="text-sm text-gray-600">2000 IU • Nature Made</p>
                        <span class="badge badge-secondary text-xs">15 day streak</span>
                      </div>
                    </div>
                    <button class="btn btn-primary">✓ Take</button>
                  </div>
                </div>
              </div>
              
              <div class="bg-white p-6 rounded-lg shadow-sm">
                <h3 class="text-lg font-semibold mb-4">Today's Intake</h3>
                <div class="space-y-4">
                  <div class="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                    <div class="flex items-center">
                      <div class="text-green-600 mr-3">✓</div>
                      <div>
                        <h4 class="font-medium">Omega-3 Fish Oil</h4>
                        <p class="text-sm text-gray-600">1000mg at 8:05 AM</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="flex flex-wrap gap-2">
            <span class="badge badge-success">✓ Daily Logging</span>
            <span class="badge badge-success">✓ Schedule Management</span>
            <span class="badge badge-success">✓ Streak Tracking</span>
            <span class="badge badge-success">✓ History View</span>
          </div>
        </div>

        <!-- Research Hub Demo -->
        <div class="demo-section">
          <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <div class="h-8 w-8 bg-purple-500 rounded-lg flex items-center justify-center text-white mr-3">🧪</div>
            Research Hub Component
          </h2>
          <p class="text-gray-600 mb-6">
            Platform for discovering supplement research studies and tracking participation progress.
          </p>
          
          <div class="bg-gray-50 p-6 rounded-lg mb-6">
            <div class="bg-white p-6 rounded-lg shadow-sm">
              <div class="flex items-start justify-between mb-4">
                <div class="flex-1">
                  <h3 class="text-lg font-semibold mb-2">Vitamin D3 and Sleep Quality Study</h3>
                  <p class="text-gray-600 text-sm mb-3">
                    Investigating the relationship between Vitamin D3 supplementation and sleep quality metrics over 12 weeks.
                  </p>
                </div>
                <div class="flex items-center ml-4">
                  <span class="text-yellow-400 mr-1">⭐</span>
                  <span class="text-sm font-medium">4.8</span>
                </div>
              </div>
              
              <div class="flex flex-wrap gap-2 mb-4">
                <span class="badge badge-primary">recruiting</span>
                <span class="badge badge-success">beginner</span>
                <span class="badge badge-secondary">Vitamins</span>
              </div>
              
              <div class="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div class="flex items-center">
                  <span class="mr-2">⏱️</span>
                  <span>12 weeks</span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2">👥</span>
                  <span>156/200 participants</span>
                </div>
              </div>
              
              <div class="flex items-center justify-between">
                <div class="w-full bg-gray-200 rounded-full h-2 mr-4">
                  <div class="bg-blue-600 h-2 rounded-full" style="width: 78%"></div>
                </div>
                <button class="btn btn-primary">Join Study →</button>
              </div>
            </div>
          </div>
          
          <div class="flex flex-wrap gap-2">
            <span class="badge badge-success">✓ Study Discovery</span>
            <span class="badge badge-success">✓ Research Participation</span>
            <span class="badge badge-success">✓ Progress Tracking</span>
            <span class="badge badge-success">✓ Study Creation</span>
          </div>
        </div>

        <!-- Data Integration Demo -->
        <div class="demo-section">
          <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <div class="h-8 w-8 bg-orange-500 rounded-lg flex items-center justify-center text-white mr-3">🔗</div>
            Data Integration & Backend Connectivity
          </h2>
          <p class="text-gray-600 mb-6">
            Real-time API integration with graceful fallback to mock data when backend is unavailable.
          </p>

          <div class="bg-gray-50 p-6 rounded-lg mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div class="bg-white p-4 rounded-lg shadow-sm">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg mr-3">🏥</div>
                    <h4 class="font-medium">Health Check</h4>
                  </div>
                  <span class="text-green-500">✓</span>
                </div>
                <p class="text-sm text-gray-600">API connectivity test</p>
              </div>

              <div class="bg-white p-4 rounded-lg shadow-sm">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg mr-3">💊</div>
                    <h4 class="font-medium">Supplements</h4>
                  </div>
                  <span class="text-green-500">✓</span>
                </div>
                <p class="text-sm text-gray-600">Real-time supplement data</p>
              </div>

              <div class="bg-white p-4 rounded-lg shadow-sm">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg mr-3">🧪</div>
                    <h4 class="font-medium">Research</h4>
                  </div>
                  <span class="text-green-500">✓</span>
                </div>
                <p class="text-sm text-gray-600">Live research studies</p>
              </div>

              <div class="bg-white p-4 rounded-lg shadow-sm">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <div class="p-2 bg-orange-100 rounded-lg mr-3">📊</div>
                    <h4 class="font-medium">Analytics</h4>
                  </div>
                  <span class="text-green-500">✓</span>
                </div>
                <p class="text-sm text-gray-600">Data insights & trends</p>
              </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm">
              <h3 class="text-lg font-semibold mb-4">Integration Features</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 class="font-medium text-green-600 mb-2">✅ Real-Time Data Loading</h4>
                  <p class="text-sm text-gray-600">Components automatically fetch and display live data from backend APIs</p>
                </div>
                <div>
                  <h4 class="font-medium text-blue-600 mb-2">🔄 Graceful Fallback</h4>
                  <p class="text-sm text-gray-600">Seamless switch to mock data when API is unavailable</p>
                </div>
                <div>
                  <h4 class="font-medium text-purple-600 mb-2">⚡ Performance Optimized</h4>
                  <p class="text-sm text-gray-600">Efficient data loading with proper caching and error handling</p>
                </div>
                <div>
                  <h4 class="font-medium text-orange-600 mb-2">🔧 TypeScript Integration</h4>
                  <p class="text-sm text-gray-600">Full type safety with comprehensive error handling</p>
                </div>
              </div>
            </div>
          </div>

          <div class="flex flex-wrap gap-2">
            <span class="badge badge-success">✓ API Connectivity</span>
            <span class="badge badge-success">✓ Real-Time Data</span>
            <span class="badge badge-success">✓ Fallback Handling</span>
            <span class="badge badge-success">✓ Type Safety</span>
          </div>
        </div>

        <!-- Implementation Status -->
        <div class="demo-section bg-gradient-to-r from-green-50 to-blue-50">
          <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">
            🎉 UX Implementation Status
          </h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div class="text-center">
              <div class="text-4xl mb-2">✅</div>
              <h3 class="font-semibold mb-2">Dashboard</h3>
              <p class="text-sm text-gray-600">Complete with stats, schedule, and quick actions</p>
            </div>
            <div class="text-center">
              <div class="text-4xl mb-2">✅</div>
              <h3 class="font-semibold mb-2">Supplement Tracker</h3>
              <p class="text-sm text-gray-600">Full logging, scheduling, and history features</p>
            </div>
            <div class="text-center">
              <div class="text-4xl mb-2">✅</div>
              <h3 class="font-semibold mb-2">Research Hub</h3>
              <p class="text-sm text-gray-600">Study discovery and participation interface</p>
            </div>
          </div>
          
          <div class="text-center">
            <p class="text-lg text-gray-700 mb-6">
              All core UX components are implemented and ready for backend integration!
            </p>
            <div class="space-x-4">
              <a href="/login" class="btn btn-primary">Try the Full App</a>
              <a href="/" class="btn btn-outline">Back to Home</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
