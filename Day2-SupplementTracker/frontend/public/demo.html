<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supplement Tracker - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .card h3 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }
        
        .card p {
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        
        .api-demo {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .api-demo h3 {
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .api-response {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .status {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 0.5rem;
        }
        
        .status.online {
            background: #d4edda;
            color: #155724;
        }
        
        .status.offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        .footer {
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 3rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 Supplement Tracker</h1>
            <p>Modern supplement tracking with health metrics integration</p>
        </div>
        
        <div class="cards">
            <div class="card">
                <h3>🎯 Modern UI Components</h3>
                <p>Built with React, TypeScript, and Tailwind CSS. Features dark/light themes, responsive design, and accessibility compliance (WCAG 2.1 AA).</p>
                <p><strong>Status:</strong> <span class="status online">Ready</span></p>
            </div>
            
            <div class="card">
                <h3>🌐 Internationalization</h3>
                <p>Multi-language support with English and Thai translations. Easy to extend for additional languages with react-i18next.</p>
                <p><strong>Languages:</strong> English, ไทย</p>
            </div>
            
            <div class="card">
                <h3>🔧 Backend API</h3>
                <p>FastAPI backend with automatic documentation, CORS support, and health monitoring endpoints.</p>
                <p><strong>API Status:</strong> <span id="api-status" class="status offline">Checking...</span></p>
            </div>
            
            <div class="card">
                <h3>📊 Health Metrics</h3>
                <p>Integration with wearable devices, health tracking, and analytics dashboard with comprehensive insights.</p>
                <p><strong>Features:</strong> Heart rate, sleep, steps, weight tracking</p>
            </div>
            
            <div class="card">
                <h3>🔒 Enterprise Security</h3>
                <p>Authentication, authorization, and data protection with industry-standard security practices.</p>
                <p><strong>Auth:</strong> JWT, OAuth2, Role-based access</p>
            </div>
            
            <div class="card">
                <h3>🚀 DevOps Ready</h3>
                <p>Docker containerization, Traefik reverse proxy, and automated deployment with health checks.</p>
                <p><strong>Infrastructure:</strong> Docker, Traefik, CI/CD</p>
            </div>
        </div>
        
        <div class="api-demo">
            <h3>🔌 API Demo</h3>
            <p>Test the backend API endpoints:</p>
            <button class="btn" onclick="testAPI()">Test API Connection</button>
            <button class="btn" onclick="getSupplements()">Get Supplements</button>
            <button class="btn" onclick="getHealth()">Get Health Data</button>
            <div id="api-response" class="api-response">Click a button to test the API...</div>
        </div>
        
        <div class="footer">
            <p>🌟 Supplement Tracker - Built with modern web technologies</p>
            <p>Traefik Dashboard: <a href="http://traefik.pills.localhost:9081" style="color: #fff;">http://traefik.pills.localhost:9081</a></p>
        </div>
    </div>

    <script>
        // Check API status on load
        window.onload = function() {
            checkAPIStatus();
        };
        
        async function checkAPIStatus() {
            try {
                const response = await fetch('http://api.pills.localhost:9080/health/');
                if (response.ok) {
                    document.getElementById('api-status').textContent = 'Online';
                    document.getElementById('api-status').className = 'status online';
                } else {
                    throw new Error('API not responding');
                }
            } catch (error) {
                document.getElementById('api-status').textContent = 'Offline';
                document.getElementById('api-status').className = 'status offline';
            }
        }
        
        async function testAPI() {
            const responseDiv = document.getElementById('api-response');
            responseDiv.textContent = 'Testing API connection...';
            
            try {
                const response = await fetch('http://api.pills.localhost:9080/');
                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseDiv.textContent = `Error: ${error.message}`;
            }
        }
        
        async function getSupplements() {
            const responseDiv = document.getElementById('api-response');
            responseDiv.textContent = 'Fetching supplements...';
            
            try {
                const response = await fetch('http://api.pills.localhost:9080/api/v1/supplements/');
                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseDiv.textContent = `Error: ${error.message}`;
            }
        }
        
        async function getHealth() {
            const responseDiv = document.getElementById('api-response');
            responseDiv.textContent = 'Fetching health metrics...';
            
            try {
                const response = await fetch('http://api.pills.localhost:9080/api/v1/health/');
                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseDiv.textContent = `Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
