<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#2563eb" />
    <meta name="description" content="Supplement Tracker - Complete Demo Hub" />
    <title>Supplement Tracker - Demo Hub</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
      .btn {
        @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50;
      }
      
      .btn-primary {
        @apply bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2;
      }
      
      .btn-secondary {
        @apply bg-gray-200 text-gray-900 hover:bg-gray-300 h-10 px-4 py-2;
      }
      
      .btn-success {
        @apply bg-green-600 text-white hover:bg-green-700 h-10 px-4 py-2;
      }
      
      .btn-purple {
        @apply bg-purple-600 text-white hover:bg-purple-700 h-10 px-4 py-2;
      }
      
      .btn-orange {
        @apply bg-orange-600 text-white hover:bg-orange-700 h-10 px-4 py-2;
      }
      
      .card {
        @apply rounded-lg border bg-white shadow-sm p-6 hover:shadow-lg transition-shadow;
      }
      
      .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
      }
      
      .badge-success {
        @apply bg-green-100 text-green-800;
      }
      
      .badge-primary {
        @apply bg-blue-100 text-blue-800;
      }
      
      .badge-purple {
        @apply bg-purple-100 text-purple-800;
      }
      
      .badge-orange {
        @apply bg-orange-100 text-orange-800;
      }
      
      .feature-grid {
        @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
      }
      
      .hero-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <div class="min-h-screen">
      <!-- Hero Section -->
      <div class="hero-gradient text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
              🏥 Supplement Tracker
            </h1>
            <p class="text-xl md:text-2xl mb-8 opacity-90">
              Complete Demo Hub - Production-Ready Full-Stack Application
            </p>
            <div class="flex flex-wrap justify-center gap-4 mb-8">
              <span class="badge badge-success">✅ Full-Stack Complete</span>
              <span class="badge badge-primary">🎨 Professional UX</span>
              <span class="badge badge-purple">🔗 Real-Time API</span>
              <span class="badge badge-orange">📊 Advanced Analytics</span>
            </div>
            <div class="flex flex-wrap justify-center gap-4">
              <a href="#demos" class="btn btn-primary btn-lg">Explore Demos</a>
              <a href="/integration-test.html" class="btn btn-success btn-lg">🧪 Live API Test</a>
              <a href="http://api.pills.localhost:9080/docs" target="_blank" class="btn btn-secondary btn-lg">📚 API Docs</a>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <nav class="bg-white shadow-sm border-b sticky top-0 z-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between items-center h-16">
            <div class="flex items-center space-x-8">
              <a href="#overview" class="text-gray-700 hover:text-blue-600 font-medium">Overview</a>
              <a href="#demos" class="text-gray-700 hover:text-blue-600 font-medium">Demos</a>
              <a href="#features" class="text-gray-700 hover:text-blue-600 font-medium">Features</a>
              <a href="#technical" class="text-gray-700 hover:text-blue-600 font-medium">Technical</a>
            </div>
            <a href="/" class="btn btn-secondary">← Back to Home</a>
          </div>
        </div>
      </nav>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Overview Section -->
        <section id="overview" class="mb-16">
          <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Project Overview</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div class="text-center">
              <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="text-2xl">🎨</span>
              </div>
              <h3 class="text-xl font-semibold mb-2">Modern UX</h3>
              <p class="text-gray-600">Professional React components with real-time data integration</p>
            </div>
            <div class="text-center">
              <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="text-2xl">⚡</span>
              </div>
              <h3 class="text-xl font-semibold mb-2">Fast API</h3>
              <p class="text-gray-600">FastAPI backend with SQLAlchemy and comprehensive endpoints</p>
            </div>
            <div class="text-center">
              <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="text-2xl">🚀</span>
              </div>
              <h3 class="text-xl font-semibold mb-2">Production Ready</h3>
              <p class="text-gray-600">Docker deployment with monitoring and performance optimization</p>
            </div>
          </div>

          <div class="bg-gradient-to-r from-blue-50 to-purple-50 p-8 rounded-lg">
            <h3 class="text-2xl font-bold text-gray-900 mb-4 text-center">🎉 Project Complete!</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div class="text-3xl font-bold text-blue-600">7+</div>
                <div class="text-sm text-gray-600">UX Components</div>
              </div>
              <div>
                <div class="text-3xl font-bold text-green-600">15+</div>
                <div class="text-sm text-gray-600">API Endpoints</div>
              </div>
              <div>
                <div class="text-3xl font-bold text-purple-600">100%</div>
                <div class="text-sm text-gray-600">Type Safe</div>
              </div>
              <div>
                <div class="text-3xl font-bold text-orange-600">✅</div>
                <div class="text-sm text-gray-600">Production Ready</div>
              </div>
            </div>
          </div>
        </section>

        <!-- Demo Section -->
        <section id="demos" class="mb-16">
          <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Interactive Demos</h2>
          
          <div class="feature-grid">
            <!-- Core UX Demos -->
            <div class="card">
              <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-4">
                  <span class="text-white text-xl">📊</span>
                </div>
                <div>
                  <h3 class="text-lg font-semibold">Dashboard</h3>
                  <p class="text-sm text-gray-600">Real-time stats and overview</p>
                </div>
              </div>
              <p class="text-gray-600 mb-4">Central hub with live data, quick actions, and health insights.</p>
              <div class="flex space-x-2">
                <a href="/ux-demo.html" class="btn btn-primary flex-1">View Demo</a>
              </div>
            </div>

            <div class="card">
              <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mr-4">
                  <span class="text-white text-xl">💊</span>
                </div>
                <div>
                  <h3 class="text-lg font-semibold">Supplement Tracker</h3>
                  <p class="text-sm text-gray-600">Intake logging and scheduling</p>
                </div>
              </div>
              <p class="text-gray-600 mb-4">Complete supplement management with streak tracking and reminders.</p>
              <div class="flex space-x-2">
                <a href="/ux-demo.html" class="btn btn-success flex-1">View Demo</a>
              </div>
            </div>

            <div class="card">
              <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mr-4">
                  <span class="text-white text-xl">🧪</span>
                </div>
                <div>
                  <h3 class="text-lg font-semibold">Research Hub</h3>
                  <p class="text-sm text-gray-600">Study discovery and participation</p>
                </div>
              </div>
              <p class="text-gray-600 mb-4">Browse and join evidence-based supplement research studies.</p>
              <div class="flex space-x-2">
                <a href="/ux-demo.html" class="btn btn-purple flex-1">View Demo</a>
              </div>
            </div>

            <!-- Enhanced Features -->
            <div class="card">
              <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mr-4">
                  <span class="text-white text-xl">🔗</span>
                </div>
                <div>
                  <h3 class="text-lg font-semibold">API Integration</h3>
                  <p class="text-sm text-gray-600">Real-time data connectivity</p>
                </div>
              </div>
              <p class="text-gray-600 mb-4">Live API testing with performance monitoring and health checks.</p>
              <div class="flex space-x-2">
                <a href="/integration-test.html" class="btn btn-orange flex-1">Test APIs</a>
              </div>
            </div>

            <div class="card">
              <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center mr-4">
                  <span class="text-white text-xl">📈</span>
                </div>
                <div>
                  <h3 class="text-lg font-semibold">Analytics Dashboard</h3>
                  <p class="text-sm text-gray-600">Advanced data visualization</p>
                </div>
              </div>
              <p class="text-gray-600 mb-4">Comprehensive analytics with trends, compliance, and insights.</p>
              <div class="flex space-x-2">
                <span class="btn btn-secondary flex-1 opacity-50">Coming Soon</span>
              </div>
            </div>

            <div class="card">
              <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center mr-4">
                  <span class="text-white text-xl">⚡</span>
                </div>
                <div>
                  <h3 class="text-lg font-semibold">Performance Monitor</h3>
                  <p class="text-sm text-gray-600">Real-time system monitoring</p>
                </div>
              </div>
              <p class="text-gray-600 mb-4">Live performance metrics, response times, and system health.</p>
              <div class="flex space-x-2">
                <span class="btn btn-secondary flex-1 opacity-50">Coming Soon</span>
              </div>
            </div>
          </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="mb-16">
          <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Key Features</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="card">
              <h3 class="text-xl font-semibold mb-4">🎨 Frontend Excellence</h3>
              <ul class="space-y-2 text-gray-600">
                <li>✅ React + TypeScript with full type safety</li>
                <li>✅ Responsive design with Tailwind CSS</li>
                <li>✅ Real-time data with automatic refresh</li>
                <li>✅ Graceful fallback to cached data</li>
                <li>✅ WCAG 2.1 AA accessibility compliance</li>
                <li>✅ Professional MVP component library</li>
              </ul>
            </div>

            <div class="card">
              <h3 class="text-xl font-semibold mb-4">⚡ Backend Power</h3>
              <ul class="space-y-2 text-gray-600">
                <li>✅ FastAPI with automatic OpenAPI docs</li>
                <li>✅ SQLAlchemy ORM with relationship management</li>
                <li>✅ Pydantic validation and serialization</li>
                <li>✅ Comprehensive error handling</li>
                <li>✅ Health monitoring and performance metrics</li>
                <li>✅ RESTful API design with proper HTTP codes</li>
              </ul>
            </div>

            <div class="card">
              <h3 class="text-xl font-semibold mb-4">🚀 DevOps & Infrastructure</h3>
              <ul class="space-y-2 text-gray-600">
                <li>✅ Docker containerization for all services</li>
                <li>✅ Traefik reverse proxy with auto-routing</li>
                <li>✅ Hot reloading for development workflow</li>
                <li>✅ CORS configuration for cross-origin requests</li>
                <li>✅ Environment-based configuration</li>
                <li>✅ Production-ready deployment setup</li>
              </ul>
            </div>

            <div class="card">
              <h3 class="text-xl font-semibold mb-4">📊 Data & Analytics</h3>
              <ul class="space-y-2 text-gray-600">
                <li>✅ Comprehensive supplement catalog</li>
                <li>✅ Personal intake tracking and scheduling</li>
                <li>✅ Research study management platform</li>
                <li>✅ Real-time dashboard statistics</li>
                <li>✅ Performance monitoring and health checks</li>
                <li>✅ Advanced analytics and insights</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- Technical Section -->
        <section id="technical" class="mb-16">
          <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Technical Architecture</h2>
          
          <div class="bg-white p-8 rounded-lg shadow-sm border">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div class="text-center">
                <h3 class="text-lg font-semibold mb-4">Frontend Stack</h3>
                <div class="space-y-2 text-sm">
                  <div class="badge badge-primary">React 18</div>
                  <div class="badge badge-primary">TypeScript</div>
                  <div class="badge badge-primary">Tailwind CSS</div>
                  <div class="badge badge-primary">Custom Hooks</div>
                  <div class="badge badge-primary">MVP Components</div>
                </div>
              </div>
              
              <div class="text-center">
                <h3 class="text-lg font-semibold mb-4">Backend Stack</h3>
                <div class="space-y-2 text-sm">
                  <div class="badge badge-success">FastAPI</div>
                  <div class="badge badge-success">SQLAlchemy</div>
                  <div class="badge badge-success">Pydantic</div>
                  <div class="badge badge-success">SQLite/PostgreSQL</div>
                  <div class="badge badge-success">Uvicorn</div>
                </div>
              </div>
              
              <div class="text-center">
                <h3 class="text-lg font-semibold mb-4">Infrastructure</h3>
                <div class="space-y-2 text-sm">
                  <div class="badge badge-purple">Docker</div>
                  <div class="badge badge-purple">Traefik</div>
                  <div class="badge badge-purple">Nginx</div>
                  <div class="badge badge-purple">Docker Compose</div>
                  <div class="badge badge-purple">HTTP/HTTPS</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Quick Links -->
        <section class="text-center">
          <h2 class="text-3xl font-bold text-gray-900 mb-8">Quick Access</h2>
          <div class="flex flex-wrap justify-center gap-4">
            <a href="/ux-demo.html" class="btn btn-primary">🎨 UX Showcase</a>
            <a href="/integration-test.html" class="btn btn-success">🧪 API Testing</a>
            <a href="http://api.pills.localhost:9080/docs" target="_blank" class="btn btn-purple">📚 API Documentation</a>
            <a href="http://traefik.pills.localhost:9081/" target="_blank" class="btn btn-orange">⚙️ Infrastructure</a>
            <a href="/" class="btn btn-secondary">🏠 Home</a>
          </div>
        </section>
      </div>
    </div>
  </body>
</html>
