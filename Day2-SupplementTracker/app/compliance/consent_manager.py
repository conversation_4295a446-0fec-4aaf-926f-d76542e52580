"""
Consent Management System for Legal Compliance

This module handles user consent collection, tracking, and management
for GDPR, HIPAA, and research ethics compliance.
"""

from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import J<PERSON><PERSON>

from app.database.base import Base
from app.models.user import User

# Add to User model relationship
User.consent_records = relationship("ConsentRecord", back_populates="user")


class ConsentType(str, Enum):
    """Types of consent that can be collected."""
    PLATFORM_TERMS = "platform_terms"
    PRIVACY_POLICY = "privacy_policy"
    RESEARCH_PARTICIPATION = "research_participation"
    DATA_SHARING = "data_sharing"
    MARKETING_COMMUNICATIONS = "marketing_communications"
    ANALYTICS_TRACKING = "analytics_tracking"
    COOKIE_USAGE = "cookie_usage"
    HEALTH_DATA_COLLECTION = "health_data_collection"
    THIRD_PARTY_SHARING = "third_party_sharing"


class ConsentStatus(str, Enum):
    """Status of consent."""
    GIVEN = "given"
    WITHDRAWN = "withdrawn"
    EXPIRED = "expired"
    PENDING = "pending"


class ConsentRecord(Base):
    """Database model for consent records."""
    __tablename__ = "consent_records"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    consent_type = Column(String(50), nullable=False)
    consent_status = Column(String(20), nullable=False)
    consent_version = Column(String(20), nullable=False)
    consent_text = Column(Text, nullable=True)
    consent_metadata = Column(JSON, nullable=True)
    
    # Timestamps
    given_at = Column(DateTime, nullable=True)
    withdrawn_at = Column(DateTime, nullable=True)
    expires_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Technical details
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    consent_method = Column(String(50), nullable=True)  # web, api, email, etc.
    
    # Relationships
    user = relationship("User", back_populates="consent_records")


class ConsentManager:
    """Manages user consent collection and tracking."""
    
    def __init__(self, db_session):
        self.db = db_session
    
    def record_consent(
        self,
        user_id: int,
        consent_type: ConsentType,
        consent_version: str,
        consent_text: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        consent_method: str = "web"
    ) -> ConsentRecord:
        """
        Record user consent for a specific type and version.
        
        Args:
            user_id: ID of the user giving consent
            consent_type: Type of consent being given
            consent_version: Version of the consent document
            consent_text: Full text of consent (optional)
            metadata: Additional metadata about consent
            ip_address: IP address of user when consent given
            user_agent: User agent string
            consent_method: Method used to collect consent
            
        Returns:
            ConsentRecord: The created consent record
        """
        # Withdraw any existing consent of the same type
        self._withdraw_existing_consent(user_id, consent_type)
        
        # Create new consent record
        consent_record = ConsentRecord(
            user_id=user_id,
            consent_type=consent_type.value,
            consent_status=ConsentStatus.GIVEN.value,
            consent_version=consent_version,
            consent_text=consent_text,
            consent_metadata=metadata or {},
            given_at=datetime.utcnow(),
            ip_address=ip_address,
            user_agent=user_agent,
            consent_method=consent_method
        )
        
        # Set expiration if applicable
        if consent_type in self._get_expiring_consent_types():
            consent_record.expires_at = self._calculate_expiration(consent_type)
        
        self.db.add(consent_record)
        self.db.commit()
        self.db.refresh(consent_record)
        
        return consent_record
    
    def withdraw_consent(
        self,
        user_id: int,
        consent_type: ConsentType,
        withdrawal_reason: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> bool:
        """
        Withdraw user consent for a specific type.
        
        Args:
            user_id: ID of the user withdrawing consent
            consent_type: Type of consent being withdrawn
            withdrawal_reason: Reason for withdrawal (optional)
            ip_address: IP address of user when consent withdrawn
            
        Returns:
            bool: True if consent was successfully withdrawn
        """
        current_consent = self._get_current_consent(user_id, consent_type)
        
        if not current_consent or current_consent.consent_status != ConsentStatus.GIVEN.value:
            return False
        
        # Update consent record
        current_consent.consent_status = ConsentStatus.WITHDRAWN.value
        current_consent.withdrawn_at = datetime.utcnow()
        current_consent.updated_at = datetime.utcnow()
        
        # Add withdrawal metadata
        if not current_consent.consent_metadata:
            current_consent.consent_metadata = {}
        
        current_consent.consent_metadata.update({
            "withdrawal_reason": withdrawal_reason,
            "withdrawal_ip": ip_address,
            "withdrawal_timestamp": datetime.utcnow().isoformat()
        })
        
        self.db.commit()
        return True
    
    def check_consent(
        self,
        user_id: int,
        consent_type: ConsentType,
        required_version: Optional[str] = None
    ) -> bool:
        """
        Check if user has valid consent for a specific type.
        
        Args:
            user_id: ID of the user to check
            consent_type: Type of consent to check
            required_version: Minimum required version (optional)
            
        Returns:
            bool: True if user has valid consent
        """
        current_consent = self._get_current_consent(user_id, consent_type)
        
        if not current_consent:
            return False
        
        # Check if consent is given and not expired
        if current_consent.consent_status != ConsentStatus.GIVEN.value:
            return False
        
        if current_consent.expires_at and current_consent.expires_at < datetime.utcnow():
            # Mark as expired
            current_consent.consent_status = ConsentStatus.EXPIRED.value
            self.db.commit()
            return False
        
        # Check version if required
        if required_version and current_consent.consent_version != required_version:
            return False
        
        return True
    
    def get_consent_history(
        self,
        user_id: int,
        consent_type: Optional[ConsentType] = None
    ) -> List[ConsentRecord]:
        """
        Get consent history for a user.
        
        Args:
            user_id: ID of the user
            consent_type: Specific consent type (optional)
            
        Returns:
            List[ConsentRecord]: List of consent records
        """
        query = self.db.query(ConsentRecord).filter(ConsentRecord.user_id == user_id)
        
        if consent_type:
            query = query.filter(ConsentRecord.consent_type == consent_type.value)
        
        return query.order_by(ConsentRecord.created_at.desc()).all()
    
    def get_consent_summary(self, user_id: int) -> Dict[str, Any]:
        """
        Get summary of all consent statuses for a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Dict: Summary of consent statuses
        """
        summary = {}
        
        for consent_type in ConsentType:
            current_consent = self._get_current_consent(user_id, consent_type)
            
            if current_consent:
                summary[consent_type.value] = {
                    "status": current_consent.consent_status,
                    "version": current_consent.consent_version,
                    "given_at": current_consent.given_at.isoformat() if current_consent.given_at else None,
                    "expires_at": current_consent.expires_at.isoformat() if current_consent.expires_at else None,
                    "is_valid": self.check_consent(user_id, consent_type)
                }
            else:
                summary[consent_type.value] = {
                    "status": "not_given",
                    "version": None,
                    "given_at": None,
                    "expires_at": None,
                    "is_valid": False
                }
        
        return summary
    
    def bulk_consent_check(
        self,
        user_ids: List[int],
        consent_type: ConsentType
    ) -> Dict[int, bool]:
        """
        Check consent for multiple users efficiently.
        
        Args:
            user_ids: List of user IDs to check
            consent_type: Type of consent to check
            
        Returns:
            Dict: Mapping of user_id to consent status
        """
        # Get all current consent records for these users
        consent_records = self.db.query(ConsentRecord).filter(
            ConsentRecord.user_id.in_(user_ids),
            ConsentRecord.consent_type == consent_type.value
        ).all()
        
        # Group by user_id and get most recent
        user_consents = {}
        for record in consent_records:
            if (record.user_id not in user_consents or 
                record.created_at > user_consents[record.user_id].created_at):
                user_consents[record.user_id] = record
        
        # Check validity for each user
        result = {}
        for user_id in user_ids:
            if user_id in user_consents:
                consent = user_consents[user_id]
                result[user_id] = (
                    consent.consent_status == ConsentStatus.GIVEN.value and
                    (not consent.expires_at or consent.expires_at > datetime.utcnow())
                )
            else:
                result[user_id] = False
        
        return result
    
    def expire_old_consents(self) -> int:
        """
        Mark expired consents as expired.
        
        Returns:
            int: Number of consents marked as expired
        """
        expired_consents = self.db.query(ConsentRecord).filter(
            ConsentRecord.consent_status == ConsentStatus.GIVEN.value,
            ConsentRecord.expires_at < datetime.utcnow()
        ).all()
        
        count = 0
        for consent in expired_consents:
            consent.consent_status = ConsentStatus.EXPIRED.value
            consent.updated_at = datetime.utcnow()
            count += 1
        
        self.db.commit()
        return count
    
    def _get_current_consent(
        self,
        user_id: int,
        consent_type: ConsentType
    ) -> Optional[ConsentRecord]:
        """Get the most recent consent record for a user and type."""
        return self.db.query(ConsentRecord).filter(
            ConsentRecord.user_id == user_id,
            ConsentRecord.consent_type == consent_type.value
        ).order_by(ConsentRecord.created_at.desc()).first()
    
    def _withdraw_existing_consent(self, user_id: int, consent_type: ConsentType):
        """Withdraw any existing consent of the same type."""
        existing_consent = self._get_current_consent(user_id, consent_type)
        
        if (existing_consent and 
            existing_consent.consent_status == ConsentStatus.GIVEN.value):
            existing_consent.consent_status = ConsentStatus.WITHDRAWN.value
            existing_consent.withdrawn_at = datetime.utcnow()
            existing_consent.updated_at = datetime.utcnow()
    
    def _get_expiring_consent_types(self) -> List[ConsentType]:
        """Get list of consent types that expire."""
        return [
            ConsentType.MARKETING_COMMUNICATIONS,  # Expires after 2 years
            ConsentType.ANALYTICS_TRACKING,       # Expires after 1 year
        ]
    
    def _calculate_expiration(self, consent_type: ConsentType) -> datetime:
        """Calculate expiration date for consent type."""
        if consent_type == ConsentType.MARKETING_COMMUNICATIONS:
            return datetime.utcnow() + timedelta(days=730)  # 2 years
        elif consent_type == ConsentType.ANALYTICS_TRACKING:
            return datetime.utcnow() + timedelta(days=365)  # 1 year
        else:
            return datetime.utcnow() + timedelta(days=365)  # Default 1 year


class ConsentValidator:
    """Validates consent requirements for different operations."""
    
    def __init__(self, consent_manager: ConsentManager):
        self.consent_manager = consent_manager
    
    def validate_research_participation(self, user_id: int, study_id: str) -> bool:
        """Validate consent for research participation."""
        required_consents = [
            ConsentType.PLATFORM_TERMS,
            ConsentType.PRIVACY_POLICY,
            ConsentType.RESEARCH_PARTICIPATION,
            ConsentType.HEALTH_DATA_COLLECTION
        ]
        
        for consent_type in required_consents:
            if not self.consent_manager.check_consent(user_id, consent_type):
                return False
        
        return True
    
    def validate_data_export(self, user_id: int) -> bool:
        """Validate consent for data export operations."""
        return self.consent_manager.check_consent(user_id, ConsentType.PLATFORM_TERMS)
    
    def validate_marketing_communication(self, user_id: int) -> bool:
        """Validate consent for marketing communications."""
        return self.consent_manager.check_consent(user_id, ConsentType.MARKETING_COMMUNICATIONS)
    
    def validate_analytics_tracking(self, user_id: int) -> bool:
        """Validate consent for analytics tracking."""
        return self.consent_manager.check_consent(user_id, ConsentType.ANALYTICS_TRACKING)
    
    def get_missing_consents(
        self,
        user_id: int,
        operation: str
    ) -> List[ConsentType]:
        """Get list of missing consents for an operation."""
        required_consents = self._get_required_consents(operation)
        missing_consents = []
        
        for consent_type in required_consents:
            if not self.consent_manager.check_consent(user_id, consent_type):
                missing_consents.append(consent_type)
        
        return missing_consents
    
    def _get_required_consents(self, operation: str) -> List[ConsentType]:
        """Get required consents for different operations."""
        consent_requirements = {
            "research_participation": [
                ConsentType.PLATFORM_TERMS,
                ConsentType.PRIVACY_POLICY,
                ConsentType.RESEARCH_PARTICIPATION,
                ConsentType.HEALTH_DATA_COLLECTION
            ],
            "data_sharing": [
                ConsentType.PLATFORM_TERMS,
                ConsentType.PRIVACY_POLICY,
                ConsentType.DATA_SHARING
            ],
            "marketing": [
                ConsentType.MARKETING_COMMUNICATIONS
            ],
            "analytics": [
                ConsentType.ANALYTICS_TRACKING
            ],
            "basic_platform": [
                ConsentType.PLATFORM_TERMS,
                ConsentType.PRIVACY_POLICY
            ]
        }
        
        return consent_requirements.get(operation, [])
