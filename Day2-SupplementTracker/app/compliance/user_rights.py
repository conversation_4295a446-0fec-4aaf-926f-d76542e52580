"""
User Rights Management System for GDPR, CCPA, and HIPAA Compliance

This module handles user data rights including access, rectification,
erasure, portability, and other privacy rights.
"""

import json
import zipfile
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from io import BytesIO
from dataclasses import dataclass

from sqlalchemy.orm import Session
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSON

from app.database.base import Base
from app.models.user import User
from app.compliance.consent_manager import ConsentManager
from app.compliance.data_anonymizer import DataAnonymizer, AnonymizationConfig, AnonymizationLevel


class RequestType(str, Enum):
    """Types of user rights requests."""
    ACCESS = "access"                    # Right to access personal data
    RECTIFICATION = "rectification"      # Right to correct data
    ERASURE = "erasure"                 # Right to be forgotten
    RESTRICT = "restrict"               # Right to restrict processing
    PORTABILITY = "portability"         # Right to data portability
    OBJECT = "object"                   # Right to object to processing
    WITHDRAW_CONSENT = "withdraw_consent"  # Right to withdraw consent


class RequestStatus(str, Enum):
    """Status of user rights requests."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    REJECTED = "rejected"
    PARTIALLY_COMPLETED = "partially_completed"


@dataclass
class DataExportConfig:
    """Configuration for data export operations."""
    include_research_data: bool = True
    include_health_data: bool = True
    include_activity_logs: bool = False
    anonymize_shared_data: bool = True
    format: str = "json"  # json, csv, xml


class UserRightsRequest(Base):
    """Database model for user rights requests."""
    __tablename__ = "user_rights_requests"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    request_type = Column(String(50), nullable=False)
    request_status = Column(String(50), nullable=False, default=RequestStatus.PENDING.value)
    
    # Request details
    request_description = Column(Text, nullable=True)
    request_data = Column(JSON, nullable=True)
    
    # Processing details
    processed_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    processing_notes = Column(Text, nullable=True)
    completion_data = Column(JSON, nullable=True)
    
    # Timestamps
    requested_at = Column(DateTime, default=datetime.utcnow)
    due_date = Column(DateTime, nullable=False)
    processed_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Technical details
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="rights_requests")
    processor = relationship("User", foreign_keys=[processed_by])


class UserRightsManager:
    """Manages user data rights requests and processing."""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.consent_manager = ConsentManager(db_session)
        self.data_anonymizer = DataAnonymizer(db_session)
    
    def submit_request(
        self,
        user_id: int,
        request_type: RequestType,
        description: Optional[str] = None,
        request_data: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> UserRightsRequest:
        """
        Submit a user rights request.
        
        Args:
            user_id: ID of the user making the request
            request_type: Type of rights request
            description: Optional description of the request
            request_data: Additional request data
            ip_address: IP address of the request
            user_agent: User agent string
            
        Returns:
            UserRightsRequest: The created request
        """
        # Calculate due date based on regulation requirements
        due_date = self._calculate_due_date(request_type)
        
        request = UserRightsRequest(
            user_id=user_id,
            request_type=request_type.value,
            request_description=description,
            request_data=request_data or {},
            due_date=due_date,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        self.db.add(request)
        self.db.commit()
        self.db.refresh(request)
        
        # Auto-process certain types of requests
        if request_type in [RequestType.ACCESS, RequestType.PORTABILITY]:
            self._auto_process_request(request)
        
        return request
    
    def process_access_request(
        self,
        request_id: int,
        processor_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Process a data access request.
        
        Args:
            request_id: ID of the request to process
            processor_id: ID of the user processing the request
            
        Returns:
            Dict: User's personal data
        """
        request = self._get_request(request_id)
        if not request or request.request_type != RequestType.ACCESS.value:
            raise ValueError("Invalid access request")
        
        # Update request status
        request.request_status = RequestStatus.IN_PROGRESS.value
        request.processed_by = processor_id
        request.processed_at = datetime.utcnow()
        
        # Collect user data
        user_data = self._collect_user_data(request.user_id)
        
        # Complete request
        request.request_status = RequestStatus.COMPLETED.value
        request.completed_at = datetime.utcnow()
        request.completion_data = {"data_exported": True, "export_size": len(str(user_data))}
        
        self.db.commit()
        
        return user_data
    
    def process_rectification_request(
        self,
        request_id: int,
        corrections: Dict[str, Any],
        processor_id: Optional[int] = None
    ) -> bool:
        """
        Process a data rectification request.
        
        Args:
            request_id: ID of the request to process
            corrections: Dictionary of corrections to apply
            processor_id: ID of the user processing the request
            
        Returns:
            bool: True if successful
        """
        request = self._get_request(request_id)
        if not request or request.request_type != RequestType.RECTIFICATION.value:
            raise ValueError("Invalid rectification request")
        
        # Update request status
        request.request_status = RequestStatus.IN_PROGRESS.value
        request.processed_by = processor_id
        request.processed_at = datetime.utcnow()
        
        # Apply corrections
        success = self._apply_data_corrections(request.user_id, corrections)
        
        # Complete request
        request.request_status = RequestStatus.COMPLETED.value if success else RequestStatus.REJECTED.value
        request.completed_at = datetime.utcnow()
        request.completion_data = {"corrections_applied": corrections, "success": success}
        
        self.db.commit()
        
        return success
    
    def process_erasure_request(
        self,
        request_id: int,
        erasure_scope: str = "complete",
        processor_id: Optional[int] = None
    ) -> bool:
        """
        Process a data erasure (right to be forgotten) request.
        
        Args:
            request_id: ID of the request to process
            erasure_scope: Scope of erasure (complete, partial, anonymize)
            processor_id: ID of the user processing the request
            
        Returns:
            bool: True if successful
        """
        request = self._get_request(request_id)
        if not request or request.request_type != RequestType.ERASURE.value:
            raise ValueError("Invalid erasure request")
        
        # Update request status
        request.request_status = RequestStatus.IN_PROGRESS.value
        request.processed_by = processor_id
        request.processed_at = datetime.utcnow()
        
        # Process erasure
        if erasure_scope == "complete":
            success = self._complete_data_erasure(request.user_id)
        elif erasure_scope == "anonymize":
            success = self._anonymize_user_data(request.user_id)
        else:
            success = self._partial_data_erasure(request.user_id, request.request_data)
        
        # Complete request
        request.request_status = RequestStatus.COMPLETED.value if success else RequestStatus.REJECTED.value
        request.completed_at = datetime.utcnow()
        request.completion_data = {"erasure_scope": erasure_scope, "success": success}
        
        self.db.commit()
        
        return success
    
    def export_user_data(
        self,
        user_id: int,
        config: DataExportConfig
    ) -> bytes:
        """
        Export user data in requested format.
        
        Args:
            user_id: ID of the user
            config: Export configuration
            
        Returns:
            bytes: Exported data as bytes
        """
        # Collect user data
        user_data = self._collect_user_data(user_id, config)
        
        if config.format == "json":
            return json.dumps(user_data, indent=2, default=str).encode('utf-8')
        elif config.format == "csv":
            return self._export_as_csv(user_data)
        elif config.format == "xml":
            return self._export_as_xml(user_data)
        else:
            raise ValueError(f"Unsupported export format: {config.format}")
    
    def create_data_package(
        self,
        user_id: int,
        config: DataExportConfig
    ) -> bytes:
        """
        Create a complete data package for the user.
        
        Args:
            user_id: ID of the user
            config: Export configuration
            
        Returns:
            bytes: ZIP file containing all user data
        """
        zip_buffer = BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # Add main data export
            user_data = self.export_user_data(user_id, config)
            zip_file.writestr(f"user_data.{config.format}", user_data)
            
            # Add consent history
            consent_history = self.consent_manager.get_consent_history(user_id)
            consent_data = [
                {
                    "consent_type": record.consent_type,
                    "status": record.consent_status,
                    "version": record.consent_version,
                    "given_at": record.given_at.isoformat() if record.given_at else None,
                    "withdrawn_at": record.withdrawn_at.isoformat() if record.withdrawn_at else None
                }
                for record in consent_history
            ]
            zip_file.writestr("consent_history.json", json.dumps(consent_data, indent=2))
            
            # Add rights request history
            rights_requests = self._get_user_requests(user_id)
            requests_data = [
                {
                    "request_type": req.request_type,
                    "status": req.request_status,
                    "requested_at": req.requested_at.isoformat(),
                    "completed_at": req.completed_at.isoformat() if req.completed_at else None,
                    "description": req.request_description
                }
                for req in rights_requests
            ]
            zip_file.writestr("rights_requests.json", json.dumps(requests_data, indent=2))
            
            # Add data processing summary
            processing_summary = self._create_processing_summary(user_id)
            zip_file.writestr("data_processing_summary.json", json.dumps(processing_summary, indent=2))
        
        zip_buffer.seek(0)
        return zip_buffer.read()
    
    def get_request_status(self, request_id: int) -> Dict[str, Any]:
        """Get status of a user rights request."""
        request = self._get_request(request_id)
        if not request:
            raise ValueError("Request not found")
        
        return {
            "request_id": request.id,
            "request_type": request.request_type,
            "status": request.request_status,
            "requested_at": request.requested_at.isoformat(),
            "due_date": request.due_date.isoformat(),
            "processed_at": request.processed_at.isoformat() if request.processed_at else None,
            "completed_at": request.completed_at.isoformat() if request.completed_at else None,
            "description": request.request_description
        }
    
    def _calculate_due_date(self, request_type: RequestType) -> datetime:
        """Calculate due date based on regulation requirements."""
        if request_type in [RequestType.ACCESS, RequestType.PORTABILITY]:
            # GDPR: 1 month (30 days)
            return datetime.utcnow() + timedelta(days=30)
        elif request_type == RequestType.ERASURE:
            # GDPR: Without undue delay
            return datetime.utcnow() + timedelta(days=7)
        elif request_type == RequestType.RECTIFICATION:
            # GDPR: Without undue delay
            return datetime.utcnow() + timedelta(days=7)
        else:
            # Default: 30 days
            return datetime.utcnow() + timedelta(days=30)
    
    def _auto_process_request(self, request: UserRightsRequest):
        """Auto-process certain types of requests."""
        if request.request_type == RequestType.ACCESS.value:
            try:
                self.process_access_request(request.id)
            except Exception as e:
                request.request_status = RequestStatus.REJECTED.value
                request.processing_notes = f"Auto-processing failed: {str(e)}"
                self.db.commit()
    
    def _collect_user_data(
        self,
        user_id: int,
        config: Optional[DataExportConfig] = None
    ) -> Dict[str, Any]:
        """Collect all user data for export."""
        if config is None:
            config = DataExportConfig()
        
        user_data = {
            "user_profile": self._get_user_profile(user_id),
            "account_settings": self._get_account_settings(user_id),
            "consent_records": self._get_consent_data(user_id)
        }
        
        if config.include_health_data:
            user_data["health_data"] = self._get_health_data(user_id)
            user_data["supplement_data"] = self._get_supplement_data(user_id)
        
        if config.include_research_data:
            user_data["research_participation"] = self._get_research_data(user_id)
        
        if config.include_activity_logs:
            user_data["activity_logs"] = self._get_activity_logs(user_id)
        
        return user_data
    
    def _get_user_profile(self, user_id: int) -> Dict[str, Any]:
        """Get user profile data."""
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            return {}
        
        return {
            "id": user.id,
            "email": user.email,
            "username": user.username,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "last_login": user.last_login.isoformat() if user.last_login else None,
            "is_active": user.is_active,
            "profile_data": user.profile_data or {}
        }
    
    def _get_account_settings(self, user_id: int) -> Dict[str, Any]:
        """Get user account settings."""
        # Implementation depends on your settings model
        return {"notification_preferences": {}, "privacy_settings": {}}
    
    def _get_consent_data(self, user_id: int) -> List[Dict[str, Any]]:
        """Get user consent records."""
        consent_history = self.consent_manager.get_consent_history(user_id)
        return [
            {
                "consent_type": record.consent_type,
                "status": record.consent_status,
                "version": record.consent_version,
                "given_at": record.given_at.isoformat() if record.given_at else None,
                "withdrawn_at": record.withdrawn_at.isoformat() if record.withdrawn_at else None
            }
            for record in consent_history
        ]
    
    def _get_health_data(self, user_id: int) -> Dict[str, Any]:
        """Get user health data."""
        # Implementation depends on your health data models
        return {"health_metrics": [], "symptoms": [], "conditions": []}
    
    def _get_supplement_data(self, user_id: int) -> Dict[str, Any]:
        """Get user supplement data."""
        # Implementation depends on your supplement models
        return {"supplements": [], "dosages": [], "effectiveness_ratings": []}
    
    def _get_research_data(self, user_id: int) -> Dict[str, Any]:
        """Get user research participation data."""
        # Implementation depends on your research models
        return {"studies": [], "protocols": [], "measurements": []}
    
    def _get_activity_logs(self, user_id: int) -> List[Dict[str, Any]]:
        """Get user activity logs."""
        # Implementation depends on your logging system
        return []
    
    def _apply_data_corrections(
        self,
        user_id: int,
        corrections: Dict[str, Any]
    ) -> bool:
        """Apply data corrections for rectification request."""
        try:
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                return False
            
            # Apply corrections to user profile
            for field, value in corrections.items():
                if hasattr(user, field) and field not in ['id', 'created_at']:
                    setattr(user, field, value)
            
            self.db.commit()
            return True
        except Exception:
            self.db.rollback()
            return False
    
    def _complete_data_erasure(self, user_id: int) -> bool:
        """Perform complete data erasure."""
        try:
            # Anonymize research data first
            self._anonymize_user_data(user_id)
            
            # Delete user account and personal data
            user = self.db.query(User).filter(User.id == user_id).first()
            if user:
                self.db.delete(user)
                self.db.commit()
            
            return True
        except Exception:
            self.db.rollback()
            return False
    
    def _anonymize_user_data(self, user_id: int) -> bool:
        """Anonymize user data while preserving research value."""
        try:
            # Use data anonymizer to anonymize research contributions
            config = AnonymizationConfig(level=AnonymizationLevel.ANONYMIZATION)
            
            # Anonymize research data
            # Implementation depends on your research data models
            
            return True
        except Exception:
            return False
    
    def _partial_data_erasure(
        self,
        user_id: int,
        erasure_data: Dict[str, Any]
    ) -> bool:
        """Perform partial data erasure."""
        # Implementation depends on specific erasure requirements
        return True
    
    def _get_request(self, request_id: int) -> Optional[UserRightsRequest]:
        """Get a user rights request by ID."""
        return self.db.query(UserRightsRequest).filter(
            UserRightsRequest.id == request_id
        ).first()
    
    def _get_user_requests(self, user_id: int) -> List[UserRightsRequest]:
        """Get all requests for a user."""
        return self.db.query(UserRightsRequest).filter(
            UserRightsRequest.user_id == user_id
        ).order_by(UserRightsRequest.requested_at.desc()).all()
    
    def _create_processing_summary(self, user_id: int) -> Dict[str, Any]:
        """Create a summary of data processing activities."""
        return {
            "data_categories": ["health_data", "supplement_data", "research_data"],
            "processing_purposes": ["research", "analytics", "service_provision"],
            "data_retention": "As specified in Privacy Policy",
            "third_party_sharing": "Anonymized research data only",
            "user_rights": ["access", "rectification", "erasure", "portability"]
        }
    
    def _export_as_csv(self, data: Dict[str, Any]) -> bytes:
        """Export data as CSV format."""
        # Simple CSV export - in production, use proper CSV library
        csv_content = "Category,Key,Value\n"
        for category, category_data in data.items():
            if isinstance(category_data, dict):
                for key, value in category_data.items():
                    csv_content += f"{category},{key},{value}\n"
        
        return csv_content.encode('utf-8')
    
    def _export_as_xml(self, data: Dict[str, Any]) -> bytes:
        """Export data as XML format."""
        # Simple XML export - in production, use proper XML library
        xml_content = '<?xml version="1.0" encoding="UTF-8"?>\n<user_data>\n'
        for category, category_data in data.items():
            xml_content += f"  <{category}>\n"
            if isinstance(category_data, dict):
                for key, value in category_data.items():
                    xml_content += f"    <{key}>{value}</{key}>\n"
            xml_content += f"  </{category}>\n"
        xml_content += "</user_data>"
        
        return xml_content.encode('utf-8')


# Add relationship to User model
# User.rights_requests = relationship("UserRightsRequest", foreign_keys="UserRightsRequest.user_id", back_populates="user")
