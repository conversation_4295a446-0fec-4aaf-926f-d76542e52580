"""
Community features database models.

This module defines SQLAlchemy models for community interactions,
discussions, and social features.
"""

from datetime import datetime
from typing import Optional
from uuid import uuid4

from sqlalchemy import (
    Boolean, Column, DateTime, Foreign<PERSON>ey, Integer, String, Text, UniqueConstraint
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class UserFollow(Base):
    """
    User follow relationship model for social connections.

    This model tracks follower/following relationships between users,
    enabling social features and content discovery.

    Attributes:
        id: Unique follow relationship identifier
        follower_id: User who is following
        followed_id: User being followed
        created_at: When the follow relationship was created
    """

    __tablename__ = "user_follows"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique follow relationship identifier"
    )

    follower_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="User who is following"
    )

    followed_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="User being followed"
    )

    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="When the follow relationship was created"
    )

    # Ensure a user can't follow the same person twice
    __table_args__ = (
        UniqueConstraint('follower_id', 'followed_id', name='uq_user_follow'),
    )

    def __repr__(self) -> str:
        """String representation of the UserFollow model."""
        return f"<UserFollow(follower_id={self.follower_id}, followed_id={self.followed_id})>"


class CommunityGroup(Base):
    """
    Community group model for organizing discussions and research topics.

    This model represents groups where users can gather around specific
    topics, supplements, or research interests.

    Attributes:
        id: Unique group identifier
        name: Group name
        description: Group description and purpose
        category: Group category (e.g., "Supplement", "Research", "General")
        is_public: Whether the group is publicly visible
        is_moderated: Whether posts require moderation
        member_count: Number of group members
        created_by_user_id: User who created the group
        created_at: Group creation timestamp
        updated_at: Last update timestamp
    """

    __tablename__ = "community_groups"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique group identifier"
    )

    name = Column(
        String(255),
        nullable=False,
        index=True,
        doc="Group name"
    )

    description = Column(
        Text,
        nullable=True,
        doc="Group description and purpose"
    )

    category = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Group category"
    )

    is_public = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether the group is publicly visible"
    )

    is_moderated = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether posts require moderation"
    )

    member_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of group members"
    )

    created_by_user_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="User who created the group"
    )

    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Group creation timestamp"
    )

    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="Last update timestamp"
    )

    def __repr__(self) -> str:
        """String representation of the CommunityGroup model."""
        return f"<CommunityGroup(id={self.id}, name={self.name}, category={self.category})>"


class CommunityPost(Base):
    """
    Community post model for discussions and content sharing.

    This model represents posts made by users in community groups or
    general discussions, including questions, insights, and research findings.

    Attributes:
        id: Unique post identifier
        title: Post title
        content: Post content/body
        post_type: Type of post (e.g., "discussion", "question", "research")
        group_id: Optional group where post was made
        author_id: User who created the post
        is_pinned: Whether the post is pinned in the group
        is_locked: Whether comments are disabled
        view_count: Number of times post was viewed
        like_count: Number of likes received
        comment_count: Number of comments
        created_at: Post creation timestamp
        updated_at: Last update timestamp
    """

    __tablename__ = "community_posts"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique post identifier"
    )

    title = Column(
        String(500),
        nullable=False,
        index=True,
        doc="Post title"
    )

    content = Column(
        Text,
        nullable=False,
        doc="Post content/body"
    )

    post_type = Column(
        String(50),
        nullable=False,
        default="discussion",
        index=True,
        doc="Type of post"
    )

    group_id = Column(
        UUID(as_uuid=True),
        ForeignKey("community_groups.id"),
        nullable=True,
        index=True,
        doc="Optional group where post was made"
    )

    author_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="User who created the post"
    )

    is_pinned = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the post is pinned in the group"
    )

    is_locked = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether comments are disabled"
    )

    view_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of times post was viewed"
    )

    like_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of likes received"
    )

    comment_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of comments"
    )

    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        doc="Post creation timestamp"
    )

    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="Last update timestamp"
    )

    # Relationships
    group = relationship("CommunityGroup", backref="posts")

    def __repr__(self) -> str:
        """String representation of the CommunityPost model."""
        return f"<CommunityPost(id={self.id}, title={self.title[:50]}, author_id={self.author_id})>"


class PostComment(Base):
    """
    Post comment model for threaded discussions.

    This model represents comments on community posts, supporting
    threaded discussions and replies.

    Attributes:
        id: Unique comment identifier
        post_id: Post being commented on
        author_id: User who wrote the comment
        parent_comment_id: Optional parent comment for threading
        content: Comment content
        like_count: Number of likes received
        is_deleted: Whether comment was deleted (soft delete)
        created_at: Comment creation timestamp
        updated_at: Last update timestamp
    """

    __tablename__ = "post_comments"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique comment identifier"
    )

    post_id = Column(
        UUID(as_uuid=True),
        ForeignKey("community_posts.id"),
        nullable=False,
        index=True,
        doc="Post being commented on"
    )

    author_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="User who wrote the comment"
    )

    parent_comment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("post_comments.id"),
        nullable=True,
        index=True,
        doc="Optional parent comment for threading"
    )

    content = Column(
        Text,
        nullable=False,
        doc="Comment content"
    )

    like_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of likes received"
    )

    is_deleted = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether comment was deleted (soft delete)"
    )

    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        doc="Comment creation timestamp"
    )

    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="Last update timestamp"
    )

    # Relationships
    post = relationship("CommunityPost", backref="comments")
    parent_comment = relationship("PostComment", remote_side=[id], backref="replies")

    def __repr__(self) -> str:
        """String representation of the PostComment model."""
        return f"<PostComment(id={self.id}, post_id={self.post_id}, author_id={self.author_id})>"


class PeerReview(Base):
    """
    Peer review model for community content validation.

    This model tracks peer review processes for posts, research findings,
    and other community content requiring validation.

    Attributes:
        id: Unique review identifier
        content_type: Type of content being reviewed (e.g., "post", "research")
        content_id: ID of the content being reviewed
        reviewer_id: User performing the review
        status: Review status (e.g., "pending", "approved", "rejected")
        score: Numerical review score (1-10)
        feedback: Optional reviewer feedback
        expertise_level: Reviewer's claimed expertise level
        created_at: Review creation timestamp
        updated_at: Last update timestamp
    """

    __tablename__ = "peer_reviews"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique review identifier"
    )

    content_type = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Type of content being reviewed"
    )

    content_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="ID of the content being reviewed"
    )

    reviewer_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="User performing the review"
    )

    status = Column(
        String(20),
        nullable=False,
        default="pending",
        index=True,
        doc="Review status"
    )

    score = Column(
        Integer,
        nullable=True,
        doc="Numerical review score (1-10)"
    )

    feedback = Column(
        Text,
        nullable=True,
        doc="Optional reviewer feedback"
    )

    expertise_level = Column(
        String(20),
        nullable=False,
        default="general",
        doc="Reviewer's claimed expertise level"
    )

    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Review creation timestamp"
    )

    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="Last update timestamp"
    )

    # Ensure one review per reviewer per content item
    __table_args__ = (
        UniqueConstraint('content_type', 'content_id', 'reviewer_id', name='uq_peer_review'),
    )

    def __repr__(self) -> str:
        """String representation of the PeerReview model."""
        return f"<PeerReview(id={self.id}, content_type={self.content_type}, status={self.status})>"


class Notification(Base):
    """
    Notification model for real-time user notifications.

    This model stores notifications for users about various platform activities
    such as new followers, comments, peer reviews, and group activities.

    Attributes:
        id: Unique notification identifier
        user_id: User receiving the notification
        type: Notification type (e.g., "follow", "comment", "review")
        title: Notification title
        message: Notification message content
        related_id: Optional ID of related content
        related_type: Type of related content
        is_read: Whether notification has been read
        is_sent: Whether notification was sent (for push/email)
        created_at: Notification creation timestamp
    """

    __tablename__ = "notifications"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique notification identifier"
    )

    user_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="User receiving the notification"
    )

    type = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Notification type"
    )

    title = Column(
        String(255),
        nullable=False,
        doc="Notification title"
    )

    message = Column(
        Text,
        nullable=False,
        doc="Notification message content"
    )

    related_id = Column(
        UUID(as_uuid=True),
        nullable=True,
        index=True,
        doc="Optional ID of related content"
    )

    related_type = Column(
        String(50),
        nullable=True,
        doc="Type of related content"
    )

    is_read = Column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="Whether notification has been read"
    )

    is_sent = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether notification was sent (for push/email)"
    )

    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        doc="Notification creation timestamp"
    )

    def __repr__(self) -> str:
        """String representation of the Notification model."""
        return f"<Notification(id={self.id}, user_id={self.user_id}, type={self.type})>"
