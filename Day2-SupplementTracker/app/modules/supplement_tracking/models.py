"""
Supplement tracking database models.

This module defines SQLAlchemy models for supplement data, intake tracking,
and related functionality, following PEP 484 type hints and PEP 257 docstring standards.
"""

from datetime import datetime
from typing import Optional
from uuid import uuid4

from sqlalchemy import (
    Boolean, Column, DateTime, Foreign<PERSON>ey, Integer, Numeric, String, Text
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class Supplement(Base):
    """
    Supplement model for storing supplement information.
    
    This model stores comprehensive information about supplements including
    their properties, dosage information, and metadata.
    
    Attributes:
        id: Unique supplement identifier (UUID)
        name: Supplement name
        brand: Brand or manufacturer name
        description: Detailed supplement description
        category: Supplement category (e.g., "Vitamin", "Mineral")
        form: Physical form (e.g., "Capsule", "Tablet", "Powder")
        serving_size: Standard serving size
        serving_unit: Unit of measurement for serving size
        ingredients: List of active ingredients
        barcode: Product barcode (optional)
        is_verified: Whether the supplement data is verified
        created_at: Record creation timestamp
        updated_at: Last update timestamp
        created_by_user_id: User who added this supplement
    """
    
    __tablename__ = "supplements"
    
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique supplement identifier"
    )
    
    name = Column(
        String(255),
        nullable=False,
        index=True,
        doc="Supplement name"
    )
    
    brand = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Brand or manufacturer name"
    )
    
    description = Column(
        Text,
        nullable=True,
        doc="Detailed supplement description"
    )
    
    category = Column(
        String(100),
        nullable=False,
        index=True,
        doc="Supplement category"
    )
    
    form = Column(
        String(50),
        nullable=False,
        doc="Physical form of the supplement"
    )
    
    serving_size = Column(
        Numeric(10, 3),
        nullable=True,
        doc="Standard serving size"
    )
    
    serving_unit = Column(
        String(20),
        nullable=True,
        doc="Unit of measurement for serving size"
    )
    
    ingredients = Column(
        Text,
        nullable=True,
        doc="List of active ingredients"
    )
    
    barcode = Column(
        String(50),
        nullable=True,
        unique=True,
        index=True,
        doc="Product barcode"
    )
    
    is_verified = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the supplement data is verified"
    )
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Record creation timestamp"
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="Last update timestamp"
    )
    
    created_by_user_id = Column(
        UUID(as_uuid=True),
        nullable=True,
        index=True,
        doc="User who added this supplement"
    )
    
    def __repr__(self) -> str:
        """String representation of the Supplement model."""
        return f"<Supplement(id={self.id}, name={self.name}, brand={self.brand})>"


class SupplementIntake(Base):
    """
    Supplement intake model for tracking user supplement consumption.
    
    This model records individual instances of supplement intake by users,
    including dosage, timing, and contextual information.
    
    Attributes:
        id: Unique intake record identifier
        user_id: Reference to the user
        supplement_id: Reference to the supplement
        dosage: Amount taken
        dosage_unit: Unit of measurement for dosage
        taken_at: Timestamp when supplement was consumed
        notes: Optional user notes about the intake
        mood_before: Mood rating before taking supplement (1-10)
        mood_after: Mood rating after taking supplement (1-10)
        energy_before: Energy level before taking supplement (1-10)
        energy_after: Energy level after taking supplement (1-10)
        created_at: Record creation timestamp
    """
    
    __tablename__ = "supplement_intakes"
    
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique intake record identifier"
    )
    
    user_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="Reference to the user"
    )
    
    supplement_id = Column(
        UUID(as_uuid=True),
        ForeignKey("supplements.id"),
        nullable=False,
        index=True,
        doc="Reference to the supplement"
    )
    
    dosage = Column(
        Numeric(10, 3),
        nullable=False,
        doc="Amount taken"
    )
    
    dosage_unit = Column(
        String(20),
        nullable=False,
        doc="Unit of measurement for dosage"
    )
    
    taken_at = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Timestamp when supplement was consumed"
    )
    
    notes = Column(
        Text,
        nullable=True,
        doc="Optional user notes about the intake"
    )
    
    mood_before = Column(
        Integer,
        nullable=True,
        doc="Mood rating before taking supplement (1-10)"
    )
    
    mood_after = Column(
        Integer,
        nullable=True,
        doc="Mood rating after taking supplement (1-10)"
    )
    
    energy_before = Column(
        Integer,
        nullable=True,
        doc="Energy level before taking supplement (1-10)"
    )
    
    energy_after = Column(
        Integer,
        nullable=True,
        doc="Energy level after taking supplement (1-10)"
    )
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Record creation timestamp"
    )
    
    # Relationship to supplement
    supplement = relationship("Supplement", backref="intakes")
    
    def __repr__(self) -> str:
        """String representation of the SupplementIntake model."""
        return (
            f"<SupplementIntake(id={self.id}, user_id={self.user_id}, "
            f"supplement_id={self.supplement_id}, taken_at={self.taken_at})>"
        )


class SupplementStack(Base):
    """
    Supplement stack model for grouping supplements taken together.
    
    This model allows users to create and manage combinations of supplements
    that they take together as a "stack".
    
    Attributes:
        id: Unique stack identifier
        user_id: Reference to the user who owns the stack
        name: User-defined name for the stack
        description: Optional description of the stack purpose
        is_active: Whether the stack is currently being used
        created_at: Stack creation timestamp
        updated_at: Last update timestamp
    """
    
    __tablename__ = "supplement_stacks"
    
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique stack identifier"
    )
    
    user_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="Reference to the user who owns the stack"
    )
    
    name = Column(
        String(255),
        nullable=False,
        doc="User-defined name for the stack"
    )
    
    description = Column(
        Text,
        nullable=True,
        doc="Optional description of the stack purpose"
    )
    
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether the stack is currently being used"
    )
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Stack creation timestamp"
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="Last update timestamp"
    )
    
    def __repr__(self) -> str:
        """String representation of the SupplementStack model."""
        return f"<SupplementStack(id={self.id}, name={self.name}, user_id={self.user_id})>"
