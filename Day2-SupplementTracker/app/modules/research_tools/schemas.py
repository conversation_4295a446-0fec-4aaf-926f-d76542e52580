"""
Research tools Pydantic schemas.

This module defines Pydantic models for request/response validation
and serialization for research tools functionality.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


# Research Protocol Schemas
class ResearchProtocolBase(BaseModel):
    """Base schema for research protocols."""
    title: str = Field(..., min_length=1, max_length=500, description="Protocol title")
    description: str = Field(..., min_length=1, description="Detailed protocol description")
    hypothesis: Optional[str] = Field(None, description="Research hypothesis")
    methodology: str = Field(..., min_length=1, description="Experimental methodology")
    variables: Optional[Dict[str, Any]] = Field(None, description="Variables and measurements")
    duration_days: int = Field(..., gt=0, description="Study duration in days")
    sample_size_target: int = Field(..., gt=0, description="Target number of participants")
    ethics_approval: bool = Field(False, description="Ethics approval status")


class ResearchProtocolCreate(ResearchProtocolBase):
    """Schema for creating a research protocol."""
    pass


class ResearchProtocolUpdate(BaseModel):
    """Schema for updating a research protocol."""
    title: Optional[str] = Field(None, min_length=1, max_length=500)
    description: Optional[str] = Field(None, min_length=1)
    hypothesis: Optional[str] = None
    methodology: Optional[str] = Field(None, min_length=1)
    variables: Optional[Dict[str, Any]] = None
    duration_days: Optional[int] = Field(None, gt=0)
    sample_size_target: Optional[int] = Field(None, gt=0)
    status: Optional[str] = Field(None, regex="^(draft|active|completed|cancelled)$")
    ethics_approval: Optional[bool] = None


class ResearchProtocolResponse(ResearchProtocolBase):
    """Schema for research protocol responses."""
    id: UUID
    sample_size_actual: int
    status: str
    created_by_user_id: UUID
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]

    model_config = {"from_attributes": True}


class ResearchProtocolListResponse(BaseModel):
    """Schema for paginated research protocol list responses."""
    protocols: List[ResearchProtocolResponse]
    total: int
    page: int
    size: int
    pages: int


# Literature Reference Schemas
class LiteratureReferenceBase(BaseModel):
    """Base schema for literature references."""
    title: str = Field(..., min_length=1, description="Paper title")
    authors: Optional[str] = Field(None, description="List of authors")
    journal: Optional[str] = Field(None, max_length=255, description="Journal name")
    publication_date: Optional[datetime] = Field(None, description="Publication date")
    doi: Optional[str] = Field(None, max_length=255, description="Digital Object Identifier")
    pmid: Optional[str] = Field(None, max_length=20, description="PubMed ID")
    url: Optional[str] = Field(None, description="Paper URL")
    abstract: Optional[str] = Field(None, description="Paper abstract")
    keywords: Optional[str] = Field(None, description="Research keywords")
    study_type: Optional[str] = Field(None, max_length=50, description="Type of study")
    sample_size: Optional[int] = Field(None, gt=0, description="Study sample size")
    notes: Optional[str] = Field(None, description="User notes and annotations")


class LiteratureReferenceCreate(LiteratureReferenceBase):
    """Schema for creating a literature reference."""
    pass


class LiteratureReferenceUpdate(BaseModel):
    """Schema for updating a literature reference."""
    title: Optional[str] = Field(None, min_length=1)
    authors: Optional[str] = None
    journal: Optional[str] = Field(None, max_length=255)
    publication_date: Optional[datetime] = None
    doi: Optional[str] = Field(None, max_length=255)
    pmid: Optional[str] = Field(None, max_length=20)
    url: Optional[str] = None
    abstract: Optional[str] = None
    keywords: Optional[str] = None
    study_type: Optional[str] = Field(None, max_length=50)
    sample_size: Optional[int] = Field(None, gt=0)
    notes: Optional[str] = None
    quality_score: Optional[float] = Field(None, ge=0, le=10)
    relevance_score: Optional[float] = Field(None, ge=0, le=10)


class LiteratureReferenceResponse(LiteratureReferenceBase):
    """Schema for literature reference responses."""
    id: UUID
    quality_score: Optional[float]
    relevance_score: Optional[float]
    added_by_user_id: UUID
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class LiteratureReferenceListResponse(BaseModel):
    """Schema for paginated literature reference list responses."""
    references: List[LiteratureReferenceResponse]
    total: int
    page: int
    size: int
    pages: int


# Study Participant Schemas
class StudyParticipantBase(BaseModel):
    """Base schema for study participants."""
    participant_code: str = Field(..., min_length=1, max_length=50, description="Anonymous participant identifier")
    demographics: Optional[Dict[str, Any]] = Field(None, description="Demographic data")
    baseline_data: Optional[Dict[str, Any]] = Field(None, description="Baseline measurements")
    notes: Optional[str] = Field(None, description="Researcher notes")


class StudyParticipantCreate(StudyParticipantBase):
    """Schema for creating a study participant."""
    protocol_id: UUID = Field(..., description="Associated research protocol")
    user_id: Optional[UUID] = Field(None, description="Platform user (if registered)")


class StudyParticipantUpdate(BaseModel):
    """Schema for updating a study participant."""
    status: Optional[str] = Field(None, regex="^(invited|enrolled|active|completed|withdrawn)$")
    demographics: Optional[Dict[str, Any]] = None
    baseline_data: Optional[Dict[str, Any]] = None
    consent_given: Optional[bool] = None
    withdrawal_reason: Optional[str] = None
    notes: Optional[str] = None


class StudyParticipantResponse(StudyParticipantBase):
    """Schema for study participant responses."""
    id: UUID
    protocol_id: UUID
    user_id: Optional[UUID]
    status: str
    consent_given: bool
    consent_date: Optional[datetime]
    enrolled_at: Optional[datetime]
    completed_at: Optional[datetime]
    withdrawal_reason: Optional[str]
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class StudyParticipantListResponse(BaseModel):
    """Schema for paginated study participant list responses."""
    participants: List[StudyParticipantResponse]
    total: int
    page: int
    size: int
    pages: int


# Statistical Analysis Schemas
class StatisticalAnalysisBase(BaseModel):
    """Base schema for statistical analyses."""
    title: str = Field(..., min_length=1, max_length=500, description="Analysis title")
    description: Optional[str] = Field(None, description="Analysis description")
    analysis_type: str = Field(..., max_length=50, description="Type of analysis")
    data_source: str = Field(..., max_length=100, description="Source of data")
    variables: Dict[str, Any] = Field(..., description="Variables included in analysis")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Analysis parameters")
    interpretation: Optional[str] = Field(None, description="Analysis interpretation")


class StatisticalAnalysisCreate(StatisticalAnalysisBase):
    """Schema for creating a statistical analysis."""
    protocol_id: Optional[UUID] = Field(None, description="Associated research protocol")


class StatisticalAnalysisUpdate(BaseModel):
    """Schema for updating a statistical analysis."""
    title: Optional[str] = Field(None, min_length=1, max_length=500)
    description: Optional[str] = None
    analysis_type: Optional[str] = Field(None, max_length=50)
    data_source: Optional[str] = Field(None, max_length=100)
    variables: Optional[Dict[str, Any]] = None
    parameters: Optional[Dict[str, Any]] = None
    results: Optional[Dict[str, Any]] = None
    visualizations: Optional[Dict[str, Any]] = None
    interpretation: Optional[str] = None
    confidence_level: Optional[float] = Field(None, ge=0, le=1)
    p_value: Optional[float] = Field(None, ge=0, le=1)
    effect_size: Optional[float] = None


class StatisticalAnalysisResponse(StatisticalAnalysisBase):
    """Schema for statistical analysis responses."""
    id: UUID
    protocol_id: Optional[UUID]
    results: Optional[Dict[str, Any]]
    visualizations: Optional[Dict[str, Any]]
    confidence_level: Optional[float]
    p_value: Optional[float]
    effect_size: Optional[float]
    created_by_user_id: UUID
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class StatisticalAnalysisListResponse(BaseModel):
    """Schema for paginated statistical analysis list responses."""
    analyses: List[StatisticalAnalysisResponse]
    total: int
    page: int
    size: int
    pages: int


# Research Collaboration Schemas
class ResearchCollaborationBase(BaseModel):
    """Base schema for research collaborations."""
    role: str = Field(..., max_length=50, description="Collaboration role")
    permissions: Optional[Dict[str, Any]] = Field(None, description="Permissions structure")
    contribution_description: Optional[str] = Field(None, description="Contribution description")


class ResearchCollaborationCreate(ResearchCollaborationBase):
    """Schema for creating a research collaboration."""
    protocol_id: UUID = Field(..., description="Associated research protocol")
    user_id: UUID = Field(..., description="Collaborating user")


class ResearchCollaborationUpdate(BaseModel):
    """Schema for updating a research collaboration."""
    role: Optional[str] = Field(None, max_length=50)
    permissions: Optional[Dict[str, Any]] = None
    contribution_description: Optional[str] = None
    status: Optional[str] = Field(None, regex="^(invited|active|completed|removed)$")


class ResearchCollaborationResponse(ResearchCollaborationBase):
    """Schema for research collaboration responses."""
    id: UUID
    protocol_id: UUID
    user_id: UUID
    status: str
    invited_by_user_id: UUID
    invited_at: datetime
    accepted_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class ResearchCollaborationListResponse(BaseModel):
    """Schema for paginated research collaboration list responses."""
    collaborations: List[ResearchCollaborationResponse]
    total: int
    page: int
    size: int
    pages: int


# Analysis Request Schemas
class CorrelationAnalysisRequest(BaseModel):
    """Schema for correlation analysis requests."""
    variable_x: str = Field(..., description="First variable for correlation")
    variable_y: str = Field(..., description="Second variable for correlation")
    method: str = Field("pearson", regex="^(pearson|spearman|kendall)$", description="Correlation method")
    confidence_level: float = Field(0.95, ge=0.5, le=0.99, description="Confidence level")
    filter_criteria: Optional[Dict[str, Any]] = Field(None, description="Data filtering criteria")


class RegressionAnalysisRequest(BaseModel):
    """Schema for regression analysis requests."""
    dependent_variable: str = Field(..., description="Dependent variable")
    independent_variables: List[str] = Field(..., min_items=1, description="Independent variables")
    model_type: str = Field("linear", regex="^(linear|logistic|polynomial)$", description="Regression model type")
    confidence_level: float = Field(0.95, ge=0.5, le=0.99, description="Confidence level")
    filter_criteria: Optional[Dict[str, Any]] = Field(None, description="Data filtering criteria")


class HypothesisTestRequest(BaseModel):
    """Schema for hypothesis test requests."""
    test_type: str = Field(..., regex="^(t_test|chi_square|anova|mann_whitney)$", description="Type of hypothesis test")
    variables: List[str] = Field(..., min_items=1, description="Variables to test")
    groups: Optional[List[str]] = Field(None, description="Groups for comparison")
    alpha: float = Field(0.05, ge=0.01, le=0.1, description="Significance level")
    alternative: str = Field("two-sided", regex="^(two-sided|less|greater)$", description="Alternative hypothesis")
    filter_criteria: Optional[Dict[str, Any]] = Field(None, description="Data filtering criteria")
