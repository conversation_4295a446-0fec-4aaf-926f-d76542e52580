"""
User management database models.

This module defines SQLAlchemy models for user authentication and profile
management, following PEP 484 type hints and PEP 257 docstring standards.
"""

from datetime import datetime
from typing import Optional
from uuid import uuid4

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

from app.core.database import Base


class User(Base):
    """
    User model for authentication and profile management.
    
    This model stores user account information including authentication
    credentials and basic profile data.
    
    Attributes:
        id: Unique user identifier (UUID)
        email: User's email address (unique)
        username: User's chosen username (unique)
        hashed_password: Bcrypt hashed password
        full_name: User's full name
        bio: Optional user biography
        is_active: Whether the user account is active
        is_superuser: Whether the user has admin privileges
        is_verified: Whether the user's email is verified
        created_at: Account creation timestamp
        updated_at: Last profile update timestamp
        last_login_at: Last login timestamp
    """
    
    __tablename__ = "users"
    
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique user identifier"
    )
    
    email = Column(
        String(255),
        unique=True,
        index=True,
        nullable=False,
        doc="User's email address"
    )
    
    username = Column(
        String(50),
        unique=True,
        index=True,
        nullable=False,
        doc="User's chosen username"
    )
    
    hashed_password = Column(
        String(255),
        nullable=False,
        doc="Bcrypt hashed password"
    )
    
    full_name = Column(
        String(255),
        nullable=True,
        doc="User's full name"
    )
    
    bio = Column(
        Text,
        nullable=True,
        doc="User biography or description"
    )
    
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether the user account is active"
    )
    
    is_superuser = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the user has admin privileges"
    )
    
    is_verified = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the user's email is verified"
    )
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Account creation timestamp"
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="Last profile update timestamp"
    )
    
    last_login_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Last login timestamp"
    )
    
    def __repr__(self) -> str:
        """String representation of the User model."""
        return f"<User(id={self.id}, username={self.username}, email={self.email})>"


class UserSession(Base):
    """
    User session model for tracking active sessions.
    
    This model stores information about user sessions for security
    and session management purposes.
    
    Attributes:
        id: Unique session identifier
        user_id: Reference to the user
        session_token: Unique session token
        expires_at: Session expiration timestamp
        created_at: Session creation timestamp
        ip_address: IP address of the session
        user_agent: User agent string from the browser
        is_active: Whether the session is currently active
    """
    
    __tablename__ = "user_sessions"
    
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique session identifier"
    )
    
    user_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="Reference to the user"
    )
    
    session_token = Column(
        String(255),
        unique=True,
        index=True,
        nullable=False,
        doc="Unique session token"
    )
    
    expires_at = Column(
        DateTime(timezone=True),
        nullable=False,
        doc="Session expiration timestamp"
    )
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Session creation timestamp"
    )
    
    ip_address = Column(
        String(45),  # IPv6 addresses can be up to 45 characters
        nullable=True,
        doc="IP address of the session"
    )
    
    user_agent = Column(
        Text,
        nullable=True,
        doc="User agent string from the browser"
    )
    
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether the session is currently active"
    )
    
    def __repr__(self) -> str:
        """String representation of the UserSession model."""
        return f"<UserSession(id={self.id}, user_id={self.user_id}, active={self.is_active})>"
