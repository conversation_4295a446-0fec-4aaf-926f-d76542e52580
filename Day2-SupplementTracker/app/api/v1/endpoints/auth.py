"""
Authentication endpoints for the supplement tracking platform.

This module provides endpoints for user authentication, including login,
logout, and token refresh functionality.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.database import get_db
from app.modules.user_management.dependencies import get_current_user
from app.modules.user_management.models import User
from app.modules.user_management.schemas import UserLogin, UserLoginResponse
from app.modules.user_management.services import UserService

router = APIRouter()


@router.post("/login", response_model=UserLoginResponse)
async def login(
    login_data: UserLogin,
    db: AsyncSession = Depends(get_db)
):
    """
    User login endpoint.

    Authenticates a user and returns an access token.

    Args:
        login_data: User login credentials
        db: Database session

    Returns:
        UserLoginResponse: Authentication response with access token and user info

    Raises:
        HTTPException: If authentication fails
    """
    user_service = UserService(db)

    # Authenticate user
    user = await user_service.authenticate_user(login_data)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create access token
    access_token = await user_service.create_access_token_for_user(user)

    return UserLoginResponse(
        access_token=access_token,
        token_type="bearer",
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        user=user
    )


@router.post("/logout")
async def logout(
    current_user: User = Depends(get_current_user)
):
    """
    User logout endpoint.

    Invalidates the current user session.

    Args:
        current_user: Current authenticated user

    Returns:
        dict: Logout confirmation
    """
    # In a stateless JWT system, logout is handled client-side
    # by discarding the token. For enhanced security, we could
    # implement a token blacklist here.

    return {"message": "Successfully logged out"}


@router.post("/refresh")
async def refresh_token(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Token refresh endpoint.

    Creates a new access token for the current user.

    Args:
        current_user: Current authenticated user
        db: Database session

    Returns:
        dict: New access token
    """
    user_service = UserService(db)

    # Create new access token
    access_token = await user_service.create_access_token_for_user(current_user)

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }
