"""
Main API router for version 1 of the supplement tracking platform.

This module aggregates all API routers and provides the main entry point
for all API endpoints.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, supplements, community, research

# Create the main API router
api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(supplements.router, prefix="/supplements", tags=["supplements"])
api_router.include_router(community.router, prefix="/community", tags=["community"])
api_router.include_router(research.router, prefix="/research", tags=["research-tools"])
