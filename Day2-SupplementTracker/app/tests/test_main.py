"""
Tests for the main FastAPI application.

This module contains tests for the main application endpoints,
health checks, and basic functionality.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from httpx import AsyncClient


@pytest.mark.unit
def test_health_check(client: TestClient):
    """
    Test the health check endpoint.
    
    This test verifies that the health check endpoint returns
    the expected response format and status.
    
    Args:
        client: Test client fixture
    """
    response = client.get("/health")
    
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "supplement-tracker-api"
    assert data["version"] == "0.1.0"


@pytest.mark.unit
def test_root_endpoint(client: TestClient):
    """
    Test the root endpoint.
    
    This test verifies that the root endpoint returns
    the expected API information.
    
    Args:
        client: Test client fixture
    """
    response = client.get("/")
    
    assert response.status_code == 200
    
    data = response.json()
    assert "message" in data
    assert "version" in data
    assert "docs_url" in data
    assert "health_url" in data
    assert data["version"] == "0.1.0"


@pytest.mark.integration
@pytest.mark.asyncio
async def test_health_check_async(async_client: AsyncClient):
    """
    Test the health check endpoint with async client.
    
    This test verifies that the health check works correctly
    with async requests.
    
    Args:
        async_client: Async test client fixture
    """
    response = await async_client.get("/health")
    
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "supplement-tracker-api"


@pytest.mark.unit
def test_api_docs_accessible(client: TestClient):
    """
    Test that API documentation is accessible.
    
    This test verifies that the OpenAPI documentation
    endpoints are available.
    
    Args:
        client: Test client fixture
    """
    # Test OpenAPI JSON
    response = client.get("/api/v1/openapi.json")
    assert response.status_code == 200
    
    # Test Swagger UI
    response = client.get("/api/v1/docs")
    assert response.status_code == 200
    
    # Test ReDoc
    response = client.get("/api/v1/redoc")
    assert response.status_code == 200


@pytest.mark.unit
def test_cors_headers(client: TestClient):
    """
    Test CORS headers are properly set.
    
    This test verifies that CORS headers are included
    in responses when configured.
    
    Args:
        client: Test client fixture
    """
    response = client.options("/health")
    
    # Should handle OPTIONS request
    assert response.status_code in [200, 405]  # 405 if OPTIONS not explicitly handled


@pytest.mark.unit
def test_404_handling(client: TestClient):
    """
    Test 404 error handling.
    
    This test verifies that non-existent endpoints
    return proper 404 responses.
    
    Args:
        client: Test client fixture
    """
    response = client.get("/nonexistent-endpoint")
    assert response.status_code == 404
