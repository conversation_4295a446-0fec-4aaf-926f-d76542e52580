"""
Tests for research tools functionality.

This module contains tests for research protocol management,
literature references, statistical analysis, and participant management.
"""

import pytest
from uuid import uuid4

from app.modules.research_tools.models import (
    ResearchProtocol, LiteratureReference, StudyParticipant, 
    StatisticalAnalysis, ResearchCollaboration
)
from app.modules.research_tools.schemas import (
    ResearchProtocolCreate, LiteratureReferenceCreate, StudyParticipantCreate,
    StatisticalAnalysisCreate, ResearchCollaborationCreate
)
from app.modules.research_tools.services import (
    ResearchProtocolService, LiteratureReferenceService, StudyParticipantService,
    StatisticalAnalysisService, ResearchCollaborationService
)


class TestResearchProtocolService:
    """Test cases for ResearchProtocolService."""

    @pytest.mark.asyncio
    async def test_create_protocol(self, db_session):
        """Test creating a research protocol."""
        creator_id = uuid4()
        protocol_data = ResearchProtocolCreate(
            title="Magnesium Sleep Study",
            description="A randomized controlled trial examining the effects of magnesium supplementation on sleep quality",
            hypothesis="Magnesium supplementation will improve sleep quality scores",
            methodology="Double-blind, placebo-controlled trial with 60 participants over 8 weeks",
            variables={"primary": "sleep_quality_score", "secondary": ["sleep_duration", "sleep_latency"]},
            duration_days=56,
            sample_size_target=60,
            ethics_approval=True
        )
        
        protocol = await ResearchProtocolService.create_protocol(db_session, protocol_data, creator_id)
        
        assert protocol.title == "Magnesium Sleep Study"
        assert protocol.duration_days == 56
        assert protocol.sample_size_target == 60
        assert protocol.sample_size_actual == 0
        assert protocol.status == "draft"
        assert protocol.created_by_user_id == creator_id
        assert protocol.ethics_approval is True

    @pytest.mark.asyncio
    async def test_get_protocol(self, db_session):
        """Test getting a research protocol by ID."""
        creator_id = uuid4()
        protocol_data = ResearchProtocolCreate(
            title="Test Protocol",
            description="Test description",
            methodology="Test methodology",
            duration_days=30,
            sample_size_target=20
        )
        
        created_protocol = await ResearchProtocolService.create_protocol(db_session, protocol_data, creator_id)
        retrieved_protocol = await ResearchProtocolService.get_protocol(db_session, created_protocol.id)
        
        assert retrieved_protocol is not None
        assert retrieved_protocol.id == created_protocol.id
        assert retrieved_protocol.title == "Test Protocol"

    @pytest.mark.asyncio
    async def test_get_protocols_with_filtering(self, db_session):
        """Test getting protocols with status filtering."""
        creator_id = uuid4()
        
        # Create protocols with different statuses
        protocol1_data = ResearchProtocolCreate(
            title="Draft Protocol", description="Test", methodology="Test", 
            duration_days=30, sample_size_target=20
        )
        protocol2_data = ResearchProtocolCreate(
            title="Active Protocol", description="Test", methodology="Test", 
            duration_days=30, sample_size_target=20
        )
        
        protocol1 = await ResearchProtocolService.create_protocol(db_session, protocol1_data, creator_id)
        protocol2 = await ResearchProtocolService.create_protocol(db_session, protocol2_data, creator_id)
        
        # Update one protocol to active status
        from app.modules.research_tools.schemas import ResearchProtocolUpdate
        await ResearchProtocolService.update_protocol(
            db_session, protocol2.id, ResearchProtocolUpdate(status="active"), creator_id
        )
        
        # Get draft protocols
        draft_protocols = await ResearchProtocolService.get_protocols(
            db_session, status="draft"
        )
        
        assert len(draft_protocols) >= 1
        assert all(p.status == "draft" for p in draft_protocols)

    @pytest.mark.asyncio
    async def test_get_protocol_statistics(self, db_session):
        """Test getting protocol statistics."""
        creator_id = uuid4()
        protocol_data = ResearchProtocolCreate(
            title="Stats Test Protocol",
            description="Test description",
            methodology="Test methodology",
            duration_days=30,
            sample_size_target=20
        )
        
        protocol = await ResearchProtocolService.create_protocol(db_session, protocol_data, creator_id)
        stats = await ResearchProtocolService.get_protocol_statistics(db_session, protocol.id)
        
        assert "participant_counts" in stats
        assert "total_participants" in stats
        assert "analysis_count" in stats
        assert "collaboration_count" in stats
        assert stats["total_participants"] == 0
        assert stats["analysis_count"] == 0
        assert stats["collaboration_count"] == 0


class TestLiteratureReferenceService:
    """Test cases for LiteratureReferenceService."""

    @pytest.mark.asyncio
    async def test_create_reference(self, db_session):
        """Test creating a literature reference."""
        user_id = uuid4()
        reference_data = LiteratureReferenceCreate(
            title="Effects of Magnesium Supplementation on Sleep Quality: A Systematic Review",
            authors="Smith, J., Johnson, A., Brown, K.",
            journal="Journal of Sleep Research",
            doi="10.1111/jsr.12345",
            pmid="12345678",
            abstract="This systematic review examines the effects of magnesium supplementation on sleep quality...",
            study_type="systematic_review",
            sample_size=500,
            keywords="magnesium, sleep, supplementation, systematic review"
        )
        
        reference = await LiteratureReferenceService.create_reference(db_session, reference_data, user_id)
        
        assert reference.title == "Effects of Magnesium Supplementation on Sleep Quality: A Systematic Review"
        assert reference.doi == "10.1111/jsr.12345"
        assert reference.pmid == "12345678"
        assert reference.study_type == "systematic_review"
        assert reference.added_by_user_id == user_id

    @pytest.mark.asyncio
    async def test_search_by_doi(self, db_session):
        """Test searching literature by DOI."""
        user_id = uuid4()
        doi = "10.1111/jsr.12345"
        reference_data = LiteratureReferenceCreate(
            title="Test Paper",
            doi=doi
        )
        
        await LiteratureReferenceService.create_reference(db_session, reference_data, user_id)
        found_reference = await LiteratureReferenceService.search_by_doi(db_session, doi)
        
        assert found_reference is not None
        assert found_reference.doi == doi
        assert found_reference.title == "Test Paper"

    @pytest.mark.asyncio
    async def test_search_by_pmid(self, db_session):
        """Test searching literature by PubMed ID."""
        user_id = uuid4()
        pmid = "12345678"
        reference_data = LiteratureReferenceCreate(
            title="Test Paper",
            pmid=pmid
        )
        
        await LiteratureReferenceService.create_reference(db_session, reference_data, user_id)
        found_reference = await LiteratureReferenceService.search_by_pmid(db_session, pmid)
        
        assert found_reference is not None
        assert found_reference.pmid == pmid
        assert found_reference.title == "Test Paper"


class TestStudyParticipantService:
    """Test cases for StudyParticipantService."""

    @pytest.mark.asyncio
    async def test_create_participant(self, db_session):
        """Test creating a study participant."""
        # First create a protocol
        creator_id = uuid4()
        protocol_data = ResearchProtocolCreate(
            title="Test Protocol",
            description="Test description",
            methodology="Test methodology",
            duration_days=30,
            sample_size_target=20
        )
        protocol = await ResearchProtocolService.create_protocol(db_session, protocol_data, creator_id)
        
        # Create participant
        participant_data = StudyParticipantCreate(
            protocol_id=protocol.id,
            participant_code="P001",
            demographics={"age": 25, "gender": "female"},
            baseline_data={"weight": 65, "height": 165}
        )
        
        participant = await StudyParticipantService.create_participant(db_session, participant_data, creator_id)
        
        assert participant.protocol_id == protocol.id
        assert participant.participant_code == "P001"
        assert participant.status == "invited"
        assert participant.consent_given is False
        assert participant.demographics["age"] == 25

    @pytest.mark.asyncio
    async def test_generate_participant_code(self, db_session):
        """Test generating unique participant codes."""
        # First create a protocol
        creator_id = uuid4()
        protocol_data = ResearchProtocolCreate(
            title="Test Protocol",
            description="Test description",
            methodology="Test methodology",
            duration_days=30,
            sample_size_target=20
        )
        protocol = await ResearchProtocolService.create_protocol(db_session, protocol_data, creator_id)
        
        # Generate first code
        code1 = await StudyParticipantService.generate_participant_code(db_session, protocol.id)
        assert code1 == "P001"
        
        # Create a participant with this code
        participant_data = StudyParticipantCreate(
            protocol_id=protocol.id,
            participant_code=code1
        )
        await StudyParticipantService.create_participant(db_session, participant_data, creator_id)
        
        # Generate next code
        code2 = await StudyParticipantService.generate_participant_code(db_session, protocol.id)
        assert code2 == "P002"


class TestStatisticalAnalysisService:
    """Test cases for StatisticalAnalysisService."""

    @pytest.mark.asyncio
    async def test_create_analysis(self, db_session):
        """Test creating a statistical analysis."""
        analyst_id = uuid4()
        analysis_data = StatisticalAnalysisCreate(
            title="Correlation Analysis: Magnesium vs Sleep Quality",
            description="Pearson correlation analysis between magnesium dosage and sleep quality scores",
            analysis_type="correlation",
            data_source="study_participants",
            variables={"x": "magnesium_dosage", "y": "sleep_quality_score"},
            parameters={"method": "pearson", "confidence_level": 0.95}
        )
        
        analysis = await StatisticalAnalysisService.create_analysis(db_session, analysis_data, analyst_id)
        
        assert analysis.title == "Correlation Analysis: Magnesium vs Sleep Quality"
        assert analysis.analysis_type == "correlation"
        assert analysis.data_source == "study_participants"
        assert analysis.variables["x"] == "magnesium_dosage"
        assert analysis.created_by_user_id == analyst_id


class TestResearchCollaborationService:
    """Test cases for ResearchCollaborationService."""

    @pytest.mark.asyncio
    async def test_create_collaboration(self, db_session):
        """Test creating a research collaboration."""
        # First create a protocol
        creator_id = uuid4()
        collaborator_id = uuid4()
        protocol_data = ResearchProtocolCreate(
            title="Test Protocol",
            description="Test description",
            methodology="Test methodology",
            duration_days=30,
            sample_size_target=20
        )
        protocol = await ResearchProtocolService.create_protocol(db_session, protocol_data, creator_id)
        
        # Create collaboration
        collaboration_data = ResearchCollaborationCreate(
            protocol_id=protocol.id,
            user_id=collaborator_id,
            role="co_investigator",
            permissions={"can_edit": True, "can_analyze": True},
            contribution_description="Statistical analysis and data interpretation"
        )
        
        collaboration = await ResearchCollaborationService.create_collaboration(
            db_session, collaboration_data, creator_id
        )
        
        assert collaboration.protocol_id == protocol.id
        assert collaboration.user_id == collaborator_id
        assert collaboration.role == "co_investigator"
        assert collaboration.status == "invited"
        assert collaboration.invited_by_user_id == creator_id

    @pytest.mark.asyncio
    async def test_accept_collaboration(self, db_session):
        """Test accepting a collaboration invitation."""
        # First create a protocol and collaboration
        creator_id = uuid4()
        collaborator_id = uuid4()
        protocol_data = ResearchProtocolCreate(
            title="Test Protocol",
            description="Test description",
            methodology="Test methodology",
            duration_days=30,
            sample_size_target=20
        )
        protocol = await ResearchProtocolService.create_protocol(db_session, protocol_data, creator_id)
        
        collaboration_data = ResearchCollaborationCreate(
            protocol_id=protocol.id,
            user_id=collaborator_id,
            role="analyst"
        )
        collaboration = await ResearchCollaborationService.create_collaboration(
            db_session, collaboration_data, creator_id
        )
        
        # Accept the collaboration
        accepted_collaboration = await ResearchCollaborationService.accept_collaboration(
            db_session, collaboration.id, collaborator_id
        )
        
        assert accepted_collaboration is not None
        assert accepted_collaboration.status == "active"
        assert accepted_collaboration.accepted_at is not None
