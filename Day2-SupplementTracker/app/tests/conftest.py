"""
Pytest configuration and fixtures for the supplement tracking platform.

This module provides shared test fixtures and configuration for all tests,
following pytest best practices and ensuring consistent test setup.
"""

import asyncio
from typing import AsyncGenerator, Generator
from unittest.mock import AsyncMock

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.pool import StaticPool

from app.core.config import settings
from app.core.database import Base, get_db
from app.main import app

# Test database URL - using SQLite for testing
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# Create test engine with in-memory SQLite
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    echo=False,
    poolclass=StaticPool,
    connect_args={"check_same_thread": False},
)

# Test session factory
TestSessionLocal = async_sessionmaker(
    test_engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False,
)


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """
    Create an event loop for the test session.
    
    This fixture ensures that async tests have access to an event loop
    throughout the entire test session.
    
    Yields:
        asyncio.AbstractEventLoop: Event loop for async operations
    """
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Create a test database session.
    
    This fixture provides a clean database session for each test,
    ensuring test isolation and consistent state.
    
    Yields:
        AsyncSession: Database session for testing
    """
    # Create all tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create session
    async with TestSessionLocal() as session:
        yield session
    
    # Clean up - drop all tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture
def override_get_db(db_session: AsyncSession):
    """
    Override the database dependency for testing.
    
    This fixture replaces the production database dependency with
    the test database session.
    
    Args:
        db_session: Test database session
        
    Returns:
        Callable: Database dependency override function
    """
    async def _override_get_db():
        yield db_session
    
    return _override_get_db


@pytest.fixture
def client(override_get_db) -> TestClient:
    """
    Create a test client for the FastAPI application.
    
    This fixture provides a test client with the database dependency
    overridden to use the test database.
    
    Args:
        override_get_db: Database dependency override
        
    Returns:
        TestClient: Test client for making HTTP requests
    """
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    # Clean up dependency overrides
    app.dependency_overrides.clear()


@pytest_asyncio.fixture
async def async_client(override_get_db) -> AsyncGenerator[AsyncClient, None]:
    """
    Create an async test client for the FastAPI application.
    
    This fixture provides an async test client for testing async endpoints
    with the database dependency overridden.
    
    Args:
        override_get_db: Database dependency override
        
    Yields:
        AsyncClient: Async test client for making HTTP requests
    """
    app.dependency_overrides[get_db] = override_get_db
    
    async with AsyncClient(app=app, base_url="http://test") as async_test_client:
        yield async_test_client
    
    # Clean up dependency overrides
    app.dependency_overrides.clear()


@pytest.fixture
def mock_event_bus():
    """
    Create a mock event bus for testing.
    
    This fixture provides a mock event bus to isolate tests from
    event-driven side effects.
    
    Returns:
        AsyncMock: Mock event bus instance
    """
    return AsyncMock()


@pytest.fixture
def sample_user_data():
    """
    Provide sample user data for testing.
    
    Returns:
        dict: Sample user data for test scenarios
    """
    return {
        "email": "<EMAIL>",
        "username": "testuser",
        "password": "TestPassword123",
        "full_name": "Test User",
        "bio": "This is a test user account"
    }


@pytest.fixture
def sample_supplement_data():
    """
    Provide sample supplement data for testing.
    
    Returns:
        dict: Sample supplement data for test scenarios
    """
    return {
        "name": "Vitamin D3",
        "brand": "Test Brand",
        "description": "High-quality vitamin D3 supplement",
        "category": "Vitamin",
        "form": "Capsule",
        "serving_size": 1000.0,
        "serving_unit": "IU",
        "ingredients": "Cholecalciferol (Vitamin D3)",
        "barcode": "*************"
    }


@pytest.fixture
def sample_intake_data():
    """
    Provide sample supplement intake data for testing.
    
    Returns:
        dict: Sample intake data for test scenarios
    """
    return {
        "dosage": 1000.0,
        "dosage_unit": "IU",
        "notes": "Taken with breakfast",
        "mood_before": 7,
        "mood_after": 8,
        "energy_before": 6,
        "energy_after": 7
    }


# Pytest configuration
pytest_plugins = ["pytest_asyncio"]


def pytest_configure(config):
    """
    Configure pytest with custom markers and settings.
    
    Args:
        config: Pytest configuration object
    """
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


@pytest.fixture(autouse=True)
def enable_testing_mode():
    """
    Enable testing mode for all tests.
    
    This fixture automatically sets the TESTING flag to True
    for all tests to ensure proper test configuration.
    """
    original_testing = settings.TESTING
    settings.TESTING = True
    yield
    settings.TESTING = original_testing
