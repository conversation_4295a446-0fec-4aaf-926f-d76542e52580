"""
Global error handlers for FastAPI application.

This module provides centralized error handling for the FastAPI application,
ensuring consistent error responses and proper logging.
"""

import logging
from typing import Union

from fastapi import HTTPException, Request, status
from fastapi.responses import JSONResponse
from pydantic import ValidationError as PydanticValidationError
from sqlalchemy.exc import Integrity<PERSON>rror, SQLAlchemyError
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.core.exceptions import (
    SupplementTrackerException,
    ValidationError,
    NotFoundError,
    PermissionError,
    ConflictError,
    AuthenticationError,
    BusinessLogicError,
    ExternalServiceError,
    DatabaseError,
    RateLimitError,
    MaintenanceError,
    get_error_details
)

logger = logging.getLogger(__name__)


async def supplement_tracker_exception_handler(
    request: Request, 
    exc: SupplementTrackerException
) -> JSONResponse:
    """
    Handle custom SupplementTrackerException instances.
    
    Args:
        request: FastAPI request object
        exc: Custom exception instance
        
    Returns:
        JSONResponse: Formatted error response
    """
    # Map exception types to HTTP status codes
    status_code_mapping = {
        ValidationError: status.HTTP_400_BAD_REQUEST,
        NotFoundError: status.HTTP_404_NOT_FOUND,
        PermissionError: status.HTTP_403_FORBIDDEN,
        ConflictError: status.HTTP_409_CONFLICT,
        AuthenticationError: status.HTTP_401_UNAUTHORIZED,
        BusinessLogicError: status.HTTP_422_UNPROCESSABLE_ENTITY,
        ExternalServiceError: status.HTTP_502_BAD_GATEWAY,
        DatabaseError: status.HTTP_500_INTERNAL_SERVER_ERROR,
        RateLimitError: status.HTTP_429_TOO_MANY_REQUESTS,
        MaintenanceError: status.HTTP_503_SERVICE_UNAVAILABLE,
    }
    
    status_code = status_code_mapping.get(type(exc), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    # Log the error
    logger.error(
        f"Custom exception occurred: {exc.error_code}",
        extra={
            "error_code": exc.error_code,
            "message": exc.message,
            "details": exc.details,
            "path": request.url.path,
            "method": request.method,
        }
    )
    
    return JSONResponse(
        status_code=status_code,
        content=get_error_details(exc)
    )


async def http_exception_handler(
    request: Request, 
    exc: Union[HTTPException, StarletteHTTPException]
) -> JSONResponse:
    """
    Handle HTTPException instances.
    
    Args:
        request: FastAPI request object
        exc: HTTP exception instance
        
    Returns:
        JSONResponse: Formatted error response
    """
    logger.warning(
        f"HTTP exception occurred: {exc.status_code}",
        extra={
            "status_code": exc.status_code,
            "detail": exc.detail,
            "path": request.url.path,
            "method": request.method,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error_code": f"HTTP_{exc.status_code}",
            "message": exc.detail,
            "details": {}
        }
    )


async def validation_exception_handler(
    request: Request, 
    exc: PydanticValidationError
) -> JSONResponse:
    """
    Handle Pydantic validation errors.
    
    Args:
        request: FastAPI request object
        exc: Pydantic validation error
        
    Returns:
        JSONResponse: Formatted validation error response
    """
    logger.warning(
        "Validation error occurred",
        extra={
            "errors": exc.errors(),
            "path": request.url.path,
            "method": request.method,
        }
    )
    
    # Format validation errors for better client understanding
    formatted_errors = []
    for error in exc.errors():
        formatted_errors.append({
            "field": ".".join(str(loc) for loc in error["loc"]),
            "message": error["msg"],
            "type": error["type"],
        })
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error_code": "VALIDATION_ERROR",
            "message": "Input validation failed",
            "details": {
                "validation_errors": formatted_errors
            }
        }
    )


async def sqlalchemy_exception_handler(
    request: Request, 
    exc: SQLAlchemyError
) -> JSONResponse:
    """
    Handle SQLAlchemy database errors.
    
    Args:
        request: FastAPI request object
        exc: SQLAlchemy exception
        
    Returns:
        JSONResponse: Formatted database error response
    """
    logger.error(
        "Database error occurred",
        extra={
            "error": str(exc),
            "path": request.url.path,
            "method": request.method,
        },
        exc_info=True
    )
    
    # Handle specific SQLAlchemy errors
    if isinstance(exc, IntegrityError):
        # Extract meaningful error message from integrity constraint violations
        error_message = "Data integrity constraint violation"
        if "duplicate key" in str(exc).lower():
            error_message = "Duplicate entry - resource already exists"
        elif "foreign key" in str(exc).lower():
            error_message = "Referenced resource does not exist"
        elif "not null" in str(exc).lower():
            error_message = "Required field is missing"
        
        return JSONResponse(
            status_code=status.HTTP_409_CONFLICT,
            content={
                "error_code": "INTEGRITY_ERROR",
                "message": error_message,
                "details": {}
            }
        )
    
    # Generic database error
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error_code": "DATABASE_ERROR",
            "message": "An internal database error occurred",
            "details": {}
        }
    )


async def generic_exception_handler(
    request: Request, 
    exc: Exception
) -> JSONResponse:
    """
    Handle unexpected exceptions.
    
    Args:
        request: FastAPI request object
        exc: Generic exception
        
    Returns:
        JSONResponse: Formatted generic error response
    """
    logger.error(
        "Unexpected error occurred",
        extra={
            "error": str(exc),
            "error_type": type(exc).__name__,
            "path": request.url.path,
            "method": request.method,
        },
        exc_info=True
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error_code": "INTERNAL_ERROR",
            "message": "An internal server error occurred",
            "details": {}
        }
    )


# Exception handler mapping for easy registration
EXCEPTION_HANDLERS = {
    SupplementTrackerException: supplement_tracker_exception_handler,
    HTTPException: http_exception_handler,
    StarletteHTTPException: http_exception_handler,
    PydanticValidationError: validation_exception_handler,
    SQLAlchemyError: sqlalchemy_exception_handler,
    Exception: generic_exception_handler,
}
