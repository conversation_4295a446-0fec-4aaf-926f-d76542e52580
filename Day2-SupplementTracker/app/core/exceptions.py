"""
Custom exceptions and error handling for the supplement tracking platform.

This module defines custom exceptions and provides centralized error handling
for consistent API responses and proper error logging.
"""

from typing import Any, Dict, Optional


class SupplementTrackerException(Exception):
    """
    Base exception class for the supplement tracking platform.
    
    All custom exceptions should inherit from this base class to ensure
    consistent error handling throughout the application.
    
    Attributes:
        message: Human-readable error message
        error_code: Machine-readable error code
        details: Additional error details
    """
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the exception.
        
        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            details: Additional error details
        """
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(SupplementTrackerException):
    """
    Exception raised when input validation fails.
    
    This exception is used for client-side errors where the request
    data doesn't meet validation requirements.
    """
    pass


class NotFoundError(SupplementTrackerException):
    """
    Exception raised when a requested resource is not found.
    
    This exception is used when a user requests a resource that
    doesn't exist in the database.
    """
    pass


class PermissionError(SupplementTrackerException):
    """
    Exception raised when a user lacks permission for an operation.
    
    This exception is used for authorization failures where the user
    is authenticated but doesn't have the required permissions.
    """
    pass


class ConflictError(SupplementTrackerException):
    """
    Exception raised when a resource conflict occurs.
    
    This exception is used when attempting to create or update a resource
    would result in a conflict (e.g., duplicate email, barcode).
    """
    pass


class AuthenticationError(SupplementTrackerException):
    """
    Exception raised when authentication fails.
    
    This exception is used when a user provides invalid credentials
    or when token validation fails.
    """
    pass


class BusinessLogicError(SupplementTrackerException):
    """
    Exception raised when business logic rules are violated.
    
    This exception is used for domain-specific errors that don't fit
    into other categories but represent invalid business operations.
    """
    pass


class ExternalServiceError(SupplementTrackerException):
    """
    Exception raised when an external service fails.
    
    This exception is used when integrations with external APIs
    or services fail or return unexpected responses.
    """
    pass


class DatabaseError(SupplementTrackerException):
    """
    Exception raised when database operations fail.
    
    This exception is used for database-related errors that aren't
    handled by SQLAlchemy's built-in exceptions.
    """
    pass


class RateLimitError(SupplementTrackerException):
    """
    Exception raised when rate limits are exceeded.
    
    This exception is used when a user or IP address exceeds
    the configured rate limits for API requests.
    """
    pass


class MaintenanceError(SupplementTrackerException):
    """
    Exception raised when the system is under maintenance.
    
    This exception is used to indicate that the system is temporarily
    unavailable due to maintenance activities.
    """
    pass


# Error code mappings for consistent API responses
ERROR_CODE_MAPPING = {
    ValidationError: "VALIDATION_ERROR",
    NotFoundError: "NOT_FOUND",
    PermissionError: "PERMISSION_DENIED",
    ConflictError: "CONFLICT",
    AuthenticationError: "AUTHENTICATION_FAILED",
    BusinessLogicError: "BUSINESS_LOGIC_ERROR",
    ExternalServiceError: "EXTERNAL_SERVICE_ERROR",
    DatabaseError: "DATABASE_ERROR",
    RateLimitError: "RATE_LIMIT_EXCEEDED",
    MaintenanceError: "MAINTENANCE_MODE",
}


def get_error_code(exception: Exception) -> str:
    """
    Get the error code for an exception.
    
    Args:
        exception: Exception instance
        
    Returns:
        str: Error code for the exception
    """
    if isinstance(exception, SupplementTrackerException):
        return exception.error_code
    
    exception_type = type(exception)
    return ERROR_CODE_MAPPING.get(exception_type, "INTERNAL_ERROR")


def get_error_details(exception: Exception) -> Dict[str, Any]:
    """
    Get error details for an exception.
    
    Args:
        exception: Exception instance
        
    Returns:
        Dict[str, Any]: Error details dictionary
    """
    if isinstance(exception, SupplementTrackerException):
        return {
            "error_code": exception.error_code,
            "message": exception.message,
            "details": exception.details
        }
    
    return {
        "error_code": get_error_code(exception),
        "message": str(exception),
        "details": {}
    }
