"""
Configuration management for the supplement tracking platform.

This module handles all configuration settings using Pydantic Settings
for type safety and validation, following PEP 484 type hinting standards.
"""

import secrets
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, EmailStr, HttpUrl, field_validator
from pydantic_settings import BaseSettings
from pydantic_core import ValidationError


class Settings(BaseSettings):
    """
    Application settings with environment variable support.
    
    All settings can be overridden via environment variables with the
    SUPPLEMENT_TRACKER_ prefix (e.g., SUPPLEMENT_TRACKER_DEBUG=true).
    
    Attributes:
        API_V1_STR: API version prefix for all endpoints
        SECRET_KEY: Secret key for JWT token generation and encryption
        ACCESS_TOKEN_EXPIRE_MINUTES: JWT token expiration time in minutes
        SERVER_NAME: Server name for CORS and documentation
        SERVER_HOST: Host address for the server
        BACKEND_CORS_ORIGINS: List of allowed CORS origins
        PROJECT_NAME: Human-readable project name
        POSTGRES_SERVER: PostgreSQL server hostname
        POSTGRES_USER: PostgreSQL username
        POSTGRES_PASSWORD: PostgreSQL password
        POSTGRES_DB: PostgreSQL database name
        SQLALCHEMY_DATABASE_URI: Complete database connection string
        REDIS_URL: Redis connection URL for caching and sessions
        ELASTICSEARCH_URL: Elasticsearch connection URL for search
        FIRST_SUPERUSER: Email of the first superuser account
        FIRST_SUPERUSER_PASSWORD: Password for the first superuser
        USERS_OPEN_REGISTRATION: Whether to allow open user registration
        EMAIL_TEST_USER: Test email for development
        SENTRY_DSN: Sentry DSN for error tracking
        CELERY_BROKER_URL: Celery broker URL for background tasks
        CELERY_RESULT_BACKEND: Celery result backend URL
    """
    
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    SERVER_NAME: str = "localhost"
    SERVER_HOST: AnyHttpUrl = "http://localhost"
    
    # CORS origins - list of allowed origins for cross-origin requests
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        """Parse CORS origins from environment variable or list."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    PROJECT_NAME: str = "Supplement Tracker"
    
    # Database configuration
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "supplement_user"
    POSTGRES_PASSWORD: str = "supplement_password"
    POSTGRES_DB: str = "supplement_tracker"
    SQLALCHEMY_DATABASE_URI: Optional[str] = None

    @field_validator("SQLALCHEMY_DATABASE_URI", mode="before")
    @classmethod
    def assemble_db_connection(cls, v: Optional[str], info) -> Any:
        """Assemble database connection string from individual components."""
        if isinstance(v, str):
            return v
        # Build connection string manually since PostgresDsn is not available in Pydantic v2
        # For Pydantic v2, we need to use environment variables directly
        import os
        user = os.getenv("SUPPLEMENT_TRACKER_POSTGRES_USER", "supplement_user")
        password = os.getenv("SUPPLEMENT_TRACKER_POSTGRES_PASSWORD", "supplement_password")
        host = os.getenv("SUPPLEMENT_TRACKER_POSTGRES_SERVER", "localhost")
        db = os.getenv("SUPPLEMENT_TRACKER_POSTGRES_DB", "supplement_tracker")
        return f"postgresql+asyncpg://{user}:{password}@{host}/{db}"

    # Redis configuration for caching and sessions
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Elasticsearch configuration for search functionality
    ELASTICSEARCH_URL: str = "http://localhost:9200"
    
    # First superuser configuration
    FIRST_SUPERUSER: EmailStr = "<EMAIL>"
    FIRST_SUPERUSER_PASSWORD: str = "changeme"
    USERS_OPEN_REGISTRATION: bool = False

    # Email configuration for testing
    EMAIL_TEST_USER: EmailStr = "<EMAIL>"
    
    # Monitoring and error tracking
    SENTRY_DSN: Optional[HttpUrl] = None
    
    # Background task configuration
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    
    # Environment and debugging
    DEBUG: bool = False
    TESTING: bool = False
    
    # Security settings
    BCRYPT_ROUNDS: int = 12
    
    # File upload settings
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: List[str] = [".jpg", ".jpeg", ".png", ".pdf", ".csv"]
    
    # Rate limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    
    # Pagination defaults
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100

    # Email reset token expiration
    EMAIL_RESET_TOKEN_EXPIRE_HOURS: int = 48

    model_config = {
        "env_prefix": "SUPPLEMENT_TRACKER_",
        "case_sensitive": True,
    }


# Global settings instance
settings = Settings()
