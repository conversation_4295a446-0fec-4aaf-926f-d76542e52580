# Documentation build requirements
# Install with: pip install -r requirements.txt

# Core Sphinx
sphinx>=7.0.0,<8.0.0

# Sphinx themes
sphinx-rtd-theme>=1.3.0

# Sphinx extensions
sphinx-autodoc-typehints>=1.24.0
sphinx-copybutton>=0.5.2

# MyST parser for Markdown support (optional)
myst-parser>=2.0.0

# Live reload for development
sphinx-autobuild>=2021.3.14

# Additional utilities
sphinxcontrib-mermaid>=0.9.2
sphinxcontrib-plantuml>=0.25
