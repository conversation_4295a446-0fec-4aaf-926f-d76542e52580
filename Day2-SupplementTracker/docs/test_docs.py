#!/usr/bin/env python3
"""
Comprehensive documentation testing script.
Tests builds, links, examples, and documentation quality.
"""

import os
import re
import sys
import subprocess
import json
from pathlib import Path
from urllib.parse import urlparse


def run_command(cmd, cwd=None):
    """Run a command and return success status and output."""
    try:
        result = subprocess.run(
            cmd, shell=True, cwd=cwd, capture_output=True, text=True
        )
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)


def test_sphinx_build():
    """Test Sphinx documentation build."""
    print("🔨 Testing Sphinx Build...")
    
    # Test normal build
    success, stdout, stderr = run_command("./build.sh html", cwd="docs")
    if not success:
        print("  ❌ Normal build failed")
        print(f"  Error: {stderr}")
        return False
    
    print("  ✅ Normal build successful")
    
    # Test strict build (warnings as errors)
    success, stdout, stderr = run_command("./build.sh html true", cwd="docs")
    if success:
        print("  ✅ Strict build successful (no warnings)")
    else:
        print("  ⚠️  Strict build failed (has warnings)")
        # Count warnings
        warning_count = stderr.count("WARNING") + stderr.count("ERROR")
        print(f"  📊 Found {warning_count} warnings/errors")
    
    return True


def test_file_structure():
    """Test documentation file structure."""
    print("📁 Testing File Structure...")
    
    docs_dir = Path("docs")
    required_files = [
        "index.rst",
        "conf.py",
        "Makefile",
        "build.sh",
        "_static/custom.css",
        "requirements.txt"
    ]
    
    required_dirs = [
        "user-guide",
        "api", 
        "architecture",
        "developer"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not (docs_dir / file_path).exists():
            missing_files.append(file_path)
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not (docs_dir / dir_path).is_dir():
            missing_dirs.append(dir_path)
    
    if missing_files:
        print(f"  ❌ Missing files: {missing_files}")
        return False
    
    if missing_dirs:
        print(f"  ❌ Missing directories: {missing_dirs}")
        return False
    
    print("  ✅ All required files and directories present")
    return True


def test_rst_syntax():
    """Test RST syntax in all files."""
    print("📝 Testing RST Syntax...")
    
    docs_dir = Path("docs")
    rst_files = list(docs_dir.rglob("*.rst"))
    
    issues = []
    for rst_file in rst_files:
        try:
            with open(rst_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for common RST issues
            lines = content.split('\n')
            for i, line in enumerate(lines):
                # Check for title underline mismatches
                if i + 1 < len(lines):
                    next_line = lines[i + 1]
                    if re.match(r'^[=\-~^"\'`#*+<>_]+\s*$', next_line.strip()):
                        title_len = len(line.rstrip())
                        underline_len = len(next_line.rstrip())
                        # Allow some tolerance for minor differences
                        if abs(title_len - underline_len) > 1 and title_len > 0:
                            issues.append(f"{rst_file}:{i+1} - Title underline length mismatch (title: {title_len}, underline: {underline_len})")

                # Check for duplicate consecutive lines (but not empty lines)
                if i + 1 < len(lines):
                    if line == lines[i + 1] and line.strip():
                        issues.append(f"{rst_file}:{i+1} - Duplicate consecutive line")
        
        except Exception as e:
            issues.append(f"{rst_file} - Error reading file: {e}")
    
    if issues:
        print(f"  ❌ Found {len(issues)} RST syntax issues:")
        for issue in issues[:10]:  # Show first 10
            print(f"    {issue}")
        if len(issues) > 10:
            print(f"    ... and {len(issues) - 10} more")
        return False
    
    print(f"  ✅ All {len(rst_files)} RST files have valid syntax")
    return True


def test_internal_links():
    """Test internal documentation links."""
    print("🔗 Testing Internal Links...")
    
    docs_dir = Path("docs")
    rst_files = list(docs_dir.rglob("*.rst"))
    
    # Find all :doc: references
    doc_refs = []
    for rst_file in rst_files:
        try:
            with open(rst_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find :doc:`path` references
            matches = re.findall(r':doc:`([^`]+)`', content)
            for match in matches:
                doc_refs.append((rst_file, match))
        
        except Exception as e:
            print(f"  ⚠️  Error reading {rst_file}: {e}")
    
    # Check if referenced documents exist
    broken_links = []
    for rst_file, ref in doc_refs:
        # Resolve relative path
        if ref.startswith('/'):
            # Absolute path from docs root
            target_path = docs_dir / (ref[1:] + '.rst')
        else:
            # Relative path from current file
            target_path = rst_file.parent / (ref + '.rst')
            target_path = target_path.resolve()
        
        if not target_path.exists():
            broken_links.append(f"{rst_file} -> {ref}")
    
    if broken_links:
        print(f"  ❌ Found {len(broken_links)} broken internal links:")
        for link in broken_links[:10]:
            print(f"    {link}")
        if len(broken_links) > 10:
            print(f"    ... and {len(broken_links) - 10} more")
        return False
    
    print(f"  ✅ All {len(doc_refs)} internal links are valid")
    return True


def test_code_examples():
    """Test code examples in documentation."""
    print("💻 Testing Code Examples...")
    
    docs_dir = Path("docs")
    rst_files = list(docs_dir.rglob("*.rst"))
    
    code_blocks = []
    for rst_file in rst_files:
        try:
            with open(rst_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find code blocks
            matches = re.finditer(r'.. code-block:: (\w+)\n\n((?:    .*\n)*)', content)
            for match in matches:
                language = match.group(1)
                code = match.group(2)
                code_blocks.append((rst_file, language, code))
        
        except Exception as e:
            print(f"  ⚠️  Error reading {rst_file}: {e}")
    
    # Basic validation of code examples
    issues = []
    for rst_file, language, code in code_blocks:
        # Check for common issues
        if language == 'python':
            # Check for basic Python syntax issues
            if 'import' in code and not code.strip().startswith('import'):
                # Check if imports are at the top
                lines = [line.strip() for line in code.split('\n') if line.strip()]
                if lines and 'import' in lines[0]:
                    continue  # Looks good
            
        elif language == 'bash':
            # Check for dangerous commands
            dangerous_commands = ['rm -rf /', 'sudo rm', 'del /']
            for cmd in dangerous_commands:
                if cmd in code:
                    issues.append(f"{rst_file} - Dangerous command in bash example: {cmd}")
    
    if issues:
        print(f"  ❌ Found {len(issues)} code example issues:")
        for issue in issues:
            print(f"    {issue}")
        return False
    
    print(f"  ✅ All {len(code_blocks)} code examples look valid")
    return True


def test_build_outputs():
    """Test that build outputs are properly generated."""
    print("📦 Testing Build Outputs...")
    
    build_dir = Path("docs/_build/html")
    
    if not build_dir.exists():
        print("  ❌ Build directory doesn't exist")
        return False
    
    required_files = [
        "index.html",
        "genindex.html",
        "search.html",
        "_static/custom.css"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not (build_dir / file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"  ❌ Missing build files: {missing_files}")
        return False
    
    # Check that main sections are built
    required_sections = [
        "user-guide",
        "api", 
        "architecture",
        "developer"
    ]
    
    missing_sections = []
    for section in required_sections:
        section_dir = build_dir / section
        if not section_dir.exists() or not any(section_dir.glob("*.html")):
            missing_sections.append(section)
    
    if missing_sections:
        print(f"  ❌ Missing section builds: {missing_sections}")
        return False
    
    print("  ✅ All required build outputs present")
    return True


def generate_report():
    """Generate a comprehensive test report."""
    print("\n" + "="*60)
    print("📊 DOCUMENTATION TEST REPORT")
    print("="*60)
    
    tests = [
        ("File Structure", test_file_structure),
        ("RST Syntax", test_rst_syntax),
        ("Internal Links", test_internal_links),
        ("Code Examples", test_code_examples),
        ("Sphinx Build", test_sphinx_build),
        ("Build Outputs", test_build_outputs),
    ]
    
    results = {}
    total_tests = len(tests)
    passed_tests = 0
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed_tests += 1
        except Exception as e:
            print(f"  ❌ Test failed with exception: {e}")
            results[test_name] = False
    
    print("\n" + "="*60)
    print("📈 SUMMARY")
    print("="*60)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
    
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! Documentation is ready for production.")
        return True
    else:
        print("⚠️  Some tests failed. Please review and fix the issues above.")
        return False


def main():
    """Main function."""
    print("🧪 SUPPLEMENT TRACKER DOCUMENTATION TESTING")
    print("="*60)
    
    # Change to project root if we're not there
    if not Path("docs").exists():
        print("❌ Please run this script from the project root directory")
        sys.exit(1)
    
    success = generate_report()
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
