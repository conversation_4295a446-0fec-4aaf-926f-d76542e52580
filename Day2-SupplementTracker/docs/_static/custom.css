/* Custom CSS for Supplement Tracker Documentation */

/* Brand colors */
:root {
    --primary-color: #2980B9;
    --secondary-color: #27AE60;
    --accent-color: #E74C3C;
    --warning-color: #F39C12;
    --info-color: #3498DB;
    --success-color: #27AE60;
    --dark-color: #2C3E50;
    --light-color: #ECF0F1;
}

/* Custom styling for code blocks */
.highlight {
    border-radius: 4px;
    border: 1px solid #e1e4e5;
}

/* Custom admonitions */
.admonition.note {
    border-left: 4px solid var(--info-color);
}

.admonition.warning {
    border-left: 4px solid var(--warning-color);
}

.admonition.danger {
    border-left: 4px solid var(--accent-color);
}

.admonition.tip {
    border-left: 4px solid var(--success-color);
}

/* API endpoint styling */
.http-method {
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 3px;
    color: white;
    font-size: 0.8em;
}

.http-get {
    background-color: var(--success-color);
}

.http-post {
    background-color: var(--info-color);
}

.http-put {
    background-color: var(--warning-color);
}

.http-delete {
    background-color: var(--accent-color);
}

/* Table styling */
table.docutils {
    border-collapse: collapse;
    border: 1px solid #e1e4e5;
}

table.docutils th {
    background-color: var(--light-color);
    border: 1px solid #e1e4e5;
    padding: 8px 12px;
}

table.docutils td {
    border: 1px solid #e1e4e5;
    padding: 8px 12px;
}

/* Logo and branding */
.wy-side-nav-search {
    background-color: var(--primary-color);
}

.wy-side-nav-search .wy-dropdown > a,
.wy-side-nav-search > a {
    color: white;
    font-size: 1.2em;
    font-weight: bold;
}

/* Navigation improvements */
.wy-nav-content {
    max-width: 1200px;
}

/* Code documentation improvements */
.py.class > dt {
    background-color: #f8f9fa;
    border-left: 4px solid var(--primary-color);
    padding-left: 8px;
}

.py.method > dt {
    background-color: #f8f9fa;
    border-left: 4px solid var(--secondary-color);
    padding-left: 8px;
}

.py.function > dt {
    background-color: #f8f9fa;
    border-left: 4px solid var(--info-color);
    padding-left: 8px;
}

/* Responsive improvements */
@media screen and (max-width: 768px) {
    .wy-nav-content {
        margin-left: 0;
    }
}
