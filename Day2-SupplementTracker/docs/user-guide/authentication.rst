====================
Authentication Guide
====================

This guide covers user authentication, authorization, and security features in the Supplement Tracker platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Authentication Overview
=======================
======================

Authentication Methods

The Supplement Tracker supports multiple authentication methods:

1. **JWT Bearer Tokens** (Primary method)
2. **API Keys** (For integrations and automation)
3. **Session-based** (Web application support)

**Security Features**:

- Secure password hashing with bcrypt
- JWT tokens with configurable expiration
- Refresh token rotation
- Multi-factor authentication (planned)
- Rate limiting on authentication endpoints

User Registration
=================

Creating a New Account

**Endpoint**: ``POST /api/v1/auth/register``

**Required Fields**:

- ``email``: Valid email address (unique)
- ``username``: Alphanumeric username (unique, 3-50 characters)
- ``password``: Strong password (minimum 8 characters)

**Optional Fields**:

- ``full_name``: User's full name
- ``bio``: User biography or description

**Password Requirements**:

- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character

**Example Request**:

.. code-block:: bash

    curl -X POST "https://api.supplementtracker.com/api/v1/auth/register" \
         -H "Content-Type: application/json" \
         -d '{
           "email": "<EMAIL>",
           "username": "healthuser",
           "password": "SecurePass123!",
           "full_name": "Health User",
           "bio": "Passionate about health and wellness"
         }'

**Success Response** (201 Created):

.. code-block:: json

    {
        "data": {
            "id": "123e4567-e89b-12d3-a456-************",
            "email": "<EMAIL>",
            "username": "healthuser",
            "full_name": "Health User",
            "bio": "Passionate about health and wellness",
            "is_active": true,
            "is_verified": false,
            "created_at": "2025-06-18T15:30:00Z",
            "updated_at": "2025-06-18T15:30:00Z",
            "last_login_at": null
        },
        "meta": {
            "message": "User registered successfully. Please verify your email.",
            "timestamp": "2025-06-18T15:30:00Z",
            "request_id": "req_123456789"
        }
    }

**Error Responses**:

.. code-block:: json

    // Email already exists (409 Conflict)
    {
        "error": {
            "code": "RESOURCE_CONFLICT",
            "message": "Email already registered",
            "details": {
                "field": "email",
                "value": "<EMAIL>"
            }
        }
    }

    // Username already taken (409 Conflict)
    {
        "error": {
            "code": "RESOURCE_CONFLICT",
            "message": "Username already taken",
            "details": {
                "field": "username",
                "value": "healthuser"
            }
        }
    }

    // Validation error (400 Bad Request)
    {
        "error": {
            "code": "VALIDATION_ERROR",
            "message": "Password does not meet requirements",
            "details": {
                "field": "password",
                "requirements": [
                    "Minimum 8 characters",
                    "At least one uppercase letter",
                    "At least one number",
                    "At least one special character"
                ]
            }
        }
    }

User Login
==========

Standard Login

**Endpoint**: ``POST /api/v1/auth/login``

**Request Body**:

.. code-block:: json

    {
        "username": "healthuser",
        "password": "SecurePass123!"
    }

**Success Response** (200 OK):

.. code-block:: json

    {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.signature",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************.signature",
        "token_type": "bearer",
        "expires_in": 900,
        "user": {
            "id": "123e4567-e89b-12d3-a456-************",
            "username": "healthuser",
            "email": "<EMAIL>",
            "full_name": "Health User",
            "is_verified": false
        }
    }

**Error Responses**:

.. code-block:: json

    // Invalid credentials (401 Unauthorized)
    {
        "error": {
            "code": "AUTHENTICATION_FAILED",
            "message": "Invalid username or password"
        }
    }

    // Account locked (423 Locked)
    {
        "error": {
            "code": "ACCOUNT_LOCKED",
            "message": "Account temporarily locked due to multiple failed login attempts",
            "details": {
                "lockout_expires_at": "2025-06-18T16:00:00Z",
                "retry_after": 300
            }
        }
    }

Login with Email

You can also login using email instead of username:

.. code-block:: json

    {
        "email": "<EMAIL>",
        "password": "SecurePass123!"
    }

Token Management
================
===============

Understanding JWT Tokens

**Access Token**:

- **Purpose**: Authenticate API requests
- **Lifetime**: 15 minutes (configurable)
- **Usage**: Include in Authorization header
- **Security**: Short-lived to minimize exposure

**Refresh Token**:

- **Purpose**: Obtain new access tokens
- **Lifetime**: 7 days (configurable)
- **Usage**: Exchange for new access token
- **Security**: Longer-lived but can be revoked

**Token Structure**:

.. code-block:: json

    {
        "header": {
            "alg": "HS256",
            "typ": "JWT"
        },
        "payload": {
            "sub": "user_id",
            "exp": 1640995200,
            "iat": 1640994300,
            "type": "access",
            "permissions": ["read:own_data", "write:own_data"]
        }
    }

Using Access Tokens

Include the access token in the Authorization header:

.. code-block:: bash

    curl -X GET "https://api.supplementtracker.com/api/v1/users/me" \
         -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

**JavaScript Example**:

.. code-block:: javascript

    // Store tokens after login
    const loginResponse = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username: 'healthuser', password: 'password' })
    });

    const { access_token, refresh_token } = await loginResponse.json();

    // Store tokens securely
    localStorage.setItem('access_token', access_token);
    localStorage.setItem('refresh_token', refresh_token);

    // Use token for authenticated requests
    const userResponse = await fetch('/api/v1/users/me', {
        headers: {
            'Authorization': `Bearer ${access_token}`
        }
    });

Token Refresh

**Endpoint**: ``POST /api/v1/auth/refresh``

**Request**:

.. code-block:: json

    {
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }

**Response**:

.. code-block:: json

    {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "token_type": "bearer",
        "expires_in": 900
    }

**Automatic Token Refresh Example**:

.. code-block:: javascript

    class AuthManager {
        constructor() {
            this.accessToken = localStorage.getItem('access_token');
            this.refreshToken = localStorage.getItem('refresh_token');
        }

        async makeAuthenticatedRequest(url, options = {}) {
            // Try request with current token
            let response = await this.request(url, options);

            // If token expired, refresh and retry
            if (response.status === 401) {
                await this.refreshAccessToken();
                response = await this.request(url, options);
            }

            return response;
        }

        async request(url, options) {
            return fetch(url, {
                ...options,
                headers: {
                    'Authorization': `Bearer ${this.accessToken}`,
                    ...options.headers
                }
            });
        }

        async refreshAccessToken() {
            const response = await fetch('/api/v1/auth/refresh', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ refresh_token: this.refreshToken })
            });

            if (response.ok) {
                const data = await response.json();
                this.accessToken = data.access_token;
                localStorage.setItem('access_token', data.access_token);
            } else {
                // Refresh failed, redirect to login
                this.logout();
                window.location.href = '/login';
            }
        }

        logout() {
            this.accessToken = null;
            this.refreshToken = null;
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
        }
    }

Logout
======

**Endpoint**: ``POST /api/v1/auth/logout``

**Headers**: ``Authorization: Bearer YOUR_ACCESS_TOKEN``

**Request Body** (Optional):

.. code-block:: json

    {
        "revoke_all_sessions": false
    }

**Response** (200 OK):

.. code-block:: json

    {
        "message": "Successfully logged out",
        "timestamp": "2025-06-18T15:30:00Z"
    }

**Client-side Logout**:

.. code-block:: javascript

    async function logout() {
        const accessToken = localStorage.getItem('access_token');
        
        if (accessToken) {
            try {
                await fetch('/api/v1/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`
                    }
                });
            } catch (error) {
                console.error('Logout request failed:', error);
            }
        }

        // Clear local storage regardless of API call success
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        
        // Redirect to login page
        window.location.href = '/login';
    }

Password Management
===================
==================

Password Reset

**Request Password Reset**:

**Endpoint**: ``POST /api/v1/auth/password-reset-request``

.. code-block:: json

    {
        "email": "<EMAIL>"
    }

**Response**:

.. code-block:: json

    {
        "message": "If an account with this email exists, a password reset link has been sent.",
        "timestamp": "2025-06-18T15:30:00Z"
    }

**Reset Password with Token**:

**Endpoint**: ``POST /api/v1/auth/password-reset``

.. code-block:: json

    {
        "token": "password_reset_token_from_email",
        "new_password": "NewSecurePass123!"
    }

Change Password

**Endpoint**: ``PUT /api/v1/auth/password``

**Headers**: ``Authorization: Bearer YOUR_ACCESS_TOKEN``

.. code-block:: json

    {
        "current_password": "SecurePass123!",
        "new_password": "NewSecurePass456!"
    }

**Response**:

.. code-block:: json

    {
        "message": "Password updated successfully",
        "timestamp": "2025-06-18T15:30:00Z"
    }

API Keys
========

Creating API Keys

**Endpoint**: ``POST /api/v1/auth/api-keys``

**Headers**: ``Authorization: Bearer YOUR_ACCESS_TOKEN``

.. code-block:: json

    {
        "name": "Data Analysis Script",
        "description": "API key for automated data analysis",
        "permissions": ["read:supplements", "read:own_intakes"],
        "expires_at": "2025-12-31T23:59:59Z"
    }

**Response**:

.. code-block:: json

    {
        "data": {
            "id": "key_123456789",
            "name": "Data Analysis Script",
            "key": "sk_live_abcdef123456789...",
            "permissions": ["read:supplements", "read:own_intakes"],
            "created_at": "2025-06-18T15:30:00Z",
            "expires_at": "2025-12-31T23:59:59Z",
            "last_used_at": null
        },
        "warning": "This API key will only be shown once. Please store it securely."
    }

Using API Keys

Include API key in the header:

.. code-block:: bash

    curl -X GET "https://api.supplementtracker.com/api/v1/supplements" \
         -H "X-API-Key: sk_live_abcdef123456789..."

**Python Example**:

.. code-block:: python

    import httpx

    class APIKeyClient:
        def __init__(self, api_key: str, base_url: str):
            self.api_key = api_key
            self.base_url = base_url
            self.client = httpx.AsyncClient()

        @property
        def headers(self):
            return {
                "X-API-Key": self.api_key,
                "Content-Type": "application/json"
            }

        async def get_supplements(self, **params):
            response = await self.client.get(
                f"{self.base_url}/supplements",
                headers=self.headers,
                params=params
            )
            response.raise_for_status()
            return response.json()

    # Usage
    client = APIKeyClient(
        api_key="sk_live_abcdef123456789...",
        base_url="https://api.supplementtracker.com/api/v1"
    )

    supplements = await client.get_supplements(category="vitamin")

Authorization and Permissions
=============================
============================

Permission System

The platform uses a permission-based authorization system:

**User Permissions**:

- ``read:own_data`` - Read own user data
- ``write:own_data`` - Modify own user data
- ``read:supplements`` - Read supplement information
- ``write:supplements`` - Create/modify supplements
- ``read:own_intakes`` - Read own intake records
- ``write:own_intakes`` - Create/modify own intake records

**Admin Permissions**:

- ``admin:users`` - Manage user accounts
- ``admin:supplements`` - Moderate supplement database
- ``admin:system`` - System administration

**Moderator Permissions**:

- ``moderate:supplements`` - Verify supplement information
- ``moderate:content`` - Moderate community content

Checking Permissions

**Endpoint**: ``GET /api/v1/auth/permissions``

**Response**:

.. code-block:: json

    {
        "data": {
            "user_id": "123e4567-e89b-12d3-a456-************",
            "permissions": [
                "read:own_data",
                "write:own_data",
                "read:supplements",
                "write:supplements",
                "read:own_intakes",
                "write:own_intakes"
            ],
            "roles": ["user"]
        }
    }

Security Best Practices
=======================

Token Security

1. **Store tokens securely**:
   - Use secure storage (keychain, encrypted storage)
   - Never store in plain text files or logs
   - Use environment variables for server-side applications

2. **Handle token expiration**:
   - Implement automatic token refresh
   - Handle 401 responses gracefully
   - Provide clear error messages to users

3. **Protect against XSS**:
   - Use httpOnly cookies when possible
   - Sanitize user input
   - Implement Content Security Policy

**Secure Token Storage Example**:

.. code-block:: javascript

    // Use secure storage library
    import SecureStorage from 'secure-web-storage';
    import CryptoJS from 'crypto-js';

    const secureStorage = new SecureStorage(localStorage, {
        hash: function hash(key) {
            key = CryptoJS.SHA256(key, 'secret-key');
            return key.toString();
        },
        encrypt: function encrypt(data) {
            data = CryptoJS.AES.encrypt(data, 'secret-key');
            return data.toString();
        },
        decrypt: function decrypt(data) {
            data = CryptoJS.AES.decrypt(data, 'secret-key');
            return data.toString(CryptoJS.enc.Utf8);
        }
    });

    // Store tokens securely
    secureStorage.setItem('access_token', accessToken);
    secureStorage.setItem('refresh_token', refreshToken);

Password Security

1. **Strong password requirements**:
   - Minimum 8 characters
   - Mix of uppercase, lowercase, numbers, symbols
   - No common passwords or dictionary words

2. **Password validation**:
   - Client-side validation for UX
   - Server-side validation for security
   - Real-time strength feedback

3. **Secure password handling**:
   - Never log passwords
   - Use HTTPS for all authentication
   - Implement rate limiting on login attempts

**Password Strength Checker**:

.. code-block:: javascript

    function checkPasswordStrength(password) {
        const requirements = {
            length: password.length >= 8,
            uppercase: /[A-Z]/.test(password),
            lowercase: /[a-z]/.test(password),
            number: /\d/.test(password),
            special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
        };

        const score = Object.values(requirements).filter(Boolean).length;
        const strength = score < 3 ? 'weak' : score < 5 ? 'medium' : 'strong';

        return {
            score,
            strength,
            requirements,
            isValid: score >= 4
        };
    }

Session Management

1. **Session security**:
   - Use secure session cookies
   - Implement session timeout
   - Invalidate sessions on logout

2. **Multi-device support**:
   - Track active sessions
   - Allow users to revoke sessions
   - Notify of new logins

**Session Management Example**:

.. code-block:: python

    from datetime import datetime, timedelta
    import uuid

    class SessionManager:
        def __init__(self, redis_client):
            self.redis = redis_client

        async def create_session(self, user_id: str, device_info: dict) -> str:
            session_id = str(uuid.uuid4())
            session_data = {
                "user_id": user_id,
                "created_at": datetime.utcnow().isoformat(),
                "last_activity": datetime.utcnow().isoformat(),
                "device_info": device_info,
                "is_active": True
            }

            # Store session with expiration
            await self.redis.setex(
                f"session:{session_id}",
                timedelta(days=7).total_seconds(),
                json.dumps(session_data)
            )

            return session_id

        async def get_user_sessions(self, user_id: str) -> list:
            pattern = "session:*"
            sessions = []

            async for key in self.redis.scan_iter(match=pattern):
                session_data = await self.redis.get(key)
                if session_data:
                    data = json.loads(session_data)
                    if data["user_id"] == user_id and data["is_active"]:
                        sessions.append({
                            "session_id": key.decode().split(":")[1],
                            **data
                        })

            return sessions

        async def revoke_session(self, session_id: str):
            await self.redis.delete(f"session:{session_id}")

Troubleshooting
===============
==============

Common Authentication Issues

**Token Expired**:

.. code-block:: json

    {
        "error": {
            "code": "TOKEN_EXPIRED",
            "message": "Access token has expired"
        }
    }

**Solution**: Use refresh token to get new access token

**Invalid Token Format**:

.. code-block:: json

    {
        "error": {
            "code": "INVALID_TOKEN",
            "message": "Token format is invalid"
        }
    }

**Solution**: Check token format and ensure proper Bearer prefix

**Account Locked**:

.. code-block:: json

    {
        "error": {
            "code": "ACCOUNT_LOCKED",
            "message": "Account temporarily locked"
        }
    }

**Solution**: Wait for lockout period to expire or contact support

Debugging Authentication

**Check Token Validity**:

.. code-block:: bash

    # Decode JWT token (without verification)
    echo "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." | base64 -d

**Test Authentication**:

.. code-block:: bash

    # Test with curl
    curl -X GET "https://api.supplementtracker.com/api/v1/users/me" \
         -H "Authorization: Bearer YOUR_TOKEN" \
         -v

**Python Debug Script**:

.. code-block:: python

    import jwt
    import json
    from datetime import datetime

    def debug_token(token):
        try:
            # Decode without verification (for debugging only)
            decoded = jwt.decode(token, options={"verify_signature": False})
            
            print("Token payload:")
            print(json.dumps(decoded, indent=2))
            
            # Check expiration
            exp = decoded.get('exp')
            if exp:
                exp_time = datetime.fromtimestamp(exp)
                now = datetime.now()
                print(f"\nExpires at: {exp_time}")
                print(f"Current time: {now}")
                print(f"Expired: {now > exp_time}")
            
        except Exception as e:
            print(f"Error decoding token: {e}")

    # Usage
    debug_token("your_jwt_token_here")

Next Steps
==========

Now that you understand authentication:

1. **Start using the API**: :doc:`api-usage`
2. **Track supplements**: :doc:`supplement-tracking`
3. **Explore community features**: :doc:`community-features`
4. **Review security practices**: :doc:`../architecture/security`

For developers:

- :doc:`../developer/setup`
- :doc:`../api/authentication`
