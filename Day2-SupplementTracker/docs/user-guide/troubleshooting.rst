=====================
Troubleshooting Guide
=====================

This guide helps you resolve common issues with the Supplement Tracker platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Common Issues
=============

Authentication Problems

**Issue**: Cannot log in with correct credentials

**Symptoms**:
- "Invalid username or password" error
- Login form doesn't respond
- Token expired messages

**Solutions**:

1. **Check credentials**:
   
   .. code-block:: bash
   
       # Verify username/email format
       curl -X POST "https://api.supplementtracker.com/api/v1/auth/login" \
            -H "Content-Type: application/json" \
            -d '{"username": "your_username", "password": "your_password"}'

2. **Reset password**:
   
   .. code-block:: bash
   
       curl -X POST "https://api.supplementtracker.com/api/v1/auth/password-reset-request" \
            -H "Content-Type: application/json" \
            -d '{"email": "<EMAIL>"}'

3. **Check account status**:
   - Account may be locked due to failed attempts
   - Email verification may be required
   - Contact support if account is suspended

**Issue**: Token expires too quickly

**Solutions**:

1. **Implement token refresh**:
   
   .. code-block:: javascript
   
       async function refreshToken() {
           const refreshToken = localStorage.getItem('refresh_token');
           const response = await fetch('/api/v1/auth/refresh', {
               method: 'POST',
               headers: { 'Content-Type': 'application/json' },
               body: JSON.stringify({ refresh_token: refreshToken })
           });
           
           if (response.ok) {
               const data = await response.json();
               localStorage.setItem('access_token', data.access_token);
               return data.access_token;
           }
           throw new Error('Token refresh failed');
       }

2. **Configure longer token expiration** (if self-hosting):
   
   .. code-block:: bash
   
       # In .env file
       ACCESS_TOKEN_EXPIRE_MINUTES=60  # Increase from default 15

API Connection Issues

**Issue**: Cannot connect to API

**Symptoms**:
- Connection timeout errors
- "Network unreachable" messages
- SSL certificate errors

**Solutions**:

1. **Check API status**:
   
   .. code-block:: bash
   
       # Test basic connectivity
       curl -I https://api.supplementtracker.com/health
       
       # Check SSL certificate
       openssl s_client -connect api.supplementtracker.com:443

2. **Verify network configuration**:
   
   .. code-block:: bash
   
       # Check DNS resolution
       nslookup api.supplementtracker.com
       
       # Test with different DNS
       nslookup api.supplementtracker.com *******

3. **Firewall and proxy issues**:
   - Check corporate firewall settings
   - Configure proxy if required
   - Verify HTTPS traffic is allowed

**Issue**: Rate limiting errors

**Symptoms**:
- HTTP 429 "Too Many Requests"
- "Rate limit exceeded" messages

**Solutions**:

1. **Implement exponential backoff**:
   
   .. code-block:: python
   
       import asyncio
       import random
       
       async def api_request_with_backoff(client, endpoint, max_retries=3):
           for attempt in range(max_retries):
               try:
                   response = await client.get(endpoint)
                   return response
               except RateLimitError:
                   if attempt < max_retries - 1:
                       wait_time = (2 ** attempt) + random.uniform(0, 1)
                       await asyncio.sleep(wait_time)
                   else:
                       raise

2. **Check rate limit headers**:
   
   .. code-block:: python
   
       def check_rate_limits(response):
           headers = response.headers
           limit = headers.get('X-RateLimit-Limit')
           remaining = headers.get('X-RateLimit-Remaining')
           reset = headers.get('X-RateLimit-Reset')
           
           print(f"Rate limit: {remaining}/{limit}, resets at {reset}")

Database Issues

**Issue**: Database connection errors (self-hosted)

**Symptoms**:
- "Connection refused" errors
- "Database unavailable" messages
- Slow query responses

**Solutions**:

1. **Check database status**:
   
   .. code-block:: bash
   
       # PostgreSQL status
       sudo systemctl status postgresql
       
       # Test connection
       psql -h localhost -U supplement_user -d supplement_tracker -c "SELECT version();"

2. **Check connection configuration**:
   
   .. code-block:: bash
   
       # Verify DATABASE_URL format
       echo $DATABASE_URL
       
       # Should be: postgresql://user:pass@host:port/database

3. **Database performance issues**:
   
   .. code-block:: sql
   
       -- Check active connections
       SELECT count(*) FROM pg_stat_activity;
       
       -- Check slow queries
       SELECT query, mean_exec_time, calls 
       FROM pg_stat_statements 
       ORDER BY mean_exec_time DESC 
       LIMIT 10;

Installation Issues
===================
==================

Nix Shell Problems

**Issue**: Nix shell fails to start

**Solutions**:

1. **Update Nix**:
   
   .. code-block:: bash
   
       # Update Nix
       nix-channel --update
       nix-env -u
       
       # Clear cache if needed
       nix-collect-garbage -d

2. **Check shell.nix syntax**:
   
   .. code-block:: bash
   
       # Validate shell.nix
       nix-instantiate shell.nix

3. **Use fallback installation**:
   
   .. code-block:: bash
   
       # Manual Python environment
       python3 -m venv venv
       source venv/bin/activate
       pip install -r requirements.txt

**Issue**: Missing dependencies in Nix shell

**Solutions**:

1. **Update shell.nix**:
   
   .. code-block:: nix
   
       # Add missing packages to shell.nix
       buildInputs = with pkgs; [
         pythonEnv
         postgresql
         redis
         # Add other required packages
       ];

2. **Install packages manually**:
   
   .. code-block:: bash
   
       # Install specific packages
       nix-shell -p postgresql redis

Python Environment Issues

**Issue**: Import errors or missing packages

**Solutions**:

1. **Verify Python path**:
   
   .. code-block:: bash
   
       python -c "import sys; print('\n'.join(sys.path))"

2. **Reinstall dependencies**:
   
   .. code-block:: bash
   
       pip install -r requirements.txt --force-reinstall

3. **Check virtual environment**:
   
   .. code-block:: bash
   
       which python
       which pip
       
       # Should point to venv if using virtual environment

Configuration Issues
====================
===================

Environment Variables

**Issue**: Configuration not loading correctly

**Solutions**:

1. **Verify environment file**:
   
   .. code-block:: bash
   
       # Check .env file exists and is readable
       ls -la .env
       cat .env | grep -v "^#" | grep "="

2. **Test configuration loading**:
   
   .. code-block:: python
   
       from app.core.config import settings
       print(f"Database URL: {settings.DATABASE_URL}")
       print(f"Debug mode: {settings.DEBUG}")

3. **Environment variable precedence**:
   
   .. code-block:: bash
   
       # Environment variables override .env file
       export DATABASE_URL="postgresql://..."
       python -c "from app.core.config import settings; print(settings.DATABASE_URL)"

**Issue**: Database URL format errors

**Solutions**:

1. **Validate URL format**:
   
   .. code-block:: python
   
       from urllib.parse import urlparse
       
       url = "postgresql://user:pass@host:port/database"
       parsed = urlparse(url)
       print(f"Scheme: {parsed.scheme}")
       print(f"Host: {parsed.hostname}")
       print(f"Port: {parsed.port}")
       print(f"Database: {parsed.path.strip('/')}")

2. **Common URL formats**:
   
   .. code-block:: bash
   
       # Local development
       DATABASE_URL=postgresql://user:pass@localhost:5432/supplement_tracker
       
       # With connection pooling
       DATABASE_URL=********************************/db?pool_size=20&max_overflow=30
       
       # With SSL
       DATABASE_URL=********************************/db?sslmode=require

Performance Issues
==================

Slow API Responses

**Issue**: API requests taking too long

**Diagnostic Steps**:

1. **Check response times**:
   
   .. code-block:: bash
   
       # Measure response time
       curl -w "@curl-format.txt" -o /dev/null -s "https://api.supplementtracker.com/api/v1/supplements"
       
       # curl-format.txt content:
       #     time_namelookup:  %{time_namelookup}\n
       #        time_connect:  %{time_connect}\n
       #     time_appconnect:  %{time_appconnect}\n
       #    time_pretransfer:  %{time_pretransfer}\n
       #       time_redirect:  %{time_redirect}\n
       #  time_starttransfer:  %{time_starttransfer}\n
       #                     ----------\n
       #          time_total:  %{time_total}\n

2. **Database query optimization**:
   
   .. code-block:: sql
   
       -- Enable query logging
       SET log_statement = 'all';
       SET log_min_duration_statement = 1000;  -- Log queries > 1 second

3. **Application profiling**:
   
   .. code-block:: python
   
       import cProfile
       import pstats
       
       # Profile API endpoint
       profiler = cProfile.Profile()
       profiler.enable()
       
       # Your API call here
       
       profiler.disable()
       stats = pstats.Stats(profiler)
       stats.sort_stats('cumulative').print_stats(10)

**Solutions**:

1. **Database optimization**:
   - Add indexes for frequently queried columns
   - Use connection pooling
   - Optimize query patterns

2. **Caching implementation**:
   
   .. code-block:: python
   
       from functools import lru_cache
       import redis
       
       redis_client = redis.Redis.from_url(settings.REDIS_URL)
       
       @lru_cache(maxsize=128)
       def get_supplement_categories():
           # Cache frequently accessed data
           pass

Memory Issues

**Issue**: High memory usage or out of memory errors

**Solutions**:

1. **Monitor memory usage**:
   
   .. code-block:: bash
   
       # System memory
       free -h
       
       # Process memory
       ps aux | grep python
       
       # Python memory profiling
       pip install memory-profiler
       python -m memory_profiler your_script.py

2. **Optimize database connections**:
   
   .. code-block:: python
   
       # Limit connection pool size
       engine = create_async_engine(
           DATABASE_URL,
           pool_size=10,  # Reduce if memory constrained
           max_overflow=20
       )

3. **Implement pagination**:
   
   .. code-block:: python
   
       # Don't load all records at once
       async def get_supplements_paginated(skip=0, limit=50):
           query = select(Supplement).offset(skip).limit(limit)
           result = await db.execute(query)
           return result.scalars().all()

Data Issues
===========
==========

Supplement Data Problems

**Issue**: Duplicate supplements in database

**Solutions**:

1. **Find duplicates**:
   
   .. code-block:: sql
   
       -- Find potential duplicates
       SELECT name, brand, COUNT(*) 
       FROM supplements 
       GROUP BY LOWER(name), LOWER(brand) 
       HAVING COUNT(*) > 1;

2. **Merge duplicates** (admin only):
   
   .. code-block:: python
   
       async def merge_supplements(primary_id, duplicate_id):
           # Update all intakes to use primary supplement
           await db.execute(
               update(SupplementIntake)
               .where(SupplementIntake.supplement_id == duplicate_id)
               .values(supplement_id=primary_id)
           )
           
           # Delete duplicate
           await db.execute(
               delete(Supplement).where(Supplement.id == duplicate_id)
           )

**Issue**: Missing supplement information

**Solutions**:

1. **Update supplement data**:
   
   .. code-block:: bash
   
       curl -X PUT "https://api.supplementtracker.com/api/v1/supplements/{id}" \
            -H "Authorization: Bearer YOUR_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{
              "description": "Updated description",
              "ingredients": "Complete ingredient list"
            }'

2. **Request verification**:
   - Submit supplement for community review
   - Provide additional information sources
   - Contact moderators for verification

Intake Data Issues

**Issue**: Incorrect intake records

**Solutions**:

1. **Update intake record**:
   
   .. code-block:: bash
   
       curl -X PUT "https://api.supplementtracker.com/api/v1/intakes/{id}" \
            -H "Authorization: Bearer YOUR_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{
              "dosage": 1000,
              "dosage_unit": "mg",
              "notes": "Corrected dosage"
            }'

2. **Delete incorrect record**:
   
   .. code-block:: bash
   
       curl -X DELETE "https://api.supplementtracker.com/api/v1/intakes/{id}" \
            -H "Authorization: Bearer YOUR_TOKEN"

**Issue**: Data export problems

**Solutions**:

1. **Check export format**:
   
   .. code-block:: bash
   
       # Request CSV export
       curl -X GET "https://api.supplementtracker.com/api/v1/intakes/export?format=csv" \
            -H "Authorization: Bearer YOUR_TOKEN" \
            -H "Accept: text/csv"

2. **Large dataset exports**:
   
   .. code-block:: python
   
       # Export in chunks for large datasets
       async def export_all_intakes(client):
           all_intakes = []
           skip = 0
           limit = 1000
           
           while True:
               response = await client.get(
                   "/intakes/history",
                   params={"skip": skip, "limit": limit}
               )
               intakes = response.json()["data"]
               
               if not intakes:
                   break
                   
               all_intakes.extend(intakes)
               skip += limit
           
           return all_intakes

Getting Help
============
===========

Self-Service Resources

1. **Documentation**:
   - :doc:`installation`
   - :doc:`configuration`
   - :doc:`api-usage`
   - :doc:`../api/index`

2. **API Testing**:
   - Interactive docs: https://api.supplementtracker.com/docs
   - API status: https://status.supplementtracker.com

3. **Community Resources**:
   - GitHub Issues: Bug reports and feature requests
   - Community Forum: User discussions and help
   - Stack Overflow: Technical questions (tag: supplement-tracker)

Contact Support

**Before Contacting Support**:

1. Check this troubleshooting guide
2. Search existing GitHub issues
3. Test with minimal example
4. Gather relevant information:
   - Error messages (full text)
   - Steps to reproduce
   - Environment details
   - API request/response examples

**Support Channels**:

1. **GitHub Issues**: https://github.com/forkrul/day2-supplement-tracker/issues
   - Bug reports
   - Feature requests
   - Technical discussions

2. **Email Support**: <EMAIL>
   - Account issues
   - Data problems
   - Security concerns

3. **Community Forum**: https://community.supplementtracker.com
   - General questions
   - Best practices
   - User experiences

**Information to Include**:

- Platform version
- Operating system
- Browser (if web-related)
- Error messages
- Steps to reproduce
- Expected vs actual behavior

Emergency Procedures
====================
===================

Data Recovery

**If you lose access to your account**:

1. Try password reset
2. Check email for verification requirements
3. Contact support with account details
4. Provide identity verification if required

**If you accidentally delete data**:

1. Check if soft delete is implemented
2. Contact support immediately
3. Provide specific details about deleted data
4. Request data recovery if available

**Data backup recommendations**:

.. code-block:: python

    # Regular data export for backup
    async def backup_user_data(client):
        # Export all user data
        user_data = await client.get("/users/me")
        supplements = await client.get("/supplements")
        intakes = await client.get("/intakes/history", params={"limit": 10000})
        
        backup = {
            "user": user_data,
            "supplements": supplements,
            "intakes": intakes,
            "backup_date": datetime.now().isoformat()
        }
        
        with open(f"backup_{datetime.now().strftime('%Y%m%d')}.json", "w") as f:
            json.dump(backup, f, indent=2)

Security Issues

**If you suspect account compromise**:

1. Change password immediately
2. Revoke all API keys
3. Check recent activity
4. Enable additional security measures
5. Contact support

**If you find a security vulnerability**:

1. Do NOT post publicly
2. Email: <EMAIL>
3. Provide detailed information
4. Allow time for fix before disclosure

System Status
=============
============

Monitoring System Health

**Check system status**:

.. code-block:: bash

    # API health check
    curl https://api.supplementtracker.com/health
    
    # Database connectivity
    curl https://api.supplementtracker.com/ready

**Status page**: https://status.supplementtracker.com

**Service monitoring**:

- API response times
- Database performance
- Error rates
- Uptime statistics

Planned Maintenance

**Maintenance notifications**:

- Email notifications to users
- Status page updates
- API response headers during maintenance
- Social media announcements

**During maintenance**:

- Read-only mode may be available
- Some features may be temporarily disabled
- Data is always protected and backed up

Next Steps
==========

If this guide doesn't resolve your issue:

1. **Search documentation**: :doc:`../index`
2. **Check API reference**: :doc:`../api/index`
3. **Review architecture docs**: :doc:`../architecture/overview`
4. **Contact support**: Use appropriate channel based on issue type
5. **Contribute**: Help improve documentation for others

For developers:

- :doc:`../developer/setup`
- :doc:`../developer/contributing`
