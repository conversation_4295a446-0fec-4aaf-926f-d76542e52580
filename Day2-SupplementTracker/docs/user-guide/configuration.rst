===================
Configuration Guide
===================

This guide covers all configuration options for the Supplement Tracker platform, from basic setup to advanced production configurations.

.. contents:: Table of Contents
   :local:
   :depth: 2

Configuration Overview
======================

Configuration Sources

The application loads configuration from multiple sources in order of precedence:

1. **Environment Variables** (highest priority)
2. **Configuration Files** (``.env``, ``config.yaml``)
3. **Default Values** (lowest priority)

**Configuration Loading**:

.. code-block:: python

    from app.core.config import settings

    # Access configuration
    database_url = settings.DATABASE_URL
    debug_mode = settings.DEBUG
    secret_key = settings.SECRET_KEY

Environment Variables
=====================
====================

Core Settings

**Database Configuration**:

.. list-table::
   :header-rows: 1
   :widths: 30 50 20

   * - Variable
     - Description
     - Default
   * - DATABASE_URL
     - PostgreSQL connection string
     - Required
   * - DATABASE_POOL_SIZE
     - Connection pool size
     - 20
   * - DATABASE_MAX_OVERFLOW
     - Max overflow connections
     - 30
   * - DATABASE_POOL_RECYCLE
     - Connection recycle time (seconds)
     - 3600

**Example Database URLs**:

.. code-block:: bash

    # Local development
    DATABASE_URL=postgresql://user:pass@localhost:5432/supplement_tracker

    # Production with connection pooling
    DATABASE_URL=***********************************/supplement_tracker?pool_size=20&max_overflow=30

    # With SSL
    DATABASE_URL=***********************************/supplement_tracker?sslmode=require

**Redis Configuration**:

.. list-table::
   :header-rows: 1
   :widths: 30 50 20

   * - Variable
     - Description
     - Default
   * - REDIS_URL
     - Redis connection string
     - redis://localhost:6379/0
   * - REDIS_POOL_SIZE
     - Connection pool size
     - 10
   * - REDIS_SOCKET_TIMEOUT
     - Socket timeout (seconds)
     - 5

Security Settings

**Authentication & JWT**:

.. list-table::
   :header-rows: 1
   :widths: 30 50 20

   * - Variable
     - Description
     - Default
   * - SECRET_KEY
     - JWT signing secret
     - Required
   * - ACCESS_TOKEN_EXPIRE_MINUTES
     - Access token lifetime
     - 15
   * - REFRESH_TOKEN_EXPIRE_DAYS
     - Refresh token lifetime
     - 7
   * - PASSWORD_MIN_LENGTH
     - Minimum password length
     - 8

**Generate Secure Secret Key**:

.. code-block:: bash

    # Generate a secure secret key
    openssl rand -hex 32

    # Or using Python
    python -c "import secrets; print(secrets.token_hex(32))"

**CORS Configuration**:

.. code-block:: bash

    # Single origin
    CORS_ORIGINS=["https://yourdomain.com"]

    # Multiple origins
    CORS_ORIGINS=["https://yourdomain.com","https://app.yourdomain.com","http://localhost:3000"]

    # Allow all origins (development only)
    CORS_ORIGINS=["*"]

Application Settings

**Environment Control**:

.. list-table::
   :header-rows: 1
   :widths: 30 50 20

   * - Variable
     - Description
     - Default
   * - DEBUG
     - Enable debug mode
     - false
   * - TESTING
     - Enable testing mode
     - false
   * - LOG_LEVEL
     - Logging level
     - INFO
   * - PROJECT_NAME
     - Application name
     - Supplement Tracker

**API Configuration**:

.. list-table::
   :header-rows: 1
   :widths: 30 50 20

   * - Variable
     - Description
     - Default
   * - API_V1_STR
     - API version prefix
     - /api/v1
   * - DOCS_URL
     - Swagger UI path
     - /docs
   * - REDOC_URL
     - ReDoc path
     - /redoc

External Services
=================
================

Email Configuration

**SMTP Settings**:

.. code-block:: bash

    # Email server configuration
    SMTP_HOST=smtp.gmail.com
    SMTP_PORT=587
    SMTP_USER=<EMAIL>
    SMTP_PASSWORD=your-app-password
    SMTP_TLS=true
    SMTP_SSL=false

    # Email settings
    EMAIL_FROM=<EMAIL>
    EMAIL_FROM_NAME="Supplement Tracker"
    EMAIL_RESET_TOKEN_EXPIRE_HOURS=24

**Email Templates**:

.. code-block:: bash

    # Template directory
    EMAIL_TEMPLATES_DIR=app/templates/email

    # Template files
    # - welcome.html
    # - password_reset.html
    # - email_verification.html

Monitoring and Logging

**Sentry Configuration**:

.. code-block:: bash

    # Error tracking
    SENTRY_DSN=https://<EMAIL>/project-id
    SENTRY_ENVIRONMENT=production
    SENTRY_RELEASE=1.0.0

**Logging Configuration**:

.. code-block:: bash

    # Logging settings
    LOG_LEVEL=INFO
    LOG_FORMAT=json
    LOG_FILE=logs/app.log
    LOG_ROTATION=daily
    LOG_RETENTION_DAYS=30

**Metrics and Monitoring**:

.. code-block:: bash

    # Prometheus metrics
    METRICS_ENABLED=true
    METRICS_PATH=/metrics

    # Health check endpoints
    HEALTH_CHECK_PATH=/health
    READINESS_CHECK_PATH=/ready

File Storage

**Local Storage**:

.. code-block:: bash

    # Local file storage
    UPLOAD_DIR=uploads
    MAX_UPLOAD_SIZE=10485760  # 10MB in bytes
    ALLOWED_EXTENSIONS=["jpg","jpeg","png","pdf","csv"]

**AWS S3 Configuration**:

.. code-block:: bash

    # S3 settings
    AWS_ACCESS_KEY_ID=your-access-key
    AWS_SECRET_ACCESS_KEY=your-secret-key
    AWS_REGION=us-east-1
    S3_BUCKET=supplement-tracker-uploads
    S3_PREFIX=uploads/

Configuration Files
===================
==================

Environment File (.env)

**Development Configuration**:

.. code-block:: bash

    # .env.development
    DEBUG=true
    TESTING=false
    LOG_LEVEL=DEBUG

    # Database
    DATABASE_URL=postgresql://supplement_user:password@localhost:5432/supplement_tracker
    REDIS_URL=redis://localhost:6379/0

    # Security
    SECRET_KEY=your-development-secret-key
    ACCESS_TOKEN_EXPIRE_MINUTES=60  # Longer for development

    # CORS (allow local development)
    CORS_ORIGINS=["http://localhost:3000","http://localhost:8080"]

    # Email (use console backend for development)
    EMAIL_BACKEND=console

**Production Configuration**:

.. code-block:: bash

    # .env.production
    DEBUG=false
    TESTING=false
    LOG_LEVEL=INFO

    # Database with connection pooling
    DATABASE_URL=***********************************/supplement_tracker?pool_size=20&max_overflow=30

    # Redis with connection pooling
    REDIS_URL=redis://redis-host:6379/0

    # Security
    SECRET_KEY=your-super-secure-production-secret-key
    ACCESS_TOKEN_EXPIRE_MINUTES=15

    # CORS (restrict to your domains)
    CORS_ORIGINS=["https://yourdomain.com","https://api.yourdomain.com"]

    # Email
    SMTP_HOST=smtp.yourdomain.com
    SMTP_PORT=587
    SMTP_USER=<EMAIL>
    SMTP_PASSWORD=your-smtp-password
    SMTP_TLS=true

    # Monitoring
    SENTRY_DSN=https://<EMAIL>/project-id
    METRICS_ENABLED=true

YAML Configuration

**config.yaml** (Optional):

.. code-block:: yaml

    # Application configuration
    app:
      name: "Supplement Tracker"
      version: "0.1.0"
      description: "Community-Driven Supplement Research Platform"

    # Database configuration
    database:
      pool_size: 20
      max_overflow: 30
      pool_recycle: 3600
      echo: false

    # Redis configuration
    redis:
      pool_size: 10
      socket_timeout: 5
      socket_connect_timeout: 5

    # Security configuration
    security:
      password_min_length: 8
      password_require_uppercase: true
      password_require_lowercase: true
      password_require_numbers: true
      password_require_symbols: true
      max_login_attempts: 5
      lockout_duration: 300  # 5 minutes

    # Rate limiting
    rate_limiting:
      enabled: true
      default_limit: "100/hour"
      authenticated_limit: "1000/hour"
      premium_limit: "5000/hour"

    # File upload configuration
    uploads:
      max_size: 10485760  # 10MB
      allowed_extensions:
        - jpg
        - jpeg
        - png
        - pdf
        - csv
      storage_backend: "local"  # or "s3"

Docker Configuration
====================
===================

Docker Compose

**docker-compose.yml**:

.. code-block:: yaml

    version: '3.8'

    services:
      app:
        build:
          context: .
          dockerfile: Dockerfile
        ports:
          - "8000:8000"
        environment:
          - DATABASE_URL=**************************************/supplement_tracker
          - REDIS_URL=redis://redis:6379/0
          - SECRET_KEY=${SECRET_KEY}
          - DEBUG=${DEBUG:-false}
        depends_on:
          db:
            condition: service_healthy
          redis:
            condition: service_started
        volumes:
          - ./uploads:/app/uploads
          - ./logs:/app/logs
        restart: unless-stopped
        healthcheck:
          test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
          interval: 30s
          timeout: 10s
          retries: 3

      db:
        image: postgres:15
        environment:
          POSTGRES_DB: supplement_tracker
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: password
        volumes:
          - postgres_data:/var/lib/postgresql/data
          - ./init.sql:/docker-entrypoint-initdb.d/init.sql
        ports:
          - "5432:5432"
        restart: unless-stopped
        healthcheck:
          test: ["CMD-SHELL", "pg_isready -U postgres"]
          interval: 10s
          timeout: 5s
          retries: 5

      redis:
        image: redis:7-alpine
        command: redis-server --appendonly yes
        volumes:
          - redis_data:/data
        ports:
          - "6379:6379"
        restart: unless-stopped
        healthcheck:
          test: ["CMD", "redis-cli", "ping"]
          interval: 10s
          timeout: 5s
          retries: 3

    volumes:
      postgres_data:
      redis_data:

**Environment File for Docker**:

.. code-block:: bash

    # .env.docker
    SECRET_KEY=your-docker-secret-key
    DEBUG=false
    POSTGRES_PASSWORD=secure-postgres-password

Kubernetes Configuration
========================
=======================

ConfigMap

.. code-block:: yaml

    apiVersion: v1
    kind: ConfigMap
    metadata:
      name: supplement-tracker-config
    data:
      DEBUG: "false"
      LOG_LEVEL: "INFO"
      API_V1_STR: "/api/v1"
      DOCS_URL: "/docs"
      REDOC_URL: "/redoc"
      METRICS_ENABLED: "true"
      METRICS_PATH: "/metrics"

Secrets

.. code-block:: yaml

    apiVersion: v1
    kind: Secret
    metadata:
      name: supplement-tracker-secrets
    type: Opaque
    data:
      SECRET_KEY: <base64-encoded-secret-key>
      DATABASE_URL: <base64-encoded-database-url>
      REDIS_URL: <base64-encoded-redis-url>
      SMTP_PASSWORD: <base64-encoded-smtp-password>
      SENTRY_DSN: <base64-encoded-sentry-dsn>

**Create Secrets**:

.. code-block:: bash

    # Create secret from literal values
    kubectl create secret generic supplement-tracker-secrets \
      --from-literal=SECRET_KEY=your-secret-key \
      --from-literal=DATABASE_URL=********************************/db

    # Create secret from file
    kubectl create secret generic supplement-tracker-secrets \
      --from-env-file=.env.production

Configuration Validation
========================

Environment Validation

**Validation Script**:

.. code-block:: python

    #!/usr/bin/env python3
    """Configuration validation script."""

    import os
    import sys
    from urllib.parse import urlparse

    def validate_database_url(url):
        """Validate database URL format."""
        try:
            parsed = urlparse(url)
            assert parsed.scheme == 'postgresql'
            assert parsed.hostname
            assert parsed.port
            assert parsed.username
            assert parsed.password
            assert parsed.path.strip('/')
            return True
        except (AssertionError, AttributeError):
            return False

    def validate_redis_url(url):
        """Validate Redis URL format."""
        try:
            parsed = urlparse(url)
            assert parsed.scheme == 'redis'
            assert parsed.hostname
            return True
        except (AssertionError, AttributeError):
            return False

    def validate_secret_key(key):
        """Validate secret key strength."""
        return len(key) >= 32 and key != 'changeme'

    def main():
        """Run configuration validation."""
        errors = []

        # Required environment variables
        required_vars = [
            'DATABASE_URL',
            'REDIS_URL',
            'SECRET_KEY'
        ]

        for var in required_vars:
            if not os.getenv(var):
                errors.append(f"Missing required environment variable: {var}")

        # Validate database URL
        database_url = os.getenv('DATABASE_URL')
        if database_url and not validate_database_url(database_url):
            errors.append("Invalid DATABASE_URL format")

        # Validate Redis URL
        redis_url = os.getenv('REDIS_URL')
        if redis_url and not validate_redis_url(redis_url):
            errors.append("Invalid REDIS_URL format")

        # Validate secret key
        secret_key = os.getenv('SECRET_KEY')
        if secret_key and not validate_secret_key(secret_key):
            errors.append("SECRET_KEY is too weak or using default value")

        # Production-specific validations
        if not os.getenv('DEBUG', 'false').lower() == 'true':
            if os.getenv('SECRET_KEY') == 'changeme':
                errors.append("Using default SECRET_KEY in production")
            
            if not os.getenv('SENTRY_DSN'):
                errors.append("SENTRY_DSN not set for production")

        if errors:
            print("Configuration validation failed:")
            for error in errors:
                print(f"  - {error}")
            sys.exit(1)
        else:
            print("Configuration validation passed!")

    if __name__ == '__main__':
        main()

**Run Validation**:

.. code-block:: bash

    # Make script executable
    chmod +x scripts/validate_config.py

    # Run validation
    python scripts/validate_config.py

Configuration Testing
=====================
====================

Test Configuration

**pytest.ini**:

.. code-block:: ini

    [tool:pytest]
    testpaths = app/tests
    python_files = test_*.py
    python_classes = Test*
    python_functions = test_*
    addopts = 
        --strict-markers
        --disable-warnings
        --cov=app
        --cov-report=term-missing
        --cov-report=html
        --cov-report=xml

**Test Environment**:

.. code-block:: bash

    # .env.test
    TESTING=true
    DEBUG=true
    LOG_LEVEL=DEBUG

    # Use test database
    DATABASE_URL=postgresql://test_user:test_pass@localhost:5432/test_supplement_tracker

    # Use separate Redis database
    REDIS_URL=redis://localhost:6379/1

    # Test-specific settings
    SECRET_KEY=test-secret-key-not-for-production
    ACCESS_TOKEN_EXPIRE_MINUTES=1  # Short expiration for testing

Performance Configuration
=========================
========================

Database Optimization

**PostgreSQL Settings**:

.. code-block:: bash

    # Connection pooling
    DATABASE_POOL_SIZE=20
    DATABASE_MAX_OVERFLOW=30
    DATABASE_POOL_RECYCLE=3600
    DATABASE_POOL_PRE_PING=true

    # Query optimization
    DATABASE_ECHO=false
    DATABASE_ECHO_POOL=false

**SQLAlchemy Configuration**:

.. code-block:: python

    # Engine configuration
    engine_config = {
        "pool_size": settings.DATABASE_POOL_SIZE,
        "max_overflow": settings.DATABASE_MAX_OVERFLOW,
        "pool_recycle": settings.DATABASE_POOL_RECYCLE,
        "pool_pre_ping": settings.DATABASE_POOL_PRE_PING,
        "echo": settings.DATABASE_ECHO,
    }

Application Performance

**Uvicorn Configuration**:

.. code-block:: bash

    # Worker configuration
    UVICORN_WORKERS=4
    UVICORN_WORKER_CLASS=uvicorn.workers.UvicornWorker
    UVICORN_MAX_REQUESTS=1000
    UVICORN_MAX_REQUESTS_JITTER=100

    # Async settings
    UVLOOP_ENABLED=true
    HTTPTOOLS_ENABLED=true

**Caching Configuration**:

.. code-block:: bash

    # Redis caching
    CACHE_TTL=3600  # 1 hour default TTL
    CACHE_KEY_PREFIX=supplement_tracker
    CACHE_ENABLED=true

    # Application-level caching
    QUERY_CACHE_ENABLED=true
    QUERY_CACHE_TTL=300  # 5 minutes

Security Hardening
==================

Production Security

**Security Headers**:

.. code-block:: bash

    # Security configuration
    SECURITY_HEADERS_ENABLED=true
    HSTS_MAX_AGE=31536000
    CONTENT_SECURITY_POLICY="default-src 'self'"
    X_FRAME_OPTIONS=DENY
    X_CONTENT_TYPE_OPTIONS=nosniff

**Rate Limiting**:

.. code-block:: bash

    # Rate limiting configuration
    RATE_LIMITING_ENABLED=true
    RATE_LIMIT_STORAGE=redis
    RATE_LIMIT_STRATEGY=sliding_window

    # Per-endpoint limits
    AUTH_RATE_LIMIT=5/minute
    API_RATE_LIMIT=100/hour
    UPLOAD_RATE_LIMIT=10/hour

**Session Security**:

.. code-block:: bash

    # Session configuration
    SESSION_SECURE=true
    SESSION_HTTPONLY=true
    SESSION_SAMESITE=strict
    SESSION_MAX_AGE=3600

Configuration Management
========================
=======================

Environment-Specific Configs

**Directory Structure**:

.. code-block:: text

    config/
    ├── .env.development
    ├── .env.staging
    ├── .env.production
    ├── config.development.yaml
    ├── config.staging.yaml
    └── config.production.yaml

**Configuration Loading Script**:

.. code-block:: bash

    #!/bin/bash
    # load_config.sh

    ENVIRONMENT=${ENVIRONMENT:-development}
    CONFIG_FILE="config/.env.${ENVIRONMENT}"

    if [ -f "$CONFIG_FILE" ]; then
        echo "Loading configuration from $CONFIG_FILE"
        export $(cat $CONFIG_FILE | grep -v '^#' | xargs)
    else
        echo "Configuration file $CONFIG_FILE not found"
        exit 1
    fi

    # Start the application
    uvicorn app.main:app --host 0.0.0.0 --port 8000

**Usage**:

.. code-block:: bash

    # Load development config
    ENVIRONMENT=development ./load_config.sh

    # Load production config
    ENVIRONMENT=production ./load_config.sh

Troubleshooting Configuration
=============================
============================

Common Configuration Issues

**Database Connection Errors**:

.. code-block:: bash

    # Test database connection
    python -c "
    import asyncio
    from app.core.database import engine

    async def test_connection():
        async with engine.begin() as conn:
            result = await conn.execute('SELECT version()')
            print(result.fetchone())

    asyncio.run(test_connection())
""""""""""""""""""""""""""""""""""

**Redis Connection Errors**:

.. code-block:: bash

    # Test Redis connection
    python -c "
    import redis
    from app.core.config import settings

    r = redis.from_url(settings.REDIS_URL)
    print(r.ping())
"""""""""""""""""""

**Configuration Loading Issues**:

.. code-block:: bash

    # Debug configuration loading
    python -c "
    from app.core.config import settings
    import json

    config_dict = {
        'DATABASE_URL': settings.DATABASE_URL,
        'REDIS_URL': settings.REDIS_URL,
        'DEBUG': settings.DEBUG,
        'SECRET_KEY': settings.SECRET_KEY[:10] + '...'  # Partial key for security
    }

    print(json.dumps(config_dict, indent=2))
""""""""""""""""""""""""""""""""""""""""""""

Configuration Best Practices

1. **Never commit secrets**: Use ``.gitignore`` for ``.env`` files
2. **Use environment-specific configs**: Separate dev/staging/prod
3. **Validate configuration**: Run validation scripts in CI/CD
4. **Document all variables**: Maintain up-to-date documentation
5. **Use secure defaults**: Fail securely when configuration is missing
6. **Monitor configuration changes**: Log configuration updates
7. **Backup configurations**: Store encrypted backups of production configs

Next Steps
==========

After configuring your application:

1. **Test Your Configuration**: Run the validation script
2. **Set Up Authentication**: :doc:`authentication`
3. **Explore API Usage**: :doc:`api-usage`
4. **Configure Monitoring**: :doc:`../architecture/monitoring`

For advanced configurations, see:

- :doc:`../architecture/security`
- :doc:`../architecture/deployment`
