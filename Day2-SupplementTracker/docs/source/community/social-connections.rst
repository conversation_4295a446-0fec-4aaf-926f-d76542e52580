==================
Social Connections
==================

The Social Connections system enables users to build meaningful relationships within the supplement tracking community. By following other users, you can stay updated on their supplement journeys, discoveries, and insights.

Overview
========

Social connections form the backbone of community engagement, allowing users to:

* **Follow interesting users** and get updates on their activities
* **Build networks** of like-minded supplement enthusiasts  
* **Discover new content** from users with similar interests
* **Share knowledge** with your follower network
* **Get notified** when followed users post new content

How It Works
============

.. mermaid::

   graph TB
       subgraph "User A's Actions"
           A_DISCOVER[🔍 Discover User B]
           A_FOLLOW[👥 Follow User B]
           A_VIEW[👀 View B's Content]
       end
       
       subgraph "System Processing"
           VALIDATE[✅ Validate Follow Request]
           CREATE_RELATIONSHIP[🔗 Create Follow Relationship]
           NOTIFY[🔔 Send Notification]
           UPDATE_FEED[📰 Update Content Feed]
       end
       
       subgraph "User B's Experience"
           B_NOTIFICATION[📬 Receive Follow Notification]
           B_NEW_FOLLOWER[👤 New Follower Added]
           B_POST[📝 Create New Post]
           B_NOTIFY_FOLLOWERS[📢 Notify Followers]
       end
       
       A_DISCOVER --> A_FOLLOW
       A_FOLLOW --> VALIDATE
       VALIDATE --> CREATE_RELATIONSHIP
       CREATE_RELATIONSHIP --> NOTIFY
       NOTIFY --> B_NOTIFICATION
       B_NOTIFICATION --> B_NEW_FOLLOWER
       
       B_POST --> B_NOTIFY_FOLLOWERS
       B_NOTIFY_FOLLOWERS --> UPDATE_FEED
       UPDATE_FEED --> A_VIEW

Following Users
===============

Step-by-Step Guide
------------------

1. **Discover Users**
   
   * Browse community posts and find interesting contributors
   * Check user profiles to learn about their supplement interests
   * Look at their posting history and expertise areas

2. **Follow a User**
   
   * Click the "Follow" button on their profile or next to their posts
   * The system will create a follow relationship
   * The user will receive a notification about their new follower

3. **Manage Your Following List**
   
   * View all users you're following in your profile
   * Unfollow users if your interests change
   * See follower counts and engagement metrics

API Usage
---------

**Follow a User**

.. code-block:: http

   POST /api/v1/community/follow
   Content-Type: application/json
   Authorization: Bearer <your-token>

   {
     "followed_id": "123e4567-e89b-12d3-a456-************"
   }

**Response:**

.. code-block:: json

   {
     "id": "987fcdeb-51a2-43d1-9f4e-123456789abc",
     "follower_id": "456e7890-e89b-12d3-a456-************",
     "followed_id": "123e4567-e89b-12d3-a456-************",
     "created_at": "2025-06-17T22:30:00Z"
   }

**Unfollow a User**

.. code-block:: http

   DELETE /api/v1/community/follow/123e4567-e89b-12d3-a456-************
   Authorization: Bearer <your-token>

**Response:**

.. code-block:: json

   {
     "message": "Successfully unfollowed user"
   }

Building Your Network
====================

Finding Users to Follow
----------------------

.. mermaid::

   flowchart TD
       START[🎯 Start Building Network]
       
       subgraph "Discovery Methods"
           POSTS[📝 Browse Popular Posts]
           GROUPS[🏘️ Join Community Groups]
           COMMENTS[💬 Read Comment Threads]
           SEARCH[🔍 Search by Interests]
       end
       
       subgraph "Evaluation Criteria"
           EXPERTISE[🎓 Expertise Level]
           ACTIVITY[⚡ Activity Level]
           QUALITY[⭐ Content Quality]
           INTERESTS[🎯 Shared Interests]
       end
       
       subgraph "Follow Decision"
           FOLLOW[👥 Follow User]
           WATCH[👀 Watch First]
           SKIP[⏭️ Skip for Now]
       end
       
       START --> POSTS
       START --> GROUPS
       START --> COMMENTS
       START --> SEARCH
       
       POSTS --> EXPERTISE
       GROUPS --> ACTIVITY
       COMMENTS --> QUALITY
       SEARCH --> INTERESTS
       
       EXPERTISE --> FOLLOW
       ACTIVITY --> FOLLOW
       QUALITY --> WATCH
       INTERESTS --> SKIP

Network Growth Strategies
------------------------

1. **Quality over Quantity**
   
   * Focus on following users who provide valuable insights
   * Look for users with expertise in your areas of interest
   * Prioritize active contributors over passive users

2. **Diverse Perspectives**
   
   * Follow users with different supplement experiences
   * Include both beginners and experts in your network
   * Seek out users with various health goals and approaches

3. **Engagement-Based Following**
   
   * Follow users who actively engage with community content
   * Look for thoughtful commenters and discussion starters
   * Prioritize users who respond to questions and help others

Managing Your Followers
=======================

Understanding Your Audience
---------------------------

When other users follow you, they're interested in:

* Your supplement experiences and results
* Your research insights and discoveries
* Your questions and learning journey
* Your recommendations and warnings

.. mermaid::

   graph LR
       subgraph "Your Content"
           EXPERIENCES[📊 Supplement Experiences]
           RESEARCH[🔬 Research Insights]
           QUESTIONS[❓ Questions & Learning]
           RECOMMENDATIONS[💡 Recommendations]
       end
       
       subgraph "Follower Value"
           LEARN[📚 Learn from Your Journey]
           DISCOVER[🔍 Discover New Supplements]
           VALIDATE[✅ Validate Their Experiences]
           CONNECT[🤝 Connect with Like-minded Users]
       end
       
       EXPERIENCES --> LEARN
       RESEARCH --> DISCOVER
       QUESTIONS --> VALIDATE
       RECOMMENDATIONS --> CONNECT

Best Practices for Followers
----------------------------

1. **Consistent Sharing**
   
   * Regularly share your supplement experiences
   * Post updates on your health journey
   * Share interesting research you discover

2. **Quality Content**
   
   * Provide detailed, honest experiences
   * Include relevant context and background
   * Use clear, helpful descriptions

3. **Community Engagement**
   
   * Respond to comments on your posts
   * Engage with your followers' content
   * Participate in group discussions

Privacy and Control
==================

Follow Visibility
-----------------

* **Public Following Lists**: By default, following lists are visible to other users
* **Privacy Controls**: Users can adjust visibility settings in their profile
* **Follower Notifications**: Users receive notifications when someone follows them

.. mermaid::

   graph TB
       subgraph "Privacy Settings"
           PUBLIC[🌐 Public Profile]
           FOLLOWERS_VISIBLE[👥 Followers Visible]
           FOLLOWING_VISIBLE[👀 Following Visible]
           ACTIVITY_VISIBLE[⚡ Activity Visible]
       end
       
       subgraph "Notification Settings"
           FOLLOW_NOTIF[🔔 Follow Notifications]
           POST_NOTIF[📝 Post Notifications]
           COMMENT_NOTIF[💬 Comment Notifications]
           DIGEST_NOTIF[📰 Daily Digest]
       end
       
       PUBLIC --> FOLLOWERS_VISIBLE
       PUBLIC --> FOLLOWING_VISIBLE
       PUBLIC --> ACTIVITY_VISIBLE
       
       FOLLOWERS_VISIBLE --> FOLLOW_NOTIF
       FOLLOWING_VISIBLE --> POST_NOTIF
       ACTIVITY_VISIBLE --> COMMENT_NOTIF
       POST_NOTIF --> DIGEST_NOTIF

Blocking and Reporting
---------------------

* **Block Users**: Prevent specific users from following you
* **Report Issues**: Report inappropriate behavior or content
* **Unfollow Anytime**: Remove followers or stop following others at any time

Analytics and Insights
======================

Following Analytics
------------------

Track your network growth and engagement:

* **Follower Growth**: Monitor how your follower count changes over time
* **Following Activity**: See activity levels of users you follow
* **Content Engagement**: Track which of your posts get the most engagement from followers
* **Network Overlap**: Discover mutual connections and shared interests

.. mermaid::

   graph TB
       subgraph "Your Metrics"
           FOLLOWER_COUNT[👥 Follower Count]
           FOLLOWING_COUNT[👀 Following Count]
           ENGAGEMENT_RATE[📊 Engagement Rate]
           CONTENT_REACH[📈 Content Reach]
       end
       
       subgraph "Network Insights"
           MUTUAL_FOLLOWS[🤝 Mutual Connections]
           INTEREST_OVERLAP[🎯 Interest Overlap]
           ACTIVITY_PATTERNS[⏰ Activity Patterns]
           INFLUENCE_SCORE[⭐ Influence Score]
       end
       
       FOLLOWER_COUNT --> MUTUAL_FOLLOWS
       FOLLOWING_COUNT --> INTEREST_OVERLAP
       ENGAGEMENT_RATE --> ACTIVITY_PATTERNS
       CONTENT_REACH --> INFLUENCE_SCORE

API Reference
=============

Get User Followers
-----------------

.. code-block:: http

   GET /api/v1/community/followers/{user_id}?skip=0&limit=20
   Authorization: Bearer <your-token>

**Response:**

.. code-block:: json

   [
     {
       "id": "987fcdeb-51a2-43d1-9f4e-123456789abc",
       "follower_id": "456e7890-e89b-12d3-a456-************",
       "followed_id": "123e4567-e89b-12d3-a456-************",
       "created_at": "2025-06-17T22:30:00Z"
     }
   ]

Get Users Following
------------------

.. code-block:: http

   GET /api/v1/community/following/{user_id}?skip=0&limit=20
   Authorization: Bearer <your-token>

**Response:**

.. code-block:: json

   [
     {
       "id": "123fcdeb-51a2-43d1-9f4e-987456789abc",
       "follower_id": "123e4567-e89b-12d3-a456-************",
       "followed_id": "789e1234-e89b-12d3-a456-************",
       "created_at": "2025-06-17T21:15:00Z"
     }
   ]

Common Use Cases
===============

For New Users
------------

1. **Getting Started**
   
   * Follow a few active, helpful community members
   * Join relevant community groups
   * Start engaging with content before posting your own

2. **Building Credibility**
   
   * Share honest, detailed supplement experiences
   * Ask thoughtful questions
   * Provide helpful answers to others' questions

For Experienced Users
--------------------

1. **Mentoring Others**
   
   * Share knowledge and expertise
   * Guide newcomers in their supplement journey
   * Provide evidence-based recommendations

2. **Staying Updated**
   
   * Follow researchers and experts in your field
   * Keep up with latest supplement research
   * Discover new trends and approaches

For Researchers
--------------

1. **Building Research Networks**
   
   * Connect with other researchers and clinicians
   * Follow users participating in studies
   * Share research findings and methodologies

2. **Participant Recruitment**
   
   * Build a network of potential study participants
   * Share study opportunities with interested users
   * Maintain relationships with research volunteers

Troubleshooting
==============

Common Issues
------------

**Can't Follow a User**

* Check if the user has blocked you
* Verify you're not trying to follow yourself
* Ensure you're logged in with proper authentication

**Not Receiving Follow Notifications**

* Check your notification settings
* Verify your email preferences
* Look in your spam folder for email notifications

**Following List Not Updating**

* Refresh your browser or app
* Check your internet connection
* Contact support if the issue persists

Best Practices Summary
=====================

1. **Be Selective**: Follow users who provide genuine value
2. **Stay Engaged**: Actively participate in your network
3. **Respect Privacy**: Honor others' privacy settings and boundaries
4. **Quality Content**: Share valuable, honest experiences
5. **Community First**: Focus on helping the community grow and learn

The social connections system creates the foundation for a thriving supplement tracking community, enabling knowledge sharing, mutual support, and collaborative learning.
