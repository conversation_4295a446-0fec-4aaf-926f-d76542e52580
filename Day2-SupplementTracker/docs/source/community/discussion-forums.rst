==================
Discussion Forums
==================

The Discussion Forums provide organized spaces for community members to share knowledge, ask questions, and collaborate on supplement research. Forums are organized into **Community Groups** that focus on specific topics, supplements, or research areas.

Overview
========

Discussion forums enable:

* **Organized Conversations**: Topic-specific groups for focused discussions
* **Knowledge Sharing**: Share experiences, research, and insights
* **Question & Answer**: Get help from experienced community members
* **Research Collaboration**: Coordinate studies and share findings
* **Content Discovery**: Find relevant discussions through search and categories

Forum Structure
===============

.. mermaid::

   graph TB
       subgraph "Community Groups"
           VITAMINS[🍊 Vitamins & Minerals]
           NOOTROPICS[🧠 Nootropics]
           HERBS[🌿 Herbal Supplements]
           RESEARCH[🔬 Research & Studies]
           GENERAL[💬 General Discussion]
       end
       
       subgraph "Discussion Posts"
           QUESTIONS[❓ Questions]
           EXPERIENCES[📊 Experience Reports]
           RESEARCH_POSTS[📄 Research Findings]
           REVIEWS[⭐ Product Reviews]
           DISCUSSIONS[💭 General Discussions]
       end
       
       subgraph "Comments & Replies"
           COMMENTS[💬 Comments]
           REPLIES[↳ Threaded Replies]
           REACTIONS[👍 Reactions]
       end
       
       VITAMINS --> QUESTIONS
       NOOTROPICS --> EXPERIENCES
       HERBS --> RESEARCH_POSTS
       RESEARCH --> REVIEWS
       GENERAL --> DISCUSSIONS
       
       QUESTIONS --> COMMENTS
       EXPERIENCES --> COMMENTS
       RESEARCH_POSTS --> COMMENTS
       REVIEWS --> COMMENTS
       DISCUSSIONS --> COMMENTS
       
       COMMENTS --> REPLIES
       COMMENTS --> REACTIONS

Community Groups
===============

Group Categories
---------------

**Supplement Categories**

* **Vitamins & Minerals**: Discussions about essential nutrients
* **Nootropics**: Cognitive enhancement supplements
* **Herbal Supplements**: Natural and traditional remedies
* **Sports Nutrition**: Performance and recovery supplements
* **Specialized Health**: Condition-specific supplement discussions

**Research & Science**

* **Clinical Studies**: Discussion of published research
* **Personal Experiments**: Self-tracking and N=1 studies
* **Research Methods**: Study design and analysis techniques
* **Literature Reviews**: Comprehensive research summaries

**Community Support**

* **Beginners**: Getting started with supplements
* **Troubleshooting**: Side effects and interactions
* **Success Stories**: Positive outcomes and achievements
* **General Discussion**: Off-topic and community building

Creating and Managing Groups
---------------------------

.. mermaid::

   flowchart TD
       START[🎯 Identify Need for New Group]
       
       subgraph "Group Creation"
           PROPOSE[📝 Propose New Group]
           DEFINE[📋 Define Purpose & Rules]
           SETUP[⚙️ Configure Settings]
           LAUNCH[🚀 Launch Group]
       end
       
       subgraph "Group Management"
           MODERATE[🛡️ Moderate Content]
           ENGAGE[👥 Engage Members]
           GROW[📈 Grow Membership]
           EVOLVE[🔄 Evolve Guidelines]
       end
       
       START --> PROPOSE
       PROPOSE --> DEFINE
       DEFINE --> SETUP
       SETUP --> LAUNCH
       
       LAUNCH --> MODERATE
       MODERATE --> ENGAGE
       ENGAGE --> GROW
       GROW --> EVOLVE

**Group Creation API**

.. code-block:: http

   POST /api/v1/community/groups
   Content-Type: application/json
   Authorization: Bearer <your-token>

   {
     "name": "Magnesium Research",
     "description": "Dedicated to discussing magnesium supplements, research, and experiences",
     "category": "Minerals",
     "is_public": true,
     "is_moderated": false
   }

**Response:**

.. code-block:: json

   {
     "id": "123e4567-e89b-12d3-a456-************",
     "name": "Magnesium Research",
     "description": "Dedicated to discussing magnesium supplements, research, and experiences",
     "category": "Minerals",
     "is_public": true,
     "is_moderated": false,
     "member_count": 1,
     "created_by_user_id": "456e7890-e89b-12d3-a456-************",
     "created_at": "2025-06-17T22:30:00Z",
     "updated_at": "2025-06-17T22:30:00Z"
   }

Discussion Posts
===============

Post Types
----------

**Question Posts** (❓)
   Seeking advice, recommendations, or information from the community

**Experience Reports** (📊)
   Detailed accounts of supplement experiences, including dosages, effects, and timelines

**Research Findings** (📄)
   Sharing and discussing scientific studies, clinical trials, and research papers

**Product Reviews** (⭐)
   Evaluations of specific supplement products, brands, and formulations

**General Discussions** (💭)
   Open-ended conversations about supplement-related topics

Creating Posts
--------------

.. mermaid::

   sequenceDiagram
       participant U as User
       participant S as System
       participant G as Group
       participant N as Notifications
       
       U->>S: Create new post
       S->>S: Validate post content
       S->>G: Add post to group
       S->>S: Update group statistics
       S->>N: Generate notifications
       N->>G: Notify group members
       S->>U: Confirm post creation
       
       Note over U,G: Post is now live in group

**Create Post API**

.. code-block:: http

   POST /api/v1/community/posts
   Content-Type: application/json
   Authorization: Bearer <your-token>

   {
     "title": "Magnesium Glycinate vs Magnesium Oxide - Personal Experience",
     "content": "I've been experimenting with different forms of magnesium for sleep quality...",
     "post_type": "experience",
     "group_id": "123e4567-e89b-12d3-a456-************"
   }

**Response:**

.. code-block:: json

   {
     "id": "789e1234-e89b-12d3-a456-************",
     "title": "Magnesium Glycinate vs Magnesium Oxide - Personal Experience",
     "content": "I've been experimenting with different forms of magnesium for sleep quality...",
     "post_type": "experience",
     "group_id": "123e4567-e89b-12d3-a456-************",
     "author_id": "456e7890-e89b-12d3-a456-************",
     "is_pinned": false,
     "is_locked": false,
     "view_count": 0,
     "like_count": 0,
     "comment_count": 0,
     "created_at": "2025-06-17T22:35:00Z",
     "updated_at": "2025-06-17T22:35:00Z"
   }

Post Management
--------------

**View Tracking**
   Every time a user views a post, the view count is automatically incremented

**Engagement Metrics**
   Posts track likes, comments, and overall engagement for popularity ranking

**Moderation Tools**
   Group moderators can pin important posts, lock discussions, or remove inappropriate content

Comments and Replies
===================

Threaded Discussion System
-------------------------

.. mermaid::

   graph TB
       POST[📝 Original Post]
       
       subgraph "Top-Level Comments"
           C1[💬 Comment 1]
           C2[💬 Comment 2]
           C3[💬 Comment 3]
       end
       
       subgraph "Nested Replies"
           R1[↳ Reply to Comment 1]
           R2[↳ Reply to Comment 1]
           R3[↳ Reply to Comment 2]
           R4[↳ Reply to Reply 1]
       end
       
       POST --> C1
       POST --> C2
       POST --> C3
       
       C1 --> R1
       C1 --> R2
       C2 --> R3
       R1 --> R4

Comment Features
---------------

* **Threaded Replies**: Multi-level comment threading for detailed discussions
* **Like System**: Users can like helpful or insightful comments
* **Soft Deletion**: Deleted comments are hidden but preserve thread structure
* **Edit History**: Track comment modifications for transparency
* **Mention System**: Tag other users with @username notifications

**Create Comment API**

.. code-block:: http

   POST /api/v1/community/posts/789e1234-e89b-12d3-a456-************/comments
   Content-Type: application/json
   Authorization: Bearer <your-token>

   {
     "content": "Great post! I've had similar experiences with magnesium glycinate...",
     "parent_comment_id": null
   }

**Create Reply API**

.. code-block:: http

   POST /api/v1/community/posts/789e1234-e89b-12d3-a456-************/comments
   Content-Type: application/json
   Authorization: Bearer <your-token>

   {
     "content": "What dosage of magnesium glycinate did you find most effective?",
     "parent_comment_id": "abc123de-f456-7890-1234-567890abcdef"
   }

Content Discovery
================

Search and Filtering
-------------------

.. mermaid::

   graph TB
       subgraph "Search Methods"
           TEXT_SEARCH[🔍 Text Search]
           CATEGORY_FILTER[📂 Category Filter]
           POST_TYPE_FILTER[📝 Post Type Filter]
           DATE_FILTER[📅 Date Filter]
           AUTHOR_FILTER[👤 Author Filter]
       end
       
       subgraph "Sorting Options"
           NEWEST[🆕 Newest First]
           POPULAR[🔥 Most Popular]
           MOST_VIEWED[👀 Most Viewed]
           MOST_COMMENTED[💬 Most Commented]
           TRENDING[📈 Trending]
       end
       
       subgraph "Results"
           RELEVANT_POSTS[📋 Relevant Posts]
           HIGHLIGHTED_CONTENT[✨ Highlighted Content]
           RELATED_GROUPS[🏘️ Related Groups]
       end
       
       TEXT_SEARCH --> RELEVANT_POSTS
       CATEGORY_FILTER --> RELEVANT_POSTS
       POST_TYPE_FILTER --> RELEVANT_POSTS
       
       NEWEST --> HIGHLIGHTED_CONTENT
       POPULAR --> HIGHLIGHTED_CONTENT
       TRENDING --> HIGHLIGHTED_CONTENT
       
       RELEVANT_POSTS --> RELATED_GROUPS

**Search Posts API**

.. code-block:: http

   GET /api/v1/community/posts?skip=0&limit=20&group_id=123e4567&post_type=experience&author_id=456e7890
   Authorization: Bearer <your-token>

Content Quality
===============

Community Guidelines
-------------------

**High-Quality Posts Should:**

* Provide detailed, accurate information
* Include relevant context and background
* Use clear, respectful language
* Cite sources when making claims
* Share personal experiences honestly

**Avoid:**

* Medical advice or diagnosis
* Promotional content or spam
* Unsubstantiated health claims
* Personal attacks or harassment
* Off-topic discussions

Moderation System
----------------

.. mermaid::

   flowchart TD
       CONTENT[📄 User Content]
       
       subgraph "Automated Checks"
           SPAM_FILTER[🚫 Spam Detection]
           LANGUAGE_FILTER[🔤 Language Filter]
           LINK_VALIDATION[🔗 Link Validation]
       end
       
       subgraph "Community Moderation"
           REPORT_SYSTEM[🚨 User Reports]
           PEER_REVIEW[⭐ Peer Review]
           COMMUNITY_VOTING[🗳️ Community Voting]
       end
       
       subgraph "Moderator Actions"
           APPROVE[✅ Approve]
           FLAG[⚠️ Flag for Review]
           REMOVE[❌ Remove]
           EDIT[✏️ Request Edit]
       end
       
       CONTENT --> SPAM_FILTER
       CONTENT --> LANGUAGE_FILTER
       CONTENT --> LINK_VALIDATION
       
       CONTENT --> REPORT_SYSTEM
       CONTENT --> PEER_REVIEW
       CONTENT --> COMMUNITY_VOTING
       
       SPAM_FILTER --> APPROVE
       REPORT_SYSTEM --> FLAG
       PEER_REVIEW --> REMOVE
       COMMUNITY_VOTING --> EDIT

Best Practices
==============

For Group Creators
-----------------

1. **Clear Purpose**: Define the group's focus and scope clearly
2. **Welcoming Environment**: Create inclusive, supportive spaces
3. **Active Moderation**: Maintain quality through consistent moderation
4. **Engagement**: Regularly participate and encourage discussion
5. **Growth Strategy**: Plan for sustainable community growth

For Post Authors
---------------

1. **Descriptive Titles**: Use clear, specific titles that describe your content
2. **Detailed Content**: Provide comprehensive information and context
3. **Proper Categorization**: Choose the right group and post type
4. **Engage with Responses**: Reply to comments and questions
5. **Follow Up**: Update posts with new information or results

For Community Members
--------------------

1. **Constructive Comments**: Provide helpful, thoughtful responses
2. **Respectful Discourse**: Maintain respectful tone in all interactions
3. **Share Knowledge**: Contribute your expertise and experiences
4. **Ask Good Questions**: Ask specific, well-researched questions
5. **Support Others**: Help newcomers and less experienced members

Analytics and Insights
=====================

Group Analytics
--------------

* **Member Growth**: Track how group membership changes over time
* **Post Activity**: Monitor posting frequency and engagement levels
* **Popular Topics**: Identify trending discussion topics
* **Member Engagement**: Measure active vs. passive participation

Post Performance
---------------

* **View Metrics**: Track post views and reach
* **Engagement Rates**: Monitor likes, comments, and shares
* **Discussion Quality**: Measure depth and quality of conversations
* **Knowledge Impact**: Assess how posts influence community knowledge

API Reference Summary
====================

**Groups**

.. code-block:: http

   GET    /api/v1/community/groups              # List groups
   POST   /api/v1/community/groups              # Create group
   GET    /api/v1/community/groups/{id}         # Get group
   PUT    /api/v1/community/groups/{id}         # Update group
   DELETE /api/v1/community/groups/{id}         # Delete group

**Posts**

.. code-block:: http

   GET    /api/v1/community/posts               # List posts
   POST   /api/v1/community/posts               # Create post
   GET    /api/v1/community/posts/{id}          # Get post
   PUT    /api/v1/community/posts/{id}          # Update post
   DELETE /api/v1/community/posts/{id}          # Delete post

**Comments**

.. code-block:: http

   GET    /api/v1/community/posts/{id}/comments # List comments
   POST   /api/v1/community/posts/{id}/comments # Create comment
   PUT    /api/v1/community/comments/{id}       # Update comment
   DELETE /api/v1/community/comments/{id}       # Delete comment

The discussion forums provide a structured, engaging platform for supplement community members to share knowledge, collaborate on research, and support each other's health journeys. Through organized groups, threaded discussions, and quality moderation, the forums create a valuable resource for evidence-based supplement information.
