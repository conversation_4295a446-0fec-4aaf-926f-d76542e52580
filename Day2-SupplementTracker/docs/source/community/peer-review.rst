====================
Peer Review System
====================

The Peer Review System ensures content quality and scientific accuracy through community-driven validation. This system enables experts and experienced users to review posts, research findings, and supplement claims, providing credibility scores and feedback to help users make informed decisions.

Overview
========

The peer review system provides:

* **Quality Assurance**: Expert validation of supplement claims and research
* **Credibility Scoring**: Numerical ratings for content reliability
* **Expert Feedback**: Detailed reviews from knowledgeable community members
* **Transparency**: Open review process with visible reviewer credentials
* **Continuous Improvement**: Iterative content refinement through feedback

How Peer Review Works
====================

.. mermaid::

   sequenceDiagram
       participant A as Author
       participant S as System
       participant R as Reviewer
       participant C as Community
       
       A->>S: Submit content for review
       S->>S: Add to review queue
       S->>R: Notify potential reviewers
       R->>S: Accept review assignment
       R->>S: Submit review & score
       S->>S: Aggregate multiple reviews
       S->>A: Notify author of review
       S->>C: Update content credibility
       
       Note over A,C: Content now has peer review score

Review Process Flow
==================

.. mermaid::

   flowchart TD
       SUBMIT[📄 Content Submitted]
       
       subgraph "Review Assignment"
           QUEUE[📋 Review Queue]
           MATCH[🎯 Match Reviewers]
           NOTIFY[📧 Notify Reviewers]
           ACCEPT[✅ Accept Assignment]
       end
       
       subgraph "Review Process"
           EVALUATE[🔍 Evaluate Content]
           SCORE[📊 Assign Score]
           FEEDBACK[💬 Provide Feedback]
           SUBMIT_REVIEW[📝 Submit Review]
       end
       
       subgraph "Aggregation"
           COLLECT[📥 Collect Reviews]
           CALCULATE[🧮 Calculate Score]
           PUBLISH[📢 Publish Results]
       end
       
       subgraph "Outcomes"
           APPROVED[✅ Approved]
           REVISION[🔄 Needs Revision]
           REJECTED[❌ Rejected]
       end
       
       SUBMIT --> QUEUE
       QUEUE --> MATCH
       MATCH --> NOTIFY
       NOTIFY --> ACCEPT
       
       ACCEPT --> EVALUATE
       EVALUATE --> SCORE
       SCORE --> FEEDBACK
       FEEDBACK --> SUBMIT_REVIEW
       
       SUBMIT_REVIEW --> COLLECT
       COLLECT --> CALCULATE
       CALCULATE --> PUBLISH
       
       PUBLISH --> APPROVED
       PUBLISH --> REVISION
       PUBLISH --> REJECTED

Content Types for Review
=======================

Research Claims
--------------

* **Clinical Study References**: Verification of cited research
* **Mechanism of Action**: Validation of biological explanations
* **Dosage Recommendations**: Assessment of suggested dosages
* **Safety Information**: Review of contraindications and side effects

Experience Reports
-----------------

* **Methodology Quality**: Assessment of self-tracking methods
* **Data Completeness**: Evaluation of reported information
* **Bias Assessment**: Identification of potential biases
* **Reproducibility**: Likelihood others could replicate results

Product Reviews
--------------

* **Factual Accuracy**: Verification of product information
* **Balanced Assessment**: Fair evaluation of pros and cons
* **Conflict of Interest**: Disclosure of potential conflicts
* **Comparative Analysis**: Quality of product comparisons

Reviewer Qualifications
=======================

Expertise Levels
---------------

.. mermaid::

   graph TB
       subgraph "Reviewer Levels"
           GENERAL[🌟 General Reviewer]
           INTERMEDIATE[⭐⭐ Intermediate Reviewer]
           EXPERT[⭐⭐⭐ Expert Reviewer]
           SPECIALIST[🏆 Specialist Reviewer]
       end
       
       subgraph "Qualifications"
           EXPERIENCE[📊 Community Experience]
           EDUCATION[🎓 Educational Background]
           PUBLICATIONS[📄 Publications]
           CERTIFICATIONS[🏅 Certifications]
       end
       
       subgraph "Review Authority"
           BASIC_CONTENT[📝 Basic Content]
           RESEARCH_CLAIMS[🔬 Research Claims]
           CLINICAL_DATA[🏥 Clinical Data]
           EXPERT_ANALYSIS[🧠 Expert Analysis]
       end
       
       GENERAL --> EXPERIENCE
       INTERMEDIATE --> EDUCATION
       EXPERT --> PUBLICATIONS
       SPECIALIST --> CERTIFICATIONS
       
       EXPERIENCE --> BASIC_CONTENT
       EDUCATION --> RESEARCH_CLAIMS
       PUBLICATIONS --> CLINICAL_DATA
       CERTIFICATIONS --> EXPERT_ANALYSIS

Reviewer Criteria
----------------

**General Reviewers**
   * Active community members with 6+ months experience
   * Demonstrated knowledge through quality posts and comments
   * No conflicts of interest with reviewed content

**Intermediate Reviewers**
   * Relevant educational background or professional experience
   * Consistent high-quality contributions to the community
   * Successful completion of reviewer training program

**Expert Reviewers**
   * Advanced degree in relevant field (nutrition, medicine, biochemistry)
   * Published research or professional clinical experience
   * Peer recognition within the scientific community

**Specialist Reviewers**
   * Recognized experts in specific supplement categories
   * Extensive research background in particular areas
   * Invited reviewers with exceptional qualifications

Review Criteria and Scoring
===========================

Scoring System
--------------

Reviews use a **10-point scale** with specific criteria:

.. mermaid::

   graph LR
       subgraph "Score Ranges"
           EXCELLENT[9-10: Excellent]
           GOOD[7-8: Good]
           ACCEPTABLE[5-6: Acceptable]
           POOR[3-4: Poor]
           UNACCEPTABLE[1-2: Unacceptable]
       end
       
       subgraph "Quality Factors"
           ACCURACY[🎯 Accuracy]
           COMPLETENESS[📋 Completeness]
           METHODOLOGY[🔬 Methodology]
           CLARITY[💡 Clarity]
           EVIDENCE[📊 Evidence Quality]
       end
       
       ACCURACY --> EXCELLENT
       COMPLETENESS --> GOOD
       METHODOLOGY --> ACCEPTABLE
       CLARITY --> POOR
       EVIDENCE --> UNACCEPTABLE

**Detailed Scoring Criteria:**

* **9-10 (Excellent)**: Highly accurate, well-researched, comprehensive
* **7-8 (Good)**: Generally accurate with minor issues or gaps
* **5-6 (Acceptable)**: Adequate quality but needs improvement
* **3-4 (Poor)**: Significant issues with accuracy or methodology
* **1-2 (Unacceptable)**: Misleading, dangerous, or completely inaccurate

Review Categories
----------------

**Scientific Accuracy** (Weight: 30%)
   * Factual correctness of claims
   * Proper citation of sources
   * Alignment with current research

**Methodology Quality** (Weight: 25%)
   * Soundness of experimental design
   * Appropriate controls and variables
   * Statistical analysis quality

**Completeness** (Weight: 20%)
   * Comprehensive coverage of topic
   * Inclusion of relevant information
   * Acknowledgment of limitations

**Clarity and Presentation** (Weight: 15%)
   * Clear, understandable writing
   * Logical organization
   * Appropriate use of terminology

**Practical Value** (Weight: 10%)
   * Usefulness to community members
   * Actionable insights
   * Real-world applicability

Creating and Managing Reviews
============================

Submitting Content for Review
-----------------------------

**Create Review Request API**

.. code-block:: http

   POST /api/v1/community/reviews
   Content-Type: application/json
   Authorization: Bearer <your-token>

   {
     "content_type": "post",
     "content_id": "789e1234-e89b-12d3-a456-426614174000",
     "score": 8,
     "feedback": "Well-researched post with good methodology. Minor suggestion: include more details about timing of measurements.",
     "expertise_level": "intermediate"
   }

**Response:**

.. code-block:: json

   {
     "id": "abc123de-f456-7890-1234-567890abcdef",
     "content_type": "post",
     "content_id": "789e1234-e89b-12d3-a456-426614174000",
     "reviewer_id": "456e7890-e89b-12d3-a456-426614174000",
     "status": "pending",
     "score": 8,
     "feedback": "Well-researched post with good methodology...",
     "expertise_level": "intermediate",
     "created_at": "2025-06-17T22:40:00Z",
     "updated_at": "2025-06-17T22:40:00Z"
   }

Review Management
----------------

**Get Content Reviews API**

.. code-block:: http

   GET /api/v1/community/reviews/content/post/789e1234-e89b-12d3-a456-426614174000
   Authorization: Bearer <your-token>

**Response:**

.. code-block:: json

   [
     {
       "id": "abc123de-f456-7890-1234-567890abcdef",
       "reviewer_id": "456e7890-e89b-12d3-a456-426614174000",
       "status": "completed",
       "score": 8,
       "feedback": "Well-researched post with good methodology...",
       "expertise_level": "intermediate",
       "created_at": "2025-06-17T22:40:00Z"
     },
     {
       "id": "def456gh-i789-0123-4567-890123456789",
       "reviewer_id": "789e1234-e89b-12d3-a456-426614174000",
       "status": "completed",
       "score": 9,
       "feedback": "Excellent analysis with comprehensive data...",
       "expertise_level": "expert",
       "created_at": "2025-06-17T23:15:00Z"
     }
   ]

Review Aggregation
==================

Calculating Aggregate Scores
---------------------------

.. mermaid::

   graph TB
       subgraph "Individual Reviews"
           R1[Review 1: Score 8]
           R2[Review 2: Score 9]
           R3[Review 3: Score 7]
       end
       
       subgraph "Weighting Factors"
           W1[Reviewer Expertise]
           W2[Review Completeness]
           W3[Community Validation]
       end
       
       subgraph "Aggregation"
           WEIGHTED[Weighted Average]
           CONFIDENCE[Confidence Interval]
           FINAL[Final Score]
       end
       
       R1 --> WEIGHTED
       R2 --> WEIGHTED
       R3 --> WEIGHTED
       
       W1 --> WEIGHTED
       W2 --> CONFIDENCE
       W3 --> FINAL
       
       WEIGHTED --> FINAL

**Aggregation Formula:**

```
Final Score = Σ(Individual Score × Reviewer Weight × Completeness Factor) / Σ(Weights)

Where:
- Expert Reviewer Weight = 3.0
- Intermediate Reviewer Weight = 2.0  
- General Reviewer Weight = 1.0
- Completeness Factor = 0.5 to 1.0 based on review detail
```

Quality Indicators
-----------------

* **Review Count**: Number of reviews received
* **Reviewer Diversity**: Mix of expertise levels
* **Score Consistency**: Agreement between reviewers
* **Confidence Level**: Statistical confidence in aggregate score

Review Display and Impact
========================

Content Credibility Indicators
-----------------------------

.. mermaid::

   graph LR
       subgraph "Credibility Badges"
           VERIFIED[✅ Peer Verified]
           HIGH_QUALITY[⭐ High Quality]
           EXPERT_REVIEWED[🏆 Expert Reviewed]
           COMMUNITY_TRUSTED[👥 Community Trusted]
       end
       
       subgraph "Score Ranges"
           SCORE_9_10[Score 9-10]
           SCORE_7_8[Score 7-8]
           SCORE_5_6[Score 5-6]
           MULTIPLE_REVIEWS[3+ Reviews]
       end
       
       SCORE_9_10 --> VERIFIED
       SCORE_7_8 --> HIGH_QUALITY
       SCORE_5_6 --> EXPERT_REVIEWED
       MULTIPLE_REVIEWS --> COMMUNITY_TRUSTED

Review Display Format
--------------------

Content displays include:

* **Aggregate Score**: Overall peer review rating
* **Review Count**: Number of completed reviews
* **Expertise Mix**: Distribution of reviewer levels
* **Recent Reviews**: Latest review summaries
* **Credibility Badge**: Visual quality indicator

Reviewer Recognition
===================

Reviewer Reputation System
-------------------------

.. mermaid::

   graph TB
       subgraph "Reviewer Metrics"
           REVIEWS_COMPLETED[📊 Reviews Completed]
           ACCURACY_SCORE[🎯 Accuracy Score]
           HELPFULNESS[👍 Helpfulness Rating]
           EXPERTISE_RECOGNITION[🏅 Expertise Recognition]
       end
       
       subgraph "Recognition Levels"
           BRONZE[🥉 Bronze Reviewer]
           SILVER[🥈 Silver Reviewer]
           GOLD[🥇 Gold Reviewer]
           PLATINUM[💎 Platinum Reviewer]
       end
       
       subgraph "Benefits"
           PRIORITY_ACCESS[⚡ Priority Review Access]
           EXPERT_BADGE[🏆 Expert Badge]
           COMMUNITY_RECOGNITION[👑 Community Recognition]
           RESEARCH_OPPORTUNITIES[🔬 Research Opportunities]
       end
       
       REVIEWS_COMPLETED --> BRONZE
       ACCURACY_SCORE --> SILVER
       HELPFULNESS --> GOLD
       EXPERTISE_RECOGNITION --> PLATINUM
       
       BRONZE --> PRIORITY_ACCESS
       SILVER --> EXPERT_BADGE
       GOLD --> COMMUNITY_RECOGNITION
       PLATINUM --> RESEARCH_OPPORTUNITIES

Reviewer Incentives
------------------

* **Recognition Badges**: Visual indicators of reviewer expertise
* **Priority Access**: Early access to new features and content
* **Research Collaboration**: Opportunities to participate in studies
* **Community Status**: Enhanced profile visibility and credibility
* **Knowledge Sharing**: Platform to share expertise with community

Best Practices
==============

For Content Authors
------------------

1. **Prepare for Review**: Ensure content is complete and well-researched
2. **Cite Sources**: Include proper references and citations
3. **Be Transparent**: Disclose limitations and potential biases
4. **Respond to Feedback**: Address reviewer comments constructively
5. **Iterate and Improve**: Use feedback to enhance content quality

For Reviewers
------------

1. **Thorough Evaluation**: Carefully assess all aspects of content
2. **Constructive Feedback**: Provide specific, actionable suggestions
3. **Objective Assessment**: Maintain neutrality and avoid personal bias
4. **Timely Reviews**: Complete reviews within reasonable timeframes
5. **Professional Tone**: Maintain respectful, professional communication

For Community Members
--------------------

1. **Understand Scores**: Learn how to interpret peer review ratings
2. **Value Feedback**: Consider reviewer comments when evaluating content
3. **Contribute Reviews**: Participate in the review process when qualified
4. **Report Issues**: Flag inappropriate or biased reviews
5. **Support Quality**: Prioritize peer-reviewed content in discussions

Quality Assurance
=================

Review Quality Control
---------------------

* **Reviewer Validation**: Verification of reviewer qualifications
* **Review Monitoring**: Detection of biased or inappropriate reviews
* **Conflict of Interest**: Identification and management of conflicts
* **Appeal Process**: Mechanism for challenging unfair reviews
* **Continuous Improvement**: Regular refinement of review criteria

System Integrity
----------------

* **Anti-Gaming Measures**: Prevention of review manipulation
* **Reviewer Diversity**: Ensuring diverse perspectives in reviews
* **Transparency**: Open access to review criteria and processes
* **Accountability**: Clear responsibility for review quality
* **Community Oversight**: Community involvement in system governance

The peer review system creates a robust framework for maintaining content quality and scientific accuracy, enabling the community to build a trusted repository of supplement knowledge while recognizing and rewarding expert contributions.
