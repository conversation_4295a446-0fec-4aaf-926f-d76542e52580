API Reference
=============

The Research Tools API provides comprehensive programmatic access to all research functionality, enabling third-party integrations, automated workflows, and custom research applications.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

The Research Tools API is built on REST principles with JSON request/response format, providing:

* **Complete Functionality**: All research features accessible via API
* **Consistent Interface**: Standardized endpoints and response formats
* **Authentication**: Secure token-based authentication
* **Rate Limiting**: Fair usage policies and throttling
* **Comprehensive Documentation**: Detailed endpoint specifications
* **SDK Support**: Client libraries for popular programming languages

API Architecture
----------------

The API follows a modular architecture with clear separation of concerns and consistent patterns across all endpoints.

.. mermaid::

   graph TB
       subgraph "Client Applications"
           WEB[Web Interface]
           MOBILE[Mobile Apps]
           THIRD[Third-party Tools]
           SCRIPTS[Analysis Scripts]
       end
       
       subgraph "API Gateway"
           AUTH[Authentication]
           RATE[Rate Limiting]
           VALID[Request Validation]
           ROUTE[Request Routing]
       end
       
       subgraph "Research Services"
           PROTO[Protocol Service]
           LIT[Literature Service]
           PART[Participant Service]
           STATS[Analysis Service]
           COLLAB[Collaboration Service]
       end
       
       subgraph "Data Layer"
           DB[(PostgreSQL)]
           CACHE[(Redis Cache)]
           FILES[File Storage]
       end
       
       WEB --> AUTH
       MOBILE --> AUTH
       THIRD --> AUTH
       SCRIPTS --> AUTH
       
       AUTH --> RATE
       RATE --> VALID
       VALID --> ROUTE
       
       ROUTE --> PROTO
       ROUTE --> LIT
       ROUTE --> PART
       ROUTE --> STATS
       ROUTE --> COLLAB
       
       PROTO --> DB
       LIT --> DB
       PART --> DB
       STATS --> DB
       COLLAB --> DB
       
       PROTO --> CACHE
       STATS --> FILES
       
       style AUTH fill:#e3f2fd
       style PROTO fill:#f3e5f5
       style DB fill:#e8f5e8

Authentication
--------------

All API requests require authentication using Bearer tokens obtained through the OAuth 2.0 flow.

**Authentication Flow**:

.. mermaid::

   sequenceDiagram
       participant C as Client
       participant A as Auth Server
       participant API as Research API
       participant R as Resource
       
       C->>A: Request Authorization
       A->>C: Redirect to Login
       C->>A: Provide Credentials
       A->>C: Return Authorization Code
       C->>A: Exchange Code for Token
       A->>C: Return Access Token
       C->>API: API Request with Token
       API->>A: Validate Token
       A->>API: Token Valid
       API->>R: Access Resource
       R->>API: Return Data
       API->>C: Return Response

**Token Management**:

.. code-block:: python

   import requests
   
   # Obtain access token
   auth_data = {
       "grant_type": "authorization_code",
       "client_id": "your_client_id",
       "client_secret": "your_client_secret",
       "code": "authorization_code",
       "redirect_uri": "your_redirect_uri"
   }
   
   token_response = requests.post(
       "https://auth.supplement-tracker.com/oauth/token",
       data=auth_data
   )
   
   access_token = token_response.json()["access_token"]
   
   # Use token in API requests
   headers = {
       "Authorization": f"Bearer {access_token}",
       "Content-Type": "application/json"
   }

**Token Scopes**:

.. list-table:: API Scopes and Permissions
   :header-rows: 1
   :widths: 25 75

   * - Scope
     - Permissions
   * - **research:read**
     - Read access to research protocols, literature, and analyses
   * - **research:write**
     - Create and update research protocols and literature
   * - **research:admin**
     - Full administrative access including team management
   * - **participants:read**
     - Read access to participant data (anonymized)
   * - **participants:write**
     - Create and update participant records
   * - **analysis:execute**
     - Execute statistical analyses and generate reports
   * - **collaboration:manage**
     - Manage research team collaborations and invitations

Research Protocols API
----------------------

Manage research protocols including creation, updates, and lifecycle management.

**Base URL**: ``/api/v1/research/protocols``

**Endpoints**:

.. code-block:: http

   POST   /api/v1/research/protocols              # Create protocol
   GET    /api/v1/research/protocols              # List protocols
   GET    /api/v1/research/protocols/{id}         # Get protocol
   PUT    /api/v1/research/protocols/{id}         # Update protocol
   DELETE /api/v1/research/protocols/{id}         # Delete protocol
   GET    /api/v1/research/protocols/{id}/stats   # Protocol statistics

**Create Protocol**:

.. code-block:: python

   # POST /api/v1/research/protocols
   protocol_data = {
       "title": "Vitamin D and Immune Function Study",
       "description": "A 12-week observational study examining the relationship between vitamin D supplementation and immune system markers",
       "hypothesis": "Higher vitamin D levels will correlate with improved immune function markers and reduced infection rates",
       "methodology": "Prospective cohort study with monthly blood draws and health assessments",
       "variables": {
           "primary_outcome": "immune_function_score",
           "secondary_outcomes": ["vitamin_d_level", "infection_rate", "symptom_severity"],
           "baseline_variables": ["age", "bmi", "health_status", "current_medications"]
       },
       "duration_days": 84,
       "sample_size_target": 150,
       "ethics_approval": True
   }
   
   response = requests.post(
       "https://api.supplement-tracker.com/v1/research/protocols",
       json=protocol_data,
       headers=headers
   )

**Response Format**:

.. code-block:: json

   {
       "id": "123e4567-e89b-12d3-a456-426614174000",
       "title": "Vitamin D and Immune Function Study",
       "description": "A 12-week observational study...",
       "status": "draft",
       "sample_size_actual": 0,
       "created_by_user_id": "user_123",
       "created_at": "2023-12-01T10:00:00Z",
       "updated_at": "2023-12-01T10:00:00Z"
   }

Literature Management API
-------------------------

Manage scientific literature references with DOI/PMID integration and quality assessment.

**Base URL**: ``/api/v1/research/literature``

**Endpoints**:

.. code-block:: http

   POST   /api/v1/research/literature                    # Add reference
   GET    /api/v1/research/literature                    # Search literature
   GET    /api/v1/research/literature/{id}               # Get reference
   PUT    /api/v1/research/literature/{id}               # Update reference
   DELETE /api/v1/research/literature/{id}               # Delete reference
   GET    /api/v1/research/literature/search/doi/{doi}   # Search by DOI
   GET    /api/v1/research/literature/search/pmid/{pmid} # Search by PMID

**Add Literature Reference**:

.. code-block:: python

   # POST /api/v1/research/literature
   reference_data = {
       "title": "Effects of Magnesium Supplementation on Sleep Quality: A Systematic Review and Meta-Analysis",
       "authors": "Smith, J.A., Johnson, B.C., Williams, D.E.",
       "journal": "Sleep Medicine Reviews",
       "publication_date": "2023-06-15",
       "doi": "10.1016/j.smrv.2023.101234",
       "pmid": "37123456",
       "abstract": "Background: Magnesium supplementation has been proposed as a natural intervention for sleep disorders...",
       "study_type": "systematic_review",
       "sample_size": 1247,
       "keywords": "magnesium, sleep quality, insomnia, supplementation, meta-analysis",
       "quality_score": 9.2,
       "relevance_score": 9.5,
       "notes": "High-quality systematic review with comprehensive search strategy and rigorous methodology"
   }
   
   response = requests.post(
       "https://api.supplement-tracker.com/v1/research/literature",
       json=reference_data,
       headers=headers
   )

**Advanced Search**:

.. code-block:: python

   # GET /api/v1/research/literature with filters
   search_params = {
       "search": "magnesium AND sleep",
       "study_type": "randomized_controlled_trial",
       "publication_date_from": "2020-01-01",
       "min_quality_score": 7.0,
       "journal": "Sleep Medicine",
       "sort_by": "publication_date",
       "order": "desc",
       "limit": 20,
       "offset": 0
   }
   
   response = requests.get(
       "https://api.supplement-tracker.com/v1/research/literature",
       params=search_params,
       headers=headers
   )

Participant Management API
--------------------------

Manage study participants with privacy protection and consent tracking.

**Base URL**: ``/api/v1/research/participants``

**Endpoints**:

.. code-block:: http

   POST   /api/v1/research/protocols/{id}/participants    # Add participant
   GET    /api/v1/research/protocols/{id}/participants    # List participants
   GET    /api/v1/research/participants/{id}              # Get participant
   PUT    /api/v1/research/participants/{id}              # Update participant
   POST   /api/v1/research/participants/{id}/consent      # Record consent
   POST   /api/v1/research/participants/{id}/withdraw     # Process withdrawal

**Add Participant**:

.. code-block:: python

   # POST /api/v1/research/protocols/{protocol_id}/participants
   participant_data = {
       "demographics": {
           "age_range": "25-34",
           "gender": "female",
           "education": "bachelor_degree",
           "location": "urban"
       },
       "baseline_data": {
           "bmi": 22.5,
           "health_status": "good",
           "current_medications": [],
           "supplement_history": ["multivitamin", "vitamin_d"]
       },
       "consent_given": True,
       "consent_date": "2023-12-01T09:30:00Z",
       "notes": "Recruited through social media campaign, high motivation level"
   }
   
   response = requests.post(
       f"https://api.supplement-tracker.com/v1/research/protocols/{protocol_id}/participants",
       json=participant_data,
       headers=headers
   )

Statistical Analysis API
------------------------

Execute statistical analyses and generate reports with professional-grade tools.

**Base URL**: ``/api/v1/research/analyses``

**Endpoints**:

.. code-block:: http

   POST   /api/v1/research/analyses                       # Create analysis
   GET    /api/v1/research/analyses                       # List analyses
   GET    /api/v1/research/analyses/{id}                  # Get analysis
   PUT    /api/v1/research/analyses/{id}                  # Update analysis
   POST   /api/v1/research/analyses/{id}/execute          # Execute analysis
   GET    /api/v1/research/analyses/{id}/results          # Get results
   GET    /api/v1/research/analyses/{id}/visualizations   # Get plots

**Correlation Analysis**:

.. code-block:: python

   # POST /api/v1/research/analyses
   analysis_data = {
       "title": "Magnesium Dosage vs Sleep Quality Correlation",
       "description": "Pearson correlation analysis between daily magnesium intake and sleep quality scores",
       "analysis_type": "correlation",
       "data_source": "participant_data",
       "variables": {
           "x": "daily_magnesium_mg",
           "y": "sleep_quality_score"
       },
       "parameters": {
           "method": "pearson",
           "confidence_level": 0.95,
           "two_tailed": True
       },
       "filter_criteria": {
           "study_duration": ">=8_weeks",
           "compliance_rate": ">=0.8"
       }
   }
   
   # Create analysis
   response = requests.post(
       "https://api.supplement-tracker.com/v1/research/analyses",
       json=analysis_data,
       headers=headers
   )
   
   analysis_id = response.json()["id"]
   
   # Execute analysis
   execution_response = requests.post(
       f"https://api.supplement-tracker.com/v1/research/analyses/{analysis_id}/execute",
       headers=headers
   )

**Analysis Results**:

.. code-block:: json

   {
       "analysis_id": "analysis_123",
       "status": "completed",
       "results": {
           "correlation_coefficient": 0.67,
           "p_value": 0.001,
           "confidence_interval": [0.45, 0.82],
           "sample_size": 89,
           "effect_size": "large"
       },
       "interpretation": "Strong positive correlation between magnesium dosage and sleep quality (r=0.67, p<0.001)",
       "visualizations": {
           "scatter_plot": "https://api.supplement-tracker.com/files/plots/scatter_123.png",
           "correlation_matrix": "https://api.supplement-tracker.com/files/plots/matrix_123.png"
       }
   }

Collaboration API
-----------------

Manage research team collaborations and invitations.

**Base URL**: ``/api/v1/research/collaborations``

**Endpoints**:

.. code-block:: http

   POST   /api/v1/research/collaborations                    # Create collaboration
   GET    /api/v1/research/protocols/{id}/collaborations     # Protocol collaborations
   GET    /api/v1/research/users/{id}/collaborations         # User collaborations
   PUT    /api/v1/research/collaborations/{id}               # Update collaboration
   POST   /api/v1/research/collaborations/{id}/accept        # Accept invitation
   POST   /api/v1/research/collaborations/{id}/decline       # Decline invitation

**Create Collaboration**:

.. code-block:: python

   # POST /api/v1/research/collaborations
   collaboration_data = {
       "protocol_id": "protocol_123",
       "user_id": "user_456",
       "role": "statistical_analyst",
       "permissions": {
           "data_access": True,
           "analysis_tools": True,
           "results_export": True,
           "protocol_view": True
       },
       "contribution_description": "Advanced statistical modeling and machine learning analysis for supplement efficacy prediction",
       "expected_time_commitment": "10-15 hours per week",
       "project_duration": "4 months"
   }
   
   response = requests.post(
       "https://api.supplement-tracker.com/v1/research/collaborations",
       json=collaboration_data,
       headers=headers
   )

Error Handling
--------------

The API uses standard HTTP status codes and provides detailed error information.

**Error Response Format**:

.. code-block:: json

   {
       "error": {
           "code": "VALIDATION_ERROR",
           "message": "Invalid request parameters",
           "details": {
               "field": "sample_size_target",
               "issue": "Must be greater than 0"
           },
           "request_id": "req_123456789"
       }
   }

**Common Status Codes**:

.. list-table:: HTTP Status Codes
   :header-rows: 1
   :widths: 15 25 60

   * - Code
     - Status
     - Description
   * - **200**
     - OK
     - Request successful
   * - **201**
     - Created
     - Resource created successfully
   * - **400**
     - Bad Request
     - Invalid request parameters
   * - **401**
     - Unauthorized
     - Authentication required or invalid
   * - **403**
     - Forbidden
     - Insufficient permissions
   * - **404**
     - Not Found
     - Resource not found
   * - **429**
     - Too Many Requests
     - Rate limit exceeded
   * - **500**
     - Internal Server Error
     - Server error occurred

Rate Limiting
-------------

API requests are subject to rate limiting to ensure fair usage and system stability.

**Rate Limits**:

.. list-table:: Rate Limits by Endpoint Type
   :header-rows: 1
   :widths: 30 35 35

   * - Endpoint Type
     - Rate Limit
     - Time Window
   * - **Read Operations**
     - 1000 requests
     - Per hour
   * - **Write Operations**
     - 100 requests
     - Per hour
   * - **Analysis Execution**
     - 10 requests
     - Per hour
   * - **File Uploads**
     - 50 requests
     - Per hour

**Rate Limit Headers**:

.. code-block:: http

   X-RateLimit-Limit: 1000
   X-RateLimit-Remaining: 999
   X-RateLimit-Reset: 1640995200

SDK and Client Libraries
------------------------

Official SDKs are available for popular programming languages.

**Python SDK**:

.. code-block:: python

   # Install SDK
   pip install supplement-tracker-research-sdk
   
   # Initialize client
   from supplement_tracker import ResearchClient
   
   client = ResearchClient(
       api_key="your_api_key",
       base_url="https://api.supplement-tracker.com"
   )
   
   # Create protocol
   protocol = client.protocols.create({
       "title": "My Research Study",
       "description": "Study description",
       "duration_days": 60,
       "sample_size_target": 100
   })
   
   # Execute analysis
   analysis = client.analyses.create({
       "title": "Correlation Analysis",
       "analysis_type": "correlation",
       "variables": {"x": "dosage", "y": "outcome"}
   })
   
   results = client.analyses.execute(analysis.id)

**JavaScript SDK**:

.. code-block:: javascript

   // Install SDK
   npm install @supplement-tracker/research-sdk
   
   // Initialize client
   import { ResearchClient } from '@supplement-tracker/research-sdk';
   
   const client = new ResearchClient({
       apiKey: 'your_api_key',
       baseUrl: 'https://api.supplement-tracker.com'
   });
   
   // Create protocol
   const protocol = await client.protocols.create({
       title: 'My Research Study',
       description: 'Study description',
       durationDays: 60,
       sampleSizeTarget: 100
   });
   
   // Execute analysis
   const analysis = await client.analyses.create({
       title: 'Correlation Analysis',
       analysisType: 'correlation',
       variables: { x: 'dosage', y: 'outcome' }
   });
   
   const results = await client.analyses.execute(analysis.id);

Best Practices
--------------

**API Usage**:
* Use appropriate HTTP methods (GET, POST, PUT, DELETE)
* Include proper error handling in your applications
* Implement exponential backoff for rate limit handling
* Cache responses when appropriate to reduce API calls
* Use pagination for large result sets

**Security**:
* Store API tokens securely and rotate regularly
* Use HTTPS for all API communications
* Implement proper input validation and sanitization
* Follow principle of least privilege for API scopes
* Monitor API usage for unusual patterns

**Performance**:
* Use bulk operations when available
* Implement client-side caching for static data
* Optimize queries with appropriate filters
* Use compression for large payloads
* Monitor response times and optimize accordingly

**Integration**:
* Follow semantic versioning for API compatibility
* Implement proper logging and monitoring
* Use webhooks for real-time updates when available
* Test integrations thoroughly in staging environments
* Document your integration for team members

Next Steps
----------

* :doc:`overview` - Return to Research Tools overview
* :doc:`protocol-management` - Learn about research protocol design
* :doc:`statistical-analysis` - Explore data analysis capabilities
* :doc:`collaboration` - Set up research team collaboration

The Research Tools API provides comprehensive programmatic access to all research functionality, enabling powerful integrations and custom research applications while maintaining security and performance standards.
