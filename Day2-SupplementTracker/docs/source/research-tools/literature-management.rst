Literature Management
====================

The Literature Management system provides comprehensive tools for discovering, organizing, and analyzing scientific literature related to supplement research, enabling evidence-based decision making and systematic reviews.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

Scientific literature forms the foundation of evidence-based research. The Literature Management system offers:

* **Comprehensive Database**: Centralized repository of scientific papers
* **Smart Import**: DOI and PubMed integration for easy reference addition
* **Quality Assessment**: Systematic evaluation of study quality and relevance
* **Advanced Search**: Powerful filtering and discovery capabilities
* **Citation Management**: Professional reference formatting and export
* **Collaborative Reviews**: Team-based literature analysis

Literature Import Workflow
---------------------------

The system supports multiple methods for adding scientific references to your literature database.

.. mermaid::

   flowchart TD
       START([Add Literature]) --> METHOD{Import Method?}
       
       METHOD -->|DOI| DOI_INPUT[Enter DOI]
       METHOD -->|PubMed ID| PMID_INPUT[Enter PMID]
       METHOD -->|Manual Entry| MANUAL[Manual Form]
       METHOD -->|Bulk Import| BULK[Upload File]
       
       DOI_INPUT --> DOI_RESOLVE[Resolve DOI Metadata]
       PMID_INPUT --> PMID_FETCH[Fetch PubMed Data]
       MANUAL --> FORM_FILL[Complete Reference Form]
       BULK --> FILE_PARSE[Parse Import File]
       
       DOI_RESOLVE --> VALIDATE[Validate Metadata]
       PMID_FETCH --> VALIDATE
       FORM_FILL --> VALIDATE
       FILE_PARSE --> VALIDATE
       
       VALIDATE --> QUALITY[Quality Assessment]
       QUALITY --> SAVE[Save to Database]
       SAVE --> INDEX[Update Search Index]
       INDEX --> NOTIFY[Notify Team Members]
       NOTIFY --> END([Reference Added])
       
       style START fill:#4caf50
       style END fill:#2196f3
       style VALIDATE fill:#ff9800
       style QUALITY fill:#9c27b0

DOI Integration
~~~~~~~~~~~~~~~

**Digital Object Identifier (DOI)** integration provides automatic metadata retrieval for published papers.

**Supported DOI Sources**:
* Journal articles from major publishers
* Conference proceedings and presentations
* Preprint servers (arXiv, bioRxiv, medRxiv)
* Books and book chapters
* Datasets and supplementary materials

**DOI Import Process**:

.. code-block:: python

   # Example: Adding a paper by DOI
   import requests
   
   doi = "10.1016/j.clnu.2021.02.012"
   
   # Add reference via API
   reference_data = {
       "doi": doi,
       "notes": "Important study on magnesium and sleep quality",
       "quality_score": 8.5,
       "relevance_score": 9.0
   }
   
   response = requests.post(
       "https://api.supplement-tracker.com/v1/research/literature",
       json=reference_data,
       headers={"Authorization": "Bearer YOUR_TOKEN"}
   )

PubMed Integration
~~~~~~~~~~~~~~~~~~

**PubMed ID (PMID)** integration connects directly with the world's largest biomedical literature database.

**PubMed Features**:
* Automatic abstract retrieval
* MeSH term extraction
* Author and affiliation information
* Publication type classification
* Related article suggestions

**PMID Import Example**:

.. code-block:: python

   # Example: Adding a paper by PubMed ID
   reference_data = {
       "pmid": "33618777",
       "study_type": "randomized_controlled_trial",
       "keywords": "magnesium, sleep, insomnia, supplementation",
       "notes": "High-quality RCT with 46 participants"
   }

Quality Assessment System
-------------------------

The quality assessment framework helps researchers evaluate the reliability and validity of scientific studies.

.. mermaid::

   graph LR
       subgraph "Study Design Quality"
           SD1[Randomization]
           SD2[Blinding]
           SD3[Control Group]
           SD4[Sample Size]
       end
       
       subgraph "Methodology Quality"
           MQ1[Outcome Measures]
           MQ2[Statistical Analysis]
           MQ3[Bias Control]
           MQ4[Reporting Quality]
       end
       
       subgraph "Evidence Quality"
           EQ1[Effect Size]
           EQ2[Confidence Intervals]
           EQ3[P-values]
           EQ4[Clinical Significance]
       end
       
       subgraph "Overall Assessment"
           OA[Quality Score: 1-10]
           REL[Relevance Score: 1-10]
           REC[Recommendation Level]
       end
       
       SD1 --> OA
       SD2 --> OA
       SD3 --> OA
       SD4 --> OA
       MQ1 --> OA
       MQ2 --> OA
       MQ3 --> OA
       MQ4 --> OA
       EQ1 --> REL
       EQ2 --> REL
       EQ3 --> REL
       EQ4 --> REL
       OA --> REC
       REL --> REC
       
       style OA fill:#e3f2fd
       style REL fill:#f3e5f5
       style REC fill:#e8f5e8

Quality Assessment Criteria
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Study Design (Weight: 40%)**:

.. list-table:: Study Design Quality Factors
   :header-rows: 1
   :widths: 25 25 50

   * - Factor
     - Weight
     - Assessment Criteria
   * - **Randomization**
     - 30%
     - Proper randomization method, allocation concealment
   * - **Blinding**
     - 25%
     - Single, double, or triple blinding implementation
   * - **Control Group**
     - 25%
     - Appropriate control/placebo group
   * - **Sample Size**
     - 20%
     - Adequate power analysis and sample size

**Methodology Quality (Weight: 35%)**:
* **Outcome Measures**: Validated instruments and objective measures
* **Statistical Analysis**: Appropriate tests and multiple comparison corrections
* **Bias Control**: Selection, performance, detection, and attrition bias
* **Reporting Quality**: Complete reporting per CONSORT or similar guidelines

**Evidence Quality (Weight: 25%)**:
* **Effect Size**: Clinical and statistical significance
* **Confidence Intervals**: Precision of estimates
* **P-values**: Statistical significance and multiple testing
* **Reproducibility**: Replication potential and external validity

Advanced Search and Discovery
-----------------------------

The search system provides sophisticated filtering and discovery capabilities for finding relevant literature.

.. mermaid::

   graph TB
       subgraph "Search Interface"
           SI1[Text Search]
           SI2[Filter Panel]
           SI3[Advanced Query]
           SI4[Saved Searches]
       end
       
       subgraph "Search Engine"
           SE1[Full-Text Indexing]
           SE2[Semantic Search]
           SE3[Citation Analysis]
           SE4[Relevance Ranking]
       end
       
       subgraph "Filter Options"
           FO1[Publication Date]
           FO2[Study Type]
           FO3[Quality Score]
           FO4[Journal Impact]
           FO5[Sample Size]
           FO6[Keywords/MeSH]
       end
       
       subgraph "Results Display"
           RD1[Relevance Sorted]
           RD2[Citation Count]
           RD3[Quality Indicators]
           RD4[Export Options]
       end
       
       SI1 --> SE1
       SI2 --> FO1
       SI3 --> SE2
       SI4 --> SE3
       
       SE1 --> RD1
       SE2 --> RD2
       SE3 --> RD3
       SE4 --> RD4
       
       FO1 --> RD1
       FO2 --> RD1
       FO3 --> RD1
       FO4 --> RD1
       FO5 --> RD1
       FO6 --> RD1

Search Capabilities
~~~~~~~~~~~~~~~~~~~

**Text Search Features**:
* Full-text search across titles, abstracts, and keywords
* Boolean operators (AND, OR, NOT)
* Phrase searching with quotation marks
* Wildcard and fuzzy matching
* Stemming and synonym expansion

**Advanced Filtering**:

.. code-block:: python

   # Example: Advanced literature search
   search_params = {
       "search": "magnesium AND sleep",
       "study_type": "randomized_controlled_trial",
       "publication_date_from": "2015-01-01",
       "publication_date_to": "2023-12-31",
       "min_quality_score": 7.0,
       "min_sample_size": 30,
       "journal": "Journal of Sleep Research",
       "sort_by": "relevance_score",
       "limit": 20
   }
   
   response = requests.get(
       "https://api.supplement-tracker.com/v1/research/literature",
       params=search_params,
       headers={"Authorization": "Bearer YOUR_TOKEN"}
   )

Citation Management
-------------------

Professional citation formatting and reference management for research publications and reports.

**Supported Citation Styles**:
* APA (American Psychological Association)
* MLA (Modern Language Association)
* Chicago/Turabian
* Harvard
* Vancouver (ICMJE)
* Custom institutional styles

**Citation Features**:
* Automatic formatting based on metadata
* Bibliography generation for manuscripts
* In-text citation insertion
* Reference list management
* Export to reference managers (EndNote, Zotero, Mendeley)

**Citation Export Example**:

.. code-block:: python

   # Generate citations in different formats
   reference_id = "123e4567-e89b-12d3-a456-426614174000"
   
   # APA format
   apa_citation = requests.get(
       f"https://api.supplement-tracker.com/v1/research/literature/{reference_id}/citation",
       params={"format": "apa"},
       headers={"Authorization": "Bearer YOUR_TOKEN"}
   )
   
   # Output: "Smith, J., & Johnson, A. (2023). Effects of magnesium on sleep quality: 
   # A randomized controlled trial. Journal of Sleep Research, 32(4), 245-256."

Literature Review Workflows
---------------------------

Systematic approaches to literature review and evidence synthesis.

.. mermaid::

   sequenceDiagram
       participant R as Researcher
       participant DB as Literature Database
       participant T as Review Team
       participant S as Synthesis Tool
       
       R->>DB: Define Search Strategy
       DB->>R: Return Initial Results
       R->>R: Screen Titles/Abstracts
       R->>T: Distribute for Review
       T->>T: Full-Text Review
       T->>R: Quality Assessments
       R->>S: Data Extraction
       S->>S: Synthesis Analysis
       S->>R: Evidence Summary
       R->>R: Write Review Report

**Systematic Review Process**:

1. **Protocol Development**
   * Define research question (PICO framework)
   * Develop search strategy
   * Set inclusion/exclusion criteria
   * Plan data extraction and analysis

2. **Literature Search**
   * Comprehensive database searching
   * Reference list screening
   * Grey literature inclusion
   * Search strategy documentation

3. **Study Selection**
   * Title and abstract screening
   * Full-text review
   * Inclusion/exclusion tracking
   * Inter-reviewer agreement assessment

4. **Data Extraction**
   * Standardized extraction forms
   * Study characteristics documentation
   * Outcome data collection
   * Quality assessment completion

5. **Evidence Synthesis**
   * Qualitative synthesis
   * Quantitative meta-analysis (if appropriate)
   * Subgroup and sensitivity analyses
   * Evidence quality assessment (GRADE)

Collaborative Literature Management
-----------------------------------

Team-based literature review and knowledge sharing capabilities.

**Collaboration Features**:
* **Shared Libraries**: Team access to literature collections
* **Review Assignments**: Distribute papers among team members
* **Annotation Sharing**: Collaborative notes and highlights
* **Consensus Building**: Resolve disagreements on quality assessments
* **Progress Tracking**: Monitor review completion status

**Team Workflow Example**:

.. code-block:: python

   # Create a shared literature collection
   collection_data = {
       "name": "Magnesium and Sleep Systematic Review",
       "description": "Literature collection for systematic review project",
       "team_members": [
           {"user_id": "user1", "role": "lead_reviewer"},
           {"user_id": "user2", "role": "reviewer"},
           {"user_id": "user3", "role": "data_extractor"}
       ],
       "review_criteria": {
           "inclusion_criteria": ["RCT", "human_subjects", "magnesium_intervention"],
           "exclusion_criteria": ["animal_studies", "case_reports", "reviews"]
       }
   }

Literature Analytics
--------------------

Comprehensive analytics and insights from your literature database.

**Analytics Dashboard**:

.. mermaid::

   graph TB
       subgraph "Collection Metrics"
           CM1[Total Papers: 1,247]
           CM2[Recent Additions: 23]
           CM3[Quality Distribution]
           CM4[Study Type Breakdown]
       end
       
       subgraph "Research Trends"
           RT1[Publication Timeline]
           RT2[Author Networks]
           RT3[Journal Analysis]
           RT4[Keyword Trends]
       end
       
       subgraph "Quality Insights"
           QI1[Average Quality Score: 7.2]
           QI2[High-Quality Studies: 342]
           QI3[Methodology Gaps]
           QI4[Evidence Strength]
       end
       
       subgraph "Team Activity"
           TA1[Review Progress: 78%]
           TA2[Team Contributions]
           TA3[Recent Activity]
           TA4[Collaboration Stats]
       end
       
       style CM1 fill:#e3f2fd
       style RT1 fill:#f3e5f5
       style QI1 fill:#e8f5e8
       style TA1 fill:#fff3e0

**Trend Analysis**:
* Publication volume over time
* Emerging research topics
* Author collaboration networks
* Journal impact and specialization
* Geographic research distribution

API Integration
---------------

Complete API access for literature management functionality.

**Key Endpoints**:

.. code-block:: http

   POST   /api/v1/research/literature                    # Add reference
   GET    /api/v1/research/literature                    # Search literature
   GET    /api/v1/research/literature/{id}               # Get reference
   PUT    /api/v1/research/literature/{id}               # Update reference
   DELETE /api/v1/research/literature/{id}               # Delete reference
   GET    /api/v1/research/literature/search/doi/{doi}   # Search by DOI
   GET    /api/v1/research/literature/search/pmid/{pmid} # Search by PMID

**Bulk Operations**:

.. code-block:: python

   # Bulk import from bibliography file
   import pandas as pd
   
   # Read bibliography data
   refs_df = pd.read_csv('references.csv')
   
   # Batch import
   for _, row in refs_df.iterrows():
       reference_data = {
           "title": row['title'],
           "authors": row['authors'],
           "journal": row['journal'],
           "doi": row['doi'],
           "publication_date": row['date']
       }
       
       response = requests.post(
           "https://api.supplement-tracker.com/v1/research/literature",
           json=reference_data,
           headers={"Authorization": "Bearer YOUR_TOKEN"}
       )

Best Practices
--------------

**Literature Search Strategy**:
* Use multiple databases and sources
* Develop comprehensive search terms
* Include both published and grey literature
* Document search strategy for reproducibility
* Regular search updates for ongoing reviews

**Quality Assessment**:
* Use standardized quality assessment tools
* Train reviewers on assessment criteria
* Conduct inter-reviewer reliability testing
* Document assessment rationale
* Consider study context and limitations

**Data Management**:
* Maintain consistent metadata standards
* Regular database backups and version control
* Secure access controls for sensitive data
* Integration with institutional repositories
* Long-term preservation planning

**Team Collaboration**:
* Clear role definitions and responsibilities
* Regular team meetings and progress reviews
* Standardized workflows and procedures
* Conflict resolution protocols
* Knowledge sharing and training

Next Steps
----------

* :doc:`participant-management` - Learn about study participant management
* :doc:`statistical-analysis` - Explore data analysis capabilities
* :doc:`collaboration` - Set up research team collaboration
* :doc:`api-reference` - Technical API documentation

The Literature Management system provides the foundation for evidence-based research, enabling researchers to build upon existing knowledge and contribute to the growing body of supplement research literature.
