Compliance API Reference
========================

The Supplement Tracker Community Platform provides a comprehensive REST API for managing legal compliance, user rights, and privacy protection. This API enables developers to integrate compliance features into applications and automate compliance workflows.

.. contents:: Table of Contents
   :local:
   :depth: 3

API Overview
------------

The Compliance API is organized into several functional areas:

* **Consent Management**: Collect, track, and manage user consent
* **User Rights**: Handle data subject rights requests (GDPR, CCPA, HIPAA)
* **Data Export**: Provide data portability and access rights
* **Compliance Validation**: Verify compliance before data processing
* **Legal Documents**: Access current legal documents and policies

Base URL and Authentication
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: text

   Base URL: https://api.supplementtracker.com/api/compliance
   Authentication: Bearer token (JWT)
   Content-Type: application/json

**Authentication Example**:

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        -H "Content-Type: application/json" \
        https://api.supplementtracker.com/api/compliance/consent/status

**Rate Limiting**:

.. list-table:: API Rate Limits
   :header-rows: 1
   :widths: 30 35 35

   * - Endpoint Category
     - Rate Limit
     - Burst Limit
   * - **Consent Management**
     - 100 requests/hour
     - 10 requests/minute
   * - **User Rights**
     - 50 requests/hour
     - 5 requests/minute
   * - **Data Export**
     - 10 requests/hour
     - 2 requests/minute
   * - **Validation**
     - 1000 requests/hour
     - 50 requests/minute

Consent Management API
---------------------

Record Consent
~~~~~~~~~~~~~

Record user consent for specific data processing activities.

.. http:post:: /api/compliance/consent

   Record user consent for a specific type and version.

   **Request Body**:

   .. code-block:: json

      {
        "consent_type": "research_participation",
        "consent_version": "1.0",
        "consent_text": "I consent to participate in research studies...",
        "metadata": {
          "study_id": "supplement_effectiveness_2024",
          "consent_method": "web_form"
        }
      }

   **Response**:

   .. code-block:: json

      {
        "consent_id": 12345,
        "consent_type": "research_participation",
        "status": "given",
        "version": "1.0",
        "given_at": "2024-12-17T10:30:00Z",
        "expires_at": null
      }

   :statuscode 200: Consent recorded successfully
   :statuscode 400: Invalid consent data
   :statuscode 401: Authentication required
   :statuscode 429: Rate limit exceeded

**Consent Types**:

.. list-table:: Available Consent Types
   :header-rows: 1
   :widths: 30 50 20

   * - Consent Type
     - Description
     - Required
   * - ``platform_terms``
     - Agreement to platform terms of service
     - Yes
   * - ``privacy_policy``
     - Consent to privacy policy
     - Yes
   * - ``research_participation``
     - Participation in research studies
     - No
   * - ``data_sharing``
     - Sharing anonymized data with researchers
     - No
   * - ``marketing_communications``
     - Promotional emails and notifications
     - No
   * - ``analytics_tracking``
     - Usage analytics collection
     - No
   * - ``cookie_usage``
     - Non-essential cookies
     - No
   * - ``health_data_collection``
     - Collection of health information
     - No
   * - ``third_party_sharing``
     - Sharing with approved third parties
     - No

Withdraw Consent
~~~~~~~~~~~~~~~

.. http:post:: /api/compliance/consent/withdraw

   Withdraw previously given consent.

   **Request Body**:

   .. code-block:: json

      {
        "consent_type": "marketing_communications",
        "withdrawal_reason": "No longer interested in promotional content"
      }

   **Response**:

   .. code-block:: json

      {
        "message": "Consent withdrawn successfully",
        "consent_type": "marketing_communications",
        "withdrawn_at": "2024-12-17T11:15:00Z",
        "impact": {
          "affected_features": ["promotional_emails", "product_updates"],
          "alternative_options": ["essential_communications_only"]
        }
      }

   :statuscode 200: Consent withdrawn successfully
   :statuscode 400: Invalid withdrawal request
   :statuscode 404: Consent not found

Get Consent Status
~~~~~~~~~~~~~~~~~

.. http:get:: /api/compliance/consent/status

   Get current consent status for all consent types.

   **Response**:

   .. code-block:: json

      {
        "consent_status": {
          "platform_terms": {
            "status": "given",
            "version": "1.0",
            "given_at": "2024-01-15T09:00:00Z",
            "expires_at": null,
            "is_valid": true
          },
          "research_participation": {
            "status": "given",
            "version": "1.0",
            "given_at": "2024-01-15T09:05:00Z",
            "expires_at": null,
            "is_valid": true
          },
          "marketing_communications": {
            "status": "withdrawn",
            "version": "1.0",
            "given_at": "2024-01-15T09:05:00Z",
            "withdrawn_at": "2024-12-17T11:15:00Z",
            "is_valid": false
          }
        }
      }

Get Consent History
~~~~~~~~~~~~~~~~~~

.. http:get:: /api/compliance/consent/history

   Get complete consent history for the authenticated user.

   **Query Parameters**:

   * ``consent_type`` (optional): Filter by specific consent type

   **Response**:

   .. code-block:: json

      {
        "consent_history": [
          {
            "consent_type": "research_participation",
            "status": "given",
            "version": "1.0",
            "given_at": "2024-01-15T09:05:00Z",
            "withdrawn_at": null,
            "expires_at": null
          },
          {
            "consent_type": "marketing_communications",
            "status": "withdrawn",
            "version": "1.0",
            "given_at": "2024-01-15T09:05:00Z",
            "withdrawn_at": "2024-12-17T11:15:00Z",
            "expires_at": "2026-01-15T09:05:00Z"
          }
        ]
      }

User Rights Management API
-------------------------

Submit Rights Request
~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/compliance/rights/request

   Submit a user rights request (access, rectification, erasure, etc.).

   **Request Body**:

   .. code-block:: json

      {
        "request_type": "access",
        "description": "I would like to download all my personal data",
        "request_data": {
          "format": "json",
          "include_research_data": true,
          "include_health_data": true
        }
      }

   **Response**:

   .. code-block:: json

      {
        "request_id": 67890,
        "request_type": "access",
        "status": "pending",
        "requested_at": "2024-12-17T12:00:00Z",
        "due_date": "2025-01-16T12:00:00Z",
        "completed_at": null,
        "estimated_completion": "2024-12-24T12:00:00Z"
      }

**Request Types**:

.. list-table:: User Rights Request Types
   :header-rows: 1
   :widths: 20 40 20 20

   * - Request Type
     - Description
     - Response Time
     - Auto-Processing
   * - ``access``
     - Access to personal data
     - ≤ 30 days
     - Yes
   * - ``rectification``
     - Correct inaccurate data
     - ≤ 7 days
     - Partial
   * - ``erasure``
     - Delete personal data
     - ≤ 7 days
     - No
   * - ``restrict``
     - Restrict data processing
     - ≤ 24 hours
     - Yes
   * - ``portability``
     - Export data in portable format
     - ≤ 30 days
     - Yes
   * - ``object``
     - Object to data processing
     - ≤ 24 hours
     - Partial

Get Rights Requests
~~~~~~~~~~~~~~~~~~

.. http:get:: /api/compliance/rights/requests

   Get all rights requests for the authenticated user.

   **Response**:

   .. code-block:: json

      {
        "requests": [
          {
            "request_id": 67890,
            "request_type": "access",
            "status": "completed",
            "requested_at": "2024-12-17T12:00:00Z",
            "due_date": "2025-01-16T12:00:00Z",
            "completed_at": "2024-12-20T14:30:00Z",
            "description": "Download all personal data"
          },
          {
            "request_id": 67891,
            "request_type": "rectification",
            "status": "in_progress",
            "requested_at": "2024-12-18T09:00:00Z",
            "due_date": "2024-12-25T09:00:00Z",
            "completed_at": null,
            "description": "Correct birth date"
          }
        ]
      }

Get Request Status
~~~~~~~~~~~~~~~~~

.. http:get:: /api/compliance/rights/request/{request_id}/status

   Get detailed status of a specific rights request.

   **Response**:

   .. code-block:: json

      {
        "request_id": 67890,
        "request_type": "access",
        "status": "completed",
        "requested_at": "2024-12-17T12:00:00Z",
        "due_date": "2025-01-16T12:00:00Z",
        "completed_at": "2024-12-20T14:30:00Z",
        "description": "Download all personal data",
        "processing_notes": "Data export generated successfully",
        "download_links": [
          {
            "file_name": "user_data_export.json",
            "download_url": "https://secure.supplementtracker.com/downloads/abc123",
            "expires_at": "2024-12-27T14:30:00Z",
            "file_size": "2.4 MB"
          }
        ]
      }

Data Export and Portability API
-------------------------------

Export User Data
~~~~~~~~~~~~~~~

.. http:post:: /api/compliance/data/export

   Export user data in requested format for data portability.

   **Request Body**:

   .. code-block:: json

      {
        "include_research_data": true,
        "include_health_data": true,
        "include_activity_logs": false,
        "anonymize_shared_data": true,
        "format": "json"
      }

   **Response**: Binary file download

   **Response Headers**:

   .. code-block:: text

      Content-Type: application/json
      Content-Disposition: attachment; filename=user_data_12345.json
      Content-Length: 2457600

   **Supported Formats**:

   * ``json``: Structured JSON format with nested objects
   * ``csv``: Comma-separated values for tabular data
   * ``xml``: XML format with schema validation

Download Data Package
~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/compliance/data/package

   Download complete data package as ZIP file containing all user data.

   **Response**: ZIP file download

   **Response Headers**:

   .. code-block:: text

      Content-Type: application/zip
      Content-Disposition: attachment; filename=user_data_package_12345.zip
      Content-Length: 5242880

   **Package Contents**:

   .. code-block:: text

      user_data_package.zip
      ├── user_profile.json          # Account and profile information
      ├── health_data.csv           # Health metrics and measurements
      ├── supplement_tracking.json   # Supplement usage data
      ├── research_participation.json # Research study data
      ├── consent_history.json      # Complete consent records
      ├── activity_logs.csv         # Platform activity logs
      ├── privacy_settings.json     # Privacy preferences
      ├── communication_history.json # Support interactions
      ├── data_processing_summary.json # Processing activities
      └── export_metadata.json      # Export information

Compliance Validation API
-------------------------

Validate Research Consent
~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/compliance/validation/research/{study_id}

   Validate user consent for research participation in a specific study.

   **Response**:

   .. code-block:: json

      {
        "valid": true,
        "missing_consents": [],
        "study_id": "supplement_effectiveness_2024",
        "validation_timestamp": "2024-12-17T15:00:00Z",
        "consent_details": {
          "research_participation": {
            "status": "given",
            "version": "1.0",
            "given_at": "2024-01-15T09:05:00Z"
          },
          "health_data_collection": {
            "status": "given",
            "version": "1.0",
            "given_at": "2024-01-15T09:05:00Z"
          }
        }
      }

   **Invalid Consent Response**:

   .. code-block:: json

      {
        "valid": false,
        "missing_consents": ["research_participation", "health_data_collection"],
        "study_id": "supplement_effectiveness_2024",
        "validation_timestamp": "2024-12-17T15:00:00Z",
        "required_actions": [
          {
            "action": "obtain_consent",
            "consent_type": "research_participation",
            "consent_url": "/consent/research_participation"
          }
        ]
      }

Validate Marketing Consent
~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/compliance/validation/marketing

   Validate user consent for marketing communications.

   **Response**:

   .. code-block:: json

      {
        "valid": false,
        "consent_status": "withdrawn",
        "withdrawn_at": "2024-12-17T11:15:00Z",
        "alternative_communications": ["essential_notifications", "security_alerts"]
      }

Validate Analytics Consent
~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/compliance/validation/analytics

   Validate user consent for analytics tracking.

   **Response**:

   .. code-block:: json

      {
        "valid": true,
        "consent_status": "given",
        "tracking_level": "standard",
        "excluded_tracking": [],
        "expires_at": "2025-12-17T10:30:00Z"
      }

Legal Documents API
-------------------

Get Privacy Policy
~~~~~~~~~~~~~~~~~

.. http:get:: /api/compliance/documents/privacy-policy

   Get current privacy policy information.

   **Response**:

   .. code-block:: json

      {
        "document_type": "privacy_policy",
        "version": "1.0",
        "effective_date": "2024-12-17",
        "last_updated": "2024-12-17",
        "url": "/legal/privacy-policy",
        "content_summary": "Comprehensive privacy policy covering data collection, use, and user rights",
        "key_changes": [],
        "requires_new_consent": false
      }

Get Terms of Service
~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/compliance/documents/terms-of-service

   Get current terms of service information.

   **Response**:

   .. code-block:: json

      {
        "document_type": "terms_of_service",
        "version": "1.0",
        "effective_date": "2024-12-17",
        "last_updated": "2024-12-17",
        "url": "/legal/terms-of-service",
        "content_summary": "Terms governing platform use, research participation, and user responsibilities",
        "acceptance_required": true
      }

Get Consent Form
~~~~~~~~~~~~~~~

.. http:get:: /api/compliance/documents/consent-forms/{study_id}

   Get consent form for a specific research study.

   **Response**:

   .. code-block:: json

      {
        "study_id": "supplement_effectiveness_2024",
        "consent_form_version": "1.0",
        "last_updated": "2024-12-17",
        "url": "/legal/consent-forms/supplement_effectiveness_2024",
        "required_consents": [
          "research_participation",
          "health_data_collection",
          "data_sharing"
        ],
        "study_duration": "6 months",
        "principal_investigator": "Dr. Jane Smith",
        "institution": "University Research Center"
      }

Error Handling
--------------

Standard Error Response
~~~~~~~~~~~~~~~~~~~~~~

All API endpoints return consistent error responses:

.. code-block:: json

   {
     "error": {
       "code": "CONSENT_REQUIRED",
       "message": "Valid consent required for this operation",
       "details": {
         "missing_consent_types": ["research_participation"],
         "consent_urls": ["/consent/research_participation"]
       },
       "timestamp": "2024-12-17T16:00:00Z",
       "request_id": "req_abc123def456"
     }
   }

**Common Error Codes**:

.. list-table:: API Error Codes
   :header-rows: 1
   :widths: 25 35 40

   * - Error Code
     - Description
     - Resolution
   * - ``CONSENT_REQUIRED``
     - Missing required consent
     - Obtain necessary consent
   * - ``INVALID_REQUEST``
     - Malformed request data
     - Check request format
   * - ``UNAUTHORIZED``
     - Authentication required
     - Provide valid JWT token
   * - ``FORBIDDEN``
     - Insufficient permissions
     - Check user permissions
   * - ``NOT_FOUND``
     - Resource not found
     - Verify resource exists
   * - ``RATE_LIMITED``
     - Rate limit exceeded
     - Reduce request frequency
   * - ``INTERNAL_ERROR``
     - Server error
     - Contact support

SDK and Integration Examples
---------------------------

Python SDK Example
~~~~~~~~~~~~~~~~~

.. code-block:: python

   from supplement_tracker_sdk import ComplianceClient
   
   # Initialize client
   client = ComplianceClient(
       api_key="your_api_key",
       base_url="https://api.supplementtracker.com"
   )
   
   # Record consent
   consent_response = client.consent.record(
       consent_type="research_participation",
       consent_version="1.0"
   )
   
   # Submit rights request
   request_response = client.rights.submit_request(
       request_type="access",
       description="Download my data"
   )
   
   # Export user data
   data_export = client.data.export(
       format="json",
       include_research_data=True
   )

JavaScript SDK Example
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   import { ComplianceAPI } from '@supplement-tracker/compliance-sdk';
   
   const client = new ComplianceAPI({
     apiKey: 'your_api_key',
     baseURL: 'https://api.supplementtracker.com'
   });
   
   // Record consent
   const consentResponse = await client.consent.record({
     consentType: 'research_participation',
     consentVersion: '1.0'
   });
   
   // Check consent status
   const consentStatus = await client.consent.getStatus();
   
   // Submit rights request
   const rightsRequest = await client.rights.submitRequest({
     requestType: 'access',
     description: 'Download my data'
   });

Webhooks and Notifications
--------------------------

Webhook Configuration
~~~~~~~~~~~~~~~~~~~~

Configure webhooks to receive real-time notifications about compliance events:

.. code-block:: json

   {
     "webhook_url": "https://your-app.com/webhooks/compliance",
     "events": [
       "consent.given",
       "consent.withdrawn",
       "rights_request.submitted",
       "rights_request.completed",
       "data_export.ready"
     ],
     "secret": "your_webhook_secret"
   }

**Webhook Event Example**:

.. code-block:: json

   {
     "event_type": "consent.withdrawn",
     "timestamp": "2024-12-17T11:15:00Z",
     "user_id": "user_12345",
     "data": {
       "consent_type": "marketing_communications",
       "withdrawal_reason": "No longer interested",
       "affected_features": ["promotional_emails"]
     },
     "signature": "sha256=abc123def456..."
   }

Conclusion
----------

The Compliance API provides comprehensive programmatic access to all legal compliance features of the Supplement Tracker Community Platform. Through RESTful endpoints, comprehensive error handling, and robust authentication, developers can integrate compliance features seamlessly into their applications while ensuring user privacy and regulatory compliance.

**Key API Features**:

* ✅ **Complete Compliance Coverage**: All compliance features accessible via API
* ✅ **RESTful Design**: Standard HTTP methods and status codes
* ✅ **Comprehensive Documentation**: Detailed examples and error handling
* ✅ **SDK Support**: Official SDKs for popular programming languages
* ✅ **Webhook Integration**: Real-time notifications for compliance events
* ✅ **Rate Limiting**: Fair usage policies and abuse prevention

This API enables developers to build privacy-compliant applications that respect user rights while enabling valuable health research.
