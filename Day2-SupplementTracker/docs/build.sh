#!/usr/bin/env bash
# Build script for Sphinx documentation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the docs directory
if [ ! -f "conf.py" ]; then
    print_error "This script must be run from the docs directory"
    exit 1
fi

# Default build type
BUILD_TYPE=${1:-html}
STRICT=${2:-false}

print_status "Building Sphinx documentation..."
print_status "Build type: $BUILD_TYPE"
print_status "Strict mode: $STRICT"

# Build command
if command -v nix-shell >/dev/null 2>&1; then
    print_status "Using Nix environment..."
    
    if [ "$STRICT" = "true" ]; then
        print_status "Building with warnings as errors..."
        nix-shell -p python311Packages.sphinx --run "sphinx-build -W -b $BUILD_TYPE . _build/$BUILD_TYPE"
    else
        nix-shell -p python311Packages.sphinx --run "sphinx-build -b $BUILD_TYPE . _build/$BUILD_TYPE"
    fi
elif command -v sphinx-build >/dev/null 2>&1; then
    print_status "Using system Sphinx..."
    
    if [ "$STRICT" = "true" ]; then
        print_status "Building with warnings as errors..."
        sphinx-build -W -b "$BUILD_TYPE" . "_build/$BUILD_TYPE"
    else
        sphinx-build -b "$BUILD_TYPE" . "_build/$BUILD_TYPE"
    fi
else
    print_error "Neither nix-shell nor sphinx-build found!"
    print_error "Please install Sphinx or use Nix"
    exit 1
fi

# Check if build was successful
if [ $? -eq 0 ]; then
    print_success "Documentation built successfully!"
    print_status "Output directory: _build/$BUILD_TYPE"
    
    # Open in browser if HTML build
    if [ "$BUILD_TYPE" = "html" ]; then
        if [ -f "_build/html/index.html" ]; then
            print_status "Opening documentation in browser..."
            if command -v xdg-open >/dev/null 2>&1; then
                xdg-open "_build/html/index.html"
            elif command -v open >/dev/null 2>&1; then
                open "_build/html/index.html"
            else
                print_status "Documentation available at: file://$(pwd)/_build/html/index.html"
            fi
        fi
    fi
else
    print_error "Documentation build failed!"
    exit 1
fi
