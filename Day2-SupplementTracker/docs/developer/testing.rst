=============
Testing Guide
=============

This document provides comprehensive guidelines for testing in the Supplement Tracker project.

.. contents:: Table of Contents
   :local:
   :depth: 2

Testing Philosophy
==================

Our testing approach follows these principles:

**Test Pyramid**:
- **Unit Tests** (70%): Fast, isolated tests for individual functions/methods
- **Integration Tests** (20%): Tests for component interactions
- **End-to-End Tests** (10%): Full application workflow tests

**Quality Goals**:
- **Coverage**: Aim for >90% code coverage
- **Reliability**: Tests should be deterministic and stable
- **Speed**: Test suite should run quickly for fast feedback
- **Maintainability**: Tests should be easy to understand and modify

Test Structure
==============

Directory Organization

.. code-block:: text

    tests/
    ├── conftest.py              # Shared fixtures and configuration
    ├── unit/                    # Unit tests
    │   ├── test_models/
    │   │   ├── test_user.py
    │   │   └── test_supplement.py
    │   ├── test_services/
    │   │   ├── test_user_service.py
    │   │   └── test_supplement_service.py
    │   └── test_core/
    │       ├── test_security.py
    │       └── test_config.py
    ├── integration/             # Integration tests
    │   ├── test_api/
    │   │   ├── test_auth_endpoints.py
    │   │   ├── test_user_endpoints.py
    │   │   └── test_supplement_endpoints.py
    │   └── test_database/
    │       └── test_migrations.py
    ├── e2e/                     # End-to-end tests
    │   ├── test_user_workflows.py
    │   └── test_supplement_workflows.py
    └── fixtures/                # Test data and fixtures
        ├── users.json
        └── supplements.json

Test Configuration
==================

pytest Configuration

**pytest.ini**:

.. code-block:: ini

    [tool:pytest]
    testpaths = tests
    python_files = test_*.py
    python_classes = Test*
    python_functions = test_*
    addopts = 
        --strict-markers
        --strict-config
        --verbose
        --tb=short
        --cov=app
        --cov-report=term-missing
        --cov-report=html:htmlcov
        --cov-fail-under=90
    markers =
        unit: Unit tests
        integration: Integration tests
        e2e: End-to-end tests
        slow: Slow running tests
        external: Tests that require external services

**conftest.py**:

.. code-block:: python

    import asyncio
    import pytest
    import pytest_asyncio
    from httpx import AsyncClient
    from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy.pool import StaticPool

    from app.main import app
    from app.core.database import Base, get_db
    from app.core.config import settings

    # Test database URL
    TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

    @pytest.fixture(scope="session")
    def event_loop():
        """Create an instance of the default event loop for the test session."""
        loop = asyncio.get_event_loop_policy().new_event_loop()
        yield loop
        loop.close()

    @pytest.fixture(scope="session")
    async def test_engine():
        """Create test database engine."""
        engine = create_async_engine(
            TEST_DATABASE_URL,
            poolclass=StaticPool,
            connect_args={"check_same_thread": False}
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        yield engine
        await engine.dispose()

    @pytest.fixture
    async def db_session(test_engine):
        """Create a database session for testing."""
        async_session = sessionmaker(
            test_engine, class_=AsyncSession, expire_on_commit=False
        )
        
        async with async_session() as session:
            yield session
            await session.rollback()

    @pytest.fixture
    async def client(db_session):
        """Create test client with database override."""
        def override_get_db():
            return db_session
        
        app.dependency_overrides[get_db] = override_get_db
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            yield ac
        
        app.dependency_overrides.clear()

Unit Testing
============

Model Testing

**Testing SQLAlchemy models**:

.. code-block:: python

    # tests/unit/test_models/test_user.py
    import pytest
    from datetime import datetime
    from uuid import uuid4

    from app.models.user import User

    class TestUserModel:
        """Test User model functionality."""

        def test_user_creation(self):
            """Test user model creation."""
            user = User(
                id=uuid4(),
                email="<EMAIL>",
                username="testuser",
                hashed_password="hashed_password",
                full_name="Test User"
            )
            
            assert user.email == "<EMAIL>"
            assert user.username == "testuser"
            assert user.full_name == "Test User"
            assert user.is_active is True
            assert user.is_verified is False

        def test_user_repr(self):
            """Test user string representation."""
            user_id = uuid4()
            user = User(
                id=user_id,
                email="<EMAIL>",
                username="testuser",
                hashed_password="hashed_password"
            )
            
            expected = f"<User(id={user_id}, username='testuser')>"
            assert repr(user) == expected

        async def test_user_database_operations(self, db_session):
            """Test user database operations."""
            # Create user
            user = User(
                email="<EMAIL>",
                username="testuser",
                hashed_password="hashed_password"
            )
            
            db_session.add(user)
            await db_session.commit()
            await db_session.refresh(user)
            
            # Verify user was created
            assert user.id is not None
            assert user.created_at is not None
            assert isinstance(user.created_at, datetime)

Service Testing

**Testing business logic services**:

.. code-block:: python

    # tests/unit/test_services/test_user_service.py
    import pytest
    from unittest.mock import AsyncMock, patch
    from uuid import uuid4

    from app.services.user_service import UserService
    from app.schemas.user import UserCreate
    from app.models.user import User
    from app.core.exceptions import EmailAlreadyExistsError

    class TestUserService:
        """Test UserService functionality."""

        @pytest.fixture
        def user_service(self, db_session):
            """Create UserService instance."""
            return UserService(db_session)

        @pytest.fixture
        def sample_user_data(self):
            """Sample user creation data."""
            return UserCreate(
                email="<EMAIL>",
                username="testuser",
                password="SecurePass123!",
                full_name="Test User"
            )

        async def test_create_user_success(self, user_service, sample_user_data):
            """Test successful user creation."""
            # Act
            user = await user_service.create_user(sample_user_data)
            
            # Assert
            assert user.email == sample_user_data.email
            assert user.username == sample_user_data.username
            assert user.full_name == sample_user_data.full_name
            assert user.is_active is True
            assert user.is_verified is False
            assert user.id is not None

        async def test_create_user_duplicate_email(
            self, user_service, sample_user_data, db_session
        ):
            """Test creating user with duplicate email raises error."""
            # Arrange - create first user
            await user_service.create_user(sample_user_data)
            
            # Act & Assert - try to create user with same email
            duplicate_data = UserCreate(
                email=sample_user_data.email,  # Same email
                username="different_username",
                password="SecurePass123!"
            )
            
            with pytest.raises(EmailAlreadyExistsError):
                await user_service.create_user(duplicate_data)

        async def test_get_user_by_id_exists(self, user_service, sample_user_data):
            """Test getting user by ID when user exists."""
            # Arrange
            created_user = await user_service.create_user(sample_user_data)
            
            # Act
            retrieved_user = await user_service.get_user_by_id(created_user.id)
            
            # Assert
            assert retrieved_user is not None
            assert retrieved_user.id == created_user.id
            assert retrieved_user.email == created_user.email

        async def test_get_user_by_id_not_exists(self, user_service):
            """Test getting user by ID when user doesn't exist."""
            # Act
            user = await user_service.get_user_by_id(uuid4())
            
            # Assert
            assert user is None

        @patch('app.core.security.hash_password')
        async def test_create_user_password_hashing(
            self, mock_hash_password, user_service, sample_user_data
        ):
            """Test that password is properly hashed during user creation."""
            # Arrange
            mock_hash_password.return_value = "hashed_password"
            
            # Act
            await user_service.create_user(sample_user_data)
            
            # Assert
            mock_hash_password.assert_called_once_with(sample_user_data.password)

Integration Testing
===================

API Endpoint Testing

**Testing FastAPI endpoints**:

.. code-block:: python

    # tests/integration/test_api/test_user_endpoints.py
    import pytest
    from httpx import AsyncClient

    class TestUserEndpoints:
        """Test user API endpoints."""

        async def test_register_user_success(self, client: AsyncClient):
            """Test successful user registration."""
            # Arrange
            user_data = {
                "email": "<EMAIL>",
                "username": "testuser",
                "password": "SecurePass123!",
                "full_name": "Test User"
            }
            
            # Act
            response = await client.post("/api/v1/auth/register", json=user_data)
            
            # Assert
            assert response.status_code == 201
            data = response.json()
            assert data["data"]["email"] == user_data["email"]
            assert data["data"]["username"] == user_data["username"]
            assert data["data"]["full_name"] == user_data["full_name"]
            assert "password" not in data["data"]  # Password should not be returned

        async def test_register_user_duplicate_email(self, client: AsyncClient):
            """Test registration with duplicate email returns error."""
            # Arrange
            user_data = {
                "email": "<EMAIL>",
                "username": "testuser",
                "password": "SecurePass123!"
            }
            
            # Create first user
            await client.post("/api/v1/auth/register", json=user_data)
            
            # Act - try to create user with same email
            duplicate_data = {
                "email": "<EMAIL>",  # Same email
                "username": "different_user",
                "password": "SecurePass123!"
            }
            response = await client.post("/api/v1/auth/register", json=duplicate_data)
            
            # Assert
            assert response.status_code == 409
            error = response.json()
            assert error["error"]["code"] == "RESOURCE_CONFLICT"

        async def test_login_success(self, client: AsyncClient):
            """Test successful user login."""
            # Arrange - register user first
            user_data = {
                "email": "<EMAIL>",
                "username": "testuser",
                "password": "SecurePass123!"
            }
            await client.post("/api/v1/auth/register", json=user_data)
            
            # Act
            login_data = {
                "username": "testuser",
                "password": "SecurePass123!"
            }
            response = await client.post("/api/v1/auth/login", json=login_data)
            
            # Assert
            assert response.status_code == 200
            data = response.json()
            assert "access_token" in data
            assert "refresh_token" in data
            assert data["token_type"] == "bearer"
            assert "user" in data

        async def test_get_current_user_authenticated(self, client: AsyncClient):
            """Test getting current user profile when authenticated."""
            # Arrange - register and login user
            user_data = {
                "email": "<EMAIL>",
                "username": "testuser",
                "password": "SecurePass123!"
            }
            await client.post("/api/v1/auth/register", json=user_data)
            
            login_response = await client.post("/api/v1/auth/login", json={
                "username": "testuser",
                "password": "SecurePass123!"
            })
            token = login_response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            
            # Act
            response = await client.get("/api/v1/users/me", headers=headers)
            
            # Assert
            assert response.status_code == 200
            data = response.json()
            assert data["data"]["email"] == user_data["email"]
            assert data["data"]["username"] == user_data["username"]

        async def test_get_current_user_unauthenticated(self, client: AsyncClient):
            """Test getting current user profile without authentication."""
            # Act
            response = await client.get("/api/v1/users/me")
            
            # Assert
            assert response.status_code == 401

Database Testing

**Testing database operations and migrations**:

.. code-block:: python

    # tests/integration/test_database/test_migrations.py
    import pytest
    from alembic import command
    from alembic.config import Config
    from sqlalchemy import text

    class TestMigrations:
        """Test database migrations."""

        async def test_migration_upgrade_downgrade(self, test_engine):
            """Test migration upgrade and downgrade."""
            # This would test actual migration scripts
            # Implementation depends on your migration setup
            pass

        async def test_database_constraints(self, db_session):
            """Test database constraints are properly enforced."""
            from app.models.user import User
            
            # Test unique constraint on email
            user1 = User(
                email="<EMAIL>",
                username="user1",
                hashed_password="password"
            )
            user2 = User(
                email="<EMAIL>",  # Same email
                username="user2",
                hashed_password="password"
            )
            
            db_session.add(user1)
            await db_session.commit()
            
            db_session.add(user2)
            with pytest.raises(Exception):  # Should raise integrity error
                await db_session.commit()

End-to-End Testing
==================

Workflow Testing

**Testing complete user workflows**:

.. code-block:: python

    # tests/e2e/test_user_workflows.py
    import pytest
    from httpx import AsyncClient

    class TestUserWorkflows:
        """Test complete user workflows."""

        async def test_complete_user_registration_and_supplement_tracking(
            self, client: AsyncClient
        ):
            """Test complete workflow from registration to supplement tracking."""
            # Step 1: Register user
            user_data = {
                "email": "<EMAIL>",
                "username": "healthuser",
                "password": "SecurePass123!",
                "full_name": "Health User"
            }
            register_response = await client.post(
                "/api/v1/auth/register", json=user_data
            )
            assert register_response.status_code == 201
            
            # Step 2: Login
            login_response = await client.post("/api/v1/auth/login", json={
                "username": "healthuser",
                "password": "SecurePass123!"
            })
            assert login_response.status_code == 200
            token = login_response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            
            # Step 3: Create supplement
            supplement_data = {
                "name": "Vitamin D3",
                "category": "Vitamin",
                "form": "Capsule"
            }
            supplement_response = await client.post(
                "/api/v1/supplements", json=supplement_data, headers=headers
            )
            assert supplement_response.status_code == 201
            supplement_id = supplement_response.json()["data"]["id"]
            
            # Step 4: Log supplement intake
            intake_data = {
                "supplement_id": supplement_id,
                "dosage": 5000,
                "dosage_unit": "IU",
                "notes": "Morning dose"
            }
            intake_response = await client.post(
                "/api/v1/intakes", json=intake_data, headers=headers
            )
            assert intake_response.status_code == 201
            
            # Step 5: Get intake history
            history_response = await client.get(
                "/api/v1/intakes/history", headers=headers
            )
            assert history_response.status_code == 200
            history = history_response.json()["data"]
            assert len(history) == 1
            assert history[0]["supplement_id"] == supplement_id

Test Fixtures and Factories
===========================

Data Factories

**Using factory_boy for test data**:

.. code-block:: python

    # tests/factories.py
    import factory
    from factory import fuzzy
    from datetime import datetime, timezone
    from uuid import uuid4

    from app.models.user import User
    from app.models.supplement import Supplement

    class UserFactory(factory.Factory):
        """Factory for creating User instances."""
        
        class Meta:
            model = User

        id = factory.LazyFunction(uuid4)
        email = factory.Sequence(lambda n: f"user{n}@example.com")
        username = factory.Sequence(lambda n: f"user{n}")
        hashed_password = "hashed_password"
        full_name = factory.Faker("name")
        bio = factory.Faker("text", max_nb_chars=200)
        is_active = True
        is_verified = False
        created_at = factory.LazyFunction(lambda: datetime.now(timezone.utc))

    class SupplementFactory(factory.Factory):
        """Factory for creating Supplement instances."""
        
        class Meta:
            model = Supplement

        id = factory.LazyFunction(uuid4)
        name = factory.Faker("word")
        category = fuzzy.FuzzyChoice(["Vitamin", "Mineral", "Herb", "Other"])
        form = fuzzy.FuzzyChoice(["Capsule", "Tablet", "Liquid", "Powder"])
        brand = factory.Faker("company")
        description = factory.Faker("text", max_nb_chars=500)

**Using fixtures with factories**:

.. code-block:: python

    # tests/conftest.py
    import pytest
    from tests.factories import UserFactory, SupplementFactory

    @pytest.fixture
    async def sample_user(db_session):
        """Create a sample user in the database."""
        user = UserFactory()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def sample_supplement(db_session):
        """Create a sample supplement in the database."""
        supplement = SupplementFactory()
        db_session.add(supplement)
        await db_session.commit()
        await db_session.refresh(supplement)
        return supplement

    @pytest.fixture
    async def authenticated_user(client, sample_user):
        """Create authenticated user and return auth headers."""
        # Login the user
        login_response = await client.post("/api/v1/auth/login", json={
            "username": sample_user.username,
            "password": "original_password"  # You'd need to handle this properly
        })
        token = login_response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}

Mocking and Patching
====================

External Service Mocking

**Mocking external API calls**:

.. code-block:: python

    # tests/unit/test_services/test_external_service.py
    import pytest
    from unittest.mock import AsyncMock, patch
    import httpx

    from app.services.external_service import ExternalAPIService

    class TestExternalAPIService:
        """Test external API service."""

        @patch('httpx.AsyncClient.get')
        async def test_fetch_supplement_data_success(self, mock_get):
            """Test successful external API call."""
            # Arrange
            mock_response = AsyncMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "name": "Vitamin D3",
                "description": "Essential vitamin"
            }
            mock_get.return_value = mock_response
            
            service = ExternalAPIService()
            
            # Act
            result = await service.fetch_supplement_data("vitamin-d3")
            
            # Assert
            assert result["name"] == "Vitamin D3"
            mock_get.assert_called_once()

        @patch('httpx.AsyncClient.get')
        async def test_fetch_supplement_data_api_error(self, mock_get):
            """Test handling of external API errors."""
            # Arrange
            mock_get.side_effect = httpx.RequestError("Network error")
            service = ExternalAPIService()
            
            # Act & Assert
            with pytest.raises(ExternalAPIError):
                await service.fetch_supplement_data("vitamin-d3")

Database Mocking

**Mocking database operations**:

.. code-block:: python

    @patch('app.services.user_service.UserService.get_user_by_email')
    async def test_login_user_not_found(self, mock_get_user):
        """Test login when user is not found."""
        # Arrange
        mock_get_user.return_value = None
        auth_service = AuthService(db_session)
        
        # Act & Assert
        with pytest.raises(InvalidCredentialsError):
            await auth_service.authenticate_user("<EMAIL>", "password")

Performance Testing
===================

Load Testing

**Using pytest-benchmark for performance tests**:

.. code-block:: python

    # tests/performance/test_api_performance.py
    import pytest

    class TestAPIPerformance:
        """Test API performance."""

        async def test_user_creation_performance(self, benchmark, client):
            """Test user creation performance."""
            user_data = {
                "email": "<EMAIL>",
                "username": "testuser",
                "password": "SecurePass123!"
            }
            
            async def create_user():
                response = await client.post("/api/v1/auth/register", json=user_data)
                return response
            
            result = await benchmark(create_user)
            assert result.status_code == 201

        async def test_supplement_search_performance(
            self, benchmark, client, authenticated_headers
        ):
            """Test supplement search performance."""
            async def search_supplements():
                response = await client.get(
                    "/api/v1/supplements?search=vitamin",
                    headers=authenticated_headers
                )
                return response
            
            result = await benchmark(search_supplements)
            assert result.status_code == 200

Test Utilities
==============

Custom Assertions

**Creating custom assertion helpers**:

.. code-block:: python

    # tests/utils.py
    from typing import Dict, Any
    from datetime import datetime

    def assert_valid_user_response(user_data: Dict[str, Any]) -> None:
        """Assert that user response data is valid."""
        required_fields = ["id", "email", "username", "is_active", "created_at"]
        
        for field in required_fields:
            assert field in user_data, f"Missing required field: {field}"
        
        assert isinstance(user_data["is_active"], bool)
        assert "@" in user_data["email"]
        assert len(user_data["username"]) >= 3

    def assert_valid_timestamp(timestamp_str: str) -> None:
        """Assert that timestamp string is valid ISO format."""
        try:
            datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        except ValueError:
            pytest.fail(f"Invalid timestamp format: {timestamp_str}")

Test Data Management

**Managing test data files**:

.. code-block:: python

    # tests/utils/data_loader.py
    import json
    from pathlib import Path
    from typing import Dict, Any, List

    def load_test_data(filename: str) -> Dict[str, Any]:
        """Load test data from JSON file."""
        data_path = Path(__file__).parent.parent / "fixtures" / filename
        with open(data_path, 'r') as f:
            return json.load(f)

    def load_sample_users() -> List[Dict[str, Any]]:
        """Load sample user data."""
        return load_test_data("users.json")["users"]

    def load_sample_supplements() -> List[Dict[str, Any]]:
        """Load sample supplement data."""
        return load_test_data("supplements.json")["supplements"]

Running Tests
=============

Test Commands

**Basic test execution**:

.. code-block:: bash

    # Run all tests
    pytest

    # Run with coverage
    pytest --cov=app --cov-report=html

    # Run specific test file
    pytest tests/unit/test_services/test_user_service.py

    # Run specific test
    pytest tests/unit/test_services/test_user_service.py::TestUserService::test_create_user_success

    # Run tests by marker
    pytest -m unit
    pytest -m integration
    pytest -m "not slow"

**Parallel test execution**:

.. code-block:: bash

    # Install pytest-xdist
    pip install pytest-xdist

    # Run tests in parallel
    pytest -n auto

**Test debugging**:

.. code-block:: bash

    # Run with verbose output
    pytest -v

    # Stop on first failure
    pytest -x

    # Drop into debugger on failure
    pytest --pdb

    # Run only failed tests from last run
    pytest --lf

Continuous Integration
======================

GitHub Actions

**Test workflow configuration**:

.. code-block:: yaml

    # .github/workflows/test.yml
    name: Tests

    on:
      push:
        branches: [ main, develop ]
      pull_request:
        branches: [ main ]

    jobs:
      test:
        runs-on: ubuntu-latest
        strategy:
          matrix:
            python-version: [3.11, 3.12]

        services:
          postgres:
            image: postgres:15
            env:
              POSTGRES_PASSWORD: postgres
              POSTGRES_DB: test_db
            options: >-
              --health-cmd pg_isready
              --health-interval 10s
              --health-timeout 5s
              --health-retries 5

        steps:
        - uses: actions/checkout@v3
        
        - name: Set up Python ${{ matrix.python-version }}
          uses: actions/setup-python@v4
          with:
            python-version: ${{ matrix.python-version }}
        
        - name: Install dependencies
          run: |
            python -m pip install --upgrade pip
            pip install -r requirements.txt
            pip install -r requirements-dev.txt
        
        - name: Run tests
          run: |
            pytest --cov=app --cov-report=xml
          env:
            DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        
        - name: Upload coverage to Codecov
          uses: codecov/codecov-action@v3
          with:
            file: ./coverage.xml

Best Practices Summary
======================

**Test Writing**:
- Write tests before or alongside code (TDD/BDD)
- Use descriptive test names that explain what is being tested
- Follow the Arrange-Act-Assert pattern
- Keep tests simple and focused on one thing
- Use fixtures for common setup
- Mock external dependencies

**Test Organization**:
- Group related tests in classes
- Use consistent naming conventions
- Keep test files close to the code they test
- Separate unit, integration, and e2e tests

**Test Maintenance**:
- Keep tests fast and reliable
- Update tests when code changes
- Remove obsolete tests
- Monitor test coverage but don't chase 100%
- Review test failures promptly

**Performance**:
- Use database transactions for test isolation
- Mock expensive operations
- Run tests in parallel when possible
- Use appropriate test data sizes

Related Documentation
=====================

- :doc:`coding-standards` - Code quality standards
- :doc:`setup` - Development environment setup
- :doc:`contributing` - Contributing guidelines
- :doc:`../architecture/overview` - System architecture
