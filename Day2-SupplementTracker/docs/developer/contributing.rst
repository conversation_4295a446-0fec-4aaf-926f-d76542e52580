==================
Contributing Guide
==================

Thank you for your interest in contributing to the Supplement Tracker project! This guide will help you get started with contributing code, documentation, and other improvements.

.. contents:: Table of Contents
   :local:
   :depth: 2

Getting Started
===============

Before You Begin

1. **Read the documentation**: Familiarize yourself with the project
2. **Set up development environment**: Follow :doc:`setup`
3. **Join the community**: Participate in discussions
4. **Check existing issues**: Look for open issues or feature requests

Ways to Contribute

- **Code contributions**: Bug fixes, new features, performance improvements
- **Documentation**: Improve guides, API docs, tutorials
- **Testing**: Write tests, report bugs, test new features
- **Design**: UI/UX improvements, graphics, user experience
- **Community**: Help other users, moderate discussions
- **Research**: Data analysis, supplement research, scientific validation

Code Contributions
==================

Finding Issues to Work On

**Good First Issues**:
- Look for issues labeled `good first issue`
- Documentation improvements
- Small bug fixes
- Test additions

**Priority Areas**:
- API endpoint improvements
- Database optimization
- Security enhancements
- User experience improvements

**Feature Requests**:
- Check the roadmap for planned features
- Propose new features in discussions
- Start with smaller features before major additions

Development Workflow

**1. Fork and Clone**:

.. code-block:: bash

    # Fork the repository on GitHub
    # Then clone your fork
    git clone https://github.com/YOUR_USERNAME/day2-supplement-tracker.git
    cd day2-supplement-tracker
    
    # Add upstream remote
    git remote add upstream https://github.com/forkrul/day2-supplement-tracker.git

**2. Create Feature Branch**:

.. code-block:: bash

    # Update your main branch
    git checkout main
    git pull upstream main
    
    # Create feature branch
    git checkout -b feature/your-feature-name
    
    # Or for bug fixes
    git checkout -b fix/issue-description

**3. Make Changes**:

.. code-block:: bash

    # Set up development environment
    nix-shell  # or manual setup
    
    # Make your changes
    # Follow coding standards
    # Add tests for new functionality

**4. Test Your Changes**:

.. code-block:: bash

    # Run tests
    make test
    
    # Run linting
    make lint
    
    # Check formatting
    make format
    
    # Test manually
    make dev

**5. Commit Changes**:

.. code-block:: bash

    # Stage changes
    git add .
    
    # Commit with conventional commit format
    git commit -m "feat(api): add supplement search endpoint"

**6. Push and Create PR**:

.. code-block:: bash

    # Push to your fork
    git push origin feature/your-feature-name
    
    # Create pull request on GitHub
    # Use the PR template
    # Link related issues

Code Standards
==============
=============

Coding Style

**Python Style**:
- Follow PEP 8
- Use Black for formatting
- Use isort for import sorting
- Maximum line length: 88 characters
- Use type hints for all functions

**Example**:

.. code-block:: python

    from typing import List, Optional
    from uuid import UUID

    from fastapi import APIRouter, Depends, HTTPException
    from sqlalchemy.ext.asyncio import AsyncSession

    from app.core.database import get_db
    from app.models.supplement import Supplement
    from app.schemas.supplement import SupplementCreate, SupplementResponse


    async def create_supplement(
        supplement_data: SupplementCreate,
        db: AsyncSession = Depends(get_db)
    ) -> SupplementResponse:
""""""""""""""""""""""""""""
        Create a new supplement in the database.
        
        Args:
            supplement_data: The supplement data to create
            db: Database session
            
        Returns:
            The created supplement
            
        Raises:
            HTTPException: If supplement creation fails
"""""""""""""""""""""""""""""""""""""""""""""""""""""""
        try:
            supplement = Supplement(**supplement_data.dict())
            db.add(supplement)
            await db.commit()
            await db.refresh(supplement)
            return SupplementResponse.from_orm(supplement)
        except Exception as e:
            await db.rollback()
            raise HTTPException(status_code=400, detail=str(e))

**Documentation**:
- Use docstrings for all functions and classes
- Follow Google docstring format
- Include type information
- Provide examples for complex functions

**Error Handling**:
- Use appropriate HTTP status codes
- Provide meaningful error messages
- Log errors with sufficient context
- Handle edge cases gracefully

Testing Standards

**Test Coverage**:
- Aim for >90% test coverage
- Test all public functions
- Include edge cases and error conditions
- Test both success and failure paths

**Test Structure**:

.. code-block:: python

    import pytest
    from httpx import AsyncClient

    class TestSupplementAPI:
        """Test supplement API endpoints."""
        
        async def test_create_supplement_success(
            self, 
            client: AsyncClient, 
            auth_headers: dict
        ):
            """Test successful supplement creation."""
            supplement_data = {
                "name": "Test Supplement",
                "category": "Vitamin",
                "form": "Capsule"
            }
            
            response = await client.post(
                "/api/v1/supplements",
                json=supplement_data,
                headers=auth_headers
            )
            
            assert response.status_code == 201
            data = response.json()
            assert data["data"]["name"] == "Test Supplement"
            assert data["data"]["category"] == "Vitamin"

        async def test_create_supplement_validation_error(
            self, 
            client: AsyncClient, 
            auth_headers: dict
        ):
            """Test supplement creation with invalid data."""
            invalid_data = {
                "name": "",  # Empty name should fail
                "category": "InvalidCategory"
            }
            
            response = await client.post(
                "/api/v1/supplements",
                json=invalid_data,
                headers=auth_headers
            )
            
            assert response.status_code == 400
            error = response.json()["error"]
            assert error["code"] == "VALIDATION_ERROR"

**Test Fixtures**:

.. code-block:: python

    @pytest.fixture
    async def sample_supplement(db_session):
        """Create a sample supplement for testing."""
        supplement = Supplement(
            name="Test Vitamin D3",
            category="Vitamin",
            form="Capsule",
            brand="Test Brand"
        )
        db_session.add(supplement)
        await db_session.commit()
        await db_session.refresh(supplement)
        return supplement

Database Changes
================
===============

Schema Migrations

**Creating Migrations**:

.. code-block:: bash

    # Create migration for model changes
    alembic revision --autogenerate -m "Add supplement verification table"
    
    # Review generated migration
    # Edit if necessary
    
    # Test migration
    alembic upgrade head
    alembic downgrade -1
    alembic upgrade head

**Migration Best Practices**:

1. **Review auto-generated migrations**: Always check the generated SQL
2. **Test both directions**: Test upgrade and downgrade
3. **Handle data carefully**: Preserve existing data during schema changes
4. **Use transactions**: Ensure migrations are atomic
5. **Document breaking changes**: Note any breaking changes in commit message

**Example Migration**:

.. code-block:: python

    """Add supplement verification table

    Revision ID: abc123def456
    Revises: def456ghi789
    Create Date: 2025-06-18 15:30:00.000000

    """
    from alembic import op
    import sqlalchemy as sa
    from sqlalchemy.dialects import postgresql

    # revision identifiers
    revision = 'abc123def456'
    down_revision = 'def456ghi789'
    branch_labels = None
    depends_on = None

    def upgrade():
        # Create verification table
        op.create_table(
            'supplement_verifications',
            sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
            sa.Column('supplement_id', postgresql.UUID(as_uuid=True), nullable=False),
            sa.Column('verified_by_user_id', postgresql.UUID(as_uuid=True), nullable=False),
            sa.Column('verification_notes', sa.Text(), nullable=True),
            sa.Column('confidence_level', sa.String(20), nullable=False),
            sa.Column('created_at', sa.TIMESTAMP(timezone=True), nullable=False),
            sa.ForeignKeyConstraint(['supplement_id'], ['supplements.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
        
        # Add indexes
        op.create_index('ix_supplement_verifications_supplement_id', 
                       'supplement_verifications', ['supplement_id'])

    def downgrade():
        op.drop_index('ix_supplement_verifications_supplement_id')
        op.drop_table('supplement_verifications')

API Design Guidelines
=====================
====================

RESTful Principles

**URL Structure**:
- Use nouns for resources: `/supplements`, `/users`
- Use HTTP methods appropriately: GET, POST, PUT, DELETE
- Use nested resources: `/users/{id}/intakes`
- Use query parameters for filtering: `/supplements?category=vitamin`

**Response Format**:

.. code-block:: python

    # Successful response
    {
        "data": {
            "id": "123e4567-e89b-12d3-a456-************",
            "name": "Vitamin D3",
            "category": "Vitamin"
        },
        "meta": {
            "timestamp": "2025-06-18T15:30:00Z",
            "request_id": "req_123456789"
        }
    }

    # List response with pagination
    {
        "data": [...],
        "pagination": {
            "total": 150,
            "page": 1,
            "per_page": 20,
            "pages": 8
        },
        "meta": {
            "timestamp": "2025-06-18T15:30:00Z",
            "request_id": "req_123456789"
        }
    }

**Error Handling**:

.. code-block:: python

    from fastapi import HTTPException
    from app.core.errors import ErrorCode

    # Use appropriate status codes
    if not supplement:
        raise HTTPException(
            status_code=404,
            detail={
                "code": ErrorCode.RESOURCE_NOT_FOUND,
                "message": "Supplement not found",
                "details": {
                    "resource_type": "supplement",
                    "resource_id": supplement_id
                }
            }
        )

Schema Design

**Request/Response Models**:

.. code-block:: python

    from pydantic import BaseModel, Field, validator
    from typing import Optional
    from uuid import UUID

    class SupplementCreate(BaseModel):
        """Schema for creating a supplement."""
        
        name: str = Field(..., min_length=1, max_length=255)
        category: str = Field(..., regex="^(Vitamin|Mineral|Herb|Other)$")
        brand: Optional[str] = Field(None, max_length=255)
        description: Optional[str] = Field(None, max_length=1000)
        
        @validator('name')
        def validate_name(cls, v):
            return v.strip()

    class SupplementResponse(BaseModel):
        """Schema for supplement response."""
        
        id: UUID
        name: str
        category: str
        brand: Optional[str]
        description: Optional[str]
        is_verified: bool
        created_at: datetime
        updated_at: datetime
        
        class Config:
            orm_mode = True

Documentation Contributions
===========================

Types of Documentation

**User Documentation**:
- Installation guides
- Configuration tutorials
- API usage examples
- Troubleshooting guides

**Developer Documentation**:
- Setup instructions
- Architecture overviews
- Coding standards
- Contributing guides

**API Documentation**:
- Endpoint descriptions
- Schema definitions
- Error codes
- Example requests/responses

Writing Guidelines

**Structure**:
- Use clear headings and subheadings
- Include table of contents for long documents
- Provide code examples
- Include troubleshooting sections

**Style**:
- Write in clear, concise language
- Use active voice
- Include step-by-step instructions
- Test all code examples

**Example Documentation**:

.. code-block:: rst

    ===============================================
    Supplement Tracking Tutorial
================================

    This tutorial shows you how to track supplements using the API.

    .. contents:: Table of Contents
       :local:
       :depth: 2

    Prerequisites
    =============

    Before starting, ensure you have:

    - A registered user account
    - Valid authentication token
    - Basic understanding of REST APIs

    Step 1: Create a Supplement
    ===========================

    First, create a supplement in the database:

    .. code-block:: bash

        curl -X POST "https://api.supplementtracker.com/api/v1/supplements" \
             -H "Authorization: Bearer YOUR_TOKEN" \
             -H "Content-Type: application/json" \
             -d '{
               "name": "Vitamin D3",
               "category": "Vitamin",
               "form": "Capsule"
             }'

    **Response**:

    .. code-block:: json

        {
            "data": {
                "id": "123e4567-e89b-12d3-a456-************",
                "name": "Vitamin D3",
                "category": "Vitamin",
                "form": "Capsule"
            }
        }

Community Guidelines
====================
===================

Code of Conduct

**Be Respectful**:
- Treat all community members with respect
- Welcome newcomers and help them learn
- Provide constructive feedback
- Avoid personal attacks or harassment

**Be Collaborative**:
- Work together towards common goals
- Share knowledge and resources
- Help others solve problems
- Participate in discussions constructively

**Be Professional**:
- Use appropriate language
- Stay on topic in discussions
- Respect different opinions and approaches
- Follow project guidelines and standards

Communication Channels

**GitHub Issues**:
- Bug reports
- Feature requests
- Technical discussions

**GitHub Discussions**:
- General questions
- Ideas and brainstorming
- Community announcements

**Pull Requests**:
- Code reviews
- Implementation discussions
- Collaboration on features

**Email**:
- Security issues: <EMAIL>
- General inquiries: <EMAIL>

Review Process
==============
=============

Pull Request Reviews

**What Reviewers Look For**:
- Code quality and style
- Test coverage
- Documentation updates
- Security considerations
- Performance implications

**Review Checklist**:
- [ ] Code follows style guidelines
- [ ] Tests are included and pass
- [ ] Documentation is updated
- [ ] No security vulnerabilities
- [ ] Performance is acceptable
- [ ] Breaking changes are documented

**Responding to Reviews**:
- Address all feedback
- Ask questions if unclear
- Make requested changes
- Update tests and documentation
- Be patient and professional

Merge Process

**Requirements for Merge**:
- All tests pass
- Code review approval
- Documentation updated
- No merge conflicts
- Follows commit message format

**Merge Types**:
- **Squash and merge**: For feature branches
- **Merge commit**: For release branches
- **Rebase and merge**: For small fixes

Recognition
===========
==========

Contributor Recognition

**Contributors are recognized through**:
- GitHub contributor graphs
- Release notes mentions
- Hall of fame in documentation
- Special contributor badges
- Conference speaking opportunities

**Types of Contributions Recognized**:
- Code contributions
- Documentation improvements
- Bug reports and testing
- Community support
- Design and UX improvements

Maintainer Path

**Becoming a Maintainer**:
1. Consistent high-quality contributions
2. Active community participation
3. Demonstrated expertise in project areas
4. Commitment to project values
5. Nomination by existing maintainers

**Maintainer Responsibilities**:
- Review pull requests
- Triage issues
- Guide project direction
- Mentor new contributors
- Maintain code quality

Getting Help
============
===========

Resources

- **Documentation**: Read existing guides and tutorials
- **Code Examples**: Check tests and example implementations
- **Community**: Ask questions in discussions
- **Mentorship**: Request guidance from maintainers

Common Questions

**Q: How do I get started contributing?**
A: Start with good first issues, set up your development environment, and read the documentation.

**Q: What if I'm not sure about my implementation?**
A: Create a draft pull request early and ask for feedback. We're happy to help guide you.

**Q: How long does review take?**
A: Reviews typically happen within a few days. Larger changes may take longer.

**Q: Can I work on multiple issues at once?**
A: It's better to focus on one issue at a time, especially when starting out.

Next Steps
==========

Ready to contribute? Here's what to do next:

1. **Set up your environment**: :doc:`setup`
2. **Find an issue**: Look for `good first issue` labels
3. **Join the community**: Participate in discussions
4. **Make your first contribution**: Start small and build up
5. **Help others**: Share your knowledge with the community

