===========
API Schemas
===========

This document describes the data schemas used throughout the Supplement Tracker API.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
========

All API endpoints use JSON for request and response bodies. This document describes the structure and validation rules for all data schemas.

**Data Types**:

- ``string``: Text data
- ``integer``: Whole numbers
- ``number``: Decimal numbers
- ``boolean``: true/false values
- ``array``: Lists of items
- ``object``: Nested data structures
- ``datetime``: ISO 8601 formatted timestamps
- ``uuid``: UUID v4 identifiers

User Schemas
============
===========

User

Represents a user account.

.. code-block:: json

    {
        "id": "uuid",
        "email": "string",
        "username": "string",
        "full_name": "string | null",
        "bio": "string | null",
        "is_active": "boolean",
        "is_verified": "boolean",
        "created_at": "datetime",
        "updated_at": "datetime",
        "last_login_at": "datetime | null"
    }

**Validation Rules**:

- ``email``: Valid email format, unique
- ``username``: 3-50 characters, alphanumeric + underscore/hyphen, unique
- ``full_name``: Max 255 characters
- ``bio``: Max 500 characters

UserCreate

Schema for creating a new user.

.. code-block:: json

    {
        "email": "string",
        "username": "string", 
        "password": "string",
        "full_name": "string | null",
        "bio": "string | null"
    }

**Validation Rules**:

- ``password``: Min 8 characters, must contain uppercase, lowercase, number, special character

UserUpdate

Schema for updating user information.

.. code-block:: json

    {
        "full_name": "string | null",
        "bio": "string | null"
    }

Supplement Schemas
==================
=================

Supplement

Represents a supplement product.

.. code-block:: json

    {
        "id": "uuid",
        "name": "string",
        "brand": "string | null",
        "description": "string | null",
        "category": "string",
        "form": "string",
        "serving_size": "number | null",
        "serving_unit": "string | null",
        "ingredients": "string | null",
        "barcode": "string | null",
        "is_verified": "boolean",
        "verification_count": "integer",
        "created_at": "datetime",
        "updated_at": "datetime",
        "created_by_user_id": "uuid | null"
    }

**Validation Rules**:

- ``name``: 1-255 characters, required
- ``category``: Must be valid category (Vitamin, Mineral, etc.)
- ``form``: Must be valid form (Capsule, Tablet, Powder, etc.)
- ``serving_size``: Positive number
- ``barcode``: Unique if provided

SupplementCreate

Schema for creating a new supplement.

.. code-block:: json

    {
        "name": "string",
        "brand": "string | null",
        "description": "string | null",
        "category": "string",
        "form": "string",
        "serving_size": "number | null",
        "serving_unit": "string | null",
        "ingredients": "string | null",
        "barcode": "string | null"
    }

SupplementUpdate

Schema for updating supplement information.

.. code-block:: json

    {
        "name": "string | null",
        "brand": "string | null",
        "description": "string | null",
        "category": "string | null",
        "form": "string | null",
        "serving_size": "number | null",
        "serving_unit": "string | null",
        "ingredients": "string | null",
        "barcode": "string | null"
    }

Intake Schemas
==============
=============

SupplementIntake

Represents a supplement intake record.

.. code-block:: json

    {
        "id": "uuid",
        "user_id": "uuid",
        "supplement_id": "uuid",
        "dosage": "number",
        "dosage_unit": "string",
        "taken_at": "datetime",
        "notes": "string | null",
        "mood_before": "integer | null",
        "mood_after": "integer | null",
        "energy_before": "integer | null",
        "energy_after": "integer | null",
        "created_at": "datetime",
        "supplement": "Supplement | null"
    }

**Validation Rules**:

- ``dosage``: Positive number, required
- ``dosage_unit``: Valid unit (mg, g, IU, etc.)
- ``mood_before/after``: Integer 1-10
- ``energy_before/after``: Integer 1-10

IntakeCreate

Schema for creating a new intake record.

.. code-block:: json

    {
        "supplement_id": "uuid",
        "dosage": "number",
        "dosage_unit": "string",
        "taken_at": "datetime | null",
        "notes": "string | null",
        "mood_before": "integer | null",
        "mood_after": "integer | null",
        "energy_before": "integer | null",
        "energy_after": "integer | null"
    }

IntakeUpdate

Schema for updating an intake record.

.. code-block:: json

    {
        "dosage": "number | null",
        "dosage_unit": "string | null",
        "taken_at": "datetime | null",
        "notes": "string | null",
        "mood_before": "integer | null",
        "mood_after": "integer | null",
        "energy_before": "integer | null",
        "energy_after": "integer | null"
    }

Stack Schemas
=============
============

SupplementStack

Represents a supplement stack (combination).

.. code-block:: json

    {
        "id": "uuid",
        "user_id": "uuid",
        "name": "string",
        "description": "string | null",
        "is_active": "boolean",
        "created_at": "datetime",
        "updated_at": "datetime",
        "items": "array[SupplementStackItem]"
    }

SupplementStackItem

Represents an item within a supplement stack.

.. code-block:: json

    {
        "id": "uuid",
        "stack_id": "uuid",
        "supplement_id": "uuid",
        "dosage": "number",
        "dosage_unit": "string",
        "timing": "string | null",
        "notes": "string | null",
        "created_at": "datetime",
        "supplement": "Supplement | null"
    }

StackCreate

Schema for creating a new supplement stack.

.. code-block:: json

    {
        "name": "string",
        "description": "string | null",
        "is_active": "boolean"
    }

StackItemCreate

Schema for adding an item to a supplement stack.

.. code-block:: json

    {
        "supplement_id": "uuid",
        "dosage": "number",
        "dosage_unit": "string",
        "timing": "string | null",
        "notes": "string | null"
    }

Authentication Schemas
======================
=====================

LoginRequest

Schema for user login.

.. code-block:: json

    {
        "username": "string | null",
        "email": "string | null", 
        "password": "string"
    }

**Note**: Either username or email must be provided.

LoginResponse

Schema for login response.

.. code-block:: json

    {
        "access_token": "string",
        "refresh_token": "string",
        "token_type": "string",
        "expires_in": "integer",
        "user": "User"
    }

TokenRefresh

Schema for token refresh request.

.. code-block:: json

    {
        "refresh_token": "string"
    }

PasswordReset

Schema for password reset.

.. code-block:: json

    {
        "token": "string",
        "new_password": "string"
    }

PasswordChange

Schema for password change.

.. code-block:: json

    {
        "current_password": "string",
        "new_password": "string"
    }

Response Schemas
================
===============

StandardResponse

Standard API response wrapper.

.. code-block:: json

    {
        "data": "any",
        "meta": {
            "timestamp": "datetime",
            "request_id": "string"
        }
    }

PaginatedResponse

Response with pagination information.

.. code-block:: json

    {
        "data": "array",
        "pagination": {
            "total": "integer",
            "page": "integer", 
            "per_page": "integer",
            "pages": "integer"
        },
        "meta": {
            "timestamp": "datetime",
            "request_id": "string"
        }
    }

ErrorResponse

Error response format.

.. code-block:: json

    {
        "error": {
            "code": "string",
            "message": "string",
            "details": "object | null",
            "timestamp": "datetime",
            "request_id": "string"
        }
    }

Statistics Schemas
==================
=================

UserStatistics

User activity and progress statistics.

.. code-block:: json

    {
        "period": "string",
        "start_date": "datetime",
        "end_date": "datetime",
        "total_intakes": "integer",
        "unique_supplements": "integer",
        "active_days": "integer",
        "average_intakes_per_day": "number",
        "most_taken_supplement": {
            "id": "uuid",
            "name": "string",
            "count": "integer"
        },
        "mood_trends": {
            "average_before": "number",
            "average_after": "number",
            "improvement": "number"
        },
        "energy_trends": {
            "average_before": "number",
            "average_after": "number", 
            "improvement": "number"
        }
    }

Validation Rules
================
===============

Common Patterns

**Email Format**:
- Must be valid email address
- Case insensitive
- Max 255 characters

**Username Format**:
- 3-50 characters
- Alphanumeric characters, underscore, hyphen only
- Must start with letter or number
- Case insensitive for uniqueness

**Password Requirements**:
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter  
- At least one number
- At least one special character (!@#$%^&*(),.?":{}|<>)

**UUID Format**:
- Valid UUID v4 format
- Example: "123e4567-e89b-12d3-a456-************"

**Datetime Format**:
- ISO 8601 format with timezone
- Example: "2025-06-18T15:30:00Z"

Field Constraints

**Text Fields**:
- ``name``: 1-255 characters
- ``description``: 0-1000 characters
- ``bio``: 0-500 characters
- ``notes``: 0-500 characters

**Numeric Fields**:
- ``dosage``: Positive number, max 999999.999
- ``serving_size``: Positive number, max 999999.999
- ``mood/energy ratings``: Integer 1-10

**Enum Values**:

**Categories**:
- Vitamin
- Mineral
- Amino Acid
- Fatty Acid
- Herb
- Probiotic
- Enzyme
- Other

**Forms**:
- Capsule
- Tablet
- Softgel
- Powder
- Liquid
- Gummy
- Spray
- Patch
- Other

**Dosage Units**:
- mg (milligrams)
- g (grams)
- mcg (micrograms)
- IU (International Units)
- ml (milliliters)
- drops
- capsules
- tablets
- scoops

Example Usage
=============
============

Creating a Supplement Intake

.. code-block:: python

    # Valid intake creation
    intake_data = {
        "supplement_id": "123e4567-e89b-12d3-a456-************",
        "dosage": 1000,
        "dosage_unit": "mg",
        "taken_at": "2025-06-18T08:00:00Z",
        "notes": "Taken with breakfast",
        "mood_before": 7,
        "energy_before": 6
    }

    # This will be validated against IntakeCreate schema
    response = await client.post("/intakes", json=intake_data)

Validation Errors

When validation fails, you'll receive a detailed error response:

.. code-block:: json

    {
        "error": {
            "code": "VALIDATION_ERROR",
            "message": "Invalid input data",
            "details": {
                "field": "dosage",
                "issue": "Dosage must be a positive number",
                "provided_value": -100
            },
            "timestamp": "2025-06-18T15:30:00Z",
            "request_id": "req_123456789"
        }
    }

Related Documentation
=====================
====================

- :doc:`authentication`
- :doc:`users`
- :doc:`supplements`
