=========
Users API
=========

This document describes the user management endpoints for the Supplement Tracker API.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
========

The Users API provides endpoints for managing user accounts, profiles, and user-related data.

**Base URL**: ``/api/v1/users``

**Authentication**: Required for all endpoints

Endpoints
=========

Get Current User

Get the authenticated user's profile information.

**Endpoint**: ``GET /api/v1/users/me``

**Headers**: ``Authorization: Bearer {access_token}``

**Response** (200 OK):

.. code-block:: json

    {
        "data": {
            "id": "123e4567-e89b-12d3-a456-************",
            "email": "<EMAIL>",
            "username": "testuser",
            "full_name": "Test User",
            "bio": "Health enthusiast and supplement researcher",
            "is_active": true,
            "is_verified": false,
            "created_at": "2025-06-18T15:30:00Z",
            "updated_at": "2025-06-18T15:30:00Z",
            "last_login_at": "2025-06-18T16:00:00Z"
        }
    }

Update Current User

Update the authenticated user's profile information.

**Endpoint**: ``PUT /api/v1/users/me``

**Headers**: ``Authorization: Bearer {access_token}``

**Request Body**:

.. code-block:: json

    {
        "full_name": "Updated Full Name",
        "bio": "Updated bio with more information about my health journey"
    }

**Response** (200 OK):

.. code-block:: json

    {
        "data": {
            "id": "123e4567-e89b-12d3-a456-************",
            "email": "<EMAIL>",
            "username": "testuser",
            "full_name": "Updated Full Name",
            "bio": "Updated bio with more information about my health journey",
            "is_active": true,
            "is_verified": false,
            "created_at": "2025-06-18T15:30:00Z",
            "updated_at": "2025-06-18T17:00:00Z",
            "last_login_at": "2025-06-18T16:00:00Z"
        }
    }

Get User by ID

Get public information about a specific user (admin only).

**Endpoint**: ``GET /api/v1/users/{user_id}``

**Headers**: ``Authorization: Bearer {access_token}``

**Permissions**: Admin required

**Response** (200 OK):

.. code-block:: json

    {
        "data": {
            "id": "123e4567-e89b-12d3-a456-************",
            "username": "testuser",
            "full_name": "Test User",
            "bio": "Health enthusiast and supplement researcher",
            "is_active": true,
            "is_verified": false,
            "created_at": "2025-06-18T15:30:00Z"
        }
    }

Delete Current User

Delete the authenticated user's account and all associated data.

**Endpoint**: ``DELETE /api/v1/users/me``

**Headers**: ``Authorization: Bearer {access_token}``

**Request Body** (Optional):

.. code-block:: json

    {
        "confirm_deletion": true,
        "reason": "No longer using the service"
    }

**Response** (200 OK):

.. code-block:: json

    {
        "message": "User account deleted successfully",
        "data_retention": {
            "anonymized_research_data": "retained for scientific purposes",
            "personal_data": "permanently deleted",
            "deletion_completed_at": "2025-06-18T17:30:00Z"
        }
    }

User Statistics
===============
==============

Get User Statistics

Get statistics about the authenticated user's supplement tracking.

**Endpoint**: ``GET /api/v1/users/me/stats``

**Headers**: ``Authorization: Bearer {access_token}``

**Query Parameters**:

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Parameter
     - Type
     - Description
   * - period
     - string
     - Time period: "week", "month", "year", "all"
   * - start_date
     - string
     - Start date (ISO format)
   * - end_date
     - string
     - End date (ISO format)

**Response** (200 OK):

.. code-block:: json

    {
        "data": {
            "period": "month",
            "start_date": "2025-05-18T00:00:00Z",
            "end_date": "2025-06-18T23:59:59Z",
            "total_intakes": 156,
            "unique_supplements": 8,
            "active_days": 28,
            "average_intakes_per_day": 5.6,
            "most_taken_supplement": {
                "id": "123e4567-e89b-12d3-a456-************",
                "name": "Vitamin D3",
                "count": 31
            },
            "mood_trends": {
                "average_before": 6.8,
                "average_after": 7.2,
                "improvement": 0.4
            },
            "energy_trends": {
                "average_before": 6.5,
                "average_after": 7.1,
                "improvement": 0.6
            }
        }
    }

User Preferences
================
===============

Get User Preferences

Get the authenticated user's preferences and settings.

**Endpoint**: ``GET /api/v1/users/me/preferences``

**Headers**: ``Authorization: Bearer {access_token}``

**Response** (200 OK):

.. code-block:: json

    {
        "data": {
            "timezone": "America/New_York",
            "date_format": "YYYY-MM-DD",
            "time_format": "24h",
            "units": {
                "weight": "metric",
                "temperature": "celsius"
            },
            "notifications": {
                "email_reminders": true,
                "research_updates": false,
                "community_notifications": true
            },
            "privacy": {
                "public_profile": false,
                "share_research_data": true,
                "allow_community_contact": false
            }
        }
    }

Update User Preferences

Update the authenticated user's preferences and settings.

**Endpoint**: ``PUT /api/v1/users/me/preferences``

**Headers**: ``Authorization: Bearer {access_token}``

**Request Body**:

.. code-block:: json

    {
        "timezone": "America/Los_Angeles",
        "notifications": {
            "email_reminders": false,
            "research_updates": true
        },
        "privacy": {
            "public_profile": true,
            "share_research_data": true
        }
    }

**Response** (200 OK):

.. code-block:: json

    {
        "data": {
            "timezone": "America/Los_Angeles",
            "date_format": "YYYY-MM-DD",
            "time_format": "24h",
            "units": {
                "weight": "metric",
                "temperature": "celsius"
            },
            "notifications": {
                "email_reminders": false,
                "research_updates": true,
                "community_notifications": true
            },
            "privacy": {
                "public_profile": true,
                "share_research_data": true,
                "allow_community_contact": false
            }
        }
    }

Data Export
===========

Export User Data

Export all user data in a portable format (GDPR compliance).

**Endpoint**: ``GET /api/v1/users/me/export``

**Headers**: ``Authorization: Bearer {access_token}``

**Query Parameters**:

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Parameter
     - Type
     - Description
   * - format
     - string
     - Export format: "json", "csv"
   * - include
     - array
     - Data types to include: ["profile", "intakes", "supplements", "stacks"]

**Response** (200 OK):

For JSON format:

.. code-block:: json

    {
        "export_info": {
            "user_id": "123e4567-e89b-12d3-a456-************",
            "export_date": "2025-06-18T17:30:00Z",
            "format": "json",
            "data_types": ["profile", "intakes", "supplements", "stacks"]
        },
        "profile": {
            "id": "123e4567-e89b-12d3-a456-************",
            "email": "<EMAIL>",
            "username": "testuser",
            "full_name": "Test User",
            "bio": "Health enthusiast",
            "created_at": "2025-06-18T15:30:00Z"
        },
        "intakes": [
            {
                "id": "intake_123",
                "supplement_name": "Vitamin D3",
                "dosage": 5000,
                "dosage_unit": "IU",
                "taken_at": "2025-06-18T08:00:00Z",
                "notes": "With breakfast"
            }
        ],
        "supplements": [
            {
                "id": "supp_123",
                "name": "Vitamin D3",
                "brand": "Nature's Way",
                "category": "Vitamin"
            }
        ],
        "stacks": [
            {
                "id": "stack_123",
                "name": "Morning Stack",
                "description": "Daily morning supplements",
                "items": [
                    {
                        "supplement_name": "Vitamin D3",
                        "dosage": 5000,
                        "dosage_unit": "IU"
                    }
                ]
            }
        ]
    }

For CSV format, returns a ZIP file containing separate CSV files for each data type.

User Sessions
=============
============

Get Active Sessions

Get list of active user sessions.

**Endpoint**: ``GET /api/v1/users/me/sessions``

**Headers**: ``Authorization: Bearer {access_token}``

**Response** (200 OK):

.. code-block:: json

    {
        "data": [
            {
                "id": "session_123",
                "created_at": "2025-06-18T15:30:00Z",
                "last_activity": "2025-06-18T17:30:00Z",
                "ip_address": "*************",
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "location": {
                    "country": "United States",
                    "city": "New York"
                },
                "is_current": true
            },
            {
                "id": "session_456",
                "created_at": "2025-06-17T10:00:00Z",
                "last_activity": "2025-06-17T18:00:00Z",
                "ip_address": "*********",
                "user_agent": "SupplementTracker Mobile App v1.0",
                "location": {
                    "country": "United States",
                    "city": "New York"
                },
                "is_current": false
            }
        ]
    }

Revoke Session

Revoke a specific user session.

**Endpoint**: ``DELETE /api/v1/users/me/sessions/{session_id}``

**Headers**: ``Authorization: Bearer {access_token}``

**Response** (200 OK):

.. code-block:: json

    {
        "message": "Session revoked successfully"
    }

Revoke All Sessions

Revoke all user sessions except the current one.

**Endpoint**: ``DELETE /api/v1/users/me/sessions``

**Headers**: ``Authorization: Bearer {access_token}``

**Response** (200 OK):

.. code-block:: json

    {
        "message": "All other sessions revoked successfully",
        "revoked_sessions": 3
    }

Examples
========

Python Example

.. code-block:: python

    import httpx
    import asyncio

    class UserClient:
        def __init__(self, base_url: str, access_token: str):
            self.base_url = base_url
            self.access_token = access_token
            self.client = httpx.AsyncClient()

        @property
        def headers(self):
            return {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }

        async def get_profile(self):
            """Get current user profile"""
            response = await self.client.get(
                f"{self.base_url}/users/me",
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

        async def update_profile(self, **kwargs):
            """Update user profile"""
            response = await self.client.put(
                f"{self.base_url}/users/me",
                json=kwargs,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

        async def get_statistics(self, period="month"):
            """Get user statistics"""
            response = await self.client.get(
                f"{self.base_url}/users/me/stats",
                params={"period": period},
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

        async def export_data(self, format="json", include=None):
            """Export user data"""
            params = {"format": format}
            if include:
                params["include"] = include

            response = await self.client.get(
                f"{self.base_url}/users/me/export",
                params=params,
                headers=self.headers
            )
            response.raise_for_status()
            
            if format == "json":
                return response.json()
            else:
                return response.content  # Binary data for CSV/ZIP

        async def get_preferences(self):
            """Get user preferences"""
            response = await self.client.get(
                f"{self.base_url}/users/me/preferences",
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

        async def update_preferences(self, **kwargs):
            """Update user preferences"""
            response = await self.client.put(
                f"{self.base_url}/users/me/preferences",
                json=kwargs,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

    # Usage example
    async def main():
        client = UserClient(
            "https://api.supplementtracker.com/api/v1",
            "your_access_token"
        )

        # Get user profile
        profile = await client.get_profile()
        print(f"User: {profile['data']['username']}")

        # Update profile
        updated_profile = await client.update_profile(
            full_name="New Full Name",
            bio="Updated bio"
        )

        # Get statistics
        stats = await client.get_statistics(period="month")
        print(f"Total intakes this month: {stats['data']['total_intakes']}")

        # Export data
        export_data = await client.export_data(
            format="json",
            include=["profile", "intakes"]
        )

        # Update preferences
        await client.update_preferences(
            timezone="America/Los_Angeles",
            notifications={"email_reminders": False}
        )

    asyncio.run(main())

JavaScript Example

.. code-block:: javascript

    class UserAPI {
        constructor(baseUrl, accessToken) {
            this.baseUrl = baseUrl;
            this.accessToken = accessToken;
        }

        async request(endpoint, options = {}) {
            const url = `${this.baseUrl}${endpoint}`;
            const config = {
                headers: {
                    'Authorization': `Bearer ${this.accessToken}`,
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            };

            const response = await fetch(url, config);
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error?.message || 'Request failed');
            }

            return response.json();
        }

        async getProfile() {
            return this.request('/users/me');
        }

        async updateProfile(profileData) {
            return this.request('/users/me', {
                method: 'PUT',
                body: JSON.stringify(profileData)
            });
        }

        async getStatistics(period = 'month') {
            return this.request(`/users/me/stats?period=${period}`);
        }

        async exportData(format = 'json', include = null) {
            let url = `/users/me/export?format=${format}`;
            if (include) {
                url += `&include=${include.join(',')}`;
            }
            
            const response = await fetch(`${this.baseUrl}${url}`, {
                headers: {
                    'Authorization': `Bearer ${this.accessToken}`
                }
            });

            if (!response.ok) {
                throw new Error('Export failed');
            }

            if (format === 'json') {
                return response.json();
            } else {
                return response.blob(); // For CSV/ZIP files
            }
        }

        async getPreferences() {
            return this.request('/users/me/preferences');
        }

        async updatePreferences(preferences) {
            return this.request('/users/me/preferences', {
                method: 'PUT',
                body: JSON.stringify(preferences)
            });
        }

        async getSessions() {
            return this.request('/users/me/sessions');
        }

        async revokeSession(sessionId) {
            return this.request(`/users/me/sessions/${sessionId}`, {
                method: 'DELETE'
            });
        }

        async revokeAllSessions() {
            return this.request('/users/me/sessions', {
                method: 'DELETE'
            });
        }
    }

    // Usage example
    const userAPI = new UserAPI(
        'https://api.supplementtracker.com/api/v1',
        'your_access_token'
    );

    // Get and display user profile
    userAPI.getProfile()
        .then(profile => {
            console.log('User profile:', profile.data);
        })
        .catch(error => {
            console.error('Failed to get profile:', error.message);
        });

    // Update profile
    userAPI.updateProfile({
        full_name: 'New Name',
        bio: 'Updated bio'
    })
        .then(result => {
            console.log('Profile updated:', result.data);
        });

    // Get statistics
    userAPI.getStatistics('month')
        .then(stats => {
            console.log('Monthly stats:', stats.data);
        });

Error Handling
==============

Common Error Responses

**Unauthorized** (401):

.. code-block:: json

    {
        "error": {
            "code": "AUTHENTICATION_REQUIRED",
            "message": "Authentication required"
        }
    }

**Forbidden** (403):

.. code-block:: json

    {
        "error": {
            "code": "AUTHORIZATION_FAILED",
            "message": "Insufficient permissions"
        }
    }

**Not Found** (404):

.. code-block:: json

    {
        "error": {
            "code": "RESOURCE_NOT_FOUND",
            "message": "User not found"
        }
    }

**Validation Error** (400):

.. code-block:: json

    {
        "error": {
            "code": "VALIDATION_ERROR",
            "message": "Invalid input data",
            "details": {
                "field": "bio",
                "issue": "Bio cannot exceed 500 characters"
            }
        }
    }

Related Documentation
=====================
====================

- :doc:`authentication`
- :doc:`supplements`
- :doc:`schemas`
