#!/usr/bin/env python3
"""
Comprehensive script to fix all RST formatting issues in documentation files.
"""

import os
import re
import sys
from pathlib import Path


def fix_duplicate_titles(content):
    """Fix duplicate title lines in RST files."""
    lines = content.split('\n')
    fixed_lines = []
    i = 0

    while i < len(lines):
        line = lines[i]

        # Check for overline pattern with duplicate title
        if (i + 4 < len(lines) and
            re.match(r'^[=\-~^"\'`#*+<>_]+\s*$', line) and
            lines[i + 2] == lines[i + 1] and  # Duplicate title lines
            re.match(r'^[=\-~^"\'`#*+<>_]+\s*$', lines[i + 3])):

            # Keep only one title
            overline = line
            title = lines[i + 1]
            underline = lines[i + 3]

            fixed_lines.append(overline)
            fixed_lines.append(title)
            fixed_lines.append(underline)
            i += 4  # Skip the duplicate
            continue

        # Check for simple title pattern with duplicate
        elif (i + 3 < len(lines) and
              lines[i] == lines[i + 1] and  # Duplicate title lines
              re.match(r'^[=\-~^"\'`#*+<>_]+\s*$', lines[i + 2])):

            # Keep only one title
            title = lines[i]
            underline = lines[i + 2]

            fixed_lines.append(title)
            fixed_lines.append(underline)
            i += 3  # Skip the duplicate
            continue

        fixed_lines.append(line)
        i += 1

    return '\n'.join(fixed_lines)


def fix_underlines(content):
    """Fix underline length issues and overline/underline mismatches."""
    lines = content.split('\n')
    fixed_lines = []
    i = 0

    while i < len(lines):
        line = lines[i]

        # Check for overline pattern (overline, title, underline)
        if (i + 2 < len(lines) and
            re.match(r'^[=\-~^"\'`#*+<>_]+\s*$', line) and
            re.match(r'^[=\-~^"\'`#*+<>_]+\s*$', lines[i + 2])):

            overline = line.strip()
            title = lines[i + 1]
            underline = lines[i + 2].strip()

            title_length = len(title.rstrip())
            char = overline[0] if overline else '='

            # Create matching overline and underline
            new_overline = char * title_length
            new_underline = char * title_length

            fixed_lines.append(new_overline)
            fixed_lines.append(title)
            fixed_lines.append(new_underline)
            i += 3
            continue

        # Check for simple underline pattern
        elif (i + 1 < len(lines) and
              re.match(r'^[=\-~^"\'`#*+<>_]+\s*$', lines[i + 1])):

            title = line
            underline = lines[i + 1].strip()

            title_length = len(title.rstrip())
            char = underline[0] if underline else '='

            # Create properly sized underline
            new_underline = char * title_length

            fixed_lines.append(title)
            fixed_lines.append(new_underline)
            i += 2
            continue

        fixed_lines.append(line)
        i += 1

    return '\n'.join(fixed_lines)


def fix_transitions(content):
    """Fix transition errors by removing problematic transition lines."""
    lines = content.split('\n')
    fixed_lines = []

    for i, line in enumerate(lines):
        # Check if this is a transition line (4 or more dashes)
        if re.match(r'^-{4,}\s*$', line.strip()):
            # Get context
            prev_line = lines[i-1].strip() if i > 0 else ""
            next_line = lines[i+1].strip() if i < len(lines) - 1 else ""

            # Skip transitions that are likely to cause errors:
            # 1. At the beginning or end of sections
            # 2. After empty lines
            # 3. Before section headers
            if (not prev_line or
                not next_line or
                (i + 1 < len(lines) and re.match(r'^[=\-~^"\'`#*+<>_]+\s*$', next_line)) or
                (i + 2 < len(lines) and re.match(r'^[=\-~^"\'`#*+<>_]+\s*$', lines[i + 2]))):
                continue

        fixed_lines.append(line)

    return '\n'.join(fixed_lines)


def fix_code_blocks(content):
    """Fix code block lexing issues by changing problematic language hints."""
    # Replace problematic http code blocks with text
    content = re.sub(
        r'.. code-block:: http\n\n((?:    .*\n)*)',
        r'.. code-block:: text\n\n\1',
        content
    )
    
    # Fix HCL code blocks that have issues
    content = re.sub(
        r'.. code-block:: hcl\n\n((?:    .*\n)*)',
        r'.. code-block:: text\n\n\1',
        content
    )
    
    return content


def fix_rst_file(file_path):
    """Fix all RST issues in a single file."""
    print(f"Fixing {file_path}...")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply all fixes
        content = fix_duplicate_titles(content)
        content = fix_underlines(content)
        content = fix_transitions(content)
        content = fix_code_blocks(content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✓ Fixed issues in {file_path}")
            return True
        else:
            print(f"  - No issues found in {file_path}")
            return False
            
    except Exception as e:
        print(f"  ✗ Error fixing {file_path}: {e}")
        return False


def main():
    """Main function to fix all RST files."""
    docs_dir = Path(__file__).parent
    rst_files = list(docs_dir.rglob('*.rst'))
    
    print(f"Found {len(rst_files)} RST files to fix...")
    print("=" * 50)
    
    fixed_count = 0
    for rst_file in rst_files:
        if fix_rst_file(rst_file):
            fixed_count += 1
    
    print("=" * 50)
    print(f"Fixed issues in {fixed_count} files.")
    
    if fixed_count > 0:
        print("\n✓ All RST formatting issues have been fixed!")
        print("You can now build the documentation without errors.")
    else:
        print("\n- No RST issues found.")


if __name__ == '__main__':
    main()
