# 🚀 Supplement Tracker - Startup Guide

This guide will help you start both the backend API and frontend React application for testing.

## 📋 Prerequisites

### Option 1: Using Nix (Recommended)
- **Nix Package Manager** installed
- All dependencies managed automatically

### Option 2: Manual Setup
- **Python 3.11+**
- **Node.js 18+** and npm
- **PostgreSQL** (optional - can use SQLite for testing)
- **Redis** (optional - for caching and background tasks)

## 🔧 Quick Start (Nix - Recommended)

### 1. Enter Nix Development Environment
```bash
nix-shell
```
This automatically provides all Python dependencies and development tools.

### 2. Set Up Environment
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings (optional for basic testing)
# The defaults work for local development
```

### 3. Start Backend API
```bash
# Start development server with auto-reload
make dev

# Or manually:
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. Start Frontend (New Terminal)
```bash
cd frontend

# Install dependencies (first time only)
npm install

# Start development server
npm start
```

## 🔧 Manual Setup (Without Nix)

### 1. Backend Setup
```bash
# Install Python dependencies
pip install -r requirements.txt

# Set up environment
cp .env.example .env

# Run database migrations (if using PostgreSQL)
alembic upgrade head

# Start backend server
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 2. Frontend Setup
```bash
cd frontend

# Install Node.js dependencies
npm install

# Start React development server
npm start
```

## 🌐 Access URLs

Once both services are running, you can access:

### 🖥️ **Frontend Application**
- **URL**: http://localhost:3000
- **Description**: React application with authentication, supplement tracking, and research tools

### 🔌 **Backend API**
- **URL**: http://localhost:8000
- **Description**: FastAPI backend with REST endpoints

### 📚 **API Documentation**
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### 🏥 **Health Check**
- **URL**: http://localhost:8000/api/v1/health
- **Description**: API health status

## 🧪 Testing URLs

### Authentication Flow
1. **Register**: http://localhost:3000/register
2. **Login**: http://localhost:3000/login
3. **Dashboard**: http://localhost:3000/dashboard (after login)

### Supplement Tracking
1. **Supplement Library**: http://localhost:3000/supplements
2. **Supplement Tracking**: http://localhost:3000/tracking
3. **Supplement Details**: http://localhost:3000/supplements/{id}

### Research Tools
1. **Research Hub**: http://localhost:3000/research
2. **Research Protocols**: http://localhost:3000/research/protocols/{id}

### Community Features
1. **Community**: http://localhost:3000/community
2. **Community Posts**: http://localhost:3000/community/posts/{id}

### User Management
1. **Profile**: http://localhost:3000/profile
2. **Settings**: http://localhost:3000/settings
3. **Analytics**: http://localhost:3000/analytics

## 🔍 API Testing

### Using curl
```bash
# Health check
curl http://localhost:8000/api/v1/health

# Register user
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","full_name":"Test User"}'

# Login
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Get supplements (requires auth token)
curl -X GET http://localhost:8000/api/v1/supplements \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Using Browser
- Visit http://localhost:8000/docs for interactive API documentation
- Test all endpoints directly in the Swagger UI

## 🗄️ Database Setup

### SQLite (Default - No Setup Required)
The application uses SQLite by default for easy testing.

### PostgreSQL (Optional)
```bash
# Install PostgreSQL
# Ubuntu/Debian: sudo apt install postgresql
# macOS: brew install postgresql
# Or use Docker: docker run -p 5432:5432 -e POSTGRES_PASSWORD=password postgres

# Create database
createdb supplement_tracker

# Update .env file
SUPPLEMENT_TRACKER_POSTGRES_SERVER=localhost
SUPPLEMENT_TRACKER_POSTGRES_USER=your_user
SUPPLEMENT_TRACKER_POSTGRES_PASSWORD=your_password
SUPPLEMENT_TRACKER_POSTGRES_DB=supplement_tracker

# Run migrations
alembic upgrade head
```

## 🔧 Development Tools

### Backend Testing
```bash
# Run all tests
make test

# Run with coverage
make test-cov

# Run specific test types
make test-unit
make test-integration
```

### Frontend Testing
```bash
cd frontend

# Run unit tests
npm test

# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui
```

### Code Quality
```bash
# Backend
make lint          # Linting
make format         # Code formatting
make type-check     # Type checking
make security       # Security checks

# Frontend
cd frontend
npm run lint        # ESLint
npm run format      # Prettier
npm run type-check  # TypeScript
```

## 🐛 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Kill process on port 8000
lsof -ti:8000 | xargs kill -9

# Kill process on port 3000
lsof -ti:3000 | xargs kill -9
```

#### Database Connection Issues
```bash
# Check if PostgreSQL is running
pg_isready

# Reset database
make db-reset
```

#### Frontend Build Issues
```bash
cd frontend
rm -rf node_modules package-lock.json
npm install
```

#### Nix Issues
```bash
# Update Nix channels
nix-channel --update

# Rebuild environment
nix-shell --pure
```

### Environment Variables
Make sure your `.env` file has the correct settings:
```bash
# Check current environment
cat .env

# Verify environment is loaded
python -c "from app.core.config import settings; print(settings.PROJECT_NAME)"
```

## 📊 Monitoring

### Logs
```bash
# Backend logs (if using structured logging)
tail -f logs/app.log

# Frontend logs
# Check browser console for React logs
```

### Performance
- Backend: http://localhost:8000/metrics (if enabled)
- Frontend: React DevTools in browser

## 🎯 Next Steps

1. **Create Test User**: Register at http://localhost:3000/register
2. **Explore Features**: Navigate through the application
3. **Test API**: Use Swagger UI at http://localhost:8000/docs
4. **Run Tests**: Execute test suites to verify functionality
5. **Check Logs**: Monitor application logs for any issues

## 🆘 Support

If you encounter issues:
1. Check the logs for error messages
2. Verify all services are running
3. Ensure environment variables are set correctly
4. Try restarting the services
5. Check the GitHub issues for known problems

---

**Happy Testing! 🎉**
