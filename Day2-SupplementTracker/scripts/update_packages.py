#!/usr/bin/env python3
"""
Package Update Script

This script updates all packages to the versions specified in requirements.txt
to address security vulnerabilities.
"""

import subprocess
import sys
from typing import List


def run_command(command: List[str]) -> bool:
    """Run a command and return True if successful."""
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(f"✅ {' '.join(command)}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {' '.join(command)}")
        print(f"   Error: {e.stderr}")
        return False


def update_packages():
    """Update packages to secure versions."""
    print("🔄 Updating packages to secure versions...")
    print("=" * 50)
    
    # Critical security updates first
    critical_updates = [
        "fastapi==0.115.6",
        "python-multipart==0.0.18", 
        "transformers==4.50.0",
        "scikit-learn==1.6.0",
        "sentry-sdk[fastapi]==2.21.0",
        "black==24.10.0",
    ]
    
    print("📦 Installing critical security updates...")
    for package in critical_updates:
        success = run_command(["pip", "install", "--upgrade", package])
        if not success:
            print(f"⚠️  Failed to update {package}")
    
    print("\n📦 Installing all requirements...")
    # Install all requirements
    success = run_command(["pip", "install", "--upgrade", "-r", "requirements.txt"])
    if success:
        print("✅ All requirements updated successfully")
    else:
        print("❌ Failed to update some requirements")
    
    print("\n📦 Installing development requirements...")
    success = run_command(["pip", "install", "--upgrade", "-r", "requirements-dev.txt"])
    if success:
        print("✅ Development requirements updated successfully")
    else:
        print("❌ Failed to update some development requirements")
    
    print("\n📦 Installing documentation requirements...")
    success = run_command(["pip", "install", "--upgrade", "-r", "docs/requirements.txt"])
    if success:
        print("✅ Documentation requirements updated successfully")
    else:
        print("❌ Failed to update some documentation requirements")


def verify_updates():
    """Verify that critical packages are updated."""
    print("\n🔍 Verifying critical package updates...")
    print("=" * 50)
    
    critical_packages = {
        'fastapi': '0.115.6',
        'python-multipart': '0.0.18',
        'transformers': '4.50.0',
        'scikit-learn': '1.6.0',
        'sentry-sdk': '2.21.0',
        'black': '24.10.0',
    }
    
    try:
        result = subprocess.run(["pip", "freeze"], capture_output=True, text=True, check=True)
        installed_packages = {}
        
        for line in result.stdout.split('\n'):
            if '==' in line:
                package, version = line.split('==', 1)
                installed_packages[package.lower()] = version.strip()
        
        all_updated = True
        for package, expected_version in critical_packages.items():
            installed_version = installed_packages.get(package.lower())
            if installed_version == expected_version:
                print(f"✅ {package}: {installed_version}")
            else:
                print(f"❌ {package}: {installed_version} (expected: {expected_version})")
                all_updated = False
        
        return all_updated
        
    except subprocess.CalledProcessError:
        print("❌ Failed to check installed packages")
        return False


def create_updated_lock_file():
    """Create an updated requirements lock file."""
    print("\n📝 Creating updated requirements lock file...")
    try:
        result = subprocess.run(["pip", "freeze"], capture_output=True, text=True, check=True)
        with open("requirements-lock.txt", "w") as f:
            f.write(result.stdout)
        print("✅ Updated requirements-lock.txt created")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to create lock file")
        return False


if __name__ == "__main__":
    print("🔒 Security Package Update Script")
    print("=" * 50)
    
    # Update packages
    update_packages()
    
    # Verify updates
    all_updated = verify_updates()
    
    # Create lock file
    lock_created = create_updated_lock_file()
    
    print("\n" + "=" * 50)
    if all_updated and lock_created:
        print("🎉 All packages successfully updated to secure versions!")
        print("✅ Ready for secure deployment")
        sys.exit(0)
    else:
        print("⚠️  Some packages may not have updated correctly")
        print("🔍 Please review the output above")
        sys.exit(1)
