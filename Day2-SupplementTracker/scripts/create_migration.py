#!/usr/bin/env python3
"""
Create initial database migration.

This script creates the initial Alembic migration for all database models.
"""

import subprocess
import sys
from pathlib import Path


def main():
    """Create initial migration."""
    print("🔧 Creating initial database migration...")
    
    try:
        # Create initial migration
        result = subprocess.run([
            "alembic", "revision", "--autogenerate", 
            "-m", "Initial migration with user and supplement models"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Initial migration created successfully!")
            print(f"Migration output:\n{result.stdout}")
        else:
            print(f"❌ Failed to create migration:\n{result.stderr}")
            return 1
            
    except FileNotFoundError:
        print("❌ Alembic not found. Make sure it's installed in your environment.")
        return 1
    except Exception as e:
        print(f"❌ Error creating migration: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
