#!/bin/bash

# Comprehensive test suite for Supplement Tracker services
# Tests both HTTPS (port-free) and HTTP (fallback) access

echo "🧪 Supplement Tracker - Service Test Suite"
echo "=========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Test function
run_test() {
    local test_name="$1"
    local command="$2"
    local expected_pattern="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "Testing $test_name... "
    
    result=$(eval "$command" 2>/dev/null)
    if echo "$result" | grep -q "$expected_pattern"; then
        echo -e "${GREEN}✅ PASS${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ FAIL${NC}"
        echo "   Expected: $expected_pattern"
        echo "   Got: $result"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

echo -e "${BLUE}🔍 Testing HTTPS Services (Port-Free)${NC}"
echo "-----------------------------------"

# HTTPS API Tests
run_test "HTTPS API Health" \
    "curl -k -s https://api.pills.localhost/health/" \
    "healthy"

run_test "HTTPS API Root" \
    "curl -k -s https://api.pills.localhost/" \
    "Supplement Tracker API"

run_test "HTTPS API Docs" \
    "curl -k -s https://api.pills.localhost/docs" \
    "swagger"

# HTTPS Frontend Tests
run_test "HTTPS Frontend" \
    "curl -k -s https://app.pills.localhost/" \
    "DOCTYPE html"

run_test "HTTPS Demo Page" \
    "curl -k -s https://app.pills.localhost/demo.html" \
    "Supplement Tracker"

echo ""
echo -e "${YELLOW}🔄 Testing HTTP Fallback Services${NC}"
echo "--------------------------------"

# HTTP Fallback Tests
run_test "HTTP API Health" \
    "curl -s http://api.pills.localhost:9080/health/" \
    "healthy"

run_test "HTTP API Root" \
    "curl -s http://api.pills.localhost:9080/" \
    "Supplement Tracker API"

run_test "HTTP Frontend" \
    "curl -s http://app.pills.localhost:9080/" \
    "DOCTYPE html"

run_test "HTTP Demo Page" \
    "curl -s http://app.pills.localhost:9080/demo.html" \
    "Supplement Tracker"

echo ""
echo -e "${BLUE}🔧 Testing Infrastructure${NC}"
echo "------------------------"

# Infrastructure Tests
run_test "Docker Services Running" \
    "docker compose -f docker-compose.simple.yml ps --format table | grep -c 'Up'" \
    "3"

run_test "Traefik Dashboard" \
    "curl -s http://traefik.pills.localhost:9081/api/overview" \
    "http"

echo ""
echo "📊 Test Results Summary"
echo "======================"
echo -e "Total Tests: ${BLUE}$TOTAL_TESTS${NC}"
echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 All tests passed! Services are working correctly.${NC}"
    echo ""
    echo "✅ Ready to use:"
    echo "   • Frontend: https://app.pills.localhost/"
    echo "   • API: https://api.pills.localhost/"
    echo "   • Demo: https://app.pills.localhost/demo.html"
    echo "   • Docs: https://api.pills.localhost/docs"
    exit 0
else
    echo ""
    echo -e "${RED}⚠️  Some tests failed. Please check the services.${NC}"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "   • Check if services are running: docker compose -f docker-compose.simple.yml ps"
    echo "   • Check logs: docker compose -f docker-compose.simple.yml logs"
    echo "   • Verify /etc/hosts: sudo ./setup-hosts.sh"
    exit 1
fi
