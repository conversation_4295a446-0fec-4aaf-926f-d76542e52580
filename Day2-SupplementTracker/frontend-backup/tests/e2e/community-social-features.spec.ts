/**
 * Community Social Features E2E Tests
 * 
 * End-to-end tests for community feed, post creation, and social interactions.
 */

import { test, expect, Page } from '@playwright/test';

// Test data
const testPost = {
  title: 'Amazing Results with Magnesium Glycinate',
  content: 'I\'ve been taking magnesium glycinate for 3 months now and the improvement in my sleep quality has been remarkable. I fall asleep faster and wake up more refreshed. Started with 200mg before bed and gradually increased to 400mg. No digestive issues like I had with magnesium oxide. Highly recommend for anyone struggling with sleep issues!',
  type: 'supplement_review',
  tags: ['magnesium', 'sleep', 'glycinate']
};

const testComment = {
  content: 'Thanks for sharing! I\'ve been considering trying magnesium for sleep. What brand do you use?'
};

// Helper functions
async function loginAsUser(page: Page) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'password123');
  await page.click('[data-testid="login-button"]');
  await expect(page).toHaveURL('/dashboard');
}

async function navigateToCommunity(page: Page) {
  await page.click('[data-testid="nav-community"]');
  await expect(page).toHaveURL('/community');
  await expect(page.locator('h1')).toContainText('Community Hub');
}

async function createPost(page: Page, post = testPost) {
  await page.click('button:has-text("Create Post")');
  await expect(page.locator('h2')).toContainText('Create New Post');

  // Select post type
  await page.selectOption('select', post.type);

  // Fill title if it's a review or research finding
  if (post.type === 'supplement_review' || post.type === 'research_finding') {
    await page.fill('#title', post.title);
  }

  // Fill content
  await page.fill('#content', post.content);

  // Add tags
  for (const tag of post.tags) {
    await page.fill('input[placeholder*="Add tags"]', tag);
    await page.keyboard.press('Enter');
  }

  // Submit post
  await page.click('button:has-text("Post")');
}

test.describe('Community Social Features', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test.describe('Community Feed', () => {
    test('should display community feed with posts', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Should show feed header
      await expect(page.locator('h1')).toContainText('Community Hub');
      await expect(page.locator('text=Connect with fellow supplement enthusiasts')).toBeVisible();

      // Should show feed controls
      await expect(page.locator('input[placeholder*="Search posts"]')).toBeVisible();
      await expect(page.locator('select')).toBeVisible();
      await expect(page.locator('button:has-text("Create Post")')).toBeVisible();

      // Should show feed stats
      await expect(page.locator('text=Total Posts')).toBeVisible();
      await expect(page.locator('text=Today')).toBeVisible();
      await expect(page.locator('text=Active Users')).toBeVisible();

      // Should show posts if any exist
      const posts = page.locator('[data-testid="post-card"]');
      if (await posts.count() > 0) {
        await expect(posts.first()).toBeVisible();
      }
    });

    test('should search posts by content', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Search for specific content
      await page.fill('input[placeholder*="Search posts"]', 'vitamin');
      await page.waitForTimeout(500); // Wait for debounce

      // Should filter results
      const posts = page.locator('[data-testid="post-card"]');
      if (await posts.count() > 0) {
        await expect(posts.first()).toContainText('vitamin');
      }
    });

    test('should filter posts by type', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Filter by supplement reviews
      await page.selectOption('select', 'supplement_review');

      // Should show only supplement review posts
      const posts = page.locator('[data-testid="post-card"]');
      if (await posts.count() > 0) {
        await expect(posts.first()).toContainText('Supplement Review');
      }
    });

    test('should sort posts by different criteria', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Change sort to popular
      const sortSelects = page.locator('select');
      await sortSelects.nth(1).selectOption('popular');

      // Should reorder posts (would need to verify actual ordering)
      await page.waitForTimeout(1000);
    });

    test('should clear filters', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Apply filters
      await page.fill('input[placeholder*="Search posts"]', 'test');
      await page.selectOption('select', 'supplement_review');

      // Clear filters
      await page.click('button:has-text("Clear")');

      // Should reset filters
      await expect(page.locator('input[placeholder*="Search posts"]')).toHaveValue('');
    });
  });

  test.describe('Post Creation', () => {
    test('should create a supplement review post', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      await createPost(page);

      // Should show success notification
      await expect(page.locator('.notification')).toContainText('Post created successfully');

      // Should see the new post in the feed
      await expect(page.locator('text=' + testPost.title)).toBeVisible();
      await expect(page.locator('text=Supplement Review')).toBeVisible();
    });

    test('should create a research finding post', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      const researchPost = {
        ...testPost,
        title: 'New Study on Omega-3 and Cognitive Function',
        content: 'Recent research published in the Journal of Nutrition shows that omega-3 supplementation may improve cognitive function in older adults.',
        type: 'research_finding',
        tags: ['omega-3', 'research', 'cognitive-function']
      };

      await createPost(page, researchPost);

      await expect(page.locator('.notification')).toContainText('Post created successfully');
      await expect(page.locator('text=' + researchPost.title)).toBeVisible();
      await expect(page.locator('text=Research Finding')).toBeVisible();
    });

    test('should create an experience share post', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      const experiencePost = {
        ...testPost,
        title: '',
        content: 'Just wanted to share my supplement journey over the past year. Started with basic multivitamin and gradually added targeted supplements based on blood work.',
        type: 'experience_share',
        tags: ['journey', 'blood-work', 'multivitamin']
      };

      await createPost(page, experiencePost);

      await expect(page.locator('.notification')).toContainText('Post created successfully');
      await expect(page.locator('text=Experience')).toBeVisible();
    });

    test('should validate required fields', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      await page.click('button:has-text("Create Post")');

      // Try to submit without content
      await page.click('button:has-text("Post")');

      // Should show validation error
      await expect(page.locator('text=Content is required')).toBeVisible();
    });

    test('should handle tag management', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      await page.click('button:has-text("Create Post")');

      // Add tags
      await page.fill('input[placeholder*="Add tags"]', 'vitamin-d');
      await page.keyboard.press('Enter');
      await expect(page.locator('text=#vitamin-d')).toBeVisible();

      await page.fill('input[placeholder*="Add tags"]', 'health');
      await page.keyboard.press('Enter');
      await expect(page.locator('text=#health')).toBeVisible();

      // Remove a tag
      await page.click('text=#vitamin-d button');
      await expect(page.locator('text=#vitamin-d')).not.toBeVisible();
    });

    test('should enforce character limit', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      await page.click('button:has-text("Create Post")');

      // Fill with content near limit
      const longContent = 'A'.repeat(1950);
      await page.fill('#content', longContent);

      // Should show character count
      await expect(page.locator('text=1950 / 2000')).toBeVisible();

      // Try to exceed limit
      const tooLongContent = 'A'.repeat(2100);
      await page.fill('#content', tooLongContent);

      // Should show error
      await expect(page.locator('text=Content must be less than 2000 characters')).toBeVisible();
    });

    test('should cancel post creation', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      await page.click('button:has-text("Create Post")');
      await page.fill('#content', 'Test content');

      // Cancel creation
      await page.click('button:has-text("Cancel")');

      // Should return to feed
      await expect(page.locator('h1')).toContainText('Community Hub');
      await expect(page.locator('h2:has-text("Create New Post")')).not.toBeVisible();
    });
  });

  test.describe('Social Interactions', () => {
    test('should like and unlike posts', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Find a post and like it
      const firstPost = page.locator('[data-testid="post-card"]').first();
      const likeButton = firstPost.locator('button').filter({ hasText: /\d+/ }).first();
      
      const initialLikes = await likeButton.textContent();
      await likeButton.click();

      // Should show updated like count
      await expect(likeButton).not.toContainText(initialLikes!);

      // Unlike the post
      await likeButton.click();

      // Should return to original count
      await expect(likeButton).toContainText(initialLikes!);
    });

    test('should open comment interface', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Find a post and click comment button
      const firstPost = page.locator('[data-testid="post-card"]').first();
      const commentButton = firstPost.locator('button').filter({ hasText: /\d+/ }).nth(1);
      
      await commentButton.click();

      // Should navigate to post details or open comment interface
      // Implementation depends on the specific behavior
    });

    test('should share posts', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Find a post and share it
      const firstPost = page.locator('[data-testid="post-card"]').first();
      const shareButton = firstPost.locator('button').filter({ hasText: /\d+/ }).nth(2);
      
      const initialShares = await shareButton.textContent();
      await shareButton.click();

      // Should show success notification
      await expect(page.locator('.notification')).toContainText('Post shared successfully');

      // Should update share count
      await expect(shareButton).not.toContainText(initialShares!);
    });

    test('should navigate to user profiles', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Click on a user's name
      const firstPost = page.locator('[data-testid="post-card"]').first();
      const authorName = firstPost.locator('.author-name').first();
      
      await authorName.click();

      // Should navigate to user profile
      await expect(page).toHaveURL(/\/users\/\w+/);
    });

    test('should navigate to post details', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Click on post title or content
      const firstPost = page.locator('[data-testid="post-card"]').first();
      const postTitle = firstPost.locator('h3').first();
      
      await postTitle.click();

      // Should navigate to post details
      await expect(page).toHaveURL(/\/community\/posts\/\w+/);
    });
  });

  test.describe('Mobile Responsiveness', () => {
    test('should work on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Should display mobile-friendly layout
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('button:has-text("Create Post")')).toBeVisible();

      // Feed controls should stack vertically
      const searchInput = page.locator('input[placeholder*="Search posts"]');
      const createButton = page.locator('button:has-text("Create Post")');
      
      const searchBox = await searchInput.boundingBox();
      const buttonBox = await createButton.boundingBox();
      
      // Create button should be below search (stacked layout)
      expect(buttonBox!.y).toBeGreaterThan(searchBox!.y);
    });

    test('should handle post creation on mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await loginAsUser(page);
      await navigateToCommunity(page);

      await page.click('button:has-text("Create Post")');

      // Modal should be mobile-friendly
      await expect(page.locator('#content')).toBeVisible();
      await page.fill('#content', 'Mobile test post');

      // Submit button should be accessible
      await page.click('button:has-text("Post")');
      await expect(page.locator('.notification')).toContainText('Post created successfully');
    });
  });

  test.describe('Accessibility', () => {
    test('should be keyboard navigable', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Tab through interactive elements
      await page.keyboard.press('Tab'); // Search input
      await expect(page.locator('input[placeholder*="Search posts"]')).toBeFocused();

      await page.keyboard.press('Tab'); // Filter select
      await page.keyboard.press('Tab'); // Sort select
      await page.keyboard.press('Tab'); // Create post button
      await expect(page.locator('button:has-text("Create Post")')).toBeFocused();

      // Should be able to activate with Enter
      await page.keyboard.press('Enter');
      await expect(page.locator('h2')).toContainText('Create New Post');
    });

    test('should have proper ARIA labels and roles', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Check for proper button roles
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        const button = buttons.nth(i);
        await expect(button).toHaveAttribute('type', 'button');
      }
    });

    test('should support screen readers', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Check for descriptive text and proper headings
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('text=Connect with fellow supplement enthusiasts')).toBeVisible();

      // Post cards should have meaningful content
      const posts = page.locator('[data-testid="post-card"]');
      if (await posts.count() > 0) {
        const firstPost = posts.first();
        await expect(firstPost.locator('h3')).toBeVisible(); // Post title
        await expect(firstPost.locator('text=Supplement Review, Research Finding, Experience')).toBeVisible(); // Post type
      }
    });
  });

  test.describe('Performance', () => {
    test('should load community page quickly', async ({ page }) => {
      const startTime = Date.now();
      
      await loginAsUser(page);
      await navigateToCommunity(page);
      
      const loadTime = Date.now() - startTime;
      
      // Should load within reasonable time
      expect(loadTime).toBeLessThan(3000);
      
      // Should show content
      await expect(page.locator('h1')).toBeVisible();
    });

    test('should handle infinite scroll efficiently', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Scroll to bottom to trigger load more
      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));

      // Should load more posts if available
      const loadMoreButton = page.locator('button:has-text("Load More Posts")');
      if (await loadMoreButton.isVisible()) {
        await loadMoreButton.click();
        await expect(page.locator('text=Loading...')).toBeVisible();
      }
    });
  });

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      await loginAsUser(page);
      await navigateToCommunity(page);

      // Mock network failure
      await page.route('**/community/posts', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ detail: 'Internal server error' })
        });
      });

      // Try to create a post
      await page.click('button:has-text("Create Post")');
      await page.fill('#content', 'Test post');
      await page.click('button:has-text("Post")');

      // Should show error notification
      await expect(page.locator('.notification')).toContainText('Failed to create post');
    });

    test('should handle empty feed state', async ({ page }) => {
      await loginAsUser(page);
      
      // Mock empty response
      await page.route('**/community/posts*', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ items: [], total: 0, page: 1, size: 10 })
        });
      });

      await navigateToCommunity(page);

      // Should show empty state
      await expect(page.locator('text=No posts yet')).toBeVisible();
      await expect(page.locator('text=Be the first to share')).toBeVisible();
    });
  });
});
