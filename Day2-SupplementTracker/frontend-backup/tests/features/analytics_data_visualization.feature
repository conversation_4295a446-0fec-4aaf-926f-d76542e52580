Feature: Analytics Data Visualization
  As a supplement user
  I want to view comprehensive analytics and insights about my supplement tracking
  So that I can understand my patterns and optimize my supplement routine

  Background:
    Given I am logged in as a user with supplement tracking data
    And I am on the analytics dashboard page

  Scenario: View analytics dashboard overview
    When I visit the analytics dashboard
    Then I should see the dashboard header with title "Analytics Dashboard"
    And I should see the description "Track your supplement journey with detailed insights and trends"
    And I should see an "Export Data" button
    And I should see filter controls for time range selection
    And I should see key metrics displayed:
      | Metric            |
      | Total Supplements |
      | Adherence Rate    |
      | Total Intakes     |
      | Current Streak    |

  Scenario: View key performance metrics
    When I view the analytics dashboard
    Then I should see metric cards with current values:
      | Metric            | Value | Change Indicator |
      | Total Supplements | 12    | +2% vs last period |
      | Adherence Rate    | 85%   | +5% vs last period |
      | Total Intakes     | 342   | +12% vs last period |
      | Current Streak    | 15    | +3 days vs last period |
    And each metric should show a trend indicator (up/down arrow)
    And positive changes should be highlighted in green
    And negative changes should be highlighted in red

  Scenario: Interact with data visualization charts
    When I view the analytics dashboard
    Then I should see interactive charts:
      | Chart Type | Title                      | Data Displayed        |
      | Line Chart | Adherence Trend           | Daily adherence rates |
      | Bar Chart  | Supplements by Category   | Category distribution |
      | Pie Chart  | Supplement Distribution   | Individual supplements |
    And each chart should be properly labeled with axes and legends
    And charts should be responsive and interactive

  Scenario: Hover over chart elements for detailed information
    Given I can see the adherence trend line chart
    When I hover over a data point on the chart
    Then I should see a tooltip with:
      | Information |
      | Date        |
      | Adherence % |
      | Value       |
    And the tooltip should follow my mouse cursor
    And the tooltip should disappear when I move away

  Scenario: View AI-powered insights and recommendations
    When I scroll to the insights section
    Then I should see "AI-Powered Insights" header with an insight icon
    And I should see insight cards with different types:
      | Insight Type    | Example Text                                           |
      | Positive        | Your adherence rate has improved by 5% this month     |
      | Recommendation  | Consider taking magnesium in the evening for better sleep |
      | Neutral         | Your supplement intake is consistent across weekdays   |
    And each insight should be clearly categorized
    And insights should be based on my actual data patterns

  Scenario: Filter analytics by time range
    Given I am viewing the analytics dashboard
    When I change the time range filter to "Last 7 days"
    Then the metrics should update to reflect the new time period
    And all charts should refresh with 7-day data
    And the filter selection should be highlighted

    When I change the time range to "Last 3 months"
    Then the data should update to show 3-month trends
    And charts should adjust their scale appropriately

  Scenario: Use custom date range filtering
    When I select "Custom range" from the time filter
    Then I should see start date and end date input fields
    And both fields should have date picker functionality

    When I set the start date to "2024-01-01"
    And I set the end date to "2024-01-31"
    Then the analytics should update to show January 2024 data only
    And charts should reflect the custom date range

  Scenario: Export analytics data
    When I click the "Export Data" button
    Then I should see export options or a download should begin
    And the exported data should include:
      | Data Type           |
      | Metrics summary     |
      | Trend data          |
      | Distribution data   |
      | Insights summary    |

  Scenario: View supplement distribution analysis
    Given I can see the supplement distribution pie chart
    Then I should see my supplements represented as pie slices
    And each slice should be labeled with supplement name
    And each slice should show percentage of total intake
    And there should be a legend showing:
      | Information        |
      | Supplement name    |
      | Color indicator    |
      | Percentage value   |

  Scenario: Analyze adherence trends over time
    Given I can see the adherence trend line chart
    Then I should see my adherence rate plotted over time
    And the chart should show clear trend patterns
    And I should be able to identify:
      | Pattern Type      |
      | Improving trends  |
      | Declining trends  |
      | Stable periods    |
      | Irregular patterns |

  Scenario: View category-based supplement analysis
    Given I can see the supplements by category bar chart
    Then I should see bars representing different supplement categories
    And each bar should show the count of supplements in that category
    And categories should be clearly labeled on the x-axis
    And the y-axis should show the count scale

  Scenario: Handle empty or insufficient data
    Given I have minimal supplement tracking data
    When I visit the analytics dashboard
    Then I should see a message "No data available"
    And I should see guidance "Start tracking your supplements to see analytics and insights here"
    And I should see a call-to-action to begin tracking

  Scenario: Mobile analytics experience
    Given I am using a mobile device
    When I view the analytics dashboard
    Then the layout should be mobile-optimized
    And metric cards should stack vertically
    And charts should be touch-friendly and responsive
    And all text should be readable on small screens
    And filter controls should be easily accessible

  Scenario: Accessibility in analytics dashboard
    Given I am using assistive technology
    When I navigate the analytics dashboard
    Then all charts should have proper alt text descriptions
    And metric values should be announced clearly
    And I should be able to navigate using only the keyboard
    And all interactive elements should have proper focus indicators
    And screen readers should announce chart data meaningfully

  Scenario: Real-time data updates
    Given I am viewing the analytics dashboard
    When new supplement intake data is added
    Then the metrics should update automatically
    Or I should see a notification to refresh the data
    And the "last updated" timestamp should be visible

  Scenario: Performance with large datasets
    Given I have extensive supplement tracking history
    When I load the analytics dashboard
    Then the page should load within 3 seconds
    And charts should render smoothly without lag
    And filtering should be responsive
    And scrolling should be smooth

  Scenario: Data accuracy and consistency
    When I view analytics data
    Then the total intakes shown in metrics should match chart data
    And adherence percentages should be mathematically correct
    And all visualizations should show consistent information
    And date ranges should be accurately reflected across all components

  Scenario: Correlation insights
    Given I have been tracking multiple supplements
    When I view the insights section
    Then I should see correlations between supplements and adherence
    And I should see patterns in my supplement timing
    And I should receive recommendations based on my data
    And insights should be personalized to my tracking history

  Scenario: Comparative analysis
    When I view supplement distribution data
    Then I should be able to compare supplement usage
    And I should see which supplements I take most consistently
    And I should identify supplements with low adherence
    And I should see recommendations for improvement

  Scenario: Trend prediction and forecasting
    Given I have sufficient historical data
    When I view trend charts
    Then I should see projected trend lines where applicable
    And I should receive predictions about future adherence
    And I should see recommendations for maintaining or improving trends

  Scenario: Error handling in analytics
    Given there is a temporary data loading issue
    When I visit the analytics dashboard
    Then I should see a clear error message
    And I should have an option to retry loading data
    And the page should gracefully handle partial data loading
    And I should be able to continue using available features

  Scenario: Data privacy and security
    When I view my analytics data
    Then all data should be properly secured and private
    And I should only see my own supplement data
    And data transmission should be encrypted
    And I should have control over data export and sharing

  Scenario: Integration with supplement tracking
    When I view analytics and identify areas for improvement
    Then I should be able to navigate directly to supplement tracking
    And I should be able to add new supplements based on insights
    And I should be able to modify my supplement routine
    And changes should be reflected in future analytics
