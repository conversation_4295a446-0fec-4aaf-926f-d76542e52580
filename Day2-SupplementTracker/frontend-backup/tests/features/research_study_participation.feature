Feature: Research Study Participation
  As a supplement user
  I want to discover and participate in research studies
  So that I can contribute to evidence-based supplement research

  Background:
    Given I am logged in as a participant
    And there are available research protocols

  Scenario: Browse available research studies
    When I visit the research hub
    Then I should see a list of available research protocols
    And each protocol should display:
      | Information          |
      | Protocol title       |
      | Brief description    |
      | Study status         |
      | Duration             |
      | Participant count    |
      | Join button          |
    And I should see study statistics:
      | Statistic           |
      | Total protocols     |
      | Recruiting studies  |
      | Active studies      |
      | Completed studies   |

  Scenario: Search for specific research studies
    Given there are multiple research protocols available
    When I search for "Vitamin D"
    Then I should see only protocols related to Vitamin D
    And the search results should be highlighted

    When I clear the search
    Then I should see all available protocols again

  Scenario: Filter research studies by status
    Given there are protocols with different statuses
    When I filter by "recruiting" status
    Then I should see only recruiting protocols
    And each protocol should show "recruiting" status

    When I filter by "active" status
    Then I should see only active protocols
    And each protocol should show "active" status

    When I select "All Statuses"
    Then I should see protocols with all statuses

  Scenario: Join a research study
    Given there is a recruiting study "Omega-3 Brain Health Study"
    When I click "Join Study" for that protocol
    Then I should see a confirmation dialog
    And the dialog should explain study requirements

    When I confirm joining the study
    Then I should see a success message "Successfully joined Omega-3 Brain Health Study"
    And the join button should change to indicate I'm enrolled
    And I should receive study onboarding information

  Scenario: View detailed protocol information
    Given there is a protocol "Probiotics Gut Health Study"
    When I click on the protocol card
    Then I should be taken to the protocol details page
    And I should see comprehensive information:
      | Section                 |
      | Study overview          |
      | Objectives              |
      | Inclusion criteria      |
      | Exclusion criteria      |
      | Study timeline          |
      | Data collection methods |
      | Researcher information  |
      | Ethical considerations  |

  Scenario: Check eligibility for study participation
    Given there is a study with specific inclusion criteria
    When I view the study details
    Then I should see the inclusion criteria clearly listed
    And I should see the exclusion criteria clearly listed
    And there should be guidance on eligibility assessment

  Scenario: Handle study enrollment limits
    Given there is a study that is nearly full
    And the study has "48/50" participants
    When I view the study
    Then I should see the current participant count
    And I should still be able to join if spots are available

    Given the study becomes full with "50/50" participants
    When I refresh the page
    Then the join button should show "Study Full"
    And the button should be disabled
    And I should see information about waitlist options

  Scenario: Attempt to join study when not eligible
    Given I am already enrolled in a conflicting study
    When I try to join another study with exclusion criteria
    Then I should see an error message about eligibility
    And I should not be enrolled in the new study

  Scenario: View my study participations
    Given I am enrolled in multiple studies
    When I navigate to my participations
    Then I should see all my active studies
    And each study should show:
      | Information           |
      | Study name            |
      | Enrollment date       |
      | Study progress        |
      | Next required action  |
      | Data collection status |

  Scenario: Leave a research study
    Given I am enrolled in "Magnesium Sleep Study"
    When I choose to leave the study
    Then I should see a confirmation dialog
    And the dialog should explain consequences of leaving

    When I confirm leaving the study
    Then I should be removed from the study
    And I should see a confirmation message
    And the study should show as available to join again

  Scenario: Submit study data
    Given I am enrolled in an active study
    And it's time for data collection
    When I access the data collection form
    Then I should see the required data fields
    And the form should be user-friendly

    When I fill in the required data
    And I submit the form
    Then I should see a success confirmation
    And the data should be recorded in my study timeline

  Scenario: Handle study completion
    Given I have completed all requirements for a study
    When the study period ends
    Then I should receive a completion notification
    And I should see study results if available
    And I should have access to my personal data

  Scenario: Mobile study participation
    Given I am using a mobile device
    When I browse research studies
    Then the interface should be mobile-optimized
    And all study information should be easily readable
    And join buttons should be easily tappable

    When I join a study on mobile
    Then the enrollment process should work smoothly
    And I should receive mobile-friendly notifications

  Scenario: Accessibility in study participation
    Given I am using assistive technology
    When I browse research studies
    Then all study information should be accessible
    And join buttons should have proper labels
    And navigation should work with keyboard only

  Scenario: Handle participation errors gracefully
    Given there is a network connectivity issue
    When I try to join a study
    Then I should see a clear error message
    And I should be able to retry the action
    And my progress should not be lost

  Scenario: Study notification preferences
    Given I am enrolled in studies
    When I access notification settings
    Then I should be able to configure:
      | Notification Type        |
      | Data collection reminders |
      | Study updates            |
      | Completion notifications |
      | Research results         |

  Scenario: Privacy and data protection
    When I participate in studies
    Then my personal data should be protected
    And I should understand how my data is used
    And I should be able to access my data
    And I should be able to request data deletion

  Scenario: Study feedback and communication
    Given I am enrolled in a study
    When I have questions or concerns
    Then I should be able to contact the research team
    And I should receive timely responses
    And there should be clear communication channels

  Scenario: Multi-language support
    Given the platform supports multiple languages
    When I change my language preference
    Then all study information should be translated
    And the participation process should work in my language
    And data collection forms should be localized
