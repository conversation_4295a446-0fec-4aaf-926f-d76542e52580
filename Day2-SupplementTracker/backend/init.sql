-- Supplement Tracker Database Initialization
-- This script creates the basic database structure

-- Create database if it doesn't exist
-- (This is handled by the POSTGRES_DB environment variable)

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create basic tables for demonstration
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name VA<PERSON><PERSON><PERSON>(100),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS supplements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    brand VARCHAR(255),
    category VARCHAR(100),
    dosage VARCHAR(100),
    unit VARCHAR(50),
    frequency VARCHAR(50),
    time_of_day VARCHAR(50),
    with_food BOOLEAN DEFAULT FALSE,
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS supplement_intakes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    supplement_id UUID REFERENCES supplements(id) ON DELETE CASCADE,
    taken_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    dosage_taken VARCHAR(100),
    notes TEXT,
    effectiveness_rating INTEGER CHECK (effectiveness_rating >= 1 AND effectiveness_rating <= 5),
    side_effects TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS health_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    metric_type VARCHAR(100) NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    unit VARCHAR(50),
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_supplements_user_id ON supplements(user_id);
CREATE INDEX IF NOT EXISTS idx_supplements_category ON supplements(category);
CREATE INDEX IF NOT EXISTS idx_supplement_intakes_user_id ON supplement_intakes(user_id);
CREATE INDEX IF NOT EXISTS idx_supplement_intakes_supplement_id ON supplement_intakes(supplement_id);
CREATE INDEX IF NOT EXISTS idx_supplement_intakes_taken_at ON supplement_intakes(taken_at);
CREATE INDEX IF NOT EXISTS idx_health_metrics_user_id ON health_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_health_metrics_type ON health_metrics(metric_type);
CREATE INDEX IF NOT EXISTS idx_health_metrics_recorded_at ON health_metrics(recorded_at);

-- Insert sample data
INSERT INTO users (email, username, password_hash, first_name, last_name) VALUES
('<EMAIL>', 'demo', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'Demo', 'User')
ON CONFLICT (email) DO NOTHING;

-- Get the demo user ID for sample data
DO $$
DECLARE
    demo_user_id UUID;
BEGIN
    SELECT id INTO demo_user_id FROM users WHERE email = '<EMAIL>';
    
    IF demo_user_id IS NOT NULL THEN
        -- Insert sample supplements
        INSERT INTO supplements (user_id, name, brand, category, dosage, unit, frequency, time_of_day) VALUES
        (demo_user_id, 'Vitamin D3', 'Nature Made', 'vitamins', '2000', 'IU', 'daily', 'morning'),
        (demo_user_id, 'Omega-3', 'Nordic Naturals', 'omega3', '1000', 'mg', 'daily', 'morning'),
        (demo_user_id, 'Magnesium', 'NOW Foods', 'minerals', '400', 'mg', 'daily', 'evening'),
        (demo_user_id, 'Probiotics', 'Garden of Life', 'probiotics', '50', 'billion CFU', 'daily', 'morning'),
        (demo_user_id, 'Vitamin B12', 'Solgar', 'vitamins', '1000', 'mcg', 'daily', 'morning')
        ON CONFLICT DO NOTHING;
        
        -- Insert sample health metrics
        INSERT INTO health_metrics (user_id, metric_type, value, unit) VALUES
        (demo_user_id, 'weight', 70.5, 'kg'),
        (demo_user_id, 'heart_rate', 72, 'bpm'),
        (demo_user_id, 'sleep_hours', 7.5, 'hours'),
        (demo_user_id, 'steps', 8500, 'steps'),
        (demo_user_id, 'blood_pressure_systolic', 120, 'mmHg'),
        (demo_user_id, 'blood_pressure_diastolic', 80, 'mmHg')
        ON CONFLICT DO NOTHING;
    END IF;
END $$;
