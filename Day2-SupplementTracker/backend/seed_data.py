"""
Seed database with sample data for development and testing
"""

from sqlalchemy.orm import Session
from database import SessionLocal, User, Supplement, UserSupplement, ResearchStudy, IntakeLog
from datetime import datetime, timedelta
import hashlib

def create_sample_data():
    """Create sample data for development"""
    db = SessionLocal()
    
    try:
        # Check if data already exists
        if db.query(User).first():
            print("Sample data already exists, skipping...")
            return
        
        # Create sample user
        demo_user = User(
            email="<EMAIL>",
            username="demo_user",
            hashed_password=hashlib.sha256("demo123".encode()).hexdigest(),
            full_name="Demo User",
            is_active=True
        )
        db.add(demo_user)
        db.commit()
        db.refresh(demo_user)
        
        # Create sample supplements
        supplements_data = [
            {
                "name": "Vitamin D3",
                "brand": "Nature Made",
                "category": "Vitamins",
                "description": "Supports bone health and immune function",
                "default_dosage": "2000",
                "dosage_unit": "IU",
                "evidence_level": "high"
            },
            {
                "name": "Omega-3 Fish Oil",
                "brand": "Nordic Naturals",
                "category": "Fatty Acids",
                "description": "Supports heart and brain health",
                "default_dosage": "1000",
                "dosage_unit": "mg",
                "evidence_level": "high"
            },
            {
                "name": "Magnesium Glycinate",
                "brand": "Thorne",
                "category": "Minerals",
                "description": "Supports muscle and nerve function",
                "default_dosage": "400",
                "dosage_unit": "mg",
                "evidence_level": "medium"
            },
            {
                "name": "Vitamin B12",
                "brand": "Jarrow Formulas",
                "category": "Vitamins",
                "description": "Supports energy metabolism and nerve function",
                "default_dosage": "1000",
                "dosage_unit": "mcg",
                "evidence_level": "high"
            },
            {
                "name": "Probiotics",
                "brand": "Garden of Life",
                "category": "Probiotics",
                "description": "Supports digestive and immune health",
                "default_dosage": "50",
                "dosage_unit": "billion CFU",
                "evidence_level": "medium"
            },
            {
                "name": "Curcumin",
                "brand": "Life Extension",
                "category": "Herbs",
                "description": "Anti-inflammatory support",
                "default_dosage": "500",
                "dosage_unit": "mg",
                "evidence_level": "medium"
            }
        ]
        
        supplements = []
        for supp_data in supplements_data:
            supplement = Supplement(**supp_data)
            db.add(supplement)
            supplements.append(supplement)
        
        db.commit()
        
        # Create user supplements (user's personal schedule)
        user_supplements_data = [
            {
                "supplement_id": supplements[0].id,  # Vitamin D3
                "dosage": "2000 IU",
                "frequency": "daily",
                "time_of_day": "morning",
                "notes": "Take with breakfast"
            },
            {
                "supplement_id": supplements[1].id,  # Omega-3
                "dosage": "1000mg",
                "frequency": "daily",
                "time_of_day": "morning",
                "notes": "Take with breakfast"
            },
            {
                "supplement_id": supplements[2].id,  # Magnesium
                "dosage": "400mg",
                "frequency": "daily",
                "time_of_day": "evening",
                "notes": "Take before bed"
            }
        ]
        
        user_supplements = []
        for user_supp_data in user_supplements_data:
            user_supplement = UserSupplement(
                user_id=demo_user.id,
                **user_supp_data
            )
            db.add(user_supplement)
            user_supplements.append(user_supplement)
        
        db.commit()
        
        # Create sample intake logs for the past few days
        for i in range(5):  # Last 5 days
            date = datetime.now() - timedelta(days=i)
            
            for user_supp in user_supplements[:2]:  # Only for first 2 supplements
                intake_log = IntakeLog(
                    user_id=demo_user.id,
                    user_supplement_id=user_supp.id,
                    taken_at=date.replace(hour=8, minute=0, second=0),
                    dosage_taken=user_supp.dosage,
                    notes="Regular intake" if i > 0 else "Feeling good today"
                )
                db.add(intake_log)
        
        # Create sample research studies
        research_studies_data = [
            {
                "title": "Vitamin D3 and Sleep Quality Study",
                "description": "Investigating the relationship between Vitamin D3 supplementation and sleep quality metrics over 12 weeks.",
                "category": "Vitamins",
                "duration_weeks": 12,
                "max_participants": 200,
                "current_participants": 156,
                "status": "recruiting",
                "difficulty": "beginner",
                "compensation": "$50 Amazon gift card",
                "requirements": '["Age 18-65", "No current Vitamin D supplementation", "Sleep tracking device"]',
                "researcher_name": "Dr. Sarah Johnson",
                "institution": "Stanford University",
                "start_date": datetime.now() + timedelta(days=14),
                "estimated_completion": datetime.now() + timedelta(weeks=14),
                "rating": 4.8,
                "rating_count": 23
            },
            {
                "title": "Omega-3 Cognitive Performance Research",
                "description": "Examining the effects of high-dose omega-3 supplementation on cognitive performance and memory.",
                "category": "Fatty Acids",
                "duration_weeks": 16,
                "max_participants": 150,
                "current_participants": 89,
                "status": "recruiting",
                "difficulty": "intermediate",
                "requirements": '["Age 25-55", "No fish allergies", "Cognitive assessment participation"]',
                "researcher_name": "Dr. Michael Chen",
                "institution": "Harvard Medical School",
                "start_date": datetime.now() - timedelta(days=7),
                "estimated_completion": datetime.now() + timedelta(weeks=15),
                "rating": 4.9,
                "rating_count": 18
            },
            {
                "title": "Magnesium and Exercise Recovery",
                "description": "Study on magnesium supplementation effects on muscle recovery and exercise performance.",
                "category": "Minerals",
                "duration_weeks": 8,
                "max_participants": 100,
                "current_participants": 45,
                "status": "active",
                "difficulty": "intermediate",
                "requirements": '["Regular exercise routine", "Age 20-45", "No magnesium supplementation"]',
                "researcher_name": "Dr. Emily Rodriguez",
                "institution": "UCLA",
                "start_date": datetime.now() - timedelta(weeks=2),
                "estimated_completion": datetime.now() + timedelta(weeks=6),
                "rating": 4.7,
                "rating_count": 12
            },
            {
                "title": "Probiotic Gut Health Analysis",
                "description": "Comprehensive analysis of probiotic effects on gut microbiome diversity and digestive health.",
                "category": "Probiotics",
                "duration_weeks": 10,
                "max_participants": 120,
                "current_participants": 78,
                "status": "recruiting",
                "difficulty": "beginner",
                "compensation": "$75 gift card",
                "requirements": '["Age 21-60", "No antibiotic use in past 3 months", "Willing to provide samples"]',
                "researcher_name": "Dr. Lisa Park",
                "institution": "Mayo Clinic",
                "start_date": datetime.now() + timedelta(days=21),
                "estimated_completion": datetime.now() + timedelta(weeks=12),
                "rating": 4.6,
                "rating_count": 8
            }
        ]
        
        for study_data in research_studies_data:
            study = ResearchStudy(**study_data)
            db.add(study)
        
        db.commit()
        
        print("✅ Sample data created successfully!")
        print(f"Created user: {demo_user.email}")
        print(f"Created {len(supplements)} supplements")
        print(f"Created {len(user_supplements)} user supplements")
        print(f"Created {len(research_studies_data)} research studies")
        print("Database is ready for development!")
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_sample_data()
