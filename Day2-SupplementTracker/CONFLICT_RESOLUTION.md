# Git Conflict Resolution Guide

## 🎯 **Quick Resolution Commands**

### If Conflicts Occur During Merge

1. **Check which files have conflicts:**
   ```bash
   git status
   git diff --name-only --diff-filter=U
   ```

2. **Resolve conflicts by priority:**

   **Documentation files (KEEP OURS):**
   ```bash
   # Keep all our documentation work
   git checkout --ours docs/
   git add docs/
   ```

   **Shell.nix (MERGE BOTH):**
   ```bash
   # Edit manually to include both sets of dependencies
   nano shell.nix
   # Add both our doc dependencies AND remote changes
   git add shell.nix
   ```

   **README.md (MERGE BOTH):**
   ```bash
   # Edit to include both changes
   nano README.md
   git add README.md
   ```

   **Other files (PREFER THEIRS):**
   ```bash
   git checkout --theirs <filename>
   git add <filename>
   ```

3. **Complete the merge:**
   ```bash
   git commit
   ```

## 🚀 **Simple Execution Plan**

### Option 1: Automated Merge (Recommended)
```bash
# Run the comprehensive merge script
./EXECUTE_MERGE.sh
```

### Option 2: Manual Step-by-Step
```bash
# 1. Create backup
git branch backup-docs-$(date +%Y%m%d-%H%M%S)

# 2. Fetch remote (requires SSH passphrase)
git fetch origin

# 3. Merge with strategy
git merge origin/master

# 4. If conflicts, resolve and commit
# (Follow conflict resolution above)

# 5. Push result
git push origin master
```

### Option 3: Nuclear Option (If All Else Fails)
```bash
# 1. Create patch of our work
git format-patch origin/master..HEAD

# 2. Reset to remote
git reset --hard origin/master

# 3. Apply our documentation patches
git am *.patch

# 4. Push
git push origin master
```

## 🛡️ **Safety Net**

If anything goes wrong:
```bash
# Abort ongoing merge
git merge --abort

# Or restore from backup
git checkout backup-docs-YYYYMMDD-HHMMSS
git branch -D master
git checkout -b master
```

## ✅ **Success Validation**

After merge completion:
```bash
# Test documentation
cd docs && ./build.sh html && python test_docs.py

# Test application
nix-shell --run "python -m app.main --help"

# Check git status
git log --oneline -5
git status
```

## 🎉 **Final Push**

Once everything is validated:
```bash
git push origin master
# Enter SSH passphrase when prompted
```

---

**The merge strategy prioritizes preserving our comprehensive documentation work while safely integrating remote changes. All our documentation files and infrastructure will be preserved.**
