{ pkgs ? import <nixpkgs> { config.allowUnfree = true; } }:

let
  python = pkgs.python311;
  pythonPackages = python.pkgs;

  # Define Python environment with available dependencies
  pythonEnv = python.withPackages (ps: with ps; [
    # Core FastAPI stack
    fastapi
    uvicorn
    pydantic

    # Database
    sqlalchemy
    alembic
    asyncpg
    psycopg2

    # Authentication & Security
    passlib
    cryptography

    # Caching & Background Tasks
    redis
    celery

    # HTTP Client
    httpx

    # Data Processing & ML
    pandas
    numpy
    scikit-learn

    # Development Dependencies
    pytest
    pytest-asyncio
    pytest-cov
    black
    flake8
    mypy

    # Documentation
    sphinx
    sphinx-rtd-theme
    sphinx-autodoc-typehints
    myst-parser

    # Additional tools
    pip
    setuptools
    wheel

    # Security tools
    # pip-audit  # Uncomment when available in nixpkgs
  ]);
in

pkgs.mkShell {
  name = "supplement-tracker-dev";

  buildInputs = with pkgs; [
    # Python environment with all packages
    pythonEnv

    # Additional development tools not available in Python packages
    pre-commit
    
    # Development tools
    git
    curl
    jq
    
    # System dependencies for Python packages
    gcc
    pkg-config
    libffi
    openssl
    zlib

    # Database tools (optional - can be installed separately)
    # postgresql_15
    # redis

    # Search engine (optional - can be installed separately)
    # elasticsearch7

    # Optional: Docker for containerization
    # docker
    # docker-compose
  ];
  
  shellHook = ''
    echo "🚀 Supplement Tracker Development Environment"
    echo "============================================="
    echo ""
    echo "Python version: $(python --version)"
    echo "Pip version: $(pip --version)"
    echo ""
    echo "Available services (install separately if needed):"
    echo "  - PostgreSQL: nix-shell -p postgresql"
    echo "  - Redis: nix-shell -p redis"
    echo "  - Elasticsearch: NIXPKGS_ALLOW_UNFREE=1 nix-shell -p elasticsearch7"
    echo ""
    echo "Development commands:"
    echo "  make dev         - Start development server"
    echo "  make test        - Run tests"
    echo "  make format      - Format code"
    echo "  make lint        - Run linting checks"
    echo ""
    echo "Security commands:"
    echo "  nix-shell -p pip-audit --run 'pip-audit'  - Scan for vulnerabilities"
    echo ""
    echo "To get started:"
    echo "  1. cp .env.example .env"
    echo "  2. Edit .env with your configuration"
    echo "  3. make dev"
    echo ""
    echo "All Python dependencies are managed by Nix!"
    echo ""

    # Set up Python path
    export PYTHONPATH="$PWD:$PYTHONPATH"

    # Install missing packages to a local directory that Nix can access
    # Note: Versions below include security fixes for known vulnerabilities
    export PIP_PREFIX="$PWD/.nix-pip"
    export PYTHONPATH="$PIP_PREFIX/lib/python3.11/site-packages:$PYTHONPATH"
    export PATH="$PIP_PREFIX/bin:$PATH"

    # Create pip directory if it doesn't exist
    mkdir -p "$PIP_PREFIX/lib/python3.11/site-packages"

    # Install missing packages if not already installed
    if [ ! -f "$PIP_PREFIX/.installed" ]; then
      echo "📦 Installing missing Python packages..."
      pip install --prefix="$PIP_PREFIX" \
        pydantic-settings==2.7.0 \
        "python-jose[cryptography]>=3.5.0" \
        python-multipart==0.0.20 \
        "requests>=2.32.4" \
        "urllib3>=2.5.0" \
        structlog==25.4.0 \
        email-validator==2.1.1 \
        aiofiles==24.1.0 \
        jinja2==3.1.4
      touch "$PIP_PREFIX/.installed"
      echo "✅ Missing packages installed to .nix-pip/ (with security fixes)"
    fi

    # Set up environment variables for development
    export SUPPLEMENT_TRACKER_DEBUG=true
    export SUPPLEMENT_TRACKER_TESTING=false

    # Create local directories if they don't exist
    mkdir -p logs uploads temp

    # Install pre-commit hooks if not already installed
    if [ ! -f .git/hooks/pre-commit ]; then
      echo "Installing pre-commit hooks..."
      pre-commit install > /dev/null 2>&1 || true
    fi
    
    echo "Environment ready! 🎉"
  '';
  
  # Environment variables
  SUPPLEMENT_TRACKER_DEBUG = "true";
  SUPPLEMENT_TRACKER_TESTING = "false";
  
  # Python-specific environment variables
  PYTHONPATH = ".";
  PYTHONDONTWRITEBYTECODE = "1";
  PYTHONUNBUFFERED = "1";
  
  # Development-specific settings
  FLASK_ENV = "development";
  FASTAPI_ENV = "development";
}
