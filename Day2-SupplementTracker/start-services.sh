#!/bin/bash

# Supplement Tracker - Service Startup Script
# This script starts all services with Traefik routing

set -e

echo "🚀 Starting Supplement Tracker Services with Traefik..."
echo "=================================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker compose is available
if ! docker compose version > /dev/null 2>&1; then
    echo "❌ docker compose is not available. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p backend/media
mkdir -p backend/staticfiles
mkdir -p logs

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker compose -f docker-compose.traefik.yml down --remove-orphans

# Pull latest images
echo "📥 Pulling latest images..."
docker compose -f docker-compose.traefik.yml pull

# Build and start services
echo "🔨 Building and starting services..."
docker compose -f docker-compose.traefik.yml up -d --build

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check service health
echo "🔍 Checking service health..."

services=(
    "http://app.pills.localhost:Frontend Application"
    "http://api.pills.localhost:Backend API"
    "http://traefik.pills.localhost:Traefik Dashboard"
    "http://db.pills.localhost:Database Admin"
    "http://redis.pills.localhost:Redis Commander"
    "http://static.pills.localhost/health:Static Files Server"
)

echo ""
echo "🌐 Service URLs:"
echo "================"

for service in "${services[@]}"; do
    url=$(echo $service | cut -d: -f1-2)
    name=$(echo $service | cut -d: -f3-)
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "200\|302\|401"; then
        echo "✅ $name: $url"
    else
        echo "⚠️  $name: $url (may still be starting up)"
    fi
done

echo ""
echo "📊 Container Status:"
echo "==================="
docker compose -f docker-compose.traefik.yml ps

echo ""
echo "📝 Useful Commands:"
echo "==================="
echo "View logs:           docker compose -f docker-compose.traefik.yml logs -f [service]"
echo "Stop services:       docker compose -f docker-compose.traefik.yml down"
echo "Restart service:     docker compose -f docker-compose.traefik.yml restart [service]"
echo "Shell into service:  docker compose -f docker-compose.traefik.yml exec [service] /bin/bash"
echo ""

echo "🎉 Supplement Tracker is now running!"
echo ""
echo "🌟 Main Application: http://app.pills.localhost"
echo "🔧 Admin Panel:      http://api.pills.localhost/admin/"
echo "📊 Traefik:          http://traefik.pills.localhost"
echo "🗄️  Database:        http://db.pills.localhost"
echo "🔴 Redis:            http://redis.pills.localhost"
echo ""
echo "💡 Make sure to add these entries to your /etc/hosts file:"
echo "127.0.0.1 app.pills.localhost"
echo "127.0.0.1 api.pills.localhost"
echo "127.0.0.1 traefik.pills.localhost"
echo "127.0.0.1 db.pills.localhost"
echo "127.0.0.1 redis.pills.localhost"
echo "127.0.0.1 static.pills.localhost"
