{
  description = "Project Chronos - AMD GPU Development Environment";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
    flake-utils.url = "github:numtide/flake-utils";
  };

  outputs = { self, nixpkgs, flake-utils }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        pkgs = nixpkgs.legacyPackages.${system};
      in
      {
        # Development shell
        devShells.default = pkgs.mkShell {
          name = "chronos-amd-gpu";
          
          buildInputs = with pkgs; [
            # Core tools
            git curl wget jq
            
            # Docker
            docker docker-compose
            
            # Python development
            python310
            python310Packages.pip
            python310Packages.virtualenv
            
            # AMD GPU tools
            rocminfo rocm-smi hip
            rocm-opencl-icd rocm-opencl-runtime
            
            # Audio processing
            ffmpeg sox alsa-utils
            
            # Build tools
            gcc cmake pkg-config
            
            # System libraries
            zlib libffi openssl
            
            # Graphics
            mesa vulkan-loader vulkan-tools
          ];
          
          shellHook = ''
            echo "🔥 Project Chronos AMD GPU Development (Flake)"
            echo "=============================================="
            
            # AMD GPU environment
            export ROC_ENABLE_PRE_VEGA=1
            export HSA_OVERRIDE_GFX_VERSION=11.0.0
            export PYTORCH_ROCM_ARCH=gfx1100,gfx1101,gfx1102
            export HIP_VISIBLE_DEVICES=0
            
            # Development paths
            export PYTHONPATH="$PWD:$PYTHONPATH"
            export PATH="$PWD/scripts:$PATH"
            
            echo "✅ AMD GPU development environment ready!"
          '';
        };
        
        # NixOS module for system configuration
        nixosModules.amd-gpu = { config, lib, pkgs, ... }: {
          # AMD GPU hardware support
          hardware.opengl = {
            enable = true;
            driSupport = true;
            driSupport32Bit = true;
            extraPackages = with pkgs; [
              rocm-opencl-icd
              rocm-opencl-runtime
              amdvlk
            ];
          };
          
          # Docker with GPU support
          virtualisation.docker = {
            enable = true;
            enableOnBoot = true;
          };
          
          # User groups
          users.groups.render = {};
          
          # Environment variables
          environment.variables = {
            ROC_ENABLE_PRE_VEGA = "1";
            HSA_OVERRIDE_GFX_VERSION = "11.0.0";
            PYTORCH_ROCM_ARCH = "gfx1100;gfx1101;gfx1102";
          };
          
          # Kernel modules
          boot.kernelModules = [ "amdgpu" ];
          
          # Udev rules
          services.udev.extraRules = ''
            SUBSYSTEM=="drm", KERNEL=="card*", GROUP="video", MODE="0664"
            SUBSYSTEM=="drm", KERNEL=="renderD*", GROUP="render", MODE="0664"
            SUBSYSTEM=="kfd", KERNEL=="kfd", TAG+="uaccess", GROUP="render", MODE="0666"
          '';
        };
      }
    );
}
