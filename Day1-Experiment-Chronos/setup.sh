#!/bin/bash

# 🚀 Project Chronos + Speechbot Automated Setup Script
# The World's Most Advanced ADHD-Optimized Voice Assistant Platform

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Emojis for better UX
ROCKET="🚀"
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"
BRAIN="🧠"
HEART="💙"

echo -e "${PURPLE}${ROCKET} Project Chronos + Speechbot Setup${NC}"
echo -e "${CYAN}The World's Most Advanced ADHD-Optimized Voice Assistant Platform${NC}"
echo "=================================================================="

# Function to print colored output
print_status() {
    echo -e "${GREEN}${CHECK} $1${NC}"
}

print_error() {
    echo -e "${RED}${CROSS} $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}${WARNING} $1${NC}"
}

print_info() {
    echo -e "${BLUE}${INFO} $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check system requirements
check_system_requirements() {
    print_info "Checking system requirements..."
    
    # Check OS
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_status "Operating System: Linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        print_status "Operating System: macOS"
    else
        print_warning "Operating System: $OSTYPE (may have compatibility issues)"
    fi
    
    # Check RAM
    if command_exists free; then
        RAM_GB=$(free -g | awk '/^Mem:/{print $2}')
        if [ "$RAM_GB" -ge 8 ]; then
            print_status "RAM: ${RAM_GB}GB (sufficient)"
        else
            print_warning "RAM: ${RAM_GB}GB (8GB+ recommended for optimal performance)"
        fi
    fi
    
    # Check disk space
    if command_exists df; then
        DISK_GB=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
        if [ "$DISK_GB" -ge 50 ]; then
            print_status "Disk Space: ${DISK_GB}GB available (sufficient)"
        else
            print_warning "Disk Space: ${DISK_GB}GB available (50GB+ recommended)"
        fi
    fi
    
    # Check for NVIDIA GPU
    if command_exists nvidia-smi; then
        GPU_INFO=$(nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits | head -1)
        print_status "NVIDIA GPU detected: $GPU_INFO"
    else
        print_warning "NVIDIA GPU not detected (CPU-only mode will be slower)"
    fi
}

# Function to install Docker
install_docker() {
    if command_exists docker; then
        print_status "Docker is already installed"
        docker --version
    else
        print_info "Installing Docker..."
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh
        sudo usermod -aG docker $USER
        rm get-docker.sh
        print_status "Docker installed successfully"
        print_warning "Please log out and back in for Docker group changes to take effect"
    fi
}

# Function to install Docker Compose
install_docker_compose() {
    if command_exists docker-compose; then
        print_status "Docker Compose is already installed"
        docker-compose --version
    else
        print_info "Installing Docker Compose..."
        sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        sudo chmod +x /usr/local/bin/docker-compose
        print_status "Docker Compose installed successfully"
    fi
}

# Function to install NVIDIA Container Toolkit
install_nvidia_toolkit() {
    if command_exists nvidia-smi; then
        print_info "Installing NVIDIA Container Toolkit..."
        
        # Add NVIDIA package repository
        distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
        curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
        curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
        
        # Install nvidia-container-toolkit
        sudo apt-get update
        sudo apt-get install -y nvidia-container-toolkit
        sudo systemctl restart docker
        
        print_status "NVIDIA Container Toolkit installed successfully"
    else
        print_info "Skipping NVIDIA Container Toolkit (no NVIDIA GPU detected)"
    fi
}

# Function to setup environment file
setup_environment() {
    print_info "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_status "Created .env file from template"
        else
            print_info "Creating default .env file..."
            cat > .env << EOF
# Database Configuration
DATABASE_URL=postgresql+asyncpg://chronos:chronos_dev@postgres:5432/chronos
POSTGRES_DB=chronos
POSTGRES_USER=chronos
POSTGRES_PASSWORD=chronos_dev

# Security
JWT_SECRET_KEY=$(openssl rand -base64 32)
SECRET_KEY=$(openssl rand -base64 32)

# Redis
REDIS_URL=redis://redis:6379/0

# Domain Configuration
DOMAIN=autism.localhost

# CORS Origins
CORS_ORIGINS=http://localhost:3000,http://chronos.autism.localhost:8090

# Speechbot Configuration
SPEECHBOT_URL=http://speechbot:8001

# Optional: Hugging Face Token (for model downloads)
# HF_TOKEN=your_huggingface_token_here

# Optional: Email Configuration
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password

# Development Settings
DEBUG=true
LOG_LEVEL=INFO
ENVIRONMENT=development
EOF
            print_status "Created default .env file"
        fi
    else
        print_status ".env file already exists"
    fi
    
    print_warning "Please review and update the .env file with your specific configuration"
    print_info "Important: Change the default passwords and secrets for production use!"
}

# Function to setup hosts file for local development
setup_hosts() {
    print_info "Setting up local domain resolution..."
    
    HOSTS_ENTRIES=(
        "127.0.0.1 chronos.autism.localhost"
        "127.0.0.1 api.autism.localhost"
        "127.0.0.1 speechbot.autism.localhost"
        "127.0.0.1 grafana.autism.localhost"
        "127.0.0.1 prometheus.autism.localhost"
        "127.0.0.1 mail.autism.localhost"
        "127.0.0.1 minio.autism.localhost"
        "127.0.0.1 traefik.autism.localhost"
    )
    
    for entry in "${HOSTS_ENTRIES[@]}"; do
        if ! grep -q "$entry" /etc/hosts; then
            echo "$entry" | sudo tee -a /etc/hosts > /dev/null
            print_status "Added: $entry"
        fi
    done
    
    print_status "Local domain resolution configured"
}

# Function to pull Docker images
pull_images() {
    print_info "Pulling Docker images (this may take a while)..."
    
    if docker-compose pull; then
        print_status "Docker images pulled successfully"
    else
        print_warning "Some images failed to pull, but continuing with build..."
    fi
}

# Function to build and start services
start_services() {
    print_info "Building and starting services..."
    
    # Build custom images
    docker-compose build
    
    # Start services
    docker-compose up -d
    
    print_status "Services started successfully"
}

# Function to wait for services to be ready
wait_for_services() {
    print_info "Waiting for services to be ready..."
    
    # Wait for database
    print_info "Waiting for database..."
    timeout=60
    while ! docker-compose exec -T postgres pg_isready -U chronos > /dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            print_error "Database failed to start within 60 seconds"
            return 1
        fi
    done
    print_status "Database is ready"
    
    # Wait for API
    print_info "Waiting for API..."
    timeout=60
    while ! curl -f -s http://localhost:8000/health > /dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            print_error "API failed to start within 60 seconds"
            return 1
        fi
    done
    print_status "API is ready"
    
    # Wait for Speechbot
    print_info "Waiting for Speechbot..."
    timeout=120  # Speechbot takes longer due to model loading
    while ! curl -f -s http://localhost:8001/health > /dev/null 2>&1; do
        sleep 5
        timeout=$((timeout - 5))
        if [ $timeout -le 0 ]; then
            print_warning "Speechbot is taking longer than expected (this is normal for first startup)"
            break
        fi
    done
    
    if curl -f -s http://localhost:8001/health > /dev/null 2>&1; then
        print_status "Speechbot is ready"
    else
        print_warning "Speechbot may still be initializing (check logs with: docker-compose logs speechbot)"
    fi
}

# Function to initialize database
initialize_database() {
    print_info "Initializing database schema..."
    
    if docker-compose exec -T speechbot python migrations/create_tables.py; then
        print_status "Database schema initialized successfully"
    else
        print_warning "Database initialization may have failed (check logs)"
    fi
}

# Function to run health checks
run_health_checks() {
    print_info "Running health checks..."
    
    # Check service status
    docker-compose ps
    
    # Test API health
    if curl -f -s http://chronos.autism.localhost:8090/api/health > /dev/null 2>&1; then
        print_status "Chronos API is healthy"
    else
        print_warning "Chronos API health check failed"
    fi
    
    # Test Speechbot health
    if curl -f -s http://speechbot.autism.localhost:8090/health > /dev/null 2>&1; then
        print_status "Speechbot is healthy"
    else
        print_warning "Speechbot health check failed"
    fi
    
    # Test UI
    if curl -f -s http://chronos.autism.localhost:8090 > /dev/null 2>&1; then
        print_status "Chronos UI is accessible"
    else
        print_warning "Chronos UI accessibility check failed"
    fi
}

# Function to display access information
show_access_info() {
    echo ""
    echo -e "${PURPLE}${ROCKET} Deployment Complete!${NC}"
    echo "=================================================================="
    echo -e "${GREEN}${HEART} Project Chronos + Speechbot is now running!${NC}"
    echo ""
    echo -e "${CYAN}${BRAIN} Access your ADHD-optimized platform:${NC}"
    echo ""
    echo -e "${BLUE}🏠 Main Platform:${NC}      http://chronos.autism.localhost:8090"
    echo -e "${BLUE}🎭 Speechbot API:${NC}      http://speechbot.autism.localhost:8090"
    echo -e "${BLUE}📊 API Documentation:${NC}  http://api.autism.localhost:8090/docs"
    echo -e "${BLUE}📈 Grafana Dashboard:${NC}  http://grafana.autism.localhost:8090"
    echo -e "${BLUE}🔍 Prometheus:${NC}         http://prometheus.autism.localhost:8090"
    echo -e "${BLUE}📧 Email Testing:${NC}      http://mail.autism.localhost:8090"
    echo -e "${BLUE}🗄️ MinIO Storage:${NC}      http://minio.autism.localhost:8090"
    echo -e "${BLUE}🔀 Traefik Dashboard:${NC}  http://traefik.autism.localhost:8091"
    echo ""
    echo -e "${YELLOW}${INFO} Default Credentials:${NC}"
    echo "  Grafana: admin / admin"
    echo "  MinIO: minioadmin / minioadmin"
    echo ""
    echo -e "${GREEN}${CHECK} Next Steps:${NC}"
    echo "  1. Visit the main platform and create your first user account"
    echo "  2. Explore Speechbot features and create voice profiles"
    echo "  3. Try the ADHD-optimized voice modes and body doubling"
    echo "  4. Check the deployment guide for advanced configuration"
    echo ""
    echo -e "${CYAN}${INFO} Useful Commands:${NC}"
    echo "  View logs:           docker-compose logs -f"
    echo "  Stop services:       docker-compose down"
    echo "  Restart services:    docker-compose restart"
    echo "  Update platform:     git pull && docker-compose up -d --build"
    echo ""
    echo -e "${PURPLE}🌟 Welcome to the future of ADHD assistive technology!${NC}"
}

# Main execution
main() {
    echo -e "${CYAN}Starting automated setup...${NC}"
    echo ""
    
    # Check if running as root
    if [ "$EUID" -eq 0 ]; then
        print_error "Please do not run this script as root"
        exit 1
    fi
    
    # System requirements check
    check_system_requirements
    echo ""
    
    # Install dependencies
    print_info "Installing dependencies..."
    install_docker
    install_docker_compose
    install_nvidia_toolkit
    echo ""
    
    # Setup configuration
    setup_environment
    setup_hosts
    echo ""
    
    # Deploy platform
    pull_images
    start_services
    echo ""
    
    # Wait for services and initialize
    wait_for_services
    initialize_database
    echo ""
    
    # Final checks
    run_health_checks
    echo ""
    
    # Show access information
    show_access_info
}

# Run main function
main "$@"
