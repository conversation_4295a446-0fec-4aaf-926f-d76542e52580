-- Chronos Database Initialization Script
-- This script sets up the initial database schema for the Chronos ADHD task management system

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types for ADHD-specific fields
CREATE TYPE focus_mode AS ENUM ('normal', 'deep_work', 'creative', 'administrative', 'break', 'overwhelmed');
CREATE TYPE emotional_state AS ENUM ('neutral', 'excited', 'calm', 'anxious', 'frustrated', 'motivated');
CREATE TYPE task_priority AS ENUM ('low', 'medium', 'high', 'urgent');
CREATE TYPE session_type AS ENUM ('pomodoro', 'deep_work', 'creative', 'break', 'custom');

-- Users table with ADHD profile
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VA<PERSON>HAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- ADHD profiles table
CREATE TABLE adhd_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    energy_level INTEGER DEFAULT 5 CHECK (energy_level >= 1 AND energy_level <= 10),
    cognitive_load DECIMAL(3,2) DEFAULT 0.3 CHECK (cognitive_load >= 0 AND cognitive_load <= 1),
    focus_mode focus_mode DEFAULT 'normal',
    emotional_state emotional_state DEFAULT 'neutral',
    stress_level DECIMAL(3,2) DEFAULT 0.3 CHECK (stress_level >= 0 AND stress_level <= 1),
    attention_span INTEGER DEFAULT 25, -- minutes
    hyperactivity_level INTEGER DEFAULT 5 CHECK (hyperactivity_level >= 1 AND hyperactivity_level <= 10),
    sensory_sensitivity_level INTEGER DEFAULT 5 CHECK (sensory_sensitivity_level >= 1 AND sensory_sensitivity_level <= 10),
    executive_function_score INTEGER DEFAULT 5 CHECK (executive_function_score >= 1 AND executive_function_score <= 10),
    motivation_level INTEGER DEFAULT 5 CHECK (motivation_level >= 1 AND motivation_level <= 10),
    anxiety_level INTEGER DEFAULT 5 CHECK (anxiety_level >= 1 AND anxiety_level <= 10),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tasks table with ADHD-specific fields
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    completed BOOLEAN DEFAULT false,
    priority task_priority DEFAULT 'medium',
    due_date TIMESTAMP WITH TIME ZONE,
    estimated_duration INTEGER, -- minutes
    actual_duration INTEGER, -- minutes
    energy_required INTEGER DEFAULT 5 CHECK (energy_required >= 1 AND energy_required <= 10),
    cognitive_load_required DECIMAL(3,2) DEFAULT 0.5 CHECK (cognitive_load_required >= 0 AND cognitive_load_required <= 1),
    tags TEXT[], -- Array of tags
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Focus sessions table
CREATE TABLE focus_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_type session_type DEFAULT 'pomodoro',
    planned_duration INTEGER NOT NULL, -- minutes
    actual_duration INTEGER, -- minutes
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP WITH TIME ZONE,
    completed BOOLEAN DEFAULT false,
    paused_duration INTEGER DEFAULT 0, -- total paused time in minutes
    energy_before INTEGER CHECK (energy_before >= 1 AND energy_before <= 10),
    energy_after INTEGER CHECK (energy_after >= 1 AND energy_after <= 10),
    cognitive_load_before DECIMAL(3,2) CHECK (cognitive_load_before >= 0 AND cognitive_load_before <= 1),
    cognitive_load_after DECIMAL(3,2) CHECK (cognitive_load_after >= 0 AND cognitive_load_after <= 1),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Gamification table for achievements and XP
CREATE TABLE user_gamification (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    level INTEGER DEFAULT 1,
    experience_points INTEGER DEFAULT 0,
    total_tasks_completed INTEGER DEFAULT 0,
    total_focus_sessions INTEGER DEFAULT 0,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    achievements JSONB DEFAULT '[]'::jsonb,
    last_activity_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ADHD state history for analytics
CREATE TABLE adhd_state_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    energy_level INTEGER CHECK (energy_level >= 1 AND energy_level <= 10),
    cognitive_load DECIMAL(3,2) CHECK (cognitive_load >= 0 AND cognitive_load <= 1),
    focus_mode focus_mode,
    emotional_state emotional_state,
    stress_level DECIMAL(3,2) CHECK (stress_level >= 0 AND stress_level <= 1),
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User preferences table
CREATE TABLE user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    theme VARCHAR(20) DEFAULT 'auto',
    notifications_enabled BOOLEAN DEFAULT true,
    sound_enabled BOOLEAN DEFAULT true,
    break_reminders_enabled BOOLEAN DEFAULT true,
    break_interval INTEGER DEFAULT 25, -- minutes
    daily_goal_tasks INTEGER DEFAULT 3,
    daily_goal_focus_time INTEGER DEFAULT 120, -- minutes
    timezone VARCHAR(50) DEFAULT 'UTC',
    language VARCHAR(10) DEFAULT 'en',
    accessibility_settings JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_adhd_profiles_user_id ON adhd_profiles(user_id);
CREATE INDEX idx_tasks_user_id ON tasks(user_id);
CREATE INDEX idx_tasks_completed ON tasks(completed);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);
CREATE INDEX idx_tasks_priority ON tasks(priority);
CREATE INDEX idx_focus_sessions_user_id ON focus_sessions(user_id);
CREATE INDEX idx_focus_sessions_started_at ON focus_sessions(started_at);
CREATE INDEX idx_gamification_user_id ON user_gamification(user_id);
CREATE INDEX idx_adhd_history_user_id ON adhd_state_history(user_id);
CREATE INDEX idx_adhd_history_recorded_at ON adhd_state_history(recorded_at);
CREATE INDEX idx_preferences_user_id ON user_preferences(user_id);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_gamification_updated_at BEFORE UPDATE ON user_gamification
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_preferences_updated_at BEFORE UPDATE ON user_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default ADHD profile for new users
CREATE OR REPLACE FUNCTION create_default_adhd_profile()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO adhd_profiles (user_id) VALUES (NEW.id);
    INSERT INTO user_gamification (user_id) VALUES (NEW.id);
    INSERT INTO user_preferences (user_id) VALUES (NEW.id);
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER create_user_defaults AFTER INSERT ON users
    FOR EACH ROW EXECUTE FUNCTION create_default_adhd_profile();

-- Sample data for development
INSERT INTO users (email, name, password_hash) VALUES 
('<EMAIL>', 'Demo User', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS'); -- password: demo123

-- The trigger will automatically create ADHD profile, gamification, and preferences for the demo user

COMMIT;
