"""
Pydantic schemas for Project Chronos.

This package contains all Pydantic schemas for request/response validation
and serialization in the ADHD-focused productivity application, with
ADHD-specific field patterns and validation.
"""

from .task import (
    TaskBase,
    TaskCreate,
    TaskUpdate,
    TaskResponse,
    TaskChunkRequest,
    TaskJarRequest,
    TaskFilterRequest,
)

from chronos.app.schemas.focus import (
    FocusSessionCreate,
    FocusSessionUpdate,
    FocusSessionResponse,
    FocusSessionInDB,
    TimerState,
    FocusAnalytics,
    FocusModeCreate,
    FocusModeResponse,
)

from chronos.app.schemas.body_doubling import (
    BodyDoublingSessionBase,
    BodyDoublingSessionCreate,
    BodyDoublingSessionUpdate,
    BodyDoublingSessionResponse,
    BodyDoublingParticipantBase,
    BodyDoublingParticipantCreate,
    BodyDoublingParticipantUpdate,
    BodyDoublingParticipantResponse,
    SessionMessageBase,
    SessionMessageCreate,
    SessionMessageResponse,
    GroupFocusRequest,
    GroupFocusResponse,
    TaskProgressUpdate,
    EncouragementMessage,
    WebSocketMessage,
    SessionListResponse,
    ParticipantListResponse,
    SessionStatsResponse,
)

__all__ = [
    # Task management schemas (Agent 3)
    "TaskBase",
    "TaskCreate",
    "TaskUpdate",
    "TaskResponse",
    "TaskChunkRequest",
    "TaskJarRequest",
    "TaskFilterRequest",

    # Focus session schemas (Agent 5)
    "FocusSessionCreate",
    "FocusSessionUpdate",
    "FocusSessionResponse",
    "FocusSessionInDB",
    "TimerState",
    "FocusAnalytics",
    "FocusModeCreate",
    "FocusModeResponse",

    # Body doubling session schemas (Agent 6)
    "BodyDoublingSessionBase",
    "BodyDoublingSessionCreate",
    "BodyDoublingSessionUpdate",
    "BodyDoublingSessionResponse",

    # Body doubling participant schemas
    "BodyDoublingParticipantBase",
    "BodyDoublingParticipantCreate",
    "BodyDoublingParticipantUpdate",
    "BodyDoublingParticipantResponse",

    # Session message schemas
    "SessionMessageBase",
    "SessionMessageCreate",
    "SessionMessageResponse",

    # Group focus schemas
    "GroupFocusRequest",
    "GroupFocusResponse",

    # Real-time communication schemas
    "TaskProgressUpdate",
    "EncouragementMessage",
    "WebSocketMessage",

    # List and stats schemas
    "SessionListResponse",
    "ParticipantListResponse",
    "SessionStatsResponse",
]
