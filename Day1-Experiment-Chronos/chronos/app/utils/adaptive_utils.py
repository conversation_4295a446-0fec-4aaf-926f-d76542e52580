"""
Adaptive utility functions for ADHD-optimized task management.

This module provides scoring algorithms, energy level matching,
and context-aware task prioritization for users with ADHD.
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from chronos.app.models.task import Task


def calculate_task_score(
    task: Task,
    current_energy: Optional[str] = None,
    current_time: Optional[datetime] = None,
    user_context: Optional[List[str]] = None
) -> float:
    """
    Calculate adaptive score for task prioritization.
    
    This scoring algorithm considers ADHD-specific factors like energy levels,
    urgency, context matching, and task characteristics to provide intelligent
    task prioritization that works with the ADHD brain.
    
    Args:
        task: Task to score
        current_energy: User's current energy level (low, medium, high)
        current_time: Current time for urgency calculations
        user_context: User's current context tags
        
    Returns:
        float: Task score (higher = more suitable)
    """
    if current_time is None:
        current_time = datetime.utcnow()
    
    score = 0.0
    
    # Base priority score (0-10)
    priority_scores = {"low": 2, "medium": 5, "high": 8, "urgent": 10}
    score += priority_scores.get(task.priority, 5)
    
    # Energy level matching (0-5 bonus)
    if current_energy:
        energy_bonus = get_energy_level_score(task.energy_level, current_energy)
        score += energy_bonus
    
    # Urgency based on due date (0-8 bonus)
    if task.due_date:
        urgency_bonus = calculate_urgency_score(task.due_date, current_time)
        score += urgency_bonus
    
    # Duration appropriateness (0-3 bonus)
    duration_bonus = calculate_duration_score(task.estimated_duration, current_energy)
    score += duration_bonus
    
    # Context matching (0-4 bonus)
    if user_context and task.context_tags:
        context_bonus = calculate_context_score(task.context_tags, user_context)
        score += context_bonus
    
    # ADHD-specific bonuses
    adhd_bonus = calculate_adhd_bonus(task, current_time)
    score += adhd_bonus
    
    return round(score, 2)


def get_energy_level_score(task_energy: str, current_energy: str) -> float:
    """
    Calculate energy level matching score.
    
    Args:
        task_energy: Required energy level for task
        current_energy: User's current energy level
        
    Returns:
        float: Energy matching score (0-5)
    """
    energy_levels = {"low": 1, "medium": 2, "high": 3}
    
    task_level = energy_levels.get(task_energy, 2)
    current_level = energy_levels.get(current_energy, 2)
    
    # Perfect match gets highest score
    if task_level == current_level:
        return 5.0
    
    # Task requires less energy than available (good match)
    if task_level < current_level:
        return 4.0 - (current_level - task_level) * 0.5
    
    # Task requires more energy than available (poor match)
    if task_level > current_level:
        return max(0.0, 2.0 - (task_level - current_level) * 1.5)
    
    return 2.0


def calculate_urgency_score(due_date: datetime, current_time: datetime) -> float:
    """
    Calculate urgency score based on due date.
    
    Args:
        due_date: Task due date
        current_time: Current time
        
    Returns:
        float: Urgency score (0-8)
    """
    if due_date <= current_time:
        return 8.0  # Overdue - maximum urgency
    
    time_until_due = due_date - current_time
    hours_until_due = time_until_due.total_seconds() / 3600
    
    if hours_until_due <= 2:
        return 7.0  # Due within 2 hours
    elif hours_until_due <= 6:
        return 6.0  # Due within 6 hours
    elif hours_until_due <= 24:
        return 5.0  # Due today
    elif hours_until_due <= 72:
        return 3.0  # Due within 3 days
    elif hours_until_due <= 168:  # 1 week
        return 2.0  # Due within a week
    else:
        return 1.0  # Due later


def calculate_duration_score(
    estimated_duration: Optional[int],
    current_energy: Optional[str]
) -> float:
    """
    Calculate score based on task duration appropriateness.
    
    Args:
        estimated_duration: Task duration in minutes
        current_energy: User's current energy level
        
    Returns:
        float: Duration appropriateness score (0-3)
    """
    if not estimated_duration:
        return 1.5  # Neutral score for unknown duration
    
    # Ideal duration ranges by energy level
    ideal_ranges = {
        "low": (5, 20),      # 5-20 minutes for low energy
        "medium": (15, 60),   # 15-60 minutes for medium energy
        "high": (30, 180)     # 30-180 minutes for high energy
    }
    
    if not current_energy:
        current_energy = "medium"
    
    min_ideal, max_ideal = ideal_ranges.get(current_energy, (15, 60))
    
    if min_ideal <= estimated_duration <= max_ideal:
        return 3.0  # Perfect duration match
    elif estimated_duration < min_ideal:
        # Too short - might not be satisfying
        return 2.0 - (min_ideal - estimated_duration) / min_ideal
    else:
        # Too long - might be overwhelming
        excess_ratio = (estimated_duration - max_ideal) / max_ideal
        return max(0.0, 2.0 - excess_ratio)


def calculate_context_score(
    task_tags: List[str],
    user_context: List[str]
) -> float:
    """
    Calculate context matching score.
    
    Args:
        task_tags: Task's context tags
        user_context: User's current context tags
        
    Returns:
        float: Context matching score (0-4)
    """
    if not task_tags or not user_context:
        return 1.0  # Neutral score if no context available
    
    # Calculate overlap
    matching_tags = set(task_tags) & set(user_context)
    total_task_tags = len(task_tags)
    
    if total_task_tags == 0:
        return 1.0
    
    match_ratio = len(matching_tags) / total_task_tags
    
    # Score based on match ratio
    if match_ratio >= 0.8:
        return 4.0  # Excellent context match
    elif match_ratio >= 0.6:
        return 3.0  # Good context match
    elif match_ratio >= 0.4:
        return 2.0  # Fair context match
    elif match_ratio > 0:
        return 1.0  # Some context match
    else:
        return 0.0  # No context match


def calculate_adhd_bonus(task: Task, current_time: datetime) -> float:
    """
    Calculate ADHD-specific bonus points.
    
    Args:
        task: Task to evaluate
        current_time: Current time
        
    Returns:
        float: ADHD-specific bonus score (0-3)
    """
    bonus = 0.0
    
    # Bonus for quick wins (builds momentum)
    if task.estimated_duration and task.estimated_duration <= 15:
        bonus += 1.0
    
    # Bonus for low-energy tasks (accessible when struggling)
    if task.energy_level == "low":
        bonus += 0.5
    
    # Bonus for tasks with clear, specific titles (reduces decision fatigue)
    if task.title and len(task.title.split()) >= 3:
        bonus += 0.5
    
    # Bonus for tasks that haven't been sitting too long (prevents staleness)
    days_since_created = (current_time - task.created_at).days
    if days_since_created <= 3:
        bonus += 0.5
    elif days_since_created >= 14:
        bonus -= 0.5  # Penalty for very old tasks
    
    # Bonus for tasks with helpful context tags
    helpful_tags = ["quick", "easy", "fun", "important", "urgent"]
    if any(tag in task.context_tags for tag in helpful_tags):
        bonus += 0.5
    
    return min(bonus, 3.0)  # Cap at 3.0


def get_recommended_chunk_size(
    task_title: str,
    task_description: str = "",
    user_energy: str = "medium"
) -> str:
    """
    Recommend optimal chunk size for AI task breakdown.
    
    Args:
        task_title: Task title
        task_description: Task description
        user_energy: User's current energy level
        
    Returns:
        str: Recommended chunk size (small, medium, large)
    """
    text = f"{task_title} {task_description}".lower()
    
    # Indicators for different chunk sizes
    small_indicators = [
        "quick", "simple", "brief", "check", "review", "send", "call"
    ]
    
    large_indicators = [
        "project", "system", "complete", "comprehensive", "develop",
        "create", "build", "design", "research", "analyze"
    ]
    
    # Count indicators
    small_count = sum(1 for indicator in small_indicators if indicator in text)
    large_count = sum(1 for indicator in large_indicators if indicator in text)
    
    # Consider user energy level
    if user_energy == "low":
        return "small"  # Always recommend small chunks for low energy
    
    # Make recommendation based on indicators and text length
    word_count = len(text.split())
    
    if small_count > large_count or word_count < 5:
        return "small"
    elif large_count > small_count or word_count > 20:
        return "large"
    else:
        return "medium"


def calculate_completion_momentum(
    recent_completions: int,
    days_period: int = 7
) -> float:
    """
    Calculate user's current completion momentum.
    
    Args:
        recent_completions: Number of tasks completed recently
        days_period: Period to consider for momentum calculation
        
    Returns:
        float: Momentum score (0-10)
    """
    if days_period <= 0:
        return 0.0
    
    # Calculate daily completion rate
    daily_rate = recent_completions / days_period
    
    # Convert to momentum score (0-10 scale)
    # Assuming 1-2 tasks per day is good momentum
    if daily_rate >= 2.0:
        return 10.0
    elif daily_rate >= 1.5:
        return 8.0
    elif daily_rate >= 1.0:
        return 6.0
    elif daily_rate >= 0.5:
        return 4.0
    elif daily_rate > 0:
        return 2.0
    else:
        return 0.0


def suggest_optimal_work_session(
    available_tasks: List[Task],
    session_duration: int = 60,
    current_energy: str = "medium"
) -> List[Task]:
    """
    Suggest optimal combination of tasks for a work session.
    
    Args:
        available_tasks: List of available tasks
        session_duration: Available session duration in minutes
        current_energy: User's current energy level
        
    Returns:
        List[Task]: Recommended tasks for the session
    """
    if not available_tasks:
        return []
    
    # Score all tasks
    current_time = datetime.utcnow()
    scored_tasks = [
        (task, calculate_task_score(task, current_energy, current_time))
        for task in available_tasks
    ]
    
    # Sort by score
    scored_tasks.sort(key=lambda x: x[1], reverse=True)
    
    # Select tasks that fit within session duration
    selected_tasks = []
    remaining_time = session_duration
    
    for task, score in scored_tasks:
        task_duration = task.estimated_duration or 30  # Default 30 min if unknown
        
        if task_duration <= remaining_time:
            selected_tasks.append(task)
            remaining_time -= task_duration
            
            # Stop if we have a good mix or time is running low
            if len(selected_tasks) >= 3 or remaining_time < 15:
                break
    
    return selected_tasks
