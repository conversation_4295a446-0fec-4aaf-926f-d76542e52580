"""
AI utility functions and prompt templates for task chunking.

This module provides ADHD-optimized prompt templates and response
validation for AI-powered task breakdown functionality.
"""

from typing import Dict, Any, List
import re

from chronos.app.core.exceptions import ValidationError


# ADHD-optimized chunking prompt templates
CHUNKING_PROMPTS = {
    "small": """
You are helping someone with ADHD break down an overwhelming task into tiny, specific actions.
Each subtask should be:
- A single, concrete action (5-15 minutes)
- Completely unambiguous 
- Require no additional decision-making
- Have a clear "done" state

Task: {title}
Description: {description}
Additional Context: {context}

Break this into {max_subtasks} or fewer micro-tasks. Return ONLY a JSON array:

[
  {{
    "title": "Specific action (verb + object)",
    "description": "Clear step-by-step instructions",
    "estimated_duration": 10,
    "energy_level": "low",
    "context_tags": ["relevant", "location", "tools"],
    "priority": "medium"
  }}
]

Focus on removing ALL ambiguity and decision points. Each task should be so specific that someone could do it while distracted.
""",

    "medium": """
You are helping someone with ADHD break down a complex task into manageable chunks.
Each subtask should be:
- A focused work session (15-45 minutes)
- Have clear boundaries and outcomes
- Be completable in one sitting
- Build momentum toward the larger goal

Task: {title}
Description: {description}
Additional Context: {context}

Break this into {max_subtasks} or fewer focused work sessions. Return ONLY a JSON array:

[
  {{
    "title": "Focused work session title",
    "description": "What to accomplish in this session",
    "estimated_duration": 30,
    "energy_level": "medium",
    "context_tags": ["work", "computer", "research"],
    "priority": "medium"
  }}
]

Each chunk should feel achievable and provide a sense of progress when completed.
""",

    "large": """
You are helping someone with ADHD organize a major project into logical phases.
Each subtask should be:
- A complete project phase (1-3 hours)
- Have multiple smaller steps within it
- Represent a major milestone
- Be suitable for deep work sessions

Task: {title}
Description: {description}
Additional Context: {context}

Break this into {max_subtasks} or fewer major phases. Return ONLY a JSON array:

[
  {{
    "title": "Major project phase",
    "description": "Comprehensive description of this phase",
    "estimated_duration": 120,
    "energy_level": "high",
    "context_tags": ["deep-work", "planning", "creation"],
    "priority": "high"
  }}
]

Focus on logical project phases that build upon each other and create clear progress markers.
"""
}


def validate_ai_response(chunk: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and normalize AI-generated task chunk.
    
    Args:
        chunk: Raw AI-generated chunk data
        
    Returns:
        Dict[str, Any]: Validated and normalized chunk
        
    Raises:
        ValidationError: If chunk format is invalid
    """
    required_fields = ["title"]
    
    # Check required fields
    for field in required_fields:
        if field not in chunk or not chunk[field]:
            raise ValidationError(f"Missing required field: {field}")
    
    # Validate and normalize fields
    validated_chunk = {
        "title": _validate_title(chunk["title"]),
        "description": chunk.get("description", ""),
        "estimated_duration": _validate_duration(chunk.get("estimated_duration")),
        "energy_level": _validate_energy_level(chunk.get("energy_level", "medium")),
        "context_tags": _validate_context_tags(chunk.get("context_tags", [])),
        "priority": _validate_priority(chunk.get("priority", "medium"))
    }
    
    return validated_chunk


def _validate_title(title: str) -> str:
    """Validate and clean task title."""
    if not isinstance(title, str):
        raise ValidationError("Title must be a string")
    
    title = title.strip()
    if len(title) < 1:
        raise ValidationError("Title cannot be empty")
    if len(title) > 500:
        raise ValidationError("Title too long (max 500 characters)")
    
    return title


def _validate_duration(duration: Any) -> int:
    """Validate and normalize estimated duration."""
    if duration is None:
        return 15  # Default 15 minutes for ADHD-friendly tasks
    
    try:
        duration = int(duration)
    except (ValueError, TypeError):
        return 15
    
    # Clamp to reasonable range
    if duration < 1:
        return 5
    if duration > 1440:  # Max 24 hours
        return 1440
    
    return duration


def _validate_energy_level(energy_level: str) -> str:
    """Validate energy level value."""
    valid_levels = ["low", "medium", "high"]
    
    if not isinstance(energy_level, str):
        return "medium"
    
    energy_level = energy_level.lower().strip()
    if energy_level not in valid_levels:
        return "medium"
    
    return energy_level


def _validate_context_tags(tags: Any) -> List[str]:
    """Validate and normalize context tags."""
    if not isinstance(tags, list):
        return []
    
    validated_tags = []
    for tag in tags:
        if isinstance(tag, str) and tag.strip():
            clean_tag = tag.strip().lower()
            if len(clean_tag) <= 50 and clean_tag not in validated_tags:
                validated_tags.append(clean_tag)
    
    # Limit to 10 tags to prevent overwhelm
    return validated_tags[:10]


def _validate_priority(priority: str) -> str:
    """Validate priority level."""
    valid_priorities = ["low", "medium", "high", "urgent"]
    
    if not isinstance(priority, str):
        return "medium"
    
    priority = priority.lower().strip()
    if priority not in valid_priorities:
        return "medium"
    
    return priority


def extract_action_verbs(text: str) -> List[str]:
    """
    Extract action verbs from task descriptions for better chunking.
    
    Args:
        text: Task title or description
        
    Returns:
        List[str]: Extracted action verbs
    """
    # Common action verbs for task management
    action_verbs = [
        "create", "write", "draft", "design", "build", "develop",
        "research", "analyze", "review", "edit", "revise", "update",
        "organize", "plan", "schedule", "prepare", "setup", "configure",
        "test", "validate", "verify", "check", "confirm", "approve",
        "send", "email", "call", "contact", "meet", "discuss",
        "gather", "collect", "compile", "summarize", "document"
    ]
    
    text_lower = text.lower()
    found_verbs = []
    
    for verb in action_verbs:
        if verb in text_lower:
            found_verbs.append(verb)
    
    return found_verbs


def suggest_context_tags(title: str, description: str = "") -> List[str]:
    """
    Suggest context tags based on task content.
    
    Args:
        title: Task title
        description: Task description
        
    Returns:
        List[str]: Suggested context tags
    """
    text = f"{title} {description}".lower()
    
    # Context tag patterns
    tag_patterns = {
        "computer": ["computer", "laptop", "online", "digital", "software", "app", "website"],
        "phone": ["phone", "call", "mobile", "text", "sms"],
        "email": ["email", "send", "reply", "inbox"],
        "meeting": ["meeting", "call", "zoom", "conference", "discuss"],
        "research": ["research", "google", "search", "investigate", "study"],
        "writing": ["write", "draft", "document", "report", "article"],
        "planning": ["plan", "schedule", "organize", "prepare"],
        "creative": ["design", "create", "brainstorm", "ideate"],
        "admin": ["admin", "paperwork", "forms", "filing"],
        "home": ["home", "house", "personal"],
        "work": ["work", "office", "business", "professional"],
        "urgent": ["urgent", "asap", "deadline", "important"],
        "quick": ["quick", "fast", "brief", "short"]
    }
    
    suggested_tags = []
    for tag, keywords in tag_patterns.items():
        if any(keyword in text for keyword in keywords):
            suggested_tags.append(tag)
    
    return suggested_tags[:5]  # Limit to 5 suggestions


def estimate_task_complexity(title: str, description: str = "") -> str:
    """
    Estimate task complexity for better chunking recommendations.
    
    Args:
        title: Task title
        description: Task description
        
    Returns:
        str: Complexity level (simple, moderate, complex)
    """
    text = f"{title} {description}".lower()
    
    # Complexity indicators
    complex_indicators = [
        "project", "system", "complete", "comprehensive", "full",
        "entire", "all", "multiple", "various", "several"
    ]
    
    simple_indicators = [
        "quick", "simple", "brief", "short", "small", "minor",
        "check", "review", "send", "call", "email"
    ]
    
    # Count indicators
    complex_count = sum(1 for indicator in complex_indicators if indicator in text)
    simple_count = sum(1 for indicator in simple_indicators if indicator in text)
    
    # Estimate based on length and indicators
    word_count = len(text.split())
    
    if simple_count > complex_count and word_count < 10:
        return "simple"
    elif complex_count > simple_count or word_count > 30:
        return "complex"
    else:
        return "moderate"
