"""
Speechbot API Integration
Proxy endpoints for Speechbot voice synthesis service
"""

import logging
import httpx
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Request, Response, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User

logger = logging.getLogger(__name__)

router = APIRouter()

# Speechbot service configuration
SPEECHBOT_BASE_URL = getattr(settings, 'SPEECHBOT_URL', 'http://speechbot:8001')
SPEECHBOT_TIMEOUT = 30.0


async def proxy_to_speechbot(
    method: str,
    path: str,
    params: Optional[Dict] = None,
    json_data: Optional[Dict] = None,
    files: Optional[Dict] = None,
    headers: Optional[Dict] = None
) -> httpx.Response:
    """Proxy request to Speechbot service."""
    
    url = f"{SPEECHBOT_BASE_URL}{path}"
    
    # Default headers
    proxy_headers = {
        "Content-Type": "application/json",
        "User-Agent": "Chronos-Platform/1.0"
    }
    
    if headers:
        proxy_headers.update(headers)
    
    # Remove Content-Type for file uploads
    if files:
        proxy_headers.pop("Content-Type", None)
    
    try:
        async with httpx.AsyncClient(timeout=SPEECHBOT_TIMEOUT) as client:
            response = await client.request(
                method=method,
                url=url,
                params=params,
                json=json_data,
                files=files,
                headers=proxy_headers
            )
            return response
            
    except httpx.TimeoutException:
        logger.error(f"Speechbot request timeout: {method} {path}")
        raise HTTPException(
            status_code=504,
            detail="Speechbot service timeout. Please try again."
        )
    except httpx.ConnectError:
        logger.error(f"Speechbot connection error: {method} {path}")
        raise HTTPException(
            status_code=503,
            detail="Speechbot service unavailable. Please try again later."
        )
    except Exception as e:
        logger.error(f"Speechbot proxy error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while contacting Speechbot service."
        )


@router.get("/health")
async def speechbot_health():
    """Check Speechbot service health."""
    try:
        response = await proxy_to_speechbot("GET", "/health")
        
        if response.status_code == 200:
            return response.json()
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail="Speechbot service health check failed"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail="Speechbot service unavailable"
        )


@router.get("/capabilities")
async def speechbot_capabilities():
    """Get Speechbot capabilities."""
    try:
        response = await proxy_to_speechbot("GET", "/api/v1/tts/capabilities")
        
        if response.status_code == 200:
            return response.json()
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail="Failed to get Speechbot capabilities"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Capabilities check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail="Failed to get Speechbot capabilities"
        )


@router.post("/tts/synthesize")
async def synthesize_speech(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """Synthesize speech with user context."""
    try:
        # Get request body
        body = await request.json()
        
        # Add user context
        body["user_id"] = str(current_user.id)
        
        response = await proxy_to_speechbot(
            "POST", 
            "/api/v1/tts/synthesize",
            json_data=body
        )
        
        if response.status_code == 200:
            # Return audio as streaming response
            return StreamingResponse(
                iter([response.content]),
                media_type="audio/wav",
                headers={
                    "X-Audio-Duration": response.headers.get("X-Audio-Duration", "0"),
                    "X-Sample-Rate": response.headers.get("X-Sample-Rate", "24000"),
                    "X-ADHD-Mode": response.headers.get("X-ADHD-Mode", "calm"),
                    "X-Voice-Profile": response.headers.get("X-Voice-Profile", "default")
                }
            )
        else:
            error_detail = "Speech synthesis failed"
            try:
                error_data = response.json()
                error_detail = error_data.get("detail", error_detail)
            except:
                pass
                
            raise HTTPException(
                status_code=response.status_code,
                detail=error_detail
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Speech synthesis failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Speech synthesis failed"
        )


@router.post("/tts/quick")
async def quick_synthesis(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """Quick speech synthesis."""
    try:
        body = await request.json()
        body["user_id"] = str(current_user.id)
        
        response = await proxy_to_speechbot(
            "POST",
            "/api/v1/tts/quick",
            json_data=body
        )
        
        if response.status_code == 200:
            return StreamingResponse(
                iter([response.content]),
                media_type="audio/wav"
            )
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail="Quick synthesis failed"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Quick synthesis failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Quick synthesis failed"
        )


@router.post("/voice-profiles/create")
async def create_voice_profile(
    name: str = Form(...),
    description: Optional[str] = Form(None),
    audio_file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """Create voice profile with user context."""
    try:
        # Prepare files for upload
        files = {
            "audio_file": (audio_file.filename, audio_file.file, audio_file.content_type)
        }
        
        # Prepare form data
        form_data = {
            "name": name,
            "user_id": str(current_user.id)
        }
        
        if description:
            form_data["description"] = description
        
        response = await proxy_to_speechbot(
            "POST",
            "/api/v1/voice-profiles/create",
            files=files,
            headers={"Content-Type": None}  # Let httpx set multipart headers
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            error_detail = "Voice profile creation failed"
            try:
                error_data = response.json()
                error_detail = error_data.get("detail", error_detail)
            except:
                pass
                
            raise HTTPException(
                status_code=response.status_code,
                detail=error_detail
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Voice profile creation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Voice profile creation failed"
        )


@router.get("/voice-profiles/list")
async def list_voice_profiles(
    current_user: User = Depends(get_current_user)
):
    """List user's voice profiles."""
    try:
        response = await proxy_to_speechbot(
            "GET",
            f"/api/v1/voice-profiles/list/{current_user.id}"
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail="Failed to list voice profiles"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Voice profile listing failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to list voice profiles"
        )


@router.delete("/voice-profiles/{profile_id}")
async def delete_voice_profile(
    profile_id: str,
    current_user: User = Depends(get_current_user)
):
    """Delete voice profile."""
    try:
        response = await proxy_to_speechbot(
            "DELETE",
            f"/api/v1/voice-profiles/{profile_id}",
            params={"user_id": str(current_user.id)}
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail="Failed to delete voice profile"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Voice profile deletion failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to delete voice profile"
        )


@router.get("/preferences")
async def get_user_preferences(
    current_user: User = Depends(get_current_user)
):
    """Get user preferences."""
    try:
        response = await proxy_to_speechbot(
            "GET",
            f"/api/v1/preferences/{current_user.id}"
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail="Failed to get user preferences"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get preferences failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to get user preferences"
        )


@router.put("/preferences")
async def update_user_preferences(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """Update user preferences."""
    try:
        body = await request.json()
        
        response = await proxy_to_speechbot(
            "PUT",
            f"/api/v1/preferences/{current_user.id}",
            json_data=body
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail="Failed to update user preferences"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update preferences failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to update user preferences"
        )


@router.post("/dialogue/generate")
async def generate_dialogue(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """Generate multi-speaker dialogue."""
    try:
        body = await request.json()
        body["user_id"] = str(current_user.id)
        
        response = await proxy_to_speechbot(
            "POST",
            "/api/v1/dialogue/generate",
            json_data=body
        )
        
        if response.status_code == 200:
            return StreamingResponse(
                iter([response.content]),
                media_type="audio/wav"
            )
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail="Dialogue generation failed"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Dialogue generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Dialogue generation failed"
        )


@router.post("/dialogue/body-doubling")
async def start_body_doubling(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """Start body doubling session."""
    try:
        body = await request.json()
        body["user_id"] = str(current_user.id)
        
        response = await proxy_to_speechbot(
            "POST",
            "/api/v1/dialogue/body-doubling",
            json_data=body
        )
        
        if response.status_code == 200:
            return StreamingResponse(
                iter([response.content]),
                media_type="audio/wav"
            )
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail="Body doubling session failed"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Body doubling session failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Body doubling session failed"
        )
