"""
Focus session API endpoints for Project Chronos.

This module provides REST API endpoints for managing focus sessions,
timers, and ADHD-specific focus features.
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from chronos.app.core.database import get_db
from chronos.app.core.exceptions import (
    FocusSessionError,
    FocusSessionNotFoundError,
    InvalidFocusSessionStateError,
)
from chronos.app.schemas.focus import (
    FocusAnalytics,
    FocusSessionCreate,
    FocusSessionResponse,
    FocusSessionUpdate,
    PauseRequest,
    SessionCompletionRequest,
    TimerState,
)
from chronos.app.services.focus_service import FocusSessionService
from chronos.app.services.timer_service import TimerService

router = APIRouter()


# Dependency to get current user (placeholder - will be implemented with auth)
async def get_current_user() -> UUID:
    """Get current authenticated user ID."""
    # TODO: Implement proper authentication
    # For now, return a dummy user ID for testing
    return UUID("12345678-1234-5678-9012-123456789012")


# Dependency to get focus service
async def get_focus_service(db: AsyncSession = Depends(get_db)) -> FocusSessionService:
    """Get focus session service instance."""
    timer_service = TimerService()
    return FocusSessionService(db=db, timer_service=timer_service)


@router.post("/sessions", response_model=FocusSessionResponse, status_code=status.HTTP_201_CREATED)
async def create_focus_session(
    session_data: FocusSessionCreate,
    current_user: UUID = Depends(get_current_user),
    focus_service: FocusSessionService = Depends(get_focus_service)
) -> FocusSessionResponse:
    """
    Create a new focus session.
    
    Creates a new focus session with ADHD-optimized settings.
    Only one active session per user is allowed.
    """
    try:
        session = await focus_service.create_focus_session(
            user_id=current_user,
            session_data=session_data
        )
        
        # Convert to response model with computed fields
        return _session_to_response(session)
        
    except FocusSessionError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.friendly_message
        )


@router.post("/sessions/{session_id}/start", response_model=FocusSessionResponse)
async def start_focus_session(
    session_id: UUID,
    current_user: UUID = Depends(get_current_user),
    focus_service: FocusSessionService = Depends(get_focus_service)
) -> FocusSessionResponse:
    """
    Start a planned focus session.
    
    Begins the timer and activates focus mode for the session.
    """
    try:
        session = await focus_service.start_focus_session(
            user_id=current_user,
            session_id=session_id
        )
        
        return _session_to_response(session)
        
    except FocusSessionNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Focus session not found"
        )
    except InvalidFocusSessionStateError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.friendly_message
        )


@router.put("/sessions/{session_id}/pause", response_model=FocusSessionResponse)
async def pause_focus_session(
    session_id: UUID,
    pause_data: Optional[PauseRequest] = None,
    current_user: UUID = Depends(get_current_user),
    focus_service: FocusSessionService = Depends(get_focus_service)
) -> FocusSessionResponse:
    """
    Pause an active focus session.
    
    Pauses the timer and allows the user to resume later.
    """
    try:
        pause_reason = pause_data.reason if pause_data else None
        session = await focus_service.pause_focus_session(
            user_id=current_user,
            session_id=session_id,
            pause_reason=pause_reason
        )
        
        return _session_to_response(session)
        
    except FocusSessionNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Focus session not found"
        )
    except InvalidFocusSessionStateError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.friendly_message
        )


@router.put("/sessions/{session_id}/resume", response_model=FocusSessionResponse)
async def resume_focus_session(
    session_id: UUID,
    current_user: UUID = Depends(get_current_user),
    focus_service: FocusSessionService = Depends(get_focus_service)
) -> FocusSessionResponse:
    """
    Resume a paused focus session.
    
    Resumes the timer and reactivates focus mode.
    """
    try:
        session = await focus_service.resume_focus_session(
            user_id=current_user,
            session_id=session_id
        )
        
        return _session_to_response(session)
        
    except FocusSessionNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Focus session not found"
        )
    except InvalidFocusSessionStateError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.friendly_message
        )


@router.post("/sessions/{session_id}/complete", response_model=FocusSessionResponse)
async def complete_focus_session(
    session_id: UUID,
    completion_data: SessionCompletionRequest,
    current_user: UUID = Depends(get_current_user),
    focus_service: FocusSessionService = Depends(get_focus_service)
) -> FocusSessionResponse:
    """
    Complete a focus session.
    
    Marks the session as completed and stops the timer.
    """
    try:
        session = await focus_service.complete_focus_session(
            user_id=current_user,
            session_id=session_id,
            actual_duration=completion_data.actual_duration,
            completion_notes=completion_data.completion_notes
        )
        
        return _session_to_response(session)
        
    except FocusSessionNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Focus session not found"
        )
    except InvalidFocusSessionStateError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.friendly_message
        )


@router.get("/sessions/active", response_model=Optional[FocusSessionResponse])
async def get_active_session(
    current_user: UUID = Depends(get_current_user),
    focus_service: FocusSessionService = Depends(get_focus_service)
) -> Optional[FocusSessionResponse]:
    """
    Get the currently active focus session.
    
    Returns the active or paused session for the user, if any.
    """
    session = await focus_service.get_active_session(user_id=current_user)
    
    if not session:
        return None
    
    return _session_to_response(session)


@router.get("/sessions/{session_id}", response_model=FocusSessionResponse)
async def get_focus_session(
    session_id: UUID,
    current_user: UUID = Depends(get_current_user),
    focus_service: FocusSessionService = Depends(get_focus_service)
) -> FocusSessionResponse:
    """
    Get a specific focus session by ID.
    """
    try:
        session = await focus_service.get_session_by_id(
            user_id=current_user,
            session_id=session_id
        )
        
        return _session_to_response(session)
        
    except FocusSessionNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Focus session not found"
        )


@router.get("/sessions", response_model=List[FocusSessionResponse])
async def get_user_sessions(
    limit: int = Query(50, ge=1, le=100, description="Maximum number of sessions to return"),
    offset: int = Query(0, ge=0, description="Number of sessions to skip"),
    status: Optional[str] = Query(None, description="Filter by session status"),
    current_user: UUID = Depends(get_current_user),
    focus_service: FocusSessionService = Depends(get_focus_service)
) -> List[FocusSessionResponse]:
    """
    Get focus sessions for the current user.
    
    Returns a paginated list of focus sessions with optional status filtering.
    """
    sessions = await focus_service.get_user_sessions(
        user_id=current_user,
        limit=limit,
        offset=offset,
        status_filter=status
    )
    
    return [_session_to_response(session) for session in sessions]


@router.get("/sessions/{session_id}/timer", response_model=TimerState)
async def get_timer_state(
    session_id: UUID,
    current_user: UUID = Depends(get_current_user),
    focus_service: FocusSessionService = Depends(get_focus_service)
) -> TimerState:
    """
    Get real-time timer state for a focus session.
    
    Returns current timer information including elapsed and remaining time.
    """
    try:
        # Verify session belongs to user
        await focus_service.get_session_by_id(
            user_id=current_user,
            session_id=session_id
        )
        
        # Get timer state from timer service
        timer_service = TimerService()
        timer_state = await timer_service.get_timer_state(session_id)
        
        return timer_state
        
    except FocusSessionNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Focus session not found"
        )


@router.post("/sessions/{session_id}/extend", response_model=FocusSessionResponse)
async def extend_focus_session(
    session_id: UUID,
    additional_minutes: int = Query(..., ge=1, le=120, description="Minutes to add to session"),
    current_user: UUID = Depends(get_current_user),
    focus_service: FocusSessionService = Depends(get_focus_service)
) -> FocusSessionResponse:
    """
    Extend a focus session duration.
    
    Useful for hyperfocus protection - allows extending session
    without breaking flow state.
    """
    try:
        session = await focus_service.extend_session(
            user_id=current_user,
            session_id=session_id,
            additional_minutes=additional_minutes
        )
        
        return _session_to_response(session)
        
    except FocusSessionNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Focus session not found"
        )
    except InvalidFocusSessionStateError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.friendly_message
        )


@router.get("/analytics", response_model=FocusAnalytics)
async def get_focus_analytics(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: UUID = Depends(get_current_user),
    focus_service: FocusSessionService = Depends(get_focus_service)
) -> FocusAnalytics:
    """
    Get focus session analytics and insights.
    
    Returns analytics data for the specified time period,
    including productivity patterns and ADHD-specific insights.
    """
    from datetime import datetime, timedelta
    
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    analytics = await focus_service.get_focus_analytics(
        user_id=current_user,
        date_range=(start_date, end_date)
    )
    
    return analytics


def _session_to_response(session) -> FocusSessionResponse:
    """Convert FocusSession model to response schema with computed fields."""
    return FocusSessionResponse(
        id=session.id,
        user_id=session.user_id,
        task_id=session.task_id,
        session_type=session.session_type,
        planned_duration=session.planned_duration,
        actual_duration=session.actual_duration,
        break_duration=session.break_duration,
        status=session.status,
        focus_mode_settings=session.focus_mode_settings,
        started_at=session.started_at,
        completed_at=session.completed_at,
        paused_at=session.paused_at,
        hyperfocus_detected=session.hyperfocus_detected,
        hyperfocus_duration=session.hyperfocus_duration,
        created_at=session.created_at,
        updated_at=session.updated_at,
        # Computed fields
        elapsed_time=session.get_elapsed_time(),
        remaining_time=session.get_remaining_time(),
        progress_percentage=session.get_progress_percentage(),
        is_active=session.is_active(),
        should_show_break_reminder=session.should_show_break_reminder()
    )
