"""
AI-powered task chunking service for ADHD users.

This service provides intelligent task breakdown using OpenAI and Anthropic APIs,
with ADHD-specific prompt engineering and response validation.
"""

import json
import logging
from typing import List, Dict, Any, Optional
from uuid import UUID

from openai import AsyncOpenAI
from anthropic import Async<PERSON>nthropic
from sqlalchemy.ext.asyncio import AsyncSession

from chronos.app.models.task import Task
from chronos.app.models.user import User
from chronos.app.schemas.task import TaskChunkRequest, TaskCreate
from chronos.app.services.task_service import TaskService
from chronos.app.utils.ai_utils import CHUNKING_PROMPTS, validate_ai_response
from chronos.app.core.config import settings
from chronos.app.core.exceptions import AIServiceError, ValidationError

logger = logging.getLogger(__name__)


class AIChunkingService:
    """
    AI-powered task chunking service for ADHD users.
    
    This service breaks down large, overwhelming tasks into smaller,
    actionable subtasks using AI with ADHD-specific prompt engineering.
    """
    
    def __init__(self, db: AsyncSession):
        """
        Initialize AI chunking service.
        
        Args:
            db: Database session for operations
        """
        self.db = db
        self.task_service = TaskService(db)
        
        # Initialize AI clients
        self.openai_client = None
        self.anthropic_client = None
        
        if settings.OPENAI_API_KEY:
            self.openai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        
        if settings.ANTHROPIC_API_KEY:
            self.anthropic_client = AsyncAnthropic(api_key=settings.ANTHROPIC_API_KEY)
        
        if not self.openai_client and not self.anthropic_client:
            logger.warning("No AI API keys configured - chunking service will be disabled")
    
    async def chunk_task(
        self,
        user_id: UUID,
        task_id: UUID,
        chunk_request: TaskChunkRequest
    ) -> List[Task]:
        """
        Break down a task into smaller subtasks using AI.
        
        Args:
            user_id: ID of the user requesting chunking
            task_id: ID of the task to chunk
            chunk_request: Chunking parameters
            
        Returns:
            List[Task]: Created subtasks
            
        Raises:
            AIServiceError: If AI service fails or returns invalid response
            ValidationError: If task is already chunked or invalid
        """
        # Get the task to chunk
        task = await self.task_service.get_task_by_id(user_id, task_id)
        if not task:
            raise ValidationError("Task not found")
        
        if task.is_chunked:
            raise ValidationError("Task is already chunked")
        
        # Get user preferences for chunking
        user = await self._get_user(user_id)
        user_prefs = user.get_chunking_preferences() if user else {}
        
        try:
            # Generate AI chunks
            ai_chunks = await self._generate_chunks(
                task=task,
                chunk_request=chunk_request,
                user_preferences=user_prefs
            )
            
            # Create subtasks from AI response
            subtasks = await self._create_subtasks(
                user_id=user_id,
                parent_task_id=task_id,
                ai_chunks=ai_chunks
            )
            
            # Mark parent task as chunked
            await self.task_service.mark_task_as_chunked(user_id, task_id)
            
            logger.info(f"Successfully chunked task {task_id} into {len(subtasks)} subtasks")
            return subtasks
            
        except Exception as e:
            logger.error(f"Failed to chunk task {task_id}: {str(e)}")
            raise AIServiceError(f"Task chunking failed: {str(e)}")
    
    async def _generate_chunks(
        self,
        task: Task,
        chunk_request: TaskChunkRequest,
        user_preferences: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Generate task chunks using AI service.
        
        Args:
            task: Task to chunk
            chunk_request: Chunking parameters
            user_preferences: User's chunking preferences
            
        Returns:
            List[Dict[str, Any]]: AI-generated chunk data
        """
        # Build prompt context
        prompt_context = {
            "title": task.title,
            "description": task.description or "",
            "context": chunk_request.context or "",
            "chunk_size": chunk_request.chunk_size,
            "max_subtasks": chunk_request.max_subtasks,
            "include_time_estimates": chunk_request.include_time_estimates,
            "user_preferences": user_preferences
        }
        
        # Try OpenAI first, then Anthropic as fallback
        if self.openai_client:
            try:
                return await self._generate_chunks_openai(prompt_context)
            except Exception as e:
                logger.warning(f"OpenAI chunking failed: {str(e)}")
                if not self.anthropic_client:
                    raise
        
        if self.anthropic_client:
            return await self._generate_chunks_anthropic(prompt_context)
        
        raise AIServiceError("No AI services available for chunking")
    
    async def _generate_chunks_openai(
        self,
        prompt_context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate chunks using OpenAI GPT-4."""
        
        # Get appropriate prompt template
        prompt_template = CHUNKING_PROMPTS[prompt_context["chunk_size"]]
        prompt = prompt_template.format(**prompt_context)
        
        response = await self.openai_client.chat.completions.create(
            model="gpt-4",
            messages=[
                {
                    "role": "system",
                    "content": "You are an ADHD-specialized task management assistant. "
                              "Break down tasks into clear, actionable steps that minimize "
                              "decision fatigue and overwhelm."
                },
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            temperature=0.3,  # Lower temperature for more consistent results
            max_tokens=1500
        )
        
        content = response.choices[0].message.content
        return self._parse_ai_response(content)
    
    async def _generate_chunks_anthropic(
        self,
        prompt_context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate chunks using Anthropic Claude."""
        
        prompt_template = CHUNKING_PROMPTS[prompt_context["chunk_size"]]
        prompt = prompt_template.format(**prompt_context)
        
        response = await self.anthropic_client.messages.create(
            model="claude-3-sonnet-20240229",
            max_tokens=1500,
            temperature=0.3,
            messages=[
                {
                    "role": "user",
                    "content": f"You are an ADHD-specialized task management assistant. "
                              f"Break down tasks into clear, actionable steps that minimize "
                              f"decision fatigue and overwhelm.\n\n{prompt}"
                }
            ]
        )
        
        content = response.content[0].text
        return self._parse_ai_response(content)
    
    def _parse_ai_response(self, content: str) -> List[Dict[str, Any]]:
        """
        Parse AI response and validate chunk format.
        
        Args:
            content: Raw AI response content
            
        Returns:
            List[Dict[str, Any]]: Parsed and validated chunks
            
        Raises:
            AIServiceError: If response format is invalid
        """
        try:
            # Try to extract JSON from response
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            
            if start_idx == -1 or end_idx == 0:
                raise ValueError("No JSON array found in response")
            
            json_str = content[start_idx:end_idx]
            chunks = json.loads(json_str)
            
            # Validate chunk format
            validated_chunks = []
            for chunk in chunks:
                validated_chunk = validate_ai_response(chunk)
                validated_chunks.append(validated_chunk)
            
            return validated_chunks
            
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            logger.error(f"Failed to parse AI response: {str(e)}")
            logger.error(f"Raw response: {content}")
            raise AIServiceError(f"Invalid AI response format: {str(e)}")
    
    async def _create_subtasks(
        self,
        user_id: UUID,
        parent_task_id: UUID,
        ai_chunks: List[Dict[str, Any]]
    ) -> List[Task]:
        """
        Create subtasks from AI-generated chunks.
        
        Args:
            user_id: ID of the user
            parent_task_id: ID of the parent task
            ai_chunks: AI-generated chunk data
            
        Returns:
            List[Task]: Created subtasks
        """
        subtasks = []
        
        for chunk in ai_chunks:
            task_data = TaskCreate(
                title=chunk["title"],
                description=chunk.get("description", ""),
                priority=chunk.get("priority", "medium"),
                energy_level=chunk.get("energy_level", "medium"),
                estimated_duration=chunk.get("estimated_duration"),
                context_tags=chunk.get("context_tags", []),
                parent_task_id=parent_task_id
            )
            
            subtask = await self.task_service.create_task(user_id, task_data)
            subtasks.append(subtask)
        
        return subtasks
    
    async def _get_user(self, user_id: UUID) -> Optional[User]:
        """Get user by ID for preferences."""
        from sqlalchemy import select
        
        query = select(User).where(User.id == user_id)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_chunking_history(
        self,
        user_id: UUID,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Get user's task chunking history for learning and improvement.
        
        Args:
            user_id: ID of the user
            limit: Maximum number of chunking sessions to return
            
        Returns:
            List[Dict[str, Any]]: Chunking history with success metrics
        """
        from sqlalchemy import select, func
        
        # Get chunked tasks with their subtasks
        query = select(Task).where(
            Task.user_id == user_id,
            Task.is_chunked == True
        ).order_by(Task.updated_at.desc()).limit(limit)
        
        result = await self.db.execute(query)
        chunked_tasks = result.scalars().all()
        
        history = []
        for task in chunked_tasks:
            # Get subtask completion stats
            subtask_query = select(
                func.count(Task.id).label("total"),
                func.count(Task.completed_at).label("completed")
            ).where(Task.parent_task_id == task.id)
            
            subtask_result = await self.db.execute(subtask_query)
            stats = subtask_result.first()
            
            completion_rate = (stats.completed / stats.total * 100) if stats.total > 0 else 0
            
            history.append({
                "task_id": task.id,
                "task_title": task.title,
                "chunked_at": task.updated_at,
                "subtask_count": stats.total,
                "completed_subtasks": stats.completed,
                "completion_rate": round(completion_rate, 2)
            })
        
        return history
