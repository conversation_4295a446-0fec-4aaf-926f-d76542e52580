"""
Focus session service for Project Chronos.

This module provides business logic for managing focus sessions, including
ADHD-specific features like hyperfocus detection and gentle interventions.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional
from uuid import UUID

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from chronos.app.core.exceptions import (
    FocusSessionError,
    FocusSessionNotFoundError,
    InvalidFocusSessionStateError,
)
from chronos.app.models.focus import FocusSession
from chronos.app.schemas.focus import (
    FocusSessionCreate,
    FocusSessionUpdate,
    FocusAnalytics,
)
from chronos.app.services.timer_service import TimerService


class FocusSessionService:
    """Service for managing focus sessions and flow states."""
    
    def __init__(self, db: AsyncSession, timer_service: Optional[TimerService] = None):
        """
        Initialize focus session service.
        
        Args:
            db: Database session
            timer_service: Timer service for real-time functionality
        """
        self.db = db
        self.timer_service = timer_service or TimerService()
    
    async def create_focus_session(
        self,
        user_id: UUID,
        session_data: FocusSessionCreate
    ) -> FocusSession:
        """
        Create a new focus session.
        
        Args:
            user_id: User identifier
            session_data: Focus session creation data
            
        Returns:
            Created focus session
            
        Raises:
            FocusSessionError: If session cannot be created
        """
        try:
            # Check for existing active session
            existing_session = await self.get_active_session(user_id)
            if existing_session:
                raise FocusSessionError(
                    "Cannot create new session while another session is active. "
                    "Please complete or cancel the current session first."
                )
            
            # Create new session
            focus_session = FocusSession(
                user_id=user_id,
                task_id=session_data.task_id,
                session_type=session_data.session_type,
                planned_duration=session_data.planned_duration,
                break_duration=session_data.break_duration,
                focus_mode_settings=session_data.focus_mode_settings,
                status="planned"
            )
            
            self.db.add(focus_session)
            await self.db.commit()
            await self.db.refresh(focus_session)
            
            return focus_session
            
        except Exception as e:
            await self.db.rollback()
            raise FocusSessionError(f"Failed to create focus session: {str(e)}")
    
    async def start_focus_session(
        self,
        user_id: UUID,
        session_id: UUID
    ) -> FocusSession:
        """
        Start a planned focus session.
        
        Args:
            user_id: User identifier
            session_id: Session identifier
            
        Returns:
            Started focus session
            
        Raises:
            FocusSessionNotFoundError: If session not found
            InvalidFocusSessionStateError: If session cannot be started
        """
        session = await self.get_session_by_id(user_id, session_id)
        
        if session.status != "planned":
            raise InvalidFocusSessionStateError(
                f"Cannot start session with status '{session.status}'. "
                "Only planned sessions can be started."
            )
        
        try:
            # Update session status and start time
            session.status = "active"
            session.started_at = datetime.utcnow()
            
            # Create timer for real-time tracking
            await self.timer_service.create_timer(
                session_id=session.id,
                duration=session.planned_duration,
                timer_type="focus"
            )
            
            await self.db.commit()
            await self.db.refresh(session)
            
            return session
            
        except Exception as e:
            await self.db.rollback()
            raise FocusSessionError(f"Failed to start focus session: {str(e)}")
    
    async def pause_focus_session(
        self,
        user_id: UUID,
        session_id: UUID,
        pause_reason: Optional[str] = None
    ) -> FocusSession:
        """
        Pause an active focus session.
        
        Args:
            user_id: User identifier
            session_id: Session identifier
            pause_reason: Optional reason for pausing
            
        Returns:
            Paused focus session
        """
        session = await self.get_session_by_id(user_id, session_id)
        
        if session.status != "active":
            raise InvalidFocusSessionStateError(
                f"Cannot pause session with status '{session.status}'. "
                "Only active sessions can be paused."
            )
        
        try:
            # Update session status and pause time
            session.status = "paused"
            session.paused_at = datetime.utcnow()
            
            # Store pause reason in focus mode settings
            if pause_reason:
                session.focus_mode_settings["last_pause_reason"] = pause_reason
            
            # Pause timer
            await self.timer_service.pause_timer(session.id)
            
            await self.db.commit()
            await self.db.refresh(session)
            
            return session
            
        except Exception as e:
            await self.db.rollback()
            raise FocusSessionError(f"Failed to pause focus session: {str(e)}")
    
    async def resume_focus_session(
        self,
        user_id: UUID,
        session_id: UUID
    ) -> FocusSession:
        """
        Resume a paused focus session.
        
        Args:
            user_id: User identifier
            session_id: Session identifier
            
        Returns:
            Resumed focus session
        """
        session = await self.get_session_by_id(user_id, session_id)
        
        if session.status != "paused":
            raise InvalidFocusSessionStateError(
                f"Cannot resume session with status '{session.status}'. "
                "Only paused sessions can be resumed."
            )
        
        try:
            # Update session status
            session.status = "active"
            session.paused_at = None
            
            # Resume timer
            await self.timer_service.resume_timer(session.id)
            
            await self.db.commit()
            await self.db.refresh(session)
            
            return session
            
        except Exception as e:
            await self.db.rollback()
            raise FocusSessionError(f"Failed to resume focus session: {str(e)}")
    
    async def complete_focus_session(
        self,
        user_id: UUID,
        session_id: UUID,
        actual_duration: Optional[int] = None,
        completion_notes: Optional[str] = None
    ) -> FocusSession:
        """
        Complete a focus session.
        
        Args:
            user_id: User identifier
            session_id: Session identifier
            actual_duration: Actual duration if different from elapsed
            completion_notes: Optional completion notes
            
        Returns:
            Completed focus session
        """
        session = await self.get_session_by_id(user_id, session_id)
        
        if session.status not in ["active", "paused"]:
            raise InvalidFocusSessionStateError(
                f"Cannot complete session with status '{session.status}'. "
                "Only active or paused sessions can be completed."
            )
        
        try:
            # Update session status and completion time
            session.status = "completed"
            session.completed_at = datetime.utcnow()
            session.actual_duration = actual_duration or session.get_elapsed_time()
            
            # Store completion notes
            if completion_notes:
                session.focus_mode_settings["completion_notes"] = completion_notes
            
            # Stop timer
            await self.timer_service.stop_timer(session.id)
            
            await self.db.commit()
            await self.db.refresh(session)
            
            return session
            
        except Exception as e:
            await self.db.rollback()
            raise FocusSessionError(f"Failed to complete focus session: {str(e)}")
    
    async def get_session_by_id(
        self,
        user_id: UUID,
        session_id: UUID
    ) -> FocusSession:
        """
        Get focus session by ID.
        
        Args:
            user_id: User identifier
            session_id: Session identifier
            
        Returns:
            Focus session
            
        Raises:
            FocusSessionNotFoundError: If session not found
        """
        stmt = select(FocusSession).where(
            and_(
                FocusSession.id == session_id,
                FocusSession.user_id == user_id
            )
        )
        result = await self.db.execute(stmt)
        session = result.scalar_one_or_none()
        
        if not session:
            raise FocusSessionNotFoundError(
                f"Focus session {session_id} not found for user {user_id}"
            )
        
        return session
    
    async def get_active_session(self, user_id: UUID) -> Optional[FocusSession]:
        """
        Get currently active focus session for user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Active focus session or None
        """
        stmt = select(FocusSession).where(
            and_(
                FocusSession.user_id == user_id,
                FocusSession.status.in_(["active", "paused"])
            )
        ).order_by(desc(FocusSession.started_at))
        
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_user_sessions(
        self,
        user_id: UUID,
        limit: int = 50,
        offset: int = 0,
        status_filter: Optional[str] = None
    ) -> List[FocusSession]:
        """
        Get focus sessions for user with pagination.
        
        Args:
            user_id: User identifier
            limit: Maximum number of sessions to return
            offset: Number of sessions to skip
            status_filter: Optional status filter
            
        Returns:
            List of focus sessions
        """
        stmt = select(FocusSession).where(FocusSession.user_id == user_id)
        
        if status_filter:
            stmt = stmt.where(FocusSession.status == status_filter)
        
        stmt = stmt.order_by(desc(FocusSession.created_at)).limit(limit).offset(offset)
        
        result = await self.db.execute(stmt)
        return list(result.scalars().all())

    async def check_hyperfocus(self, user_id: UUID, session_id: UUID) -> bool:
        """
        Check and handle hyperfocus detection for active session.

        Args:
            user_id: User identifier
            session_id: Session identifier

        Returns:
            True if hyperfocus was detected and handled
        """
        session = await self.get_session_by_id(user_id, session_id)

        if not session.is_active():
            return False

        # Check if hyperfocus is detected
        if session.detect_hyperfocus():
            # Update session in database
            await self.db.commit()
            await self.db.refresh(session)
            return True

        return False

    async def extend_session(
        self,
        user_id: UUID,
        session_id: UUID,
        additional_minutes: int
    ) -> FocusSession:
        """
        Extend focus session duration (for hyperfocus protection).

        Args:
            user_id: User identifier
            session_id: Session identifier
            additional_minutes: Minutes to add to session

        Returns:
            Extended focus session
        """
        session = await self.get_session_by_id(user_id, session_id)

        if not session.is_active():
            raise InvalidFocusSessionStateError(
                "Cannot extend inactive session"
            )

        try:
            # Extend planned duration
            session.planned_duration += additional_minutes

            # Extend timer
            await self.timer_service.extend_timer(session.id, additional_minutes)

            await self.db.commit()
            await self.db.refresh(session)

            return session

        except Exception as e:
            await self.db.rollback()
            raise FocusSessionError(f"Failed to extend focus session: {str(e)}")

    async def get_focus_analytics(
        self,
        user_id: UUID,
        date_range: Optional[tuple] = None
    ) -> FocusAnalytics:
        """
        Get focus session analytics and patterns.

        Args:
            user_id: User identifier
            date_range: Optional date range tuple (start_date, end_date)

        Returns:
            Focus analytics data
        """
        # Default to last 30 days if no range provided
        if not date_range:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=30)
            date_range = (start_date, end_date)

        start_date, end_date = date_range

        # Base query for sessions in date range
        base_stmt = select(FocusSession).where(
            and_(
                FocusSession.user_id == user_id,
                FocusSession.created_at >= start_date,
                FocusSession.created_at <= end_date
            )
        )

        # Get all sessions
        result = await self.db.execute(base_stmt)
        sessions = list(result.scalars().all())

        # Calculate analytics
        total_sessions = len(sessions)
        completed_sessions = len([s for s in sessions if s.status == "completed"])
        total_focus_time = sum(
            s.actual_duration or s.get_elapsed_time() for s in sessions
        )

        # Calculate averages and rates
        avg_duration = total_focus_time / total_sessions if total_sessions > 0 else 0
        completion_rate = completed_sessions / total_sessions if total_sessions > 0 else 0
        hyperfocus_incidents = len([s for s in sessions if s.hyperfocus_detected])

        # Session type breakdown
        session_type_breakdown = {}
        for session in sessions:
            session_type = session.session_type
            session_type_breakdown[session_type] = session_type_breakdown.get(session_type, 0) + 1

        # Most productive hours (simplified - would need more complex analysis)
        productive_hours = []
        for session in completed_sessions:
            if session.started_at:
                hour = session.started_at.hour
                productive_hours.append(hour)

        # Get top 3 most common hours
        hour_counts = {}
        for hour in productive_hours:
            hour_counts[hour] = hour_counts.get(hour, 0) + 1

        most_productive_hours = sorted(
            hour_counts.keys(),
            key=lambda h: hour_counts[h],
            reverse=True
        )[:3]

        # Weekly trend (simplified)
        weekly_trend = []
        for i in range(7):
            day_start = start_date + timedelta(days=i * 7)
            day_end = day_start + timedelta(days=7)
            week_sessions = [
                s for s in sessions
                if day_start <= s.created_at < day_end
            ]
            weekly_trend.append({
                "week": i + 1,
                "sessions": len(week_sessions),
                "focus_time": sum(
                    s.actual_duration or s.get_elapsed_time()
                    for s in week_sessions
                )
            })

        return FocusAnalytics(
            total_sessions=total_sessions,
            completed_sessions=completed_sessions,
            total_focus_time=total_focus_time,
            average_session_duration=avg_duration,
            completion_rate=completion_rate,
            hyperfocus_incidents=hyperfocus_incidents,
            most_productive_hours=most_productive_hours,
            session_type_breakdown=session_type_breakdown,
            weekly_trend=weekly_trend
        )
