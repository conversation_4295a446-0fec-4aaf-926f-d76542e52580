"""
Database models for Project Chronos.

This package contains all SQLAlchemy models for the ADHD-focused productivity
application, including ADHD-specific fields and relationships.
"""

from chronos.app.models.base import Base, TimestampMixin, ADHDMixin, SoftDeleteMixin
from chronos.app.models.user import User
from chronos.app.models.task import Task
from chronos.app.models.focus import FocusSession
from chronos.app.models.notification import Notification
from chronos.app.models.timeblock import TimeBlock
from chronos.app.models.gamification import (
    UserGamification,
    Achievement,
    UserAchievement,
    Streak,
    PointsAward,
)
from chronos.app.models.body_doubling import (
    BodyDoublingSession,
    BodyDoublingParticipant,
    SessionMessage,
)

__all__ = [
    "Base",
    "TimestampMixin",
    "ADHDMixin",
    "SoftDeleteMixin",
    "User",
    "Task",
    "FocusSession",
    "Notification",
    "TimeBlock",
    "UserGamification",
    "Achievement",
    "UserAchievement",
    "Streak",
    "PointsAward",
    "BodyDoublingSession",
    "BodyDoublingParticipant",
    "SessionMessage",
]
