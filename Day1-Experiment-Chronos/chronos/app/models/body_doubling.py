"""
Body doubling models for Project Chronos.

This module defines models for virtual body doubling sessions, participants,
and real-time collaboration features for ADHD users.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, Integer, String, DateTime, Text
from sqlalchemy.dialects.postgresql import J<PERSON><PERSON><PERSON>, UUID as PostgresUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from chronos.app.models.base import Base, TimestampMixin, get_table_name


class BodyDoublingSession(Base, TimestampMixin):
    """
    Body doubling session model for virtual co-working sessions.
    
    This model supports virtual body doubling sessions where users can work
    together for accountability and shared focus, particularly helpful for ADHD users.
    """
    
    __tablename__ = get_table_name("BodyDoublingSession")
    
    # Session host
    host_user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who created/hosts this session"
    )
    
    # Session configuration
    title: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        doc="Session title/description"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Optional detailed description of the session"
    )
    
    max_participants: Mapped[int] = mapped_column(
        Integer,
        default=4,
        nullable=False,
        doc="Maximum number of participants (including host)"
    )
    
    session_type: Mapped[str] = mapped_column(
        String(20),
        default="open",
        nullable=False,
        doc="Session type: open, private, focus_group, study_group"
    )
    
    # Privacy and access control
    is_public: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether the session is publicly discoverable"
    )
    
    requires_approval: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether host approval is required to join"
    )
    
    password_protected: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the session requires a password"
    )
    
    session_password: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="Hashed password for protected sessions"
    )
    
    # Session timing
    scheduled_start: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Scheduled start time (optional for immediate sessions)"
    )
    
    scheduled_end: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Scheduled end time (optional)"
    )
    
    actual_start: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the session actually started"
    )
    
    actual_end: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the session actually ended"
    )
    
    # Session status
    status: Mapped[str] = mapped_column(
        String(20),
        default="waiting",
        nullable=False,
        doc="Session status: waiting, active, paused, completed, cancelled"
    )
    
    # Session settings and preferences
    session_settings: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        default=dict,
        nullable=False,
        doc="Session configuration including focus modes, break schedules, etc."
    )
    
    # Focus session integration
    current_focus_session_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("focus_sessions.id", ondelete="SET NULL"),
        nullable=True,
        doc="Currently active group focus session"
    )
    
    # Relationships
    host: Mapped["User"] = relationship(
        "User",
        foreign_keys=[host_user_id],
        doc="User who hosts this session"
    )
    
    participants: Mapped[List["BodyDoublingParticipant"]] = relationship(
        "BodyDoublingParticipant",
        back_populates="session",
        cascade="all, delete-orphan",
        doc="Participants in this session"
    )
    
    current_focus_session: Mapped[Optional["FocusSession"]] = relationship(
        "FocusSession",
        foreign_keys=[current_focus_session_id],
        doc="Currently active group focus session"
    )
    
    def __repr__(self) -> str:
        """String representation of BodyDoublingSession."""
        return f"<BodyDoublingSession(id={self.id}, title='{self.title}', status='{self.status}', participants={len(self.participants)})>"


class BodyDoublingParticipant(Base, TimestampMixin):
    """
    Participant in a body doubling session.
    
    This model tracks individual participants in body doubling sessions,
    including their status, preferences, and activity.
    """
    
    __tablename__ = get_table_name("BodyDoublingParticipant")
    
    # Foreign keys
    session_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("body_doubling_sessions.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the body doubling session"
    )
    
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the participating user"
    )
    
    # Participation details
    joined_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        default=datetime.utcnow,
        doc="When the user joined the session"
    )
    
    left_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the user left the session"
    )
    
    status: Mapped[str] = mapped_column(
        String(20),
        default="active",
        nullable=False,
        doc="Participant status: active, away, focused, break, left"
    )
    
    # Privacy preferences
    share_progress: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether to share task progress with other participants"
    )
    
    anonymous_mode: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether to participate anonymously"
    )
    
    # Activity tracking
    last_activity: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        default=datetime.utcnow,
        doc="Last activity timestamp"
    )
    
    current_task_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("tasks.id", ondelete="SET NULL"),
        nullable=True,
        doc="Currently active task (if shared)"
    )
    
    # Participant preferences
    participant_settings: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        default=dict,
        nullable=False,
        doc="Participant-specific settings and preferences"
    )
    
    # Relationships
    session: Mapped["BodyDoublingSession"] = relationship(
        "BodyDoublingSession",
        back_populates="participants",
        doc="Body doubling session this participant belongs to"
    )
    
    user: Mapped["User"] = relationship(
        "User",
        doc="User participating in the session"
    )
    
    current_task: Mapped[Optional["Task"]] = relationship(
        "Task",
        foreign_keys=[current_task_id],
        doc="Currently active task (if shared)"
    )
    
    def __repr__(self) -> str:
        """String representation of BodyDoublingParticipant."""
        return f"<BodyDoublingParticipant(session_id={self.session_id}, user_id={self.user_id}, status='{self.status}')>"


class SessionMessage(Base, TimestampMixin):
    """
    Messages sent within body doubling sessions.
    
    This model supports chat functionality and encouragement messages
    within body doubling sessions.
    """
    
    __tablename__ = get_table_name("SessionMessage")
    
    # Foreign keys
    session_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("body_doubling_sessions.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the body doubling session"
    )
    
    sender_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who sent the message"
    )
    
    # Message content
    message_type: Mapped[str] = mapped_column(
        String(20),
        default="chat",
        nullable=False,
        doc="Message type: chat, encouragement, system, progress_update"
    )
    
    content: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Message content"
    )
    
    # Message metadata
    message_data: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        default=dict,
        nullable=False,
        doc="Additional message data (reactions, attachments, etc.)"
    )
    
    # Relationships
    session: Mapped["BodyDoublingSession"] = relationship(
        "BodyDoublingSession",
        doc="Body doubling session this message belongs to"
    )
    
    sender: Mapped["User"] = relationship(
        "User",
        doc="User who sent the message"
    )
    
    def __repr__(self) -> str:
        """String representation of SessionMessage."""
        return f"<SessionMessage(id={self.id}, type='{self.message_type}', session_id={self.session_id})>"
