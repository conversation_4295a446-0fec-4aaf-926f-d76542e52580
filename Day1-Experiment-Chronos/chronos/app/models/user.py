"""
User model for Project Chronos.

This module defines the User model with ADHD-specific fields and
functionality for neurodivergent user support.
"""

from typing import Any, Dict, List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, String, Text
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column, relationship

from chronos.app.models.base import Base, TimestampMixin, get_table_name


class User(Base, TimestampMixin):
    """
    User model with ADHD-specific fields and preferences.
    
    This model stores user information with special consideration for
    ADHD-related preferences, energy patterns, and accessibility needs.
    """
    
    __tablename__ = get_table_name("User")
    
    # Basic user information
    email: Mapped[str] = mapped_column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        doc="User's email address (unique identifier)"
    )
    
    hashed_password: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Hashed password for authentication"
    )
    
    full_name: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="User's full name (optional)"
    )
    
    # Account status
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether the user account is active"
    )
    
    is_verified: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the user's email is verified"
    )
    
    # ADHD-specific fields
    adhd_diagnosed: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the user has been diagnosed with ADHD"
    )
    
    timezone: Mapped[str] = mapped_column(
        String(50),
        default="UTC",
        nullable=False,
        doc="User's timezone for scheduling and notifications"
    )
    
    # ADHD preferences and patterns stored as JSON
    preferences: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        default=dict,
        nullable=False,
        doc="User preferences including ADHD-specific settings"
    )
    
    # Optional bio/notes for personalization
    bio: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Optional user bio or notes"
    )

    # Relationships
    tasks: Mapped[List["Task"]] = relationship(
        "Task",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="Tasks owned by this user"
    )

    focus_sessions: Mapped[List["FocusSession"]] = relationship(
        "FocusSession",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="Focus sessions for this user"
    )

    notifications: Mapped[List["Notification"]] = relationship(
        "Notification",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="Notifications for this user"
    )

    time_blocks: Mapped[List["TimeBlock"]] = relationship(
        "TimeBlock",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="Time blocks for this user"
    )

    gamification: Mapped[Optional["UserGamification"]] = relationship(
        "UserGamification",
        back_populates="user",
        cascade="all, delete-orphan",
        uselist=False,
        doc="Gamification profile for this user"
    )

    achievements: Mapped[List["UserAchievement"]] = relationship(
        "UserAchievement",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="Achievements unlocked by this user"
    )

    streaks: Mapped[List["Streak"]] = relationship(
        "Streak",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="Streaks for this user"
    )

    points_awards: Mapped[List["PointsAward"]] = relationship(
        "PointsAward",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="Points awards for this user"
    )
    
    def __repr__(self) -> str:
        """String representation of User."""
        return f"<User(id={self.id}, email='{self.email}', adhd_diagnosed={self.adhd_diagnosed})>"
    
    def get_preference(self, key: str, default: Any = None) -> Any:
        """
        Get a user preference value.
        
        Args:
            key: Preference key (supports dot notation for nested values)
            default: Default value if preference not found
            
        Returns:
            Any: Preference value or default
            
        Example:
            >>> user.get_preference("energy_patterns.morning", "medium")
            "high"
        """
        if "." in key:
            keys = key.split(".")
            value = self.preferences
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            return value
        return self.preferences.get(key, default)
    
    def set_preference(self, key: str, value: Any) -> None:
        """
        Set a user preference value.
        
        Args:
            key: Preference key (supports dot notation for nested values)
            value: Value to set
            
        Example:
            >>> user.set_preference("energy_patterns.morning", "high")
            >>> user.set_preference("notification_preferences.persistent", True)
        """
        if "." in key:
            keys = key.split(".")
            current = self.preferences
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
            current[keys[-1]] = value
        else:
            self.preferences[key] = value
    
    def get_energy_pattern(self, time_of_day: str) -> str:
        """
        Get energy level pattern for a specific time of day.
        
        Args:
            time_of_day: Time period (morning, afternoon, evening, night)
            
        Returns:
            str: Energy level (low, medium, high)
        """
        energy_patterns = self.get_preference("energy_patterns", {})
        return energy_patterns.get(time_of_day, "medium")
    
    def set_energy_pattern(self, time_of_day: str, energy_level: str) -> None:
        """
        Set energy level pattern for a specific time of day.
        
        Args:
            time_of_day: Time period (morning, afternoon, evening, night)
            energy_level: Energy level (low, medium, high)
        """
        energy_patterns = self.get_preference("energy_patterns", {})
        energy_patterns[time_of_day] = energy_level
        self.set_preference("energy_patterns", energy_patterns)
    
    def get_notification_preferences(self) -> Dict[str, Any]:
        """
        Get notification preferences with ADHD-friendly defaults.
        
        Returns:
            Dict[str, Any]: Notification preferences
        """
        defaults = {
            "persistent": True,  # ADHD users often need persistent reminders
            "gentle": True,      # Gentle notifications to avoid overwhelm
            "staggered": True,   # Multiple reminders at different intervals
            "focus_mode_respect": True,  # Respect focus modes
            "max_per_hour": 5,   # Limit to prevent overwhelm
            "quiet_hours": {
                "enabled": False,
                "start": "22:00",
                "end": "08:00"
            }
        }
        
        user_prefs = self.get_preference("notification_preferences", {})
        return {**defaults, **user_prefs}
    
    def get_chunking_preferences(self) -> Dict[str, Any]:
        """
        Get AI task chunking preferences.
        
        Returns:
            Dict[str, Any]: Chunking preferences
        """
        defaults = {
            "default_size": "small",  # ADHD users often benefit from smaller chunks
            "max_subtasks": 5,        # Limit to prevent overwhelm
            "include_time_estimates": True,
            "include_energy_levels": True,
            "context_aware": True
        }
        
        user_prefs = self.get_preference("chunking_preferences", {})
        return {**defaults, **user_prefs}
    
    def get_focus_preferences(self) -> Dict[str, Any]:
        """
        Get focus session preferences.
        
        Returns:
            Dict[str, Any]: Focus preferences
        """
        defaults = {
            "default_duration": 25,    # Pomodoro default
            "break_duration": 5,       # Short breaks
            "long_break_duration": 15, # Longer breaks every 4 sessions
            "hyperfocus_warning": 120, # Warn after 2 hours
            "gentle_transitions": True, # Gentle session transitions
            "background_sounds": False  # No distracting sounds by default
        }
        
        user_prefs = self.get_preference("focus_preferences", {})
        return {**defaults, **user_prefs}
    
    def is_adhd_user(self) -> bool:
        """
        Check if user has ADHD diagnosis or ADHD-specific preferences.
        
        Returns:
            bool: True if user is identified as having ADHD
        """
        return (
            self.adhd_diagnosed or
            self.get_preference("adhd_support_mode", False)
        )
    
    def get_accessibility_preferences(self) -> Dict[str, Any]:
        """
        Get accessibility preferences for ADHD support.
        
        Returns:
            Dict[str, Any]: Accessibility preferences
        """
        defaults = {
            "high_contrast": False,
            "reduced_motion": False,
            "simplified_ui": False,
            "larger_text": False,
            "color_coding": True,      # Helpful for ADHD organization
            "visual_timers": True,     # Visual time representation
            "progress_indicators": True # Clear progress feedback
        }
        
        user_prefs = self.get_preference("accessibility", {})
        return {**defaults, **user_prefs}
