"""
Task model for Project Chronos.

This module defines the Task model with ADHD-specific fields including
energy levels, context tags, and hierarchical task relationships for chunking.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import <PERSON><PERSON>an, Foreign<PERSON>ey, Integer, String, Text, DateTime, ARRAY
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from chronos.app.models.base import Base, TimestampMixin, ADHDMixin, get_table_name


class Task(Base, TimestampMixin, ADHDMixin):
    """
    Task model with ADHD-specific features and hierarchical relationships.
    
    This model supports task chunking, energy level tracking, and context-aware
    organization to help users with ADHD manage their workload effectively.
    """
    
    __tablename__ = get_table_name("Task")
    
    # Foreign key to user
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who owns this task"
    )
    
    # Basic task information
    title: Mapped[str] = mapped_column(
        String(500),
        nullable=False,
        doc="Task title (up to 500 characters for detailed descriptions)"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Detailed task description"
    )
    
    # Task status and priority
    status: Mapped[str] = mapped_column(
        String(20),
        default="pending",
        nullable=False,
        doc="Task status: pending, in_progress, completed, cancelled"
    )
    
    priority: Mapped[str] = mapped_column(
        String(10),
        default="medium",
        nullable=False,
        doc="Task priority: low, medium, high, urgent"
    )
    
    # ADHD-specific fields
    energy_level: Mapped[str] = mapped_column(
        String(10),
        default="medium",
        nullable=False,
        doc="Required energy level: low, medium, high"
    )
    
    estimated_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Estimated duration in minutes"
    )
    
    actual_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Actual time spent on task in minutes"
    )
    
    # Context tags for adaptive filtering
    context_tags: Mapped[List[str]] = mapped_column(
        ARRAY(String),
        default=list,
        nullable=False,
        doc="Context tags for filtering (e.g., 'home', 'computer', 'phone')"
    )
    
    # Task chunking support
    is_chunked: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether this task has been broken down into subtasks"
    )
    
    parent_task_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("tasks.id", ondelete="CASCADE"),
        nullable=True,
        doc="Parent task ID if this is a subtask"
    )
    
    # Scheduling and completion
    due_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Task due date"
    )
    
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the task was completed"
    )
    
    # Relationships
    subtasks: Mapped[List["Task"]] = relationship(
        "Task",
        back_populates="parent_task",
        cascade="all, delete-orphan",
        doc="Subtasks created through AI chunking"
    )
    
    parent_task: Mapped[Optional["Task"]] = relationship(
        "Task",
        back_populates="subtasks",
        remote_side="Task.id",
        doc="Parent task if this is a subtask"
    )
    
    # User relationship
    user: Mapped["User"] = relationship(
        "User",
        back_populates="tasks",
        doc="User who owns this task"
    )

    # Focus sessions relationship
    focus_sessions: Mapped[List["FocusSession"]] = relationship(
        "FocusSession",
        back_populates="task",
        cascade="all, delete-orphan",
        doc="Focus sessions for this task"
    )

    # Notifications relationship
    notifications: Mapped[List["Notification"]] = relationship(
        "Notification",
        back_populates="task",
        cascade="all, delete-orphan",
        doc="Notifications related to this task"
    )

    # Time blocks relationship
    time_blocks: Mapped[List["TimeBlock"]] = relationship(
        "TimeBlock",
        back_populates="task",
        cascade="all, delete-orphan",
        doc="Time blocks for this task"
    )
    
    def __repr__(self) -> str:
        """String representation of Task."""
        return f"<Task(id={self.id}, title='{self.title}', status='{self.status}', energy='{self.energy_level}')>"
    
    def is_overdue(self) -> bool:
        """
        Check if task is overdue.
        
        Returns:
            bool: True if task has a due date and is past due
        """
        if not self.due_date or self.status in ["completed", "cancelled"]:
            return False
        return datetime.utcnow() > self.due_date
    
    def is_subtask(self) -> bool:
        """
        Check if this is a subtask.
        
        Returns:
            bool: True if this task has a parent task
        """
        return self.parent_task_id is not None
    
    def has_subtasks(self) -> bool:
        """
        Check if this task has been chunked into subtasks.
        
        Returns:
            bool: True if task has subtasks
        """
        return self.is_chunked and len(self.subtasks) > 0
    
    def get_completion_percentage(self) -> float:
        """
        Get completion percentage for chunked tasks.
        
        Returns:
            float: Completion percentage (0.0 to 1.0)
        """
        if not self.has_subtasks():
            return 1.0 if self.status == "completed" else 0.0
        
        if not self.subtasks:
            return 0.0
        
        completed_subtasks = sum(
            1 for subtask in self.subtasks 
            if subtask.status == "completed"
        )
        return completed_subtasks / len(self.subtasks)
    
    def get_estimated_total_duration(self) -> int:
        """
        Get total estimated duration including subtasks.
        
        Returns:
            int: Total estimated duration in minutes
        """
        if not self.has_subtasks():
            return self.estimated_duration or 0
        
        total = 0
        for subtask in self.subtasks:
            if subtask.estimated_duration:
                total += subtask.estimated_duration
        
        return total
    
    def get_actual_total_duration(self) -> int:
        """
        Get total actual duration including subtasks.
        
        Returns:
            int: Total actual duration in minutes
        """
        if not self.has_subtasks():
            return self.actual_duration or 0
        
        total = 0
        for subtask in self.subtasks:
            if subtask.actual_duration:
                total += subtask.actual_duration
        
        return total
    
    def add_context_tag(self, tag: str) -> None:
        """
        Add a context tag to the task.
        
        Args:
            tag: Context tag to add
        """
        if tag not in self.context_tags:
            self.context_tags = self.context_tags + [tag]
    
    def remove_context_tag(self, tag: str) -> None:
        """
        Remove a context tag from the task.
        
        Args:
            tag: Context tag to remove
        """
        if tag in self.context_tags:
            self.context_tags = [t for t in self.context_tags if t != tag]
    
    def matches_context(self, required_tags: List[str]) -> bool:
        """
        Check if task matches required context tags.
        
        Args:
            required_tags: List of required context tags
            
        Returns:
            bool: True if task has all required tags
        """
        return all(tag in self.context_tags for tag in required_tags)
    
    def is_suitable_for_energy_level(self, current_energy: str) -> bool:
        """
        Check if task is suitable for current energy level.
        
        Args:
            current_energy: Current energy level (low, medium, high)
            
        Returns:
            bool: True if task energy requirement <= current energy
        """
        energy_levels = {"low": 1, "medium": 2, "high": 3}
        task_energy = energy_levels.get(self.energy_level, 2)
        current_energy_level = energy_levels.get(current_energy, 2)
        
        return task_energy <= current_energy_level
    
    def get_urgency_score(self) -> float:
        """
        Calculate urgency score based on due date and priority.
        
        Returns:
            float: Urgency score (higher = more urgent)
        """
        priority_scores = {"low": 1, "medium": 2, "high": 3, "urgent": 4}
        base_score = priority_scores.get(self.priority, 2)
        
        if not self.due_date:
            return base_score
        
        # Add urgency based on time until due date
        time_until_due = (self.due_date - datetime.utcnow()).total_seconds()
        days_until_due = time_until_due / (24 * 3600)
        
        if days_until_due < 0:  # Overdue
            return base_score + 5
        elif days_until_due < 1:  # Due today
            return base_score + 3
        elif days_until_due < 3:  # Due within 3 days
            return base_score + 1
        
        return base_score
