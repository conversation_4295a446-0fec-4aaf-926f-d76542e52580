"""
Configuration management for Project Chronos.

This module provides environment-based configuration management using Pydantic
settings, ensuring type safety and validation for all configuration values.
"""

import secrets
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, EmailStr, HttpUrl, PostgresDsn, RedisDsn, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings with environment-based configuration.
    
    All settings can be overridden via environment variables with the
    CHRONOS_ prefix (e.g., CHRONOS_SECRET_KEY).
    """
    
    # Application settings
    PROJECT_NAME: str = "Project Chronos"
    VERSION: str = "0.1.0"
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    
    # Security settings
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 15
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    JWT_ALGORITHM: str = "HS256"
    
    # Server settings
    SERVER_NAME: str = "localhost"
    SERVER_HOST: AnyHttpUrl = "http://localhost"
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        """Parse CORS origins from environment variable."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # Database settings
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "chronos"
    POSTGRES_PASSWORD: str = "chronos_password"
    POSTGRES_DB: str = "chronos"
    POSTGRES_PORT: int = 5432
    DATABASE_URL: Optional[PostgresDsn] = None
    
    @validator("DATABASE_URL", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        """Assemble database URL from components."""
        if isinstance(v, str):
            return v

        user = values.get("POSTGRES_USER")
        password = values.get("POSTGRES_PASSWORD")
        host = values.get("POSTGRES_SERVER")
        port = values.get("POSTGRES_PORT")
        db = values.get("POSTGRES_DB")

        return f"postgresql+asyncpg://{user}:{password}@{host}:{port}/{db}"
    
    # Redis settings
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    REDIS_URL: Optional[RedisDsn] = None
    
    @validator("REDIS_URL", pre=True)
    def assemble_redis_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        """Assemble Redis URL from components."""
        if isinstance(v, str):
            return v
        
        password_part = ""
        if values.get("REDIS_PASSWORD"):
            password_part = f":{values.get('REDIS_PASSWORD')}@"
        
        return f"redis://{password_part}{values.get('REDIS_HOST')}:{values.get('REDIS_PORT')}/{values.get('REDIS_DB')}"
    
    # Celery settings
    CELERY_BROKER_URL: Optional[str] = None
    CELERY_RESULT_BACKEND: Optional[str] = None
    
    @validator("CELERY_BROKER_URL", pre=True)
    def assemble_celery_broker(cls, v: Optional[str], values: Dict[str, Any]) -> str:
        """Assemble Celery broker URL."""
        if isinstance(v, str):
            return v
        redis_url = values.get("REDIS_URL")
        if redis_url:
            return str(redis_url)
        return "redis://localhost:6379/0"

    @validator("CELERY_RESULT_BACKEND", pre=True)
    def assemble_celery_backend(cls, v: Optional[str], values: Dict[str, Any]) -> str:
        """Assemble Celery result backend URL."""
        if isinstance(v, str):
            return v
        redis_url = values.get("REDIS_URL")
        if redis_url:
            return str(redis_url)
        return "redis://localhost:6379/0"
    
    # AI Integration settings
    OPENAI_API_KEY: Optional[str] = None
    ANTHROPIC_API_KEY: Optional[str] = None
    AI_CHUNKING_ENABLED: bool = True
    AI_CHUNKING_CACHE_TTL: int = 3600  # 1 hour
    
    # Email settings
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[EmailStr] = None
    EMAILS_FROM_NAME: Optional[str] = None
    
    @validator("EMAILS_FROM_NAME")
    def get_project_name(cls, v: Optional[str], values: Dict[str, Any]) -> str:
        """Get project name for email sender."""
        if not v:
            return values["PROJECT_NAME"]
        return v
    
    # Notification settings
    NOTIFICATION_RATE_LIMIT: int = 100  # per minute
    PERSISTENT_NOTIFICATION_TIMEOUT: int = 300  # 5 minutes
    
    # ADHD-specific settings
    DEFAULT_CHUNK_SIZE: str = "small"
    MAX_SUBTASKS_PER_CHUNK: int = 7
    DEFAULT_FOCUS_DURATION: int = 25  # minutes
    DEFAULT_BREAK_DURATION: int = 5   # minutes
    HYPERFOCUS_WARNING_THRESHOLD: int = 120  # 2 hours
    
    # Rate limiting settings
    RATE_LIMIT_PER_MINUTE: int = 100
    RATE_LIMIT_PER_HOUR: int = 1000
    
    # Logging settings
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Testing settings
    TESTING: bool = False
    TEST_DATABASE_URL: Optional[str] = None
    
    class Config:
        """Pydantic configuration."""
        
        env_prefix = "CHRONOS_"
        case_sensitive = True
        env_file = ".env"


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """
    Get application settings.
    
    Returns:
        Settings: Application configuration settings
    """
    return settings
