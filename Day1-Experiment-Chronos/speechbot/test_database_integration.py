#!/usr/bin/env python3
"""
Test script for database integration
Tests voice profiles, user preferences, and analytics functionality
"""

import asyncio
import logging
import os
import sys
import time
import uuid
from pathlib import Path

# Add speechbot to path
sys.path.insert(0, str(Path(__file__).parent))

from services.database import db_service
from services.dia_engine import DiaEngine

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_database_initialization():
    """Test database initialization and connection."""
    logger.info("🗄️ Testing database initialization...")
    
    try:
        await db_service.initialize()
        logger.info("✅ Database initialized successfully")
        
        # Test health check
        health = await db_service.health_check()
        logger.info(f"✅ Database health: {health}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        return False


async def test_voice_profile_operations():
    """Test voice profile CRUD operations."""
    logger.info("🎭 Testing voice profile operations...")
    
    try:
        test_user_id = "test_user_123"
        
        # Create test voice profile
        import numpy as np
        test_audio = np.random.random(24000 * 8).astype(np.float32)  # 8 seconds at 24kHz
        test_features = {
            "pitch_mean": 150.0,
            "pitch_std": 25.0,
            "energy": 0.5,
            "spectral_features": test_audio[:100].tolist()
        }
        
        profile = await db_service.create_voice_profile(
            user_id=test_user_id,
            name="Test Voice",
            duration=8.0,
            quality_score=0.85,
            features=test_features,
            description="Test voice profile for database integration",
            sample_rate=24000
        )
        
        logger.info(f"✅ Created voice profile: {profile.id}")
        
        # Test retrieval
        retrieved_profile = await db_service.get_voice_profile(str(profile.id), test_user_id)
        if retrieved_profile and retrieved_profile.name == "Test Voice":
            logger.info("✅ Voice profile retrieval successful")
        else:
            logger.error("❌ Voice profile retrieval failed")
            return False
        
        # Test listing
        profiles = await db_service.list_voice_profiles(test_user_id)
        if len(profiles) >= 1:
            logger.info(f"✅ Listed {len(profiles)} voice profiles")
        else:
            logger.error("❌ Voice profile listing failed")
            return False
        
        # Test usage update
        await db_service.update_voice_profile_usage(str(profile.id))
        logger.info("✅ Voice profile usage updated")
        
        # Test deletion
        deleted = await db_service.delete_voice_profile(str(profile.id), test_user_id)
        if deleted:
            logger.info("✅ Voice profile deleted successfully")
        else:
            logger.error("❌ Voice profile deletion failed")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Voice profile operations failed: {e}")
        return False


async def test_user_preferences():
    """Test user preferences operations."""
    logger.info("⚙️ Testing user preferences...")
    
    try:
        test_user_id = "test_user_456"
        
        # Get default preferences (should create if not exists)
        prefs = await db_service.get_user_preferences(test_user_id)
        if prefs and prefs.default_adhd_mode == "calm":
            logger.info("✅ Default preferences created")
        else:
            logger.error("❌ Default preferences creation failed")
            return False
        
        # Update preferences
        update_data = {
            "default_adhd_mode": "focused",
            "enable_nonverbals": False,
            "nonverbal_frequency": 0.2,
            "attention_span_minutes": 30,
            "trigger_words": ["deadline", "urgent"],
            "comfort_phrases": ["you've got this", "take your time"]
        }
        
        updated_prefs = await db_service.update_user_preferences(test_user_id, update_data)
        if (updated_prefs and 
            updated_prefs.default_adhd_mode == "focused" and
            updated_prefs.attention_span_minutes == 30):
            logger.info("✅ Preferences updated successfully")
        else:
            logger.error("❌ Preferences update failed")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ User preferences test failed: {e}")
        return False


async def test_synthesis_session_logging():
    """Test synthesis session logging and analytics."""
    logger.info("📊 Testing synthesis session logging...")
    
    try:
        test_user_id = "test_user_789"
        
        # Log multiple synthesis sessions
        sessions_data = [
            {
                "session_type": "tts",
                "text_input": "Hello, this is a test message.",
                "adhd_mode": "calm",
                "generation_time": 1.5,
                "audio_duration": 3.0
            },
            {
                "session_type": "dialogue",
                "text_input": "Let's have a conversation about productivity.",
                "adhd_mode": "focused",
                "generation_time": 2.1,
                "audio_duration": 5.2,
                "speaker_configuration": {"[S1]": "user", "[S2]": "companion"}
            },
            {
                "session_type": "body_doubling",
                "text_input": "Working on my project today.",
                "adhd_mode": "motivated",
                "generation_time": 0.8,
                "audio_duration": 2.5
            }
        ]
        
        for session_data in sessions_data:
            session = await db_service.log_synthesis_session(
                user_id=test_user_id,
                **session_data
            )
            logger.info(f"✅ Logged session: {session.session_type}")
        
        # Test analytics
        analytics = await db_service.get_user_analytics(test_user_id, days=30)
        if analytics.get("total_sessions", 0) >= 3:
            logger.info(f"✅ Analytics generated: {analytics}")
        else:
            logger.error("❌ Analytics generation failed")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Synthesis session logging failed: {e}")
        return False


async def test_dialogue_templates():
    """Test dialogue template operations."""
    logger.info("💬 Testing dialogue templates...")
    
    try:
        # Create custom template
        template = await db_service.create_dialogue_template(
            name="Test Coaching Session",
            description="Custom coaching template for testing",
            category="coaching",
            dialogue_script=[
                {"speaker": "[S1]", "text": "I need help with this task."},
                {"speaker": "[S2]", "text": "Let's break it down step by step."}
            ],
            speaker_configuration={"[S1]": "user", "[S2]": "coach"},
            recommended_adhd_mode="focused",
            created_by="test_user",
            is_public=False
        )
        
        logger.info(f"✅ Created dialogue template: {template.id}")
        
        # Get templates
        templates = await db_service.get_dialogue_templates(category="coaching")
        if len(templates) >= 1:
            logger.info(f"✅ Retrieved {len(templates)} coaching templates")
        else:
            logger.error("❌ Template retrieval failed")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Dialogue template test failed: {e}")
        return False


async def test_integration_with_dia_engine():
    """Test database integration with Dia engine."""
    logger.info("🎭 Testing Dia engine database integration...")
    
    try:
        # Initialize Dia engine
        dia_engine = DiaEngine()
        await dia_engine.initialize()
        
        test_user_id = "test_user_integration"
        
        # Create voice profile through Dia engine
        import numpy as np
        test_audio = np.random.random(24000 * 10).astype(np.float32)  # 10 seconds
        
        profile_info = await dia_engine.create_voice_profile(
            profile_name="Integration Test Voice",
            audio_sample=test_audio,
            sample_rate=24000,
            user_id=test_user_id,
            description="Voice profile created through Dia engine integration test"
        )
        
        logger.info(f"✅ Dia engine created voice profile: {profile_info['profile_id']}")
        
        # Verify it's in database
        db_profile = await db_service.get_voice_profile(
            profile_info['profile_id'], 
            test_user_id
        )
        
        if db_profile and db_profile.name == "Integration Test Voice":
            logger.info("✅ Voice profile found in database")
        else:
            logger.error("❌ Voice profile not found in database")
            return False
        
        # Test synthesis with database logging
        audio, sample_rate = await dia_engine.synthesize_speech(
            text="This is a test of database integration with voice synthesis.",
            voice_profile=profile_info['profile_id'],
            adhd_mode="calm"
        )
        
        logger.info("✅ Synthesis with database logging completed")
        
        # Cleanup
        await dia_engine.cleanup()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Dia engine integration test failed: {e}")
        return False


async def test_performance_and_concurrency():
    """Test database performance under load."""
    logger.info("⚡ Testing database performance...")
    
    try:
        test_user_id = "test_user_performance"
        
        # Test concurrent operations
        tasks = []
        for i in range(10):
            task = db_service.log_synthesis_session(
                user_id=test_user_id,
                session_type="tts",
                text_input=f"Performance test message {i}",
                adhd_mode="calm",
                generation_time=1.0,
                audio_duration=2.0
            )
            tasks.append(task)
        
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        successful = sum(1 for r in results if not isinstance(r, Exception))
        logger.info(f"✅ Concurrent operations: {successful}/10 successful in {end_time-start_time:.2f}s")
        
        if successful >= 8:  # Allow for some failures
            return True
        else:
            logger.error("❌ Too many concurrent operation failures")
            return False
        
    except Exception as e:
        logger.error(f"❌ Performance test failed: {e}")
        return False


async def cleanup_test_data():
    """Clean up test data."""
    logger.info("🧹 Cleaning up test data...")
    
    try:
        # Note: In a real implementation, you'd want to clean up test data
        # For now, we'll just log that cleanup would happen here
        logger.info("✅ Test data cleanup completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Cleanup failed: {e}")
        return False


async def main():
    """Main test function."""
    logger.info("🎭 Speechbot Database Integration Tests")
    logger.info("=" * 50)
    
    success = True
    
    # Test database initialization
    if not await test_database_initialization():
        success = False
    
    # Test voice profile operations
    if success and not await test_voice_profile_operations():
        success = False
    
    # Test user preferences
    if success and not await test_user_preferences():
        success = False
    
    # Test synthesis session logging
    if success and not await test_synthesis_session_logging():
        success = False
    
    # Test dialogue templates
    if success and not await test_dialogue_templates():
        success = False
    
    # Test Dia engine integration
    if success and not await test_integration_with_dia_engine():
        success = False
    
    # Test performance
    if success and not await test_performance_and_concurrency():
        success = False
    
    # Cleanup
    await cleanup_test_data()
    await db_service.cleanup()
    
    logger.info("=" * 50)
    
    if success:
        logger.info("🎉 All database integration tests passed!")
        logger.info("Database integration is ready for production use")
    else:
        logger.error("❌ Some database integration tests failed")
        logger.info("Please review the errors and fix issues before proceeding")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
