# Speechbot Requirements - Dia TTS Integration
# Core dependencies for ADHD-optimized voice synthesis

# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database and caching
sqlalchemy[asyncio]==2.0.23
asyncpg==0.29.0
psycopg2-binary==2.9.9
redis==5.0.1
alembic==1.13.0

# Audio processing
librosa==0.10.1
soundfile==0.12.1
pydub==0.25.1
scipy==1.11.3
numpy==1.24.3

# Machine Learning and TTS (AMD ROCm optimized)
# Note: PyTorch ROCm versions installed separately in Dockerfile
transformers==4.35.0
accelerate==0.24.0
datasets==2.14.0

# Dia TTS - Real implementation
# Note: Install with: pip install git+https://github.com/nari-labs/dia.git
# dia @ git+https://github.com/nari-labs/dia.git

# Monitoring and observability
prometheus-client==0.19.0
structlog==23.2.0

# Utilities
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
httpx==0.25.2
aiofiles==23.2.1

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
mypy==1.7.1
