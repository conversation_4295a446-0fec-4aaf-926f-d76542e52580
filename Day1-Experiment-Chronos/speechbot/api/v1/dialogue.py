"""
Dialogue Generation API
Multi-speaker dialogue for ADHD body doubling and conversation scenarios
"""

import io
import logging
from typing import Dict, List, Optional

import soundfile as sf
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

from speechbot.services.dia_engine import DiaEngine

logger = logging.getLogger(__name__)

router = APIRouter()


# Request/Response Models
class DialogueTurn(BaseModel):
    """Single turn in a dialogue."""
    
    speaker: str = Field(
        ...,
        pattern="^\\[S[1-4]\\]$",
        description="Speaker identifier ([S1], [S2], etc.)"
    )
    
    text: str = Field(
        ...,
        min_length=1,
        max_length=1000,
        description="Text for this dialogue turn"
    )
    
    emotion: Optional[str] = Field(
        default=None,
        description="Emotional tone for this turn"
    )
    
    pause_before: Optional[float] = Field(
        default=0.5,
        ge=0,
        le=5.0,
        description="Pause before this turn in seconds"
    )


class DialogueRequest(BaseModel):
    """Dialogue generation request."""
    
    dialogue: List[DialogueTurn] = Field(
        ...,
        min_items=1,
        max_items=20,
        description="List of dialogue turns"
    )
    
    voice_profiles: Dict[str, str] = Field(
        default={},
        description="Mapping of speaker IDs to voice profile names"
    )
    
    adhd_mode: str = Field(
        default="conversational",
        pattern="^(conversational|focused|supportive|energetic|calm)$",
        description="ADHD optimization mode for dialogue"
    )
    
    scenario: Optional[str] = Field(
        default=None,
        description="Dialogue scenario (body_doubling, coaching, etc.)"
    )
    
    format: str = Field(
        default="wav",
        pattern="^(wav|mp3|flac)$",
        description="Audio format"
    )


class BodyDoublingRequest(BaseModel):
    """Body doubling session request."""
    
    user_task: str = Field(
        ...,
        max_length=200,
        description="Task the user is working on"
    )
    
    session_duration: int = Field(
        default=25,
        ge=5,
        le=120,
        description="Session duration in minutes"
    )
    
    encouragement_frequency: str = Field(
        default="medium",
        pattern="^(low|medium|high)$",
        description="How often to provide encouragement"
    )
    
    user_voice_profile: Optional[str] = Field(
        default=None,
        description="User's voice profile for responses"
    )
    
    companion_voice_profile: str = Field(
        default="default",
        description="Companion voice profile"
    )


class ConversationTemplate(BaseModel):
    """Conversation template for common scenarios."""
    
    name: str
    description: str
    speakers: List[str]
    turns: List[DialogueTurn]
    adhd_benefits: List[str]


def get_dia_engine(request: Request) -> DiaEngine:
    """Get Dia engine from application state."""
    if not hasattr(request.app.state, 'dia_engine'):
        raise HTTPException(
            status_code=503,
            detail="Dia TTS engine not available"
        )
    return request.app.state.dia_engine


@router.post(
    "/generate",
    summary="Generate multi-speaker dialogue",
    description="Generate realistic dialogue between multiple speakers"
)
async def generate_dialogue(
    request: DialogueRequest,
    dia_engine: DiaEngine = Depends(get_dia_engine)
):
    """
    Generate multi-speaker dialogue audio.
    
    This endpoint creates realistic conversations between multiple speakers,
    perfect for ADHD body doubling, coaching scenarios, and social practice.
    """
    try:
        logger.info(f"Generating dialogue: {len(request.dialogue)} turns")
        
        # Prepare dialogue script
        dialogue_script = []
        for turn in request.dialogue:
            dialogue_script.append({
                "speaker": turn.speaker,
                "text": turn.text,
                "emotion": turn.emotion,
                "pause_before": turn.pause_before
            })
        
        # Generate dialogue audio
        audio_array, sample_rate = await dia_engine.generate_dialogue(
            dialogue_script=dialogue_script,
            voice_profiles=request.voice_profiles,
            adhd_mode=request.adhd_mode
        )
        
        # Convert to audio file
        audio_buffer = io.BytesIO()
        
        if request.format == "wav":
            sf.write(audio_buffer, audio_array, sample_rate, format='WAV')
            media_type = "audio/wav"
        elif request.format == "mp3":
            sf.write(audio_buffer, audio_array, sample_rate, format='WAV')
            media_type = "audio/wav"  # Fallback
        else:
            sf.write(audio_buffer, audio_array, sample_rate, format='FLAC')
            media_type = "audio/flac"
        
        audio_buffer.seek(0)
        
        # Prepare response headers
        headers = {
            "X-Dialogue-Turns": str(len(request.dialogue)),
            "X-Audio-Duration": str(len(audio_array) / sample_rate),
            "X-ADHD-Mode": request.adhd_mode,
            "X-Speakers": ",".join(request.voice_profiles.keys())
        }
        
        return StreamingResponse(
            io.BytesIO(audio_buffer.read()),
            media_type=media_type,
            headers=headers
        )
        
    except Exception as e:
        logger.error(f"Dialogue generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Dialogue generation failed: {str(e)}"
        )


@router.post(
    "/body-doubling",
    summary="Generate body doubling session",
    description="Create a virtual body doubling companion for ADHD focus support"
)
async def generate_body_doubling_session(
    request: BodyDoublingRequest,
    dia_engine: DiaEngine = Depends(get_dia_engine)
):
    """
    Generate a body doubling session.
    
    Creates a virtual companion that provides presence and encouragement
    during work sessions, specifically designed for ADHD users.
    """
    try:
        logger.info(f"Creating body doubling session: {request.user_task}")
        
        # Create body doubling dialogue script
        dialogue_script = await _create_body_doubling_script(
            task=request.user_task,
            duration=request.session_duration,
            encouragement_frequency=request.encouragement_frequency
        )
        
        # Set up voice profiles
        voice_profiles = {
            "[S1]": request.user_voice_profile or "default",
            "[S2]": request.companion_voice_profile
        }
        
        # Generate dialogue
        audio_array, sample_rate = await dia_engine.generate_dialogue(
            dialogue_script=dialogue_script,
            voice_profiles=voice_profiles,
            adhd_mode="supportive"
        )
        
        # Convert to audio
        audio_buffer = io.BytesIO()
        sf.write(audio_buffer, audio_array, sample_rate, format='WAV')
        audio_buffer.seek(0)
        
        headers = {
            "X-Session-Type": "body-doubling",
            "X-Task": request.user_task,
            "X-Duration": str(request.session_duration),
            "X-Encouragement": request.encouragement_frequency
        }
        
        return StreamingResponse(
            io.BytesIO(audio_buffer.read()),
            media_type="audio/wav",
            headers=headers
        )
        
    except Exception as e:
        logger.error(f"Body doubling session generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Body doubling session failed: {str(e)}"
        )


@router.get(
    "/templates",
    response_model=List[ConversationTemplate],
    summary="Get conversation templates",
    description="Get pre-built conversation templates for common ADHD scenarios"
)
async def get_conversation_templates() -> List[ConversationTemplate]:
    """Get available conversation templates."""
    
    templates = [
        ConversationTemplate(
            name="Body Doubling - Work Session",
            description="Virtual companion for focused work sessions",
            speakers=["[S1]", "[S2]"],
            turns=[
                DialogueTurn(speaker="[S2]", text="Hi! Ready to work together on your task?"),
                DialogueTurn(speaker="[S1]", text="Yes, I'm working on my project today."),
                DialogueTurn(speaker="[S2]", text="Great! I'll be here working alongside you. Let's get started.")
            ],
            adhd_benefits=[
                "Provides accountability",
                "Reduces isolation",
                "Maintains motivation",
                "Gentle presence without pressure"
            ]
        ),
        ConversationTemplate(
            name="Task Coaching",
            description="Step-by-step guidance for complex tasks",
            speakers=["[S1]", "[S2]"],
            turns=[
                DialogueTurn(speaker="[S2]", text="Let's break this task down into smaller steps."),
                DialogueTurn(speaker="[S1]", text="That sounds helpful. Where should I start?"),
                DialogueTurn(speaker="[S2]", text="First, let's identify the main components...")
            ],
            adhd_benefits=[
                "Breaks down overwhelming tasks",
                "Provides structure",
                "Reduces executive function load",
                "Celebrates small wins"
            ]
        ),
        ConversationTemplate(
            name="Motivation Boost",
            description="Encouraging dialogue for difficult moments",
            speakers=["[S1]", "[S2]"],
            turns=[
                DialogueTurn(speaker="[S1]", text="I'm feeling overwhelmed with everything I need to do."),
                DialogueTurn(speaker="[S2]", text="That's completely understandable. Let's take this one step at a time."),
                DialogueTurn(speaker="[S2]", text="You've handled challenges before, and you can handle this too.")
            ],
            adhd_benefits=[
                "Provides emotional support",
                "Normalizes ADHD struggles",
                "Builds confidence",
                "Offers coping strategies"
            ]
        ),
        ConversationTemplate(
            name="Break Reminder",
            description="Gentle reminders to take breaks",
            speakers=["[S2]"],
            turns=[
                DialogueTurn(speaker="[S2]", text="You've been working hard for a while now. (gentle sigh)"),
                DialogueTurn(speaker="[S2]", text="How about taking a short break? Your brain will thank you."),
                DialogueTurn(speaker="[S2]", text="Even just 5 minutes can help you recharge.")
            ],
            adhd_benefits=[
                "Prevents burnout",
                "Maintains cognitive capacity",
                "Encourages self-care",
                "Improves long-term productivity"
            ]
        )
    ]
    
    return templates


@router.get(
    "/scenarios",
    summary="Get dialogue scenarios",
    description="Get available dialogue scenarios optimized for ADHD"
)
async def get_dialogue_scenarios() -> Dict:
    """Get available dialogue scenarios."""
    
    scenarios = {
        "body_doubling": {
            "name": "Body Doubling",
            "description": "Virtual companion for work sessions",
            "typical_duration": "25-90 minutes",
            "speakers": 2,
            "adhd_benefits": [
                "Accountability",
                "Reduced isolation",
                "Sustained motivation"
            ]
        },
        "task_coaching": {
            "name": "Task Coaching",
            "description": "Step-by-step guidance for complex tasks",
            "typical_duration": "10-30 minutes",
            "speakers": 2,
            "adhd_benefits": [
                "Task breakdown",
                "Executive function support",
                "Reduced overwhelm"
            ]
        },
        "social_practice": {
            "name": "Social Practice",
            "description": "Practice conversations and social scenarios",
            "typical_duration": "5-15 minutes",
            "speakers": "2-4",
            "adhd_benefits": [
                "Social skills development",
                "Anxiety reduction",
                "Confidence building"
            ]
        },
        "motivation_support": {
            "name": "Motivation Support",
            "description": "Encouraging dialogue for difficult moments",
            "typical_duration": "3-10 minutes",
            "speakers": "1-2",
            "adhd_benefits": [
                "Emotional support",
                "Confidence boost",
                "Coping strategies"
            ]
        }
    }
    
    return {
        "scenarios": scenarios,
        "total_count": len(scenarios),
        "adhd_optimized": True
    }


async def _create_body_doubling_script(
    task: str,
    duration: int,
    encouragement_frequency: str
) -> List[Dict]:
    """Create a body doubling dialogue script."""
    
    script = []
    
    # Opening
    script.append({
        "speaker": "[S2]",
        "text": f"Hi! I see you're working on {task} today. I'll be here working alongside you.",
        "pause_before": 0.5
    })
    
    script.append({
        "speaker": "[S2]",
        "text": "Let's start our focus session together. (gentle breath)",
        "pause_before": 1.0
    })
    
    # Middle encouragements based on frequency
    encouragement_intervals = {
        "low": duration // 3,
        "medium": duration // 4,
        "high": duration // 6
    }
    
    interval = encouragement_intervals.get(encouragement_frequency, duration // 4)
    
    for i in range(1, duration // interval):
        script.append({
            "speaker": "[S2]",
            "text": "You're doing great! Keep up the good work.",
            "pause_before": 2.0
        })
    
    # Closing
    script.append({
        "speaker": "[S2]",
        "text": "Great work on your session! You stayed focused and made progress.",
        "pause_before": 1.0
    })
    
    return script
