"""
User Preferences API
ADHD-specific settings and customization for voice synthesis
"""

import logging
from typing import Dict, Optional

from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException, Request
from pydantic import BaseModel, Field

from speechbot.services.database import db_service

logger = logging.getLogger(__name__)

router = APIRouter()


# Request/Response Models
class UserPreferencesUpdate(BaseModel):
    """User preferences update request."""
    
    default_adhd_mode: Optional[str] = Field(
        default=None,
        pattern="^(calm|excited|focused|overwhelmed|motivated)$",
        description="Default ADHD emotional mode"
    )
    
    default_voice_profile_id: Optional[str] = Field(
        default=None,
        description="Default voice profile UUID"
    )
    
    enable_nonverbals: Optional[bool] = Field(
        default=None,
        description="Enable nonverbal communication sounds"
    )
    
    nonverbal_frequency: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=1.0,
        description="Frequency of nonverbal sounds (0.0-1.0)"
    )
    
    default_encouragement_frequency: Optional[str] = Field(
        default=None,
        pattern="^(low|medium|high)$",
        description="Default encouragement frequency for body doubling"
    )
    
    enable_break_reminders: Optional[bool] = Field(
        default=None,
        description="Enable break reminders during sessions"
    )
    
    preferred_audio_format: Optional[str] = Field(
        default=None,
        pattern="^(wav|mp3|flac)$",
        description="Preferred audio format"
    )
    
    preferred_sample_rate: Optional[int] = Field(
        default=None,
        description="Preferred audio sample rate"
    )
    
    attention_span_minutes: Optional[int] = Field(
        default=None,
        ge=5,
        le=180,
        description="Typical attention span for session planning"
    )
    
    energy_patterns: Optional[Dict] = Field(
        default=None,
        description="Time-of-day energy level mapping"
    )
    
    trigger_words: Optional[list] = Field(
        default=None,
        description="Words or phrases that cause issues"
    )
    
    comfort_phrases: Optional[list] = Field(
        default=None,
        description="Phrases that provide comfort and support"
    )
    
    allow_analytics: Optional[bool] = Field(
        default=None,
        description="Allow usage analytics collection"
    )
    
    share_anonymous_data: Optional[bool] = Field(
        default=None,
        description="Share anonymous usage data for research"
    )


class UserPreferencesResponse(BaseModel):
    """User preferences response."""
    
    id: str
    user_id: str
    default_adhd_mode: str
    default_voice_profile_id: Optional[str]
    enable_nonverbals: bool
    nonverbal_frequency: float
    default_encouragement_frequency: str
    enable_break_reminders: bool
    preferred_audio_format: str
    preferred_sample_rate: int
    attention_span_minutes: Optional[int]
    energy_patterns: Optional[Dict]
    trigger_words: Optional[list]
    comfort_phrases: Optional[list]
    allow_analytics: bool
    share_anonymous_data: bool
    created_at: str
    updated_at: str


@router.get(
    "/{user_id}",
    response_model=UserPreferencesResponse,
    summary="Get user preferences",
    description="Get ADHD-specific preferences and settings for a user"
)
async def get_user_preferences(user_id: str) -> UserPreferencesResponse:
    """Get user preferences, creating defaults if none exist."""
    try:
        preferences = await db_service.get_user_preferences(user_id)
        
        if not preferences:
            raise HTTPException(
                status_code=404,
                detail="User preferences not found"
            )
        
        return UserPreferencesResponse(
            id=str(preferences.id),
            user_id=preferences.user_id,
            default_adhd_mode=preferences.default_adhd_mode,
            default_voice_profile_id=str(preferences.default_voice_profile_id) if preferences.default_voice_profile_id else None,
            enable_nonverbals=preferences.enable_nonverbals,
            nonverbal_frequency=preferences.nonverbal_frequency,
            default_encouragement_frequency=preferences.default_encouragement_frequency,
            enable_break_reminders=preferences.enable_break_reminders,
            preferred_audio_format=preferences.preferred_audio_format,
            preferred_sample_rate=preferences.preferred_sample_rate,
            attention_span_minutes=preferences.attention_span_minutes,
            energy_patterns=preferences.energy_patterns,
            trigger_words=preferences.trigger_words,
            comfort_phrases=preferences.comfort_phrases,
            allow_analytics=preferences.allow_analytics,
            share_anonymous_data=preferences.share_anonymous_data,
            created_at=preferences.created_at.isoformat(),
            updated_at=preferences.updated_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get user preferences: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get user preferences: {str(e)}"
        )


@router.put(
    "/{user_id}",
    response_model=UserPreferencesResponse,
    summary="Update user preferences",
    description="Update ADHD-specific preferences and settings"
)
async def update_user_preferences(
    user_id: str,
    preferences_update: UserPreferencesUpdate
) -> UserPreferencesResponse:
    """Update user preferences."""
    try:
        # Convert to dict, excluding None values
        update_data = {
            k: v for k, v in preferences_update.dict().items() 
            if v is not None
        }
        
        if not update_data:
            raise HTTPException(
                status_code=400,
                detail="No valid preferences provided for update"
            )
        
        # Update preferences
        preferences = await db_service.update_user_preferences(user_id, update_data)
        
        if not preferences:
            raise HTTPException(
                status_code=500,
                detail="Failed to update user preferences"
            )
        
        return UserPreferencesResponse(
            id=str(preferences.id),
            user_id=preferences.user_id,
            default_adhd_mode=preferences.default_adhd_mode,
            default_voice_profile_id=str(preferences.default_voice_profile_id) if preferences.default_voice_profile_id else None,
            enable_nonverbals=preferences.enable_nonverbals,
            nonverbal_frequency=preferences.nonverbal_frequency,
            default_encouragement_frequency=preferences.default_encouragement_frequency,
            enable_break_reminders=preferences.enable_break_reminders,
            preferred_audio_format=preferences.preferred_audio_format,
            preferred_sample_rate=preferences.preferred_sample_rate,
            attention_span_minutes=preferences.attention_span_minutes,
            energy_patterns=preferences.energy_patterns,
            trigger_words=preferences.trigger_words,
            comfort_phrases=preferences.comfort_phrases,
            allow_analytics=preferences.allow_analytics,
            share_anonymous_data=preferences.share_anonymous_data,
            created_at=preferences.created_at.isoformat(),
            updated_at=preferences.updated_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update user preferences: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update user preferences: {str(e)}"
        )


@router.get(
    "/{user_id}/analytics",
    summary="Get user analytics",
    description="Get usage analytics and insights for ADHD optimization"
)
async def get_user_analytics(
    user_id: str,
    days: int = 30
) -> Dict:
    """Get user analytics for the specified period."""
    try:
        if days < 1 or days > 365:
            raise HTTPException(
                status_code=400,
                detail="Days must be between 1 and 365"
            )
        
        analytics = await db_service.get_user_analytics(user_id, days)
        
        return {
            "user_id": user_id,
            "analytics": analytics,
            "insights": await _generate_adhd_insights(analytics)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get user analytics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get user analytics: {str(e)}"
        )


@router.post(
    "/{user_id}/reset",
    summary="Reset user preferences",
    description="Reset user preferences to default values"
)
async def reset_user_preferences(user_id: str) -> Dict:
    """Reset user preferences to defaults."""
    try:
        # Get current preferences to preserve some settings
        current_prefs = await db_service.get_user_preferences(user_id)
        
        # Reset to defaults while preserving user choice on analytics
        reset_data = {
            "default_adhd_mode": "calm",
            "default_voice_profile_id": None,
            "enable_nonverbals": True,
            "nonverbal_frequency": 0.1,
            "default_encouragement_frequency": "medium",
            "enable_break_reminders": True,
            "preferred_audio_format": "wav",
            "preferred_sample_rate": 24000,
            "attention_span_minutes": 25,
            "energy_patterns": None,
            "trigger_words": None,
            "comfort_phrases": None,
            # Preserve analytics preferences
            "allow_analytics": current_prefs.allow_analytics if current_prefs else True,
            "share_anonymous_data": current_prefs.share_anonymous_data if current_prefs else False
        }
        
        preferences = await db_service.update_user_preferences(user_id, reset_data)
        
        if not preferences:
            raise HTTPException(
                status_code=500,
                detail="Failed to reset user preferences"
            )
        
        return {
            "success": True,
            "message": "User preferences reset to defaults",
            "user_id": user_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reset user preferences: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to reset user preferences: {str(e)}"
        )


async def _generate_adhd_insights(analytics: Dict) -> Dict:
    """Generate ADHD-specific insights from analytics data."""
    insights = {
        "recommendations": [],
        "patterns": {},
        "achievements": []
    }
    
    try:
        total_sessions = analytics.get("total_sessions", 0)
        mode_usage = analytics.get("mode_usage", {})
        avg_generation_time = analytics.get("avg_generation_time", 0)
        avg_realtime_factor = analytics.get("avg_realtime_factor", 0)
        
        # Usage patterns
        if total_sessions > 0:
            insights["achievements"].append(f"Completed {total_sessions} voice synthesis sessions")
            
            # Most used ADHD mode
            if mode_usage:
                most_used_mode = max(mode_usage.items(), key=lambda x: x[1])
                insights["patterns"]["preferred_mode"] = most_used_mode[0]
                insights["patterns"]["mode_distribution"] = mode_usage
                
                # Mode-specific insights
                if most_used_mode[0] == "overwhelmed" and most_used_mode[1] > total_sessions * 0.3:
                    insights["recommendations"].append(
                        "Consider using 'calm' mode more often for better emotional regulation"
                    )
                elif most_used_mode[0] == "excited" and most_used_mode[1] > total_sessions * 0.5:
                    insights["recommendations"].append(
                        "Great energy! Try mixing in 'focused' mode for sustained attention"
                    )
        
        # Performance insights
        if avg_realtime_factor > 1.5:
            insights["achievements"].append("Excellent synthesis performance - faster than realtime!")
        elif avg_realtime_factor < 0.5:
            insights["recommendations"].append(
                "Consider optimizing your setup for better performance"
            )
        
        # Usage frequency insights
        if total_sessions < 5:
            insights["recommendations"].append(
                "Try using voice synthesis more regularly to build familiarity"
            )
        elif total_sessions > 50:
            insights["achievements"].append("Power user! You're making great use of voice synthesis")
        
    except Exception as e:
        logger.warning(f"Failed to generate insights: {e}")
    
    return insights
