"""
Streaming Audio API
Real-time audio generation with emotion detection
"""

import asyncio
import logging
import json
import time
from typing import Dict, Any, Optional

from fastapi import API<PERSON>outer, Depends, HTTPException, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from sse_starlette.sse import EventSourceResponse

from speechbot.services.streaming_engine import get_streaming_engine, get_emotion_engine
from speechbot.services.database import db_service

logger = logging.getLogger(__name__)

router = APIRouter()


# Request/Response Models
class StreamingSynthesisRequest(BaseModel):
    """Streaming synthesis request."""
    
    text: str = Field(..., min_length=1, max_length=5000, description="Text to synthesize")
    voice_profile: str = Field(default="default", description="Voice profile ID")
    adhd_mode: str = Field(
        default="calm",
        pattern="^(calm|excited|focused|overwhelmed|motivated)$",
        description="ADHD emotional mode"
    )
    user_id: str = Field(..., description="User ID")
    include_nonverbals: bool = Field(default=True, description="Include nonverbal sounds")
    auto_emotion_detection: bool = Field(default=False, description="Auto-detect emotion from text")
    stream_id: Optional[str] = Field(default=None, description="Custom stream ID")


class EmotionAnalysisRequest(BaseModel):
    """Emotion analysis request."""
    
    text: str = Field(..., min_length=1, max_length=5000, description="Text to analyze")
    user_id: str = Field(..., description="User ID")


class EmotionAnalysisResponse(BaseModel):
    """Emotion analysis response."""
    
    emotion_scores: Dict[str, float]
    suggested_adhd_mode: str
    insights: Dict[str, Any]
    confidence: float


@router.post(
    "/synthesize",
    summary="Stream audio synthesis",
    description="Generate streaming audio for real-time playback"
)
async def stream_synthesis(request: StreamingSynthesisRequest):
    """Stream audio synthesis with real-time chunks."""
    
    try:
        streaming_engine = get_streaming_engine()
        
        # Auto-detect emotion if requested
        adhd_mode = request.adhd_mode
        if request.auto_emotion_detection:
            emotion_engine = get_emotion_engine()
            emotion_scores = emotion_engine.detect_emotion_from_text(request.text)
            suggested_mode = emotion_engine.suggest_adhd_mode(emotion_scores)
            
            if suggested_mode != "calm":  # Only override if we detect something specific
                adhd_mode = suggested_mode
                logger.info(f"Auto-detected emotion mode: {adhd_mode}")
        
        async def generate_audio_stream():
            """Generate streaming audio chunks."""
            
            try:
                chunk_count = 0
                async for chunk in streaming_engine.stream_synthesis(
                    text=request.text,
                    voice_profile=request.voice_profile,
                    adhd_mode=adhd_mode,
                    user_id=request.user_id,
                    include_nonverbals=request.include_nonverbals,
                    stream_id=request.stream_id
                ):
                    # Convert audio chunk to bytes
                    if len(chunk.audio_data) > 0:
                        # Convert numpy array to WAV bytes
                        import io
                        import wave
                        import struct
                        
                        buffer = io.BytesIO()
                        with wave.open(buffer, 'wb') as wav_file:
                            wav_file.setnchannels(1)  # Mono
                            wav_file.setsampwidth(2)  # 16-bit
                            wav_file.setframerate(chunk.sample_rate)
                            
                            # Convert float32 to int16
                            audio_int16 = (chunk.audio_data * 32767).astype('int16')
                            wav_file.writeframes(audio_int16.tobytes())
                        
                        buffer.seek(0)
                        audio_bytes = buffer.read()
                        
                        # Create chunk header with metadata
                        chunk_header = f"--chunk-{chunk_count}\r\n"
                        chunk_header += f"Content-Type: audio/wav\r\n"
                        chunk_header += f"X-Chunk-Index: {chunk.chunk_index}\r\n"
                        chunk_header += f"X-Sample-Rate: {chunk.sample_rate}\r\n"
                        chunk_header += f"X-ADHD-Mode: {adhd_mode}\r\n"
                        chunk_header += f"X-Is-Final: {chunk.is_final}\r\n"
                        
                        if chunk.metadata:
                            chunk_header += f"X-Metadata: {json.dumps(chunk.metadata)}\r\n"
                        
                        chunk_header += f"Content-Length: {len(audio_bytes)}\r\n\r\n"
                        
                        yield chunk_header.encode() + audio_bytes + b"\r\n"
                        chunk_count += 1
                    
                    if chunk.is_final:
                        # Send final boundary
                        yield f"--chunk-{chunk_count}--\r\n".encode()
                        break
                        
            except Exception as e:
                logger.error(f"Streaming generation failed: {e}")
                error_response = f"--error\r\nContent-Type: application/json\r\n\r\n"
                error_response += json.dumps({"error": str(e)}) + "\r\n--error--\r\n"
                yield error_response.encode()
        
        return StreamingResponse(
            generate_audio_stream(),
            media_type="multipart/x-mixed-replace; boundary=chunk",
            headers={
                "X-Streaming": "true",
                "X-ADHD-Mode": adhd_mode,
                "Cache-Control": "no-cache",
                "Connection": "keep-alive"
            }
        )
        
    except Exception as e:
        logger.error(f"Streaming synthesis failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Streaming synthesis failed: {str(e)}"
        )


@router.get(
    "/events/{stream_id}",
    summary="Stream synthesis events",
    description="Server-sent events for streaming synthesis status"
)
async def stream_events(stream_id: str):
    """Server-sent events for streaming synthesis."""
    
    async def event_generator():
        """Generate server-sent events."""
        
        streaming_engine = get_streaming_engine()
        
        while True:
            try:
                # Get stream status
                active_streams = streaming_engine.get_active_streams()
                
                if stream_id in active_streams:
                    stream_data = active_streams[stream_id]
                    
                    event_data = {
                        "stream_id": stream_id,
                        "status": "active",
                        "chunks_sent": stream_data["chunks_sent"],
                        "total_duration": stream_data["total_duration"],
                        "elapsed_time": time.time() - stream_data["start_time"]
                    }
                    
                    yield {
                        "event": "stream_progress",
                        "data": json.dumps(event_data)
                    }
                else:
                    # Stream completed or not found
                    yield {
                        "event": "stream_complete",
                        "data": json.dumps({"stream_id": stream_id, "status": "complete"})
                    }
                    break
                
                await asyncio.sleep(0.5)  # Update every 500ms
                
            except Exception as e:
                logger.error(f"Event generation failed: {e}")
                yield {
                    "event": "error",
                    "data": json.dumps({"error": str(e)})
                }
                break
    
    return EventSourceResponse(event_generator())


@router.post(
    "/emotion/analyze",
    response_model=EmotionAnalysisResponse,
    summary="Analyze text emotion",
    description="Detect emotional state and suggest optimal ADHD mode"
)
async def analyze_emotion(request: EmotionAnalysisRequest):
    """Analyze emotional content of text."""
    
    try:
        emotion_engine = get_emotion_engine()
        
        # Detect emotions
        emotion_scores = emotion_engine.detect_emotion_from_text(request.text)
        suggested_mode = emotion_engine.suggest_adhd_mode(emotion_scores)
        insights = emotion_engine.get_emotion_insights(emotion_scores)
        
        # Calculate confidence based on highest emotion score
        confidence = max(emotion_scores.values()) if emotion_scores else 0.0
        
        # Log emotion analysis for user insights
        try:
            await db_service.log_synthesis_session(
                user_id=request.user_id,
                session_type="emotion_analysis",
                text_input=request.text,
                adhd_mode=suggested_mode,
                generation_time=0.0,
                audio_duration=0.0,
                metadata={
                    "emotion_scores": emotion_scores,
                    "confidence": confidence,
                    "suggested_mode": suggested_mode
                }
            )
        except Exception as e:
            logger.warning(f"Failed to log emotion analysis: {e}")
        
        return EmotionAnalysisResponse(
            emotion_scores=emotion_scores,
            suggested_adhd_mode=suggested_mode,
            insights=insights,
            confidence=confidence
        )
        
    except Exception as e:
        logger.error(f"Emotion analysis failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Emotion analysis failed: {str(e)}"
        )


@router.get(
    "/streams",
    summary="List active streams",
    description="Get information about currently active streaming sessions"
)
async def list_active_streams():
    """List all active streaming sessions."""
    
    try:
        streaming_engine = get_streaming_engine()
        active_streams = streaming_engine.get_active_streams()
        
        # Remove sensitive information
        public_streams = {}
        for stream_id, stream_data in active_streams.items():
            public_streams[stream_id] = {
                "start_time": stream_data["start_time"],
                "adhd_mode": stream_data["adhd_mode"],
                "chunks_sent": stream_data["chunks_sent"],
                "total_duration": stream_data["total_duration"],
                "elapsed_time": time.time() - stream_data["start_time"]
            }
        
        return {
            "active_streams": public_streams,
            "total_active": len(public_streams)
        }
        
    except Exception as e:
        logger.error(f"Failed to list streams: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to list active streams"
        )


@router.delete(
    "/streams/{stream_id}",
    summary="Stop streaming session",
    description="Stop an active streaming synthesis session"
)
async def stop_stream(stream_id: str):
    """Stop an active streaming session."""
    
    try:
        streaming_engine = get_streaming_engine()
        success = streaming_engine.stop_stream(stream_id)
        
        if success:
            return {
                "success": True,
                "message": f"Stream {stream_id} stopped successfully"
            }
        else:
            raise HTTPException(
                status_code=404,
                detail=f"Stream {stream_id} not found"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to stop stream: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to stop stream"
        )


@router.get(
    "/emotion/keywords",
    summary="Get emotion keywords",
    description="Get the keywords used for emotion detection"
)
async def get_emotion_keywords():
    """Get emotion detection keywords for transparency."""
    
    try:
        emotion_engine = get_emotion_engine()
        
        return {
            "emotion_keywords": emotion_engine.emotion_keywords,
            "description": "Keywords used for automatic emotion detection from text",
            "note": "These keywords help determine the optimal ADHD mode for synthesis"
        }
        
    except Exception as e:
        logger.error(f"Failed to get emotion keywords: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to get emotion keywords"
        )
