"""
Voice Profiles API
Voice cloning and personalization for ADHD users
"""

import io
import logging
from typing import Dict, List, Optional

import numpy as np
import soundfile as sf
from fastapi import APIRouter, Depends, File, Form, HTTPException, Request, UploadFile
from pydantic import BaseModel, Field

from speechbot.services.dia_engine import DiaEngine
from speechbot.services.database import db_service
from speechbot.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


# Request/Response Models
class VoiceProfileCreate(BaseModel):
    """Voice profile creation request."""
    
    name: str = Field(
        ...,
        min_length=1,
        max_length=50,
        description="Name for the voice profile"
    )
    
    description: Optional[str] = Field(
        default="",
        max_length=200,
        description="Optional description of the voice"
    )
    
    user_id: str = Field(
        ...,
        description="User identifier"
    )


class VoiceProfileResponse(BaseModel):
    """Voice profile response."""
    
    profile_id: str
    name: str
    user_id: str
    duration: float
    quality_score: float
    status: str
    created_at: float


class VoiceProfileUpdate(BaseModel):
    """Voice profile update request."""
    
    name: Optional[str] = Field(default=None, max_length=50)
    description: Optional[str] = Field(default=None, max_length=200)


def get_dia_engine(request: Request) -> DiaEngine:
    """Get Dia engine from application state."""
    if not hasattr(request.app.state, 'dia_engine'):
        raise HTTPException(
            status_code=503,
            detail="Dia TTS engine not available"
        )
    return request.app.state.dia_engine


@router.post(
    "/create",
    response_model=VoiceProfileResponse,
    summary="Create voice profile from audio sample",
    description="Create a personalized voice profile using 5-10 second audio sample"
)
async def create_voice_profile(
    name: str = Form(..., description="Voice profile name"),
    user_id: str = Form(..., description="User identifier"),
    description: str = Form("", description="Optional description"),
    audio_file: UploadFile = File(..., description="Audio sample (5-10 seconds)"),
    dia_engine: DiaEngine = Depends(get_dia_engine)
) -> VoiceProfileResponse:
    """
    Create a voice profile from an audio sample.
    
    This endpoint allows users to create personalized voice profiles
    using short audio samples (5-10 seconds) for voice cloning.
    """
    try:
        logger.info(f"Creating voice profile: {name} for user: {user_id}")
        
        # Validate file format
        if not audio_file.content_type.startswith('audio/'):
            raise HTTPException(
                status_code=400,
                detail="File must be an audio file"
            )
        
        # Check file size
        if audio_file.size > settings.UPLOAD_MAX_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: {settings.UPLOAD_MAX_SIZE} bytes"
            )
        
        # Read audio file
        audio_content = await audio_file.read()
        
        # Load audio data
        try:
            audio_data, sample_rate = sf.read(io.BytesIO(audio_content))
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid audio file: {str(e)}"
            )
        
        # Ensure mono audio
        if len(audio_data.shape) > 1:
            audio_data = np.mean(audio_data, axis=1)
        
        # Create voice profile
        profile_info = await dia_engine.create_voice_profile(
            profile_name=name,
            audio_sample=audio_data,
            sample_rate=sample_rate,
            user_id=user_id,
            description=description,
            audio_file_path=f"voice_samples/{user_id}/{audio_file.filename}"
        )
        
        return VoiceProfileResponse(
            profile_id=profile_info["profile_id"],
            name=profile_info["name"],
            user_id=user_id,
            duration=profile_info["duration"],
            quality_score=profile_info["quality_score"],
            status=profile_info["status"],
            created_at=profile_info.get("created_at", 0)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Voice profile creation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create voice profile: {str(e)}"
        )


@router.get(
    "/list/{user_id}",
    response_model=List[VoiceProfileResponse],
    summary="List user's voice profiles",
    description="Get all voice profiles for a specific user"
)
async def list_voice_profiles(
    user_id: str,
    dia_engine: DiaEngine = Depends(get_dia_engine)
) -> List[VoiceProfileResponse]:
    """List all voice profiles for a user."""
    try:
        # Get profiles from database
        db_profiles = await db_service.list_voice_profiles(user_id)

        user_profiles = []
        for profile in db_profiles:
            user_profiles.append(VoiceProfileResponse(
                profile_id=str(profile.id),
                name=profile.name,
                user_id=profile.user_id,
                duration=profile.duration,
                quality_score=profile.quality_score,
                status="ready",
                created_at=profile.created_at.timestamp()
            ))

        return user_profiles

    except Exception as e:
        logger.error(f"Failed to list voice profiles: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list voice profiles: {str(e)}"
        )


@router.get(
    "/{profile_id}",
    response_model=VoiceProfileResponse,
    summary="Get voice profile details",
    description="Get detailed information about a specific voice profile"
)
async def get_voice_profile(
    profile_id: str,
    dia_engine: DiaEngine = Depends(get_dia_engine)
) -> VoiceProfileResponse:
    """Get details of a specific voice profile."""
    try:
        if profile_id not in dia_engine.voice_profiles:
            raise HTTPException(
                status_code=404,
                detail="Voice profile not found"
            )
        
        profile_data = dia_engine.voice_profiles[profile_id]
        
        return VoiceProfileResponse(
            profile_id=profile_id,
            name=profile_data.get("name", "Unknown"),
            user_id=profile_data.get("user_id", "unknown"),
            duration=profile_data.get("duration", 0.0),
            quality_score=profile_data.get("quality_score", 0.0),
            status="ready",
            created_at=profile_data.get("created_at", 0)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get voice profile: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get voice profile: {str(e)}"
        )


@router.delete(
    "/{profile_id}",
    summary="Delete voice profile",
    description="Delete a voice profile and free up resources"
)
async def delete_voice_profile(
    profile_id: str,
    dia_engine: DiaEngine = Depends(get_dia_engine)
) -> Dict:
    """Delete a voice profile."""
    try:
        if profile_id not in dia_engine.voice_profiles:
            raise HTTPException(
                status_code=404,
                detail="Voice profile not found"
            )
        
        # Remove from cache
        del dia_engine.voice_profiles[profile_id]
        
        logger.info(f"Deleted voice profile: {profile_id}")
        
        return {
            "success": True,
            "message": f"Voice profile {profile_id} deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete voice profile: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete voice profile: {str(e)}"
        )


@router.post(
    "/{profile_id}/test",
    summary="Test voice profile",
    description="Generate a test audio sample using the voice profile"
)
async def test_voice_profile(
    profile_id: str,
    test_text: str = Form(
        default="Hello, this is a test of my voice profile.",
        description="Text to synthesize for testing"
    ),
    adhd_mode: str = Form(
        default="calm",
        description="ADHD mode for testing"
    ),
    dia_engine: DiaEngine = Depends(get_dia_engine)
):
    """Test a voice profile by generating sample audio."""
    try:
        if profile_id not in dia_engine.voice_profiles:
            raise HTTPException(
                status_code=404,
                detail="Voice profile not found"
            )
        
        # Generate test audio
        audio_array, sample_rate = await dia_engine.synthesize_speech(
            text=test_text,
            voice_profile=profile_id,
            adhd_mode=adhd_mode,
            include_nonverbals=True,
            speaker_id="[S1]"
        )
        
        # Convert to audio file
        audio_buffer = io.BytesIO()
        sf.write(audio_buffer, audio_array, sample_rate, format='WAV')
        audio_buffer.seek(0)
        
        from fastapi.responses import StreamingResponse
        
        return StreamingResponse(
            io.BytesIO(audio_buffer.read()),
            media_type="audio/wav",
            headers={
                "X-Voice-Profile": profile_id,
                "X-Test-Text": test_text,
                "X-ADHD-Mode": adhd_mode
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Voice profile test failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Voice profile test failed: {str(e)}"
        )


@router.get(
    "/requirements/audio",
    summary="Get audio requirements",
    description="Get requirements for voice cloning audio samples"
)
async def get_audio_requirements() -> Dict:
    """Get audio requirements for voice cloning."""
    
    return {
        "duration": {
            "minimum": 5,  # seconds
            "maximum": 15,  # seconds
            "recommended": 10  # seconds
        },
        "format": {
            "supported": ["wav", "mp3", "flac", "ogg"],
            "recommended": "wav"
        },
        "quality": {
            "sample_rate": {
                "minimum": 16000,
                "recommended": 24000,
                "maximum": 48000
            },
            "bit_depth": {
                "minimum": 16,
                "recommended": 24
            },
            "channels": "mono preferred, stereo acceptable"
        },
        "content": {
            "language": "Clear speech in target language",
            "background_noise": "Minimal background noise",
            "speech_style": "Natural, conversational tone",
            "recommended_text": [
                "Hello, my name is [Your Name]. I'm recording this sample for voice cloning.",
                "The quick brown fox jumps over the lazy dog. This sentence contains many sounds.",
                "I enjoy using technology to help with my daily tasks and productivity."
            ]
        },
        "tips": [
            "Record in a quiet environment",
            "Speak clearly and naturally",
            "Avoid background music or noise",
            "Use your normal speaking voice",
            "Include varied intonation and emotion"
        ]
    }
