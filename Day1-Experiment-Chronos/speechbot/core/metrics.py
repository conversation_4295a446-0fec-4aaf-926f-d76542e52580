"""
Metrics collection for Speechbot
Prometheus metrics for ADHD-optimized TTS monitoring
"""

from prometheus_client import Counter, Histogram, Gauge, Info, start_http_server
import logging

logger = logging.getLogger(__name__)


class TTSMetrics:
    """TTS-specific metrics collection."""
    
    def __init__(self):
        # Synthesis metrics
        self.synthesis_requests_total = Counter(
            'speechbot_synthesis_requests_total',
            'Total number of synthesis requests',
            ['voice_profile', 'adhd_mode', 'user_id']
        )
        
        self.synthesis_duration_seconds = Histogram(
            'speechbot_synthesis_duration_seconds',
            'Time spent synthesizing speech',
            ['text_length_bucket'],
            buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0]
        )
        
        self.synthesis_text_length = Histogram(
            'speechbot_synthesis_text_length',
            'Length of text being synthesized',
            buckets=[10, 50, 100, 200, 500, 1000, 2000, 5000]
        )
        
        # Voice profile metrics
        self.voice_profiles_created_total = Counter(
            'speechbot_voice_profiles_created_total',
            'Total number of voice profiles created',
            ['user_id']
        )
        
        self.voice_profile_quality_score = Histogram(
            'speechbot_voice_profile_quality_score',
            'Quality score of created voice profiles',
            buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
        )
        
        self.active_voice_profiles = Gauge(
            'speechbot_active_voice_profiles',
            'Number of active voice profiles in memory'
        )
        
        # ADHD-specific metrics
        self.adhd_mode_usage = Counter(
            'speechbot_adhd_mode_usage_total',
            'Usage count of different ADHD modes',
            ['mode', 'user_id']
        )
        
        self.adhd_nonverbal_usage = Counter(
            'speechbot_nonverbal_usage_total',
            'Usage of nonverbal communication features',
            ['nonverbal_type', 'adhd_mode']
        )
        
        # Dialogue metrics
        self.dialogue_requests_total = Counter(
            'speechbot_dialogue_requests_total',
            'Total number of dialogue generation requests',
            ['scenario_type', 'speaker_count']
        )
        
        self.dialogue_duration_seconds = Histogram(
            'speechbot_dialogue_duration_seconds',
            'Duration of generated dialogue audio',
            buckets=[5, 15, 30, 60, 120, 300, 600, 1800]
        )
        
        self.body_doubling_sessions = Counter(
            'speechbot_body_doubling_sessions_total',
            'Total number of body doubling sessions',
            ['duration_bucket', 'encouragement_frequency']
        )
        
        # Performance metrics
        self.model_memory_usage = Gauge(
            'speechbot_model_memory_usage_bytes',
            'Memory usage of TTS models'
        )
        
        self.gpu_utilization = Gauge(
            'speechbot_gpu_utilization_percent',
            'GPU utilization percentage'
        )
        
        self.concurrent_requests = Gauge(
            'speechbot_concurrent_requests',
            'Number of concurrent TTS requests'
        )
        
        # Error metrics
        self.errors_total = Counter(
            'speechbot_errors_total',
            'Total number of errors',
            ['error_type', 'operation']
        )
        
        # User satisfaction metrics
        self.user_satisfaction_score = Gauge(
            'speechbot_user_satisfaction_score',
            'User satisfaction with voice synthesis',
            ['user_id', 'voice_profile']
        )
        
        # System info
        self.system_info = Info(
            'speechbot_system_info',
            'System information'
        )


# Global metrics instance
tts_metrics = TTSMetrics()


def setup_metrics():
    """Setup metrics collection and export."""
    try:
        # Start Prometheus metrics server
        start_http_server(8002)  # Different port from main app
        logger.info("Metrics server started on port 8002")
        
        # Set system info
        tts_metrics.system_info.info({
            'version': '1.0.0',
            'engine': 'Dia TTS',
            'model_size': '1.6B',
            'adhd_optimized': 'true'
        })
        
    except Exception as e:
        logger.error(f"Failed to start metrics server: {e}")


class MetricsCollector:
    """Helper class for collecting metrics with context."""
    
    @staticmethod
    def record_synthesis(
        duration: float,
        text_length: int,
        voice_profile: str,
        adhd_mode: str,
        user_id: str = "anonymous"
    ):
        """Record synthesis metrics."""
        tts_metrics.synthesis_requests_total.labels(
            voice_profile=voice_profile,
            adhd_mode=adhd_mode,
            user_id=user_id
        ).inc()
        
        tts_metrics.synthesis_duration_seconds.labels(
            text_length_bucket=_get_text_length_bucket(text_length)
        ).observe(duration)
        
        tts_metrics.synthesis_text_length.observe(text_length)
        
        tts_metrics.adhd_mode_usage.labels(
            mode=adhd_mode,
            user_id=user_id
        ).inc()
    
    @staticmethod
    def record_voice_profile_creation(
        quality_score: float,
        user_id: str = "anonymous"
    ):
        """Record voice profile creation metrics."""
        tts_metrics.voice_profiles_created_total.labels(
            user_id=user_id
        ).inc()
        
        tts_metrics.voice_profile_quality_score.observe(quality_score)
    
    @staticmethod
    def record_dialogue_generation(
        duration: float,
        scenario_type: str,
        speaker_count: int
    ):
        """Record dialogue generation metrics."""
        tts_metrics.dialogue_requests_total.labels(
            scenario_type=scenario_type,
            speaker_count=str(speaker_count)
        ).inc()
        
        tts_metrics.dialogue_duration_seconds.observe(duration)
    
    @staticmethod
    def record_body_doubling_session(
        duration_minutes: int,
        encouragement_frequency: str
    ):
        """Record body doubling session metrics."""
        duration_bucket = _get_duration_bucket(duration_minutes)
        
        tts_metrics.body_doubling_sessions.labels(
            duration_bucket=duration_bucket,
            encouragement_frequency=encouragement_frequency
        ).inc()
    
    @staticmethod
    def record_error(error_type: str, operation: str):
        """Record error metrics."""
        tts_metrics.errors_total.labels(
            error_type=error_type,
            operation=operation
        ).inc()
    
    @staticmethod
    def update_active_voice_profiles(count: int):
        """Update active voice profiles count."""
        tts_metrics.active_voice_profiles.set(count)
    
    @staticmethod
    def update_concurrent_requests(count: int):
        """Update concurrent requests count."""
        tts_metrics.concurrent_requests.set(count)
    
    @staticmethod
    def record_user_satisfaction(
        score: float,
        user_id: str,
        voice_profile: str
    ):
        """Record user satisfaction score."""
        tts_metrics.user_satisfaction_score.labels(
            user_id=user_id,
            voice_profile=voice_profile
        ).set(score)


def _get_text_length_bucket(length: int) -> str:
    """Get text length bucket for metrics."""
    if length < 50:
        return "short"
    elif length < 200:
        return "medium"
    elif length < 500:
        return "long"
    else:
        return "very_long"


def _get_duration_bucket(minutes: int) -> str:
    """Get duration bucket for metrics."""
    if minutes < 10:
        return "short"
    elif minutes < 30:
        return "medium"
    elif minutes < 60:
        return "long"
    else:
        return "extended"


# Export metrics collector
metrics_collector = MetricsCollector()
