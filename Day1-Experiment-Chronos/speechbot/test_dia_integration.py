#!/usr/bin/env python3
"""
Test script for Dia TTS integration
Tests the real Dia model with ADHD-optimized features
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path

# Add speechbot to path
sys.path.insert(0, str(Path(__file__).parent))

from services.dia_engine import DiaEngine
from core.config import DiaConfig, ADHDConfig

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_dia_initialization():
    """Test Dia model initialization."""
    logger.info("🚀 Testing Dia TTS initialization...")
    
    try:
        engine = DiaEngine()
        await engine.initialize()
        
        logger.info("✅ Dia engine initialized successfully")
        return engine
        
    except Exception as e:
        logger.error(f"❌ Dia initialization failed: {e}")
        return None


async def test_basic_synthesis(engine: DiaEngine):
    """Test basic text-to-speech synthesis."""
    logger.info("🎤 Testing basic speech synthesis...")
    
    test_texts = [
        "[S1] Hello, this is a test of the Dia TTS system.",
        "[S1] I'm feeling calm and focused today. [S2] That's wonderful to hear!",
        "[S1] You're doing great work! (encouraging) Keep up the excellent progress.",
        "[S1] Let's take a deep breath together. (gentle breath) You've got this."
    ]
    
    for i, text in enumerate(test_texts):
        try:
            logger.info(f"Synthesizing test {i+1}: {text[:50]}...")
            
            start_time = time.time()
            audio, sample_rate = await engine.synthesize_speech(
                text=text,
                voice_profile="default",
                adhd_mode="calm",
                include_nonverbals=True
            )
            generation_time = time.time() - start_time
            
            duration = len(audio) / sample_rate
            realtime_factor = duration / generation_time if generation_time > 0 else 0
            
            logger.info(f"✅ Generated {duration:.2f}s audio in {generation_time:.2f}s (RTF: {realtime_factor:.2f}x)")
            
            # Save test audio
            output_file = f"test_synthesis_{i+1}.wav"
            import soundfile as sf
            sf.write(output_file, audio, sample_rate)
            logger.info(f"💾 Saved to {output_file}")
            
        except Exception as e:
            logger.error(f"❌ Synthesis test {i+1} failed: {e}")


async def test_adhd_modes(engine: DiaEngine):
    """Test different ADHD emotional modes."""
    logger.info("🧠 Testing ADHD emotional modes...")
    
    test_text = "[S1] You're making great progress on your tasks today!"
    modes = ["calm", "excited", "focused", "overwhelmed", "motivated"]
    
    for mode in modes:
        try:
            logger.info(f"Testing ADHD mode: {mode}")
            
            audio, sample_rate = await engine.synthesize_speech(
                text=test_text,
                voice_profile="default",
                adhd_mode=mode,
                include_nonverbals=True
            )
            
            duration = len(audio) / sample_rate
            logger.info(f"✅ {mode} mode: {duration:.2f}s audio generated")
            
            # Save mode-specific audio
            output_file = f"test_adhd_mode_{mode}.wav"
            import soundfile as sf
            sf.write(output_file, audio, sample_rate)
            
        except Exception as e:
            logger.error(f"❌ ADHD mode {mode} failed: {e}")


async def test_dialogue_generation(engine: DiaEngine):
    """Test multi-speaker dialogue generation."""
    logger.info("💬 Testing dialogue generation...")
    
    dialogue_script = [
        {
            "speaker": "[S1]",
            "text": "I'm feeling overwhelmed with this project.",
            "pause_before": 0.5
        },
        {
            "speaker": "[S2]", 
            "text": "That's completely understandable. (gentle) Let's break it down together.",
            "pause_before": 1.0
        },
        {
            "speaker": "[S1]",
            "text": "Where should I start?",
            "pause_before": 0.5
        },
        {
            "speaker": "[S2]",
            "text": "What's the most important part that needs to be done first?",
            "pause_before": 0.8
        }
    ]
    
    try:
        voice_profiles = {
            "[S1]": "default",
            "[S2]": "default"
        }
        
        audio, sample_rate = await engine.generate_dialogue(
            dialogue_script=dialogue_script,
            voice_profiles=voice_profiles,
            adhd_mode="supportive"
        )
        
        duration = len(audio) / sample_rate
        logger.info(f"✅ Dialogue generated: {duration:.2f}s audio with {len(dialogue_script)} turns")
        
        # Save dialogue audio
        output_file = "test_dialogue.wav"
        import soundfile as sf
        sf.write(output_file, audio, sample_rate)
        logger.info(f"💾 Saved dialogue to {output_file}")
        
    except Exception as e:
        logger.error(f"❌ Dialogue generation failed: {e}")


async def test_nonverbal_communication(engine: DiaEngine):
    """Test nonverbal communication features."""
    logger.info("😄 Testing nonverbal communication...")
    
    nonverbal_tests = [
        "[S1] That's amazing! (laughs) I'm so proud of you!",
        "[S1] I understand this is difficult. (sighs) Take your time.",
        "[S1] Wow, that's surprising! (gasps) I didn't expect that.",
        "[S1] Let me think about this. (clears throat) Here's what I suggest.",
        "[S1] You're doing so well. (gentle breath) Keep going."
    ]
    
    for i, text in enumerate(nonverbal_tests):
        try:
            logger.info(f"Testing nonverbal: {text}")
            
            audio, sample_rate = await engine.synthesize_speech(
                text=text,
                voice_profile="default",
                adhd_mode="calm",
                include_nonverbals=True
            )
            
            duration = len(audio) / sample_rate
            logger.info(f"✅ Nonverbal test {i+1}: {duration:.2f}s audio")
            
            # Save nonverbal audio
            output_file = f"test_nonverbal_{i+1}.wav"
            import soundfile as sf
            sf.write(output_file, audio, sample_rate)
            
        except Exception as e:
            logger.error(f"❌ Nonverbal test {i+1} failed: {e}")


async def test_performance_benchmarks(engine: DiaEngine):
    """Test performance benchmarks."""
    logger.info("⚡ Testing performance benchmarks...")
    
    # Test different text lengths
    test_cases = [
        ("[S1] Short test.", "short"),
        ("[S1] This is a medium length test to see how the model performs with moderate text input.", "medium"),
        ("[S1] This is a longer test case designed to evaluate the performance of the Dia TTS model with extended text input that might be more representative of real-world usage scenarios where users provide longer instructions or descriptions for synthesis.", "long")
    ]
    
    for text, length_category in test_cases:
        try:
            logger.info(f"Benchmarking {length_category} text ({len(text)} chars)")
            
            # Multiple runs for average
            times = []
            for run in range(3):
                start_time = time.time()
                audio, sample_rate = await engine.synthesize_speech(
                    text=text,
                    voice_profile="default",
                    adhd_mode="calm"
                )
                generation_time = time.time() - start_time
                times.append(generation_time)
                
                duration = len(audio) / sample_rate
                realtime_factor = duration / generation_time if generation_time > 0 else 0
                
                logger.info(f"Run {run+1}: {generation_time:.2f}s (RTF: {realtime_factor:.2f}x)")
            
            avg_time = sum(times) / len(times)
            avg_duration = len(audio) / sample_rate
            avg_rtf = avg_duration / avg_time if avg_time > 0 else 0
            
            logger.info(f"✅ {length_category} average: {avg_time:.2f}s (RTF: {avg_rtf:.2f}x)")
            
        except Exception as e:
            logger.error(f"❌ Performance test {length_category} failed: {e}")


async def test_health_check(engine: DiaEngine):
    """Test engine health check."""
    logger.info("🏥 Testing health check...")
    
    try:
        health_status = await engine.health_check()
        logger.info(f"✅ Health check: {health_status}")
        
        is_ready = await engine.is_ready()
        logger.info(f"✅ Ready status: {is_ready}")
        
    except Exception as e:
        logger.error(f"❌ Health check failed: {e}")


async def main():
    """Main test function."""
    logger.info("🎭 Starting Dia TTS Integration Tests")
    logger.info("=" * 50)
    
    # Check environment
    hf_token = os.getenv('HF_TOKEN')
    if not hf_token:
        logger.warning("⚠️ HF_TOKEN not set. Model download may fail.")
    
    # Initialize engine
    engine = await test_dia_initialization()
    if not engine:
        logger.error("❌ Cannot proceed without Dia engine")
        return
    
    try:
        # Run all tests
        await test_basic_synthesis(engine)
        await test_adhd_modes(engine)
        await test_dialogue_generation(engine)
        await test_nonverbal_communication(engine)
        await test_performance_benchmarks(engine)
        await test_health_check(engine)
        
        logger.info("=" * 50)
        logger.info("🎉 All tests completed!")
        
    except Exception as e:
        logger.error(f"❌ Test suite failed: {e}")
        
    finally:
        # Cleanup
        if engine:
            await engine.cleanup()
            logger.info("🧹 Engine cleanup completed")


if __name__ == "__main__":
    asyncio.run(main())
