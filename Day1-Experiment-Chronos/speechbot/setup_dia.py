#!/usr/bin/env python3
"""
Setup script for Dia TTS integration
Helps users install and configure Dia TTS for ADHD-optimized voice synthesis
"""

import os
import subprocess
import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def check_python_version():
    """Check if Python version is compatible."""
    logger.info("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        logger.error("❌ Python 3.8+ required. Current version: %s", sys.version)
        return False
    
    logger.info("✅ Python version: %s", sys.version.split()[0])
    return True


def check_gpu_availability():
    """Check GPU availability for optimal performance."""
    logger.info("🎮 Checking GPU availability...")
    
    try:
        import torch
        
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3) if gpu_count > 0 else 0
            
            logger.info("✅ CUDA available: %d GPU(s)", gpu_count)
            logger.info("   Primary GPU: %s (%.1f GB)", gpu_name, gpu_memory)
            
            if gpu_memory < 8:
                logger.warning("⚠️ GPU has less than 8GB VRAM. Performance may be limited.")
            
            return True
            
        else:
            logger.warning("⚠️ CUDA not available. CPU inference will be slower.")
            return False
            
    except ImportError:
        logger.warning("⚠️ PyTorch not installed. Cannot check GPU availability.")
        return False


def install_dia_tts():
    """Install Dia TTS from GitHub."""
    logger.info("📦 Installing Dia TTS...")
    
    try:
        # Install Dia TTS
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "git+https://github.com/nari-labs/dia.git"
        ], check=True)
        
        logger.info("✅ Dia TTS installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error("❌ Failed to install Dia TTS: %s", e)
        return False


def verify_dia_installation():
    """Verify Dia TTS installation."""
    logger.info("🔍 Verifying Dia TTS installation...")
    
    try:
        from dia.model import Dia
        logger.info("✅ Dia TTS import successful")
        return True
        
    except ImportError as e:
        logger.error("❌ Dia TTS import failed: %s", e)
        return False


def setup_huggingface_token():
    """Setup Hugging Face token for model downloads."""
    logger.info("🤗 Setting up Hugging Face token...")
    
    hf_token = os.getenv('HF_TOKEN')
    
    if hf_token:
        logger.info("✅ HF_TOKEN found in environment")
        return True
    
    logger.warning("⚠️ HF_TOKEN not found in environment")
    logger.info("To download Dia models, you need a Hugging Face token:")
    logger.info("1. Go to https://huggingface.co/settings/tokens")
    logger.info("2. Create a new token with 'Read' permissions")
    logger.info("3. Set it as environment variable: export HF_TOKEN=your_token_here")
    logger.info("4. Or add it to your .env file: HF_TOKEN=your_token_here")
    
    return False


def test_dia_model_download():
    """Test downloading Dia model."""
    logger.info("📥 Testing Dia model download...")
    
    try:
        from dia.model import Dia
        
        logger.info("Attempting to load Dia-1.6B model...")
        logger.info("This may take several minutes on first run...")
        
        # Try to load the model
        model = Dia.from_pretrained("nari-labs/Dia-1.6B", compute_dtype="float16")
        
        logger.info("✅ Dia model loaded successfully")
        logger.info("Model device: %s", next(model.parameters()).device if hasattr(model, 'parameters') else "Unknown")
        
        return True
        
    except Exception as e:
        logger.error("❌ Failed to load Dia model: %s", e)
        logger.info("This might be due to:")
        logger.info("- Missing HF_TOKEN")
        logger.info("- Network connectivity issues")
        logger.info("- Insufficient disk space")
        logger.info("- GPU memory limitations")
        return False


def create_env_file():
    """Create .env file with recommended settings."""
    logger.info("📝 Creating .env file...")
    
    env_file = Path(".env")
    
    if env_file.exists():
        logger.info("✅ .env file already exists")
        return
    
    env_content = """# Speechbot Environment Configuration

# Hugging Face Token (required for Dia model downloads)
# Get your token from: https://huggingface.co/settings/tokens
HF_TOKEN=your_token_here

# Dia TTS Configuration
DIA_DEVICE=cuda
DIA_COMPUTE_DTYPE=float16
DIA_USE_TORCH_COMPILE=true
DIA_SAMPLE_RATE=24000

# ADHD Features
ADHD_EMOTION_ADAPTATION=true
ADHD_NONVERBAL_ENABLED=true
ADHD_DIALOGUE_MODE=true

# Performance Settings
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30
MODEL_CACHE_SIZE=3

# Logging
LOG_LEVEL=INFO
DEBUG=false
"""
    
    try:
        env_file.write_text(env_content)
        logger.info("✅ Created .env file with default settings")
        logger.info("Please edit .env and add your HF_TOKEN")
        
    except Exception as e:
        logger.error("❌ Failed to create .env file: %s", e)


def run_basic_test():
    """Run a basic functionality test."""
    logger.info("🧪 Running basic functionality test...")
    
    try:
        from dia.model import Dia
        
        # Load model
        model = Dia.from_pretrained("nari-labs/Dia-1.6B", compute_dtype="float16")
        
        # Generate test audio
        test_text = "[S1] Hello, this is a test of the Dia TTS system for ADHD support."
        
        logger.info("Generating test audio...")
        output = model.generate(test_text, use_torch_compile=False, verbose=True)
        
        # Save test audio
        model.save_audio("dia_test.wav", output)
        
        logger.info("✅ Basic test completed successfully")
        logger.info("Test audio saved as: dia_test.wav")
        
        return True
        
    except Exception as e:
        logger.error("❌ Basic test failed: %s", e)
        return False


def print_next_steps():
    """Print next steps for the user."""
    logger.info("🎯 Next Steps:")
    logger.info("1. Edit .env file and add your HF_TOKEN")
    logger.info("2. Run: python test_dia_integration.py")
    logger.info("3. Start Speechbot service: python main.py")
    logger.info("4. Access API at: http://localhost:8001")
    logger.info("")
    logger.info("📚 Documentation:")
    logger.info("- Speechbot docs: docs/speechbot/")
    logger.info("- API reference: docs/speechbot/api-reference.rst")
    logger.info("- ADHD features: docs/speechbot/adhd-features.rst")
    logger.info("")
    logger.info("🆘 Need help?")
    logger.info("- Check logs for error details")
    logger.info("- Ensure GPU has 8GB+ VRAM")
    logger.info("- Verify HF_TOKEN is valid")
    logger.info("- Join Nari Labs Discord: https://discord.gg/bJq6vjRRKv")


def main():
    """Main setup function."""
    logger.info("🎭 Speechbot Dia TTS Setup")
    logger.info("=" * 50)
    
    success = True
    
    # Check prerequisites
    if not check_python_version():
        success = False
    
    gpu_available = check_gpu_availability()
    if not gpu_available:
        logger.info("💡 Consider using a GPU for better performance")
    
    # Install Dia TTS
    if not install_dia_tts():
        success = False
    
    # Verify installation
    if not verify_dia_installation():
        success = False
    
    # Setup HF token
    hf_token_available = setup_huggingface_token()
    
    # Create .env file
    create_env_file()
    
    # Test model download (only if HF token is available)
    if hf_token_available:
        if not test_dia_model_download():
            logger.warning("⚠️ Model download test failed")
    else:
        logger.info("⏭️ Skipping model download test (no HF_TOKEN)")
    
    # Run basic test (only if everything else succeeded)
    if success and hf_token_available:
        if not run_basic_test():
            logger.warning("⚠️ Basic functionality test failed")
    
    logger.info("=" * 50)
    
    if success:
        logger.info("🎉 Setup completed successfully!")
        print_next_steps()
    else:
        logger.error("❌ Setup completed with errors")
        logger.info("Please resolve the issues above and run setup again")


if __name__ == "__main__":
    main()
