# Speechbot Environment Configuration
# Copy this file to .env and configure your settings

# =============================================================================
# REQUIRED SETTINGS
# =============================================================================

# Hugging Face Token (REQUIRED for Dia model downloads)
# Get your token from: https://huggingface.co/settings/tokens
# Requires 'Read' permissions
HF_TOKEN=your_token_here

# =============================================================================
# DIA TTS CONFIGURATION
# =============================================================================

# Device for TTS processing
# Options: cuda, cpu, mps (Apple Silicon)
DIA_DEVICE=cuda

# Model precision
# float16: Faster, less memory, slightly lower quality
# float32: Slower, more memory, higher quality
DIA_COMPUTE_DTYPE=float16

# Enable torch.compile for performance optimization
# Requires PyTorch 2.0+ and compatible GPU
DIA_USE_TORCH_COMPILE=true

# Audio sample rate (Hz)
# 24000: Dia's native sample rate (recommended)
# 22050: Standard high quality
# 16000: Lower quality, faster processing
DIA_SAMPLE_RATE=24000

# Model and cache paths
DIA_MODEL_PATH=/app/models/dia
DIA_CACHE_PATH=/app/cache

# =============================================================================
# ADHD OPTIMIZATION FEATURES
# =============================================================================

# Enable emotional state adaptation
ADHD_EMOTION_ADAPTATION=true

# Enable nonverbal communication sounds
ADHD_NONVERBAL_ENABLED=true

# Enable multi-speaker dialogue mode
ADHD_DIALOGUE_MODE=true

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Maximum concurrent TTS requests
# Reduce if experiencing memory issues
MAX_CONCURRENT_REQUESTS=10

# Request timeout in seconds
REQUEST_TIMEOUT=30

# Model cache size (number of models to keep in memory)
MODEL_CACHE_SIZE=3

# Maximum text length per request (characters)
MAX_TEXT_LENGTH=5000

# =============================================================================
# VOICE CLONING SETTINGS
# =============================================================================

# Voice cloning duration limits (seconds)
VOICE_CLONE_MIN_DURATION=5
VOICE_CLONE_MAX_DURATION=15

# Maximum voice profiles per user
MAX_VOICE_PROFILES=50

# Voice samples storage path
VOICE_SAMPLES_PATH=/app/audio_samples

# =============================================================================
# AUDIO PROCESSING
# =============================================================================

# Default audio format for output
# Options: wav, mp3, flac
AUDIO_FORMAT=wav

# Audio quality setting
# Options: high, medium, low
AUDIO_QUALITY=high

# Maximum audio file size for uploads (bytes)
UPLOAD_MAX_SIZE=52428800  # 50MB

# Supported audio formats for voice cloning
ALLOWED_AUDIO_FORMATS=wav,mp3,flac,ogg

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database connection URL
DATABASE_URL=postgresql+asyncpg://chronos:chronos_pass@postgres:5432/chronos

# Redis connection URL (for caching and sessions)
REDIS_URL=redis://redis:6379/2

# =============================================================================
# API CONFIGURATION
# =============================================================================

# Server host and port
HOST=0.0.0.0
PORT=8001

# Allowed hosts for CORS
ALLOWED_HOSTS=*

# API rate limiting (requests per minute per user)
RATE_LIMIT_TTS=100
RATE_LIMIT_VOICE_PROFILES=10
RATE_LIMIT_DIALOGUE=50

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Secret key for JWT tokens
SECRET_KEY=your-secret-key-here

# JWT token expiration (minutes)
ACCESS_TOKEN_EXPIRE_MINUTES=30

# =============================================================================
# LOGGING AND MONITORING
# =============================================================================

# Log level
# Options: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# Enable debug mode (detailed logging, auto-reload)
DEBUG=false

# Enable Prometheus metrics collection
METRICS_ENABLED=true

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable development features
DEV_MODE=false

# Mock Dia implementation (for testing without real model)
USE_MOCK_DIA=false

# Enable verbose logging for TTS generation
TTS_VERBOSE=false

# =============================================================================
# PRODUCTION OPTIMIZATIONS
# =============================================================================

# Enable model preloading at startup
PRELOAD_MODELS=true

# Enable audio response caching
ENABLE_AUDIO_CACHE=true

# Audio cache TTL (seconds)
AUDIO_CACHE_TTL=3600

# Enable gzip compression for API responses
ENABLE_COMPRESSION=true

# =============================================================================
# MONITORING AND HEALTH CHECKS
# =============================================================================

# Health check interval (seconds)
HEALTH_CHECK_INTERVAL=30

# Model warmup on startup
ENABLE_MODEL_WARMUP=true

# Performance monitoring
ENABLE_PERFORMANCE_MONITORING=true

# =============================================================================
# ADHD-SPECIFIC SETTINGS
# =============================================================================

# Default ADHD mode for new users
DEFAULT_ADHD_MODE=calm

# Enable automatic ADHD mode detection
AUTO_ADHD_MODE_DETECTION=false

# Nonverbal frequency (0.0 to 1.0)
NONVERBAL_FREQUENCY=0.1

# Enable break reminders in body doubling
ENABLE_BREAK_REMINDERS=true

# Default encouragement frequency for body doubling
DEFAULT_ENCOURAGEMENT_FREQUENCY=medium

# =============================================================================
# EXPERIMENTAL FEATURES
# =============================================================================

# Enable experimental features (may be unstable)
ENABLE_EXPERIMENTAL=false

# Enable real-time streaming synthesis
ENABLE_STREAMING=false

# Enable voice emotion detection
ENABLE_EMOTION_DETECTION=false

# Enable advanced dialogue AI
ENABLE_ADVANCED_DIALOGUE=false
