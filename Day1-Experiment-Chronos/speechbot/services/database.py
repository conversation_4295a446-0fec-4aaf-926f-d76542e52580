"""
Database Service for Speechbot
Handles all database operations for voice profiles, user preferences, and analytics
"""

import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy import create_engine, and_, or_, desc, func
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
import uuid

from speechbot.core.config import settings
from speechbot.models.voice_profile import Base, VoiceProfile, SynthesisSession, UserPreferences, DialogueTemplate

logger = logging.getLogger(__name__)


class DatabaseService:
    """Database service for Speechbot operations."""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize database connection and create tables."""
        try:
            logger.info("Initializing database connection...")
            
            # Create engine
            self.engine = create_engine(
                settings.DATABASE_URL,
                pool_pre_ping=True,
                pool_recycle=300,
                echo=settings.DEBUG
            )
            
            # Create session factory
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # Create tables
            Base.metadata.create_all(bind=self.engine)
            
            self._initialized = True
            logger.info("Database initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def get_session(self) -> Session:
        """Get database session."""
        if not self._initialized:
            raise RuntimeError("Database not initialized")
        return self.SessionLocal()
    
    # Voice Profile Operations
    
    async def create_voice_profile(
        self,
        user_id: str,
        name: str,
        duration: float,
        quality_score: float,
        features: Dict,
        description: Optional[str] = None,
        audio_file_path: Optional[str] = None,
        sample_rate: int = 24000
    ) -> VoiceProfile:
        """Create a new voice profile."""
        try:
            with self.get_session() as session:
                # Check if user already has a profile with this name
                existing = session.query(VoiceProfile).filter(
                    and_(
                        VoiceProfile.user_id == user_id,
                        VoiceProfile.name == name,
                        VoiceProfile.is_active == True
                    )
                ).first()
                
                if existing:
                    raise ValueError(f"Voice profile '{name}' already exists for user")
                
                # Create new profile
                profile = VoiceProfile(
                    user_id=user_id,
                    name=name,
                    description=description,
                    duration=duration,
                    quality_score=quality_score,
                    sample_rate=sample_rate,
                    features=features,
                    audio_file_path=audio_file_path
                )
                
                session.add(profile)
                session.commit()
                session.refresh(profile)
                
                logger.info(f"Created voice profile: {profile.id} for user: {user_id}")
                return profile
                
        except Exception as e:
            logger.error(f"Failed to create voice profile: {e}")
            raise
    
    async def get_voice_profile(self, profile_id: str, user_id: str) -> Optional[VoiceProfile]:
        """Get a voice profile by ID and user."""
        try:
            with self.get_session() as session:
                profile = session.query(VoiceProfile).filter(
                    and_(
                        VoiceProfile.id == uuid.UUID(profile_id),
                        VoiceProfile.user_id == user_id,
                        VoiceProfile.is_active == True
                    )
                ).first()
                
                return profile
                
        except Exception as e:
            logger.error(f"Failed to get voice profile: {e}")
            return None
    
    async def list_voice_profiles(self, user_id: str) -> List[VoiceProfile]:
        """List all voice profiles for a user."""
        try:
            with self.get_session() as session:
                profiles = session.query(VoiceProfile).filter(
                    and_(
                        VoiceProfile.user_id == user_id,
                        VoiceProfile.is_active == True
                    )
                ).order_by(desc(VoiceProfile.created_at)).all()
                
                return profiles
                
        except Exception as e:
            logger.error(f"Failed to list voice profiles: {e}")
            return []
    
    async def update_voice_profile_usage(self, profile_id: str):
        """Update voice profile usage statistics."""
        try:
            with self.get_session() as session:
                profile = session.query(VoiceProfile).filter(
                    VoiceProfile.id == uuid.UUID(profile_id)
                ).first()
                
                if profile:
                    profile.usage_count += 1
                    profile.last_used_at = datetime.utcnow()
                    session.commit()
                    
        except Exception as e:
            logger.error(f"Failed to update voice profile usage: {e}")
    
    async def delete_voice_profile(self, profile_id: str, user_id: str) -> bool:
        """Soft delete a voice profile."""
        try:
            with self.get_session() as session:
                profile = session.query(VoiceProfile).filter(
                    and_(
                        VoiceProfile.id == uuid.UUID(profile_id),
                        VoiceProfile.user_id == user_id
                    )
                ).first()
                
                if profile:
                    profile.is_active = False
                    session.commit()
                    logger.info(f"Deleted voice profile: {profile_id}")
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"Failed to delete voice profile: {e}")
            return False
    
    # User Preferences Operations
    
    async def get_user_preferences(self, user_id: str) -> Optional[UserPreferences]:
        """Get user preferences, creating defaults if none exist."""
        try:
            with self.get_session() as session:
                prefs = session.query(UserPreferences).filter(
                    UserPreferences.user_id == user_id
                ).first()
                
                if not prefs:
                    # Create default preferences
                    prefs = UserPreferences(user_id=user_id)
                    session.add(prefs)
                    session.commit()
                    session.refresh(prefs)
                    logger.info(f"Created default preferences for user: {user_id}")
                
                return prefs
                
        except Exception as e:
            logger.error(f"Failed to get user preferences: {e}")
            return None
    
    async def update_user_preferences(
        self,
        user_id: str,
        preferences: Dict
    ) -> Optional[UserPreferences]:
        """Update user preferences."""
        try:
            with self.get_session() as session:
                prefs = session.query(UserPreferences).filter(
                    UserPreferences.user_id == user_id
                ).first()
                
                if not prefs:
                    prefs = UserPreferences(user_id=user_id)
                    session.add(prefs)
                
                # Update fields
                for key, value in preferences.items():
                    if hasattr(prefs, key):
                        setattr(prefs, key, value)
                
                prefs.updated_at = datetime.utcnow()
                session.commit()
                session.refresh(prefs)
                
                logger.info(f"Updated preferences for user: {user_id}")
                return prefs
                
        except Exception as e:
            logger.error(f"Failed to update user preferences: {e}")
            return None
    
    # Synthesis Session Operations
    
    async def log_synthesis_session(
        self,
        user_id: str,
        session_type: str,
        text_input: str,
        adhd_mode: str,
        generation_time: float,
        audio_duration: float,
        voice_profile_id: Optional[str] = None,
        include_nonverbals: bool = True,
        speaker_configuration: Optional[Dict] = None
    ) -> SynthesisSession:
        """Log a synthesis session for analytics."""
        try:
            with self.get_session() as session:
                # Calculate realtime factor
                realtime_factor = audio_duration / generation_time if generation_time > 0 else 0
                
                synthesis_session = SynthesisSession(
                    user_id=user_id,
                    voice_profile_id=uuid.UUID(voice_profile_id) if voice_profile_id else None,
                    session_type=session_type,
                    text_input=text_input,
                    text_length=len(text_input),
                    adhd_mode=adhd_mode,
                    include_nonverbals=include_nonverbals,
                    speaker_configuration=speaker_configuration,
                    generation_time=generation_time,
                    audio_duration=audio_duration,
                    realtime_factor=realtime_factor,
                    device_used=settings.DIA_DEVICE
                )
                
                session.add(synthesis_session)
                session.commit()
                session.refresh(synthesis_session)
                
                # Update voice profile usage if applicable
                if voice_profile_id:
                    await self.update_voice_profile_usage(voice_profile_id)
                
                return synthesis_session
                
        except Exception as e:
            logger.error(f"Failed to log synthesis session: {e}")
            raise
    
    async def get_user_analytics(self, user_id: str, days: int = 30) -> Dict:
        """Get user analytics for the specified period."""
        try:
            with self.get_session() as session:
                # Calculate date range
                start_date = datetime.utcnow() - timedelta(days=days)
                
                # Basic usage statistics
                total_sessions = session.query(SynthesisSession).filter(
                    and_(
                        SynthesisSession.user_id == user_id,
                        SynthesisSession.created_at >= start_date
                    )
                ).count()
                
                # ADHD mode usage
                mode_usage = session.query(
                    SynthesisSession.adhd_mode,
                    func.count(SynthesisSession.id).label('count')
                ).filter(
                    and_(
                        SynthesisSession.user_id == user_id,
                        SynthesisSession.created_at >= start_date
                    )
                ).group_by(SynthesisSession.adhd_mode).all()
                
                # Performance metrics
                avg_generation_time = session.query(
                    func.avg(SynthesisSession.generation_time)
                ).filter(
                    and_(
                        SynthesisSession.user_id == user_id,
                        SynthesisSession.created_at >= start_date
                    )
                ).scalar() or 0
                
                avg_realtime_factor = session.query(
                    func.avg(SynthesisSession.realtime_factor)
                ).filter(
                    and_(
                        SynthesisSession.user_id == user_id,
                        SynthesisSession.created_at >= start_date
                    )
                ).scalar() or 0
                
                return {
                    "period_days": days,
                    "total_sessions": total_sessions,
                    "mode_usage": {mode: count for mode, count in mode_usage},
                    "avg_generation_time": float(avg_generation_time),
                    "avg_realtime_factor": float(avg_realtime_factor)
                }
                
        except Exception as e:
            logger.error(f"Failed to get user analytics: {e}")
            return {}
    
    # Dialogue Template Operations
    
    async def get_dialogue_templates(
        self,
        category: Optional[str] = None,
        public_only: bool = True
    ) -> List[DialogueTemplate]:
        """Get dialogue templates."""
        try:
            with self.get_session() as session:
                query = session.query(DialogueTemplate)
                
                if public_only:
                    query = query.filter(DialogueTemplate.is_public == True)
                
                if category:
                    query = query.filter(DialogueTemplate.category == category)
                
                templates = query.order_by(desc(DialogueTemplate.usage_count)).all()
                return templates
                
        except Exception as e:
            logger.error(f"Failed to get dialogue templates: {e}")
            return []
    
    async def create_dialogue_template(
        self,
        name: str,
        description: str,
        category: str,
        dialogue_script: List[Dict],
        speaker_configuration: Dict,
        recommended_adhd_mode: str,
        created_by: Optional[str] = None,
        is_public: bool = False
    ) -> DialogueTemplate:
        """Create a new dialogue template."""
        try:
            with self.get_session() as session:
                template = DialogueTemplate(
                    name=name,
                    description=description,
                    category=category,
                    dialogue_script=dialogue_script,
                    speaker_configuration=speaker_configuration,
                    recommended_adhd_mode=recommended_adhd_mode,
                    created_by=created_by,
                    is_public=is_public
                )
                
                session.add(template)
                session.commit()
                session.refresh(template)
                
                logger.info(f"Created dialogue template: {template.id}")
                return template
                
        except Exception as e:
            logger.error(f"Failed to create dialogue template: {e}")
            raise
    
    # Health and Maintenance
    
    async def health_check(self) -> Dict:
        """Perform database health check."""
        try:
            with self.get_session() as session:
                # Test basic connectivity
                session.execute("SELECT 1")
                
                # Get basic statistics
                voice_profiles_count = session.query(VoiceProfile).filter(
                    VoiceProfile.is_active == True
                ).count()
                
                synthesis_sessions_count = session.query(SynthesisSession).count()
                
                return {
                    "status": "healthy",
                    "voice_profiles_count": voice_profiles_count,
                    "synthesis_sessions_count": synthesis_sessions_count,
                    "database_url": settings.DATABASE_URL.split('@')[1] if '@' in settings.DATABASE_URL else "unknown"
                }
                
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def cleanup(self):
        """Cleanup database connections."""
        if self.engine:
            self.engine.dispose()
            logger.info("Database connections cleaned up")


# Global database service instance
db_service = DatabaseService()
