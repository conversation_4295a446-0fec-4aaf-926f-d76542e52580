"""
Real-Time Streaming Audio Engine
Provides chunk-based audio generation for immediate playback
"""

import asyncio
import logging
import numpy as np
import torch
import time
from typing import AsyncGenerator, Dict, Any, Optional, List
from dataclasses import dataclass
from queue import Queue
import threading

from speechbot.services.dia_engine import DiaEngine
from speechbot.services.database import db_service

logger = logging.getLogger(__name__)


@dataclass
class StreamingChunk:
    """Represents a chunk of streaming audio data."""
    audio_data: np.ndarray
    sample_rate: int
    chunk_index: int
    total_chunks: Optional[int]
    metadata: Dict[str, Any]
    is_final: bool = False


class StreamingAudioEngine:
    """
    Real-time streaming audio generation engine.
    Generates audio in chunks for immediate playback while processing.
    """
    
    def __init__(self, dia_engine: DiaEngine):
        self.dia_engine = dia_engine
        self.chunk_size = 1024  # Audio samples per chunk
        self.buffer_size = 4096  # Buffer size for smooth playback
        self.active_streams: Dict[str, Dict] = {}
        
    async def stream_synthesis(
        self,
        text: str,
        voice_profile: str = "default",
        adhd_mode: str = "calm",
        user_id: str = None,
        include_nonverbals: bool = True,
        stream_id: str = None
    ) -> AsyncGenerator[StreamingChunk, None]:
        """
        Generate streaming audio chunks for real-time playback.
        
        Args:
            text: Text to synthesize
            voice_profile: Voice profile ID or "default"
            adhd_mode: ADHD emotional mode
            user_id: User ID for analytics
            include_nonverbals: Include nonverbal sounds
            stream_id: Unique stream identifier
            
        Yields:
            StreamingChunk: Audio chunks for immediate playback
        """
        
        if not stream_id:
            stream_id = f"stream_{int(time.time() * 1000)}"
        
        logger.info(f"Starting streaming synthesis: {stream_id}")
        
        try:
            # Initialize stream tracking
            self.active_streams[stream_id] = {
                "start_time": time.time(),
                "text": text,
                "voice_profile": voice_profile,
                "adhd_mode": adhd_mode,
                "user_id": user_id,
                "chunks_sent": 0,
                "total_duration": 0.0
            }
            
            # Pre-process text for streaming
            text_chunks = self._prepare_text_for_streaming(text)
            total_text_chunks = len(text_chunks)
            
            logger.info(f"Split text into {total_text_chunks} chunks for streaming")
            
            # Generate audio chunks
            chunk_index = 0
            total_audio_duration = 0.0
            
            for text_chunk in text_chunks:
                # Generate audio for this text chunk
                audio_data, sample_rate = await self._generate_chunk_audio(
                    text_chunk,
                    voice_profile,
                    adhd_mode,
                    include_nonverbals
                )
                
                if audio_data is not None and len(audio_data) > 0:
                    # Split audio into smaller chunks for streaming
                    audio_chunks = self._split_audio_for_streaming(audio_data, sample_rate)
                    
                    for audio_chunk in audio_chunks:
                        chunk_duration = len(audio_chunk) / sample_rate
                        total_audio_duration += chunk_duration
                        
                        # Create streaming chunk
                        streaming_chunk = StreamingChunk(
                            audio_data=audio_chunk,
                            sample_rate=sample_rate,
                            chunk_index=chunk_index,
                            total_chunks=None,  # Unknown until complete
                            metadata={
                                "text_chunk": text_chunk,
                                "adhd_mode": adhd_mode,
                                "voice_profile": voice_profile,
                                "chunk_duration": chunk_duration,
                                "total_duration_so_far": total_audio_duration,
                                "stream_id": stream_id
                            },
                            is_final=False
                        )
                        
                        # Update stream tracking
                        self.active_streams[stream_id]["chunks_sent"] += 1
                        self.active_streams[stream_id]["total_duration"] = total_audio_duration
                        
                        yield streaming_chunk
                        chunk_index += 1
                        
                        # Small delay to prevent overwhelming the client
                        await asyncio.sleep(0.01)
            
            # Send final chunk
            final_chunk = StreamingChunk(
                audio_data=np.array([]),
                sample_rate=sample_rate,
                chunk_index=chunk_index,
                total_chunks=chunk_index,
                metadata={
                    "stream_complete": True,
                    "total_duration": total_audio_duration,
                    "total_chunks": chunk_index,
                    "stream_id": stream_id,
                    "generation_time": time.time() - self.active_streams[stream_id]["start_time"]
                },
                is_final=True
            )
            
            yield final_chunk
            
            # Log session for analytics
            if user_id:
                await self._log_streaming_session(stream_id, user_id)
            
            logger.info(f"Streaming synthesis complete: {stream_id} ({total_audio_duration:.2f}s)")
            
        except Exception as e:
            logger.error(f"Streaming synthesis failed: {e}")
            
            # Send error chunk
            error_chunk = StreamingChunk(
                audio_data=np.array([]),
                sample_rate=24000,
                chunk_index=0,
                total_chunks=1,
                metadata={
                    "error": str(e),
                    "stream_id": stream_id
                },
                is_final=True
            )
            
            yield error_chunk
            
        finally:
            # Clean up stream tracking
            if stream_id in self.active_streams:
                del self.active_streams[stream_id]
    
    def _prepare_text_for_streaming(self, text: str) -> List[str]:
        """
        Split text into chunks suitable for streaming synthesis.
        
        Args:
            text: Input text
            
        Returns:
            List of text chunks
        """
        
        # Split by sentences first
        import re
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        # Group sentences into chunks of appropriate size
        chunks = []
        current_chunk = ""
        max_chunk_length = 200  # Characters per chunk
        
        for sentence in sentences:
            if len(current_chunk) + len(sentence) <= max_chunk_length:
                current_chunk += sentence + ". "
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + ". "
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        # Ensure we have at least one chunk
        if not chunks:
            chunks = [text]
        
        return chunks
    
    async def _generate_chunk_audio(
        self,
        text: str,
        voice_profile: str,
        adhd_mode: str,
        include_nonverbals: bool
    ) -> tuple[np.ndarray, int]:
        """
        Generate audio for a single text chunk.
        
        Args:
            text: Text chunk to synthesize
            voice_profile: Voice profile ID
            adhd_mode: ADHD mode
            include_nonverbals: Include nonverbal sounds
            
        Returns:
            Tuple of (audio_data, sample_rate)
        """
        
        try:
            # Use the existing Dia engine for synthesis
            audio_data, sample_rate = await self.dia_engine.synthesize_speech(
                text=text,
                voice_profile=voice_profile,
                adhd_mode=adhd_mode,
                include_nonverbals=include_nonverbals
            )
            
            return audio_data, sample_rate
            
        except Exception as e:
            logger.error(f"Chunk audio generation failed: {e}")
            return np.array([]), 24000
    
    def _split_audio_for_streaming(
        self,
        audio_data: np.ndarray,
        sample_rate: int
    ) -> List[np.ndarray]:
        """
        Split audio data into smaller chunks for streaming.
        
        Args:
            audio_data: Full audio data
            sample_rate: Audio sample rate
            
        Returns:
            List of audio chunks
        """
        
        if len(audio_data) <= self.chunk_size:
            return [audio_data]
        
        chunks = []
        for i in range(0, len(audio_data), self.chunk_size):
            chunk = audio_data[i:i + self.chunk_size]
            chunks.append(chunk)
        
        return chunks
    
    async def _log_streaming_session(self, stream_id: str, user_id: str):
        """
        Log streaming session for analytics.
        
        Args:
            stream_id: Stream identifier
            user_id: User ID
        """
        
        try:
            if stream_id not in self.active_streams:
                return
            
            stream_data = self.active_streams[stream_id]
            
            await db_service.log_synthesis_session(
                user_id=user_id,
                session_type="streaming_tts",
                text_input=stream_data["text"],
                adhd_mode=stream_data["adhd_mode"],
                voice_profile_id=stream_data["voice_profile"],
                generation_time=time.time() - stream_data["start_time"],
                audio_duration=stream_data["total_duration"],
                metadata={
                    "stream_id": stream_id,
                    "chunks_sent": stream_data["chunks_sent"],
                    "streaming": True
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to log streaming session: {e}")
    
    def get_active_streams(self) -> Dict[str, Dict]:
        """Get information about currently active streams."""
        return self.active_streams.copy()
    
    def stop_stream(self, stream_id: str) -> bool:
        """
        Stop an active stream.
        
        Args:
            stream_id: Stream identifier
            
        Returns:
            True if stream was stopped, False if not found
        """
        
        if stream_id in self.active_streams:
            del self.active_streams[stream_id]
            logger.info(f"Stopped stream: {stream_id}")
            return True
        
        return False


class EmotionDetectionEngine:
    """
    Real-time emotion detection from text and audio.
    Provides ADHD-specific emotional state analysis.
    """
    
    def __init__(self):
        self.emotion_keywords = {
            "overwhelmed": [
                "too much", "can't handle", "overwhelmed", "stressed", "anxious",
                "panic", "chaos", "scattered", "frazzled", "drowning"
            ],
            "focused": [
                "concentrate", "focus", "attention", "clear", "organized",
                "systematic", "methodical", "structured", "planned"
            ],
            "excited": [
                "excited", "energetic", "motivated", "enthusiastic", "pumped",
                "ready", "eager", "passionate", "inspired", "driven"
            ],
            "calm": [
                "calm", "peaceful", "relaxed", "steady", "balanced",
                "centered", "composed", "tranquil", "serene"
            ],
            "motivated": [
                "motivated", "determined", "confident", "capable", "strong",
                "accomplished", "successful", "proud", "achieving"
            ]
        }
    
    def detect_emotion_from_text(self, text: str) -> Dict[str, float]:
        """
        Detect emotional state from text content.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Dictionary of emotion scores (0.0 to 1.0)
        """
        
        text_lower = text.lower()
        emotion_scores = {}
        
        for emotion, keywords in self.emotion_keywords.items():
            score = 0.0
            for keyword in keywords:
                if keyword in text_lower:
                    score += 1.0
            
            # Normalize by number of keywords
            emotion_scores[emotion] = min(score / len(keywords), 1.0)
        
        return emotion_scores
    
    def suggest_adhd_mode(self, emotion_scores: Dict[str, float]) -> str:
        """
        Suggest optimal ADHD mode based on detected emotions.
        
        Args:
            emotion_scores: Emotion detection results
            
        Returns:
            Recommended ADHD mode
        """
        
        if not emotion_scores:
            return "calm"
        
        # Find the highest scoring emotion
        max_emotion = max(emotion_scores.items(), key=lambda x: x[1])
        
        if max_emotion[1] > 0.3:  # Threshold for emotion detection
            return max_emotion[0]
        
        return "calm"  # Default fallback
    
    def get_emotion_insights(self, emotion_scores: Dict[str, float]) -> Dict[str, Any]:
        """
        Generate insights and recommendations based on emotions.
        
        Args:
            emotion_scores: Emotion detection results
            
        Returns:
            Insights and recommendations
        """
        
        insights = {
            "primary_emotion": self.suggest_adhd_mode(emotion_scores),
            "emotion_scores": emotion_scores,
            "recommendations": [],
            "suggested_actions": []
        }
        
        primary_emotion = insights["primary_emotion"]
        
        if primary_emotion == "overwhelmed":
            insights["recommendations"].extend([
                "Consider using 'calm' mode for soothing speech",
                "Take breaks between tasks",
                "Try breaking large tasks into smaller steps"
            ])
            insights["suggested_actions"].extend([
                "Start a body doubling session",
                "Use breathing exercises",
                "Create a simple task list"
            ])
        
        elif primary_emotion == "excited":
            insights["recommendations"].extend([
                "Channel energy with 'motivated' mode",
                "Set clear goals to maintain focus",
                "Use this energy for challenging tasks"
            ])
            insights["suggested_actions"].extend([
                "Start a deep work session",
                "Tackle your most important task",
                "Create an action plan"
            ])
        
        elif primary_emotion == "focused":
            insights["recommendations"].extend([
                "Maintain momentum with 'focused' mode",
                "Minimize distractions",
                "Work on complex tasks while in this state"
            ])
            insights["suggested_actions"].extend([
                "Continue current task",
                "Set a focus timer",
                "Avoid switching contexts"
            ])
        
        return insights


# Global streaming engine instance
streaming_engine: Optional[StreamingAudioEngine] = None


def get_streaming_engine() -> StreamingAudioEngine:
    """Get the global streaming engine instance."""
    global streaming_engine
    
    if streaming_engine is None:
        from speechbot.services.dia_engine import get_dia_engine
        dia_engine = get_dia_engine()
        streaming_engine = StreamingAudioEngine(dia_engine)
    
    return streaming_engine


def get_emotion_engine() -> EmotionDetectionEngine:
    """Get emotion detection engine instance."""
    return EmotionDetectionEngine()
