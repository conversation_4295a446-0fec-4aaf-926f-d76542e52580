#!/usr/bin/env python3
"""
Database Migration Script
Creates all necessary tables for Speechbot with ADHD-optimized features
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add speechbot to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.database import db_service
from models.voice_profile import Base

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_tables():
    """Create all database tables."""
    try:
        logger.info("🗄️ Creating database tables...")
        
        # Initialize database service
        await db_service.initialize()
        
        # Create all tables
        Base.metadata.create_all(bind=db_service.engine)
        
        logger.info("✅ Database tables created successfully")
        
        # Verify tables exist
        with db_service.get_session() as session:
            # Test basic operations
            result = session.execute("SELECT 1")
            logger.info("✅ Database connection verified")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create tables: {e}")
        return False


async def seed_default_data():
    """Seed database with default data."""
    try:
        logger.info("🌱 Seeding default data...")
        
        # Create default dialogue templates
        default_templates = [
            {
                "name": "Body Doubling - Work Session",
                "description": "Virtual companion for focused work sessions",
                "category": "body_doubling",
                "dialogue_script": [
                    {"speaker": "[S2]", "text": "Hi! Ready to work together on your task?"},
                    {"speaker": "[S1]", "text": "Yes, I'm working on my project today."},
                    {"speaker": "[S2]", "text": "Great! I'll be here working alongside you. Let's get started."}
                ],
                "speaker_configuration": {"[S1]": "user", "[S2]": "companion"},
                "recommended_adhd_mode": "focused",
                "is_public": True,
                "is_verified": True
            },
            {
                "name": "Task Coaching",
                "description": "Step-by-step guidance for complex tasks",
                "category": "coaching",
                "dialogue_script": [
                    {"speaker": "[S2]", "text": "Let's break this task down into smaller steps."},
                    {"speaker": "[S1]", "text": "I don't even know where to start."},
                    {"speaker": "[S2]", "text": "That's okay. What's the end goal you're trying to achieve?"}
                ],
                "speaker_configuration": {"[S1]": "user", "[S2]": "coach"},
                "recommended_adhd_mode": "focused",
                "is_public": True,
                "is_verified": True
            },
            {
                "name": "Motivation Boost",
                "description": "Encouraging dialogue for difficult moments",
                "category": "motivation",
                "dialogue_script": [
                    {"speaker": "[S1]", "text": "I'm feeling really down about my progress today."},
                    {"speaker": "[S2]", "text": "I understand that feeling. Can you tell me one thing you accomplished?"},
                    {"speaker": "[S2]", "text": "That's actually really significant! You're being too hard on yourself."}
                ],
                "speaker_configuration": {"[S1]": "user", "[S2]": "supporter"},
                "recommended_adhd_mode": "supportive",
                "is_public": True,
                "is_verified": True
            }
        ]
        
        for template_data in default_templates:
            try:
                await db_service.create_dialogue_template(**template_data)
                logger.info(f"✅ Created template: {template_data['name']}")
            except Exception as e:
                logger.warning(f"⚠️ Failed to create template {template_data['name']}: {e}")
        
        logger.info("✅ Default data seeded successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to seed default data: {e}")
        return False


async def verify_migration():
    """Verify migration was successful."""
    try:
        logger.info("🔍 Verifying migration...")
        
        # Check database health
        health = await db_service.health_check()
        
        if health.get("status") == "healthy":
            logger.info("✅ Database health check passed")
            logger.info(f"   Voice profiles: {health.get('voice_profiles_count', 0)}")
            logger.info(f"   Synthesis sessions: {health.get('synthesis_sessions_count', 0)}")
            return True
        else:
            logger.error(f"❌ Database health check failed: {health}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Migration verification failed: {e}")
        return False


async def main():
    """Main migration function."""
    logger.info("🎭 Speechbot Database Migration")
    logger.info("=" * 50)
    
    success = True
    
    # Create tables
    if not await create_tables():
        success = False
    
    # Seed default data
    if success and not await seed_default_data():
        logger.warning("⚠️ Default data seeding failed, but tables were created")
    
    # Verify migration
    if success and not await verify_migration():
        success = False
    
    # Cleanup
    await db_service.cleanup()
    
    logger.info("=" * 50)
    
    if success:
        logger.info("🎉 Migration completed successfully!")
        logger.info("Next steps:")
        logger.info("1. Start Speechbot service: python main.py")
        logger.info("2. Test voice profile creation")
        logger.info("3. Verify user preferences API")
    else:
        logger.error("❌ Migration completed with errors")
        logger.info("Please check the logs and resolve issues before proceeding")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
