# Project Chronos - Agent 3: Task Management & AI Chunking

## Overview

Agent 3 implements the core task management system for Project Chronos, specifically designed for users with ADHD. This agent provides intelligent task breakdown, adaptive filtering, and decision fatigue reduction features.

## Features

### 🧠 ADHD-Optimized Task Management
- **Energy Level Tracking**: Tasks categorized by required energy (low/medium/high)
- **Context Tags**: Situational task filtering (home, office, computer, phone, etc.)
- **Soft Delete**: ADHD-friendly recovery of accidentally deleted items
- **Time Estimation Learning**: Track accuracy of time estimates

### 🤖 AI-Powered Task Chunking
- **Intelligent Breakdown**: Use OpenAI/Anthropic to break overwhelming tasks into manageable steps
- **Chunk Size Options**: Small (5-15 min), Medium (15-45 min), Large (45-90 min)
- **Context-Aware**: AI considers user preferences and ADHD-specific needs
- **Fallback Support**: Multiple AI providers for reliability

### 🎯 Adaptive Task Filtering
- **Energy-Based Selection**: Show tasks matching current energy level
- **Duration Filtering**: Filter by available time
- **Context Matching**: Show tasks relevant to current situation
- **Smart Prioritization**: Urgency scoring with ADHD considerations

### 🎲 Task Jar (Decision Fatigue Reduction)
- **Random Selection**: Curated random task selection to bypass choice paralysis
- **Weighted Randomization**: Priority and deadline-aware selection
- **Recent Task Exclusion**: Avoid recently completed or skipped tasks

## Quick Start

### Prerequisites
- Python 3.11+
- PostgreSQL 15+
- Redis
- Docker & Docker Compose (recommended)

### Setup with Docker (Recommended)

1. **Clone and setup environment**:
   ```bash
   git clone <repository-url>
   cd project-chronos
   cp .env.example .env
   ```

2. **Configure AI API keys** (optional but recommended):
   ```bash
   # Edit .env file and add your API keys
   OPENAI_API_KEY=your-openai-api-key
   ANTHROPIC_API_KEY=your-anthropic-api-key
   ```

3. **Start services**:
   ```bash
   docker-compose up -d
   ```

4. **Access the application**:
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health

### Manual Setup

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Setup PostgreSQL and Redis**:
   ```bash
   # PostgreSQL
   createdb chronos
   
   # Redis (start service)
   redis-server
   ```

3. **Run the application**:
   ```bash
   uvicorn app.main:app --reload
   ```

## API Endpoints

### Task Management
- `POST /api/v1/tasks/` - Create new task
- `GET /api/v1/tasks/` - Get tasks with filtering
- `GET /api/v1/tasks/{id}` - Get specific task
- `PUT /api/v1/tasks/{id}` - Update task
- `DELETE /api/v1/tasks/{id}` - Delete task (soft delete by default)

### AI Chunking
- `POST /api/v1/tasks/{id}/chunk` - Break down task using AI

### Adaptive Filtering
- `GET /api/v1/tasks/adaptive/filtered` - Get tasks with adaptive filtering
- `GET /api/v1/tasks/jar/random` - Get random task selection (task jar)

## ADHD-Specific Features

### Energy Level System
Tasks are categorized by required energy level:
- **Low**: Quick, easy tasks (5-15 minutes)
- **Medium**: Standard tasks (15-45 minutes)  
- **High**: Complex, demanding tasks (45+ minutes)

### Context Tags
Common context tags for ADHD users:
- `home`, `office`, `computer`, `phone`
- `errands`, `creative`, `admin`, `social`
- `quiet`, `noisy`, `focused`, `casual`

### AI Chunking Prompts
Specialized prompts designed for ADHD users:
- Remove ambiguity and decision points
- Use action verbs and specific outcomes
- Consider cognitive load and energy requirements
- Provide clear success criteria

### Decision Fatigue Reduction
- **Task Jar**: Random selection from filtered tasks
- **Adaptive Filtering**: Show only relevant tasks
- **Smart Defaults**: ADHD-optimized default settings

## Testing

Run the test suite:
```bash
# Unit tests
pytest tests/

# With coverage
pytest --cov=app tests/

# Specific test file
pytest tests/test_task_service.py -v
```

## Development

### Code Quality
- **Black**: Code formatting
- **Flake8**: Linting
- **MyPy**: Type checking
- **Pytest**: Testing

```bash
# Format code
black app/ tests/

# Lint code
flake8 app/ tests/

# Type check
mypy app/

# Run tests
pytest
```

### Database Migrations
```bash
# Generate migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head
```

## Configuration

Key environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `postgresql+asyncpg://chronos:chronos@localhost/chronos` |
| `REDIS_URL` | Redis connection string | `redis://localhost:6379/0` |
| `OPENAI_API_KEY` | OpenAI API key for chunking | None |
| `ANTHROPIC_API_KEY` | Anthropic API key for chunking | None |
| `DEFAULT_CHUNK_SIZE` | Default AI chunk size | `small` |
| `MAX_TASK_JAR_SIZE` | Maximum task jar selection | `10` |

## Architecture

```
app/
├── core/           # Configuration, database, exceptions
├── models/         # SQLAlchemy models
├── schemas/        # Pydantic schemas
├── services/       # Business logic
├── api/            # FastAPI endpoints
└── main.py         # Application entry point
```

## Contributing

1. Follow the ADHD-focused design principles
2. Write comprehensive docstrings
3. Include tests for new features
4. Update documentation
5. Use descriptive commit messages

## License

This project is part of Project Chronos, an ADHD-focused productivity system.

## Support

For ADHD-specific feature requests or issues, please consider the unique needs of neurodivergent users in your feedback.
