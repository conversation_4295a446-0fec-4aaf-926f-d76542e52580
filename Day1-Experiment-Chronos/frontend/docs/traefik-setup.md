# Traefik Setup for ADHD-Optimized Project Chronos

This guide explains how to set up Traefik reverse proxy for the Project Chronos frontend with ADHD-specific optimizations.

## Prerequisites

1. Docker and Docker Compose installed
2. Traefik network created: `docker network create traefik`
3. Domain configuration for `*.autism.localhost` (for local development)

## Quick Start

### 1. Create Traefik Network
```bash
docker network create traefik
```

### 2. Start Development Environment
```bash
# From the frontend directory
docker-compose -f docker-compose.dev.yml up -d
```

### 3. Access Services
- **Main App**: https://app.autism.localhost
- **Storybook**: https://storybook.autism.localhost
- **HMR**: http://hmr.autism.localhost (development only)

## Traefik Configuration

### ADHD-Specific Middleware

The setup includes several ADHD-optimized middleware configurations:

#### 1. Development Headers
```yaml
adhd-dev-headers:
  headers:
    customRequestHeaders:
      X-ADHD-Dev: "true"
      X-Cognitive-Load: "development"
    customResponseHeaders:
      X-ADHD-Optimized: "true"
```

#### 2. Production Security
```yaml
adhd-prod-security:
  headers:
    frameDeny: true
    contentTypeNosniff: true
    browserXssFilter: true
    referrerPolicy: "strict-origin-when-cross-origin"
    customRequestHeaders:
      X-ADHD-Secure: "true"
```

#### 3. Compression for Fast Loading
```yaml
adhd-prod-compression:
  compress: true
```

#### 4. Rate Limiting (ADHD-Friendly)
```yaml
adhd-prod-ratelimit:
  ratelimit:
    average: 100
    burst: 200
    period: "1m"
```

## Domain Configuration

### Local Development (*.autism.localhost)

Add to your `/etc/hosts` file:
```
127.0.0.1 app.autism.localhost
127.0.0.1 storybook.autism.localhost
127.0.0.1 hmr.autism.localhost
127.0.0.1 api.autism.localhost
127.0.0.1 ws.autism.localhost
```

### Production Domains

Update the Docker Compose files to use your production domains:
```yaml
- "traefik.http.routers.chronos-app.rule=Host(`your-domain.com`)"
```

## SSL/TLS Configuration

### Development (Self-Signed)
The development setup uses Traefik's built-in certificate resolver.

### Production (Let's Encrypt)
```yaml
- "traefik.http.routers.chronos-app.tls.certresolver=letsencrypt"
```

## Health Checks

All services include ADHD-optimized health checks:

- **Interval**: 30s (not too frequent to avoid resource waste)
- **Timeout**: 10s (quick enough for ADHD users)
- **Retries**: 3 (reasonable for stability)
- **Start Period**: 10-15s (allows for proper startup)

## Performance Optimizations

### 1. Caching Strategy
- Static assets: 1 year cache
- HTML: No cache (for dynamic updates)
- API responses: Compressed

### 2. Response Headers
- `X-ADHD-Optimized`: Indicates ADHD optimizations are active
- `X-Cognitive-Load`: Current cognitive load level
- `X-Response-Time`: Response time for monitoring

### 3. Compression
- Gzip enabled for all text-based content
- Minimum size: 1024 bytes
- Compression level: 6 (balance of speed/size)

## Monitoring

### Health Endpoints
- `/health.json`: JSON health status
- `/health`: Simple text health check

### ADHD-Specific Metrics
- Response times
- Cognitive load indicators
- User interaction patterns

## Troubleshooting

### Common Issues

1. **Services not accessible**
   - Check Traefik network: `docker network ls`
   - Verify DNS resolution: `nslookup app.autism.localhost`

2. **SSL Certificate Issues**
   - Check certificate resolver configuration
   - Verify domain ownership for Let's Encrypt

3. **Performance Issues**
   - Monitor response times via headers
   - Check compression is working
   - Verify caching headers

### Debug Commands

```bash
# Check Traefik logs
docker logs traefik

# Check service logs
docker-compose -f docker-compose.dev.yml logs chronos-frontend-dev

# Test health endpoints
curl -I http://app.autism.localhost/health
curl http://app.autism.localhost/health.json
```

## Security Considerations

### ADHD-Specific Security
- Rate limiting prevents overwhelming users
- Security headers reduce cognitive load from security warnings
- CORS properly configured for development

### Production Security
- All security headers enabled
- HTTPS enforced
- Frame protection active
- XSS protection enabled

## Scaling

### Horizontal Scaling
```yaml
deploy:
  replicas: 3
  resources:
    limits:
      cpus: '1.0'
      memory: 512M
```

### Load Balancing
Traefik automatically load balances between replicas with ADHD-friendly sticky sessions if needed.

## Integration with Backend

The frontend is configured to proxy API requests to:
- **API**: `http://chronos-api:8000`
- **WebSocket**: `http://chronos-ws:8001`

Update these in your backend Docker Compose configuration.

## Next Steps

1. Set up monitoring and alerting
2. Configure log aggregation
3. Implement performance monitoring
4. Set up automated deployments

For more information, see the main Project Chronos documentation.
