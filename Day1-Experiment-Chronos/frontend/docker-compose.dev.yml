# Docker Compose for ADHD-optimized frontend development
version: '3.8'

services:
  # Main frontend development server
  chronos-frontend-dev:
    build:
      context: .
      dockerfile: docker/Dockerfile.dev
    container_name: chronos-frontend-dev
    volumes:
      - .:/app
      - /app/node_modules
      - adhd_cache:/app/.vite
    networks:
      - chronos-dev
      - traefik
    environment:
      - NODE_ENV=development
      - VITE_API_URL=https://api.autism.localhost
      - VITE_WS_URL=wss://ws.autism.localhost
      - VITE_ADHD_DEBUG=true
      - VITE_COGNITIVE_LOAD_TRACKING=true
      - CHOKIDAR_USEPOLLING=true
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik"
      
      # Main app routing
      - "traefik.http.routers.chronos-dev.rule=Host(`app.autism.localhost`)"
      - "traefik.http.routers.chronos-dev.entrypoints=web,websecure"
      - "traefik.http.routers.chronos-dev.tls.certresolver=letsencrypt"
      - "traefik.http.services.chronos-dev.loadbalancer.server.port=3000"
      
      # HMR routing for development
      - "traefik.http.routers.chronos-hmr.rule=Host(`hmr.autism.localhost`)"
      - "traefik.http.routers.chronos-hmr.entrypoints=web"
      - "traefik.http.services.chronos-hmr.loadbalancer.server.port=3001"
      
      # ADHD-specific middleware
      - "traefik.http.routers.chronos-dev.middlewares=adhd-dev-headers,adhd-dev-cors"
      - "traefik.http.middlewares.adhd-dev-headers.headers.customrequestheaders.X-ADHD-Dev=true"
      - "traefik.http.middlewares.adhd-dev-headers.headers.customrequestheaders.X-Cognitive-Load=development"
      - "traefik.http.middlewares.adhd-dev-headers.headers.customresponseheaders.X-ADHD-Optimized=true"
      
      # CORS middleware for development
      - "traefik.http.middlewares.adhd-dev-cors.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE,PATCH"
      - "traefik.http.middlewares.adhd-dev-cors.headers.accesscontrolallowheaders=*"
      - "traefik.http.middlewares.adhd-dev-cors.headers.accesscontrolalloworiginlist=*"
      - "traefik.http.middlewares.adhd-dev-cors.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.adhd-dev-cors.headers.addvaryheader=true"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Storybook for ADHD component development
  chronos-storybook:
    build:
      context: .
      dockerfile: docker/Dockerfile.storybook
    container_name: chronos-storybook
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - traefik
    environment:
      - NODE_ENV=development
      - STORYBOOK_ADHD_MODE=true
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik"
      - "traefik.http.routers.chronos-storybook.rule=Host(`storybook.autism.localhost`)"
      - "traefik.http.routers.chronos-storybook.entrypoints=web,websecure"
      - "traefik.http.routers.chronos-storybook.tls.certresolver=letsencrypt"
      - "traefik.http.services.chronos-storybook.loadbalancer.server.port=6006"
      
      # ADHD-specific headers for Storybook
      - "traefik.http.routers.chronos-storybook.middlewares=adhd-storybook-headers"
      - "traefik.http.middlewares.adhd-storybook-headers.headers.customresponseheaders.X-ADHD-Storybook=true"
      - "traefik.http.middlewares.adhd-storybook-headers.headers.customresponseheaders.X-Component-Dev=enabled"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6006/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s

networks:
  chronos-dev:
    driver: bridge
    labels:
      - "traefik.enable=false"
  traefik:
    external: true

volumes:
  adhd_cache:
    driver: local
    labels:
      - "adhd.cache=vite"
      - "adhd.performance=optimized"
