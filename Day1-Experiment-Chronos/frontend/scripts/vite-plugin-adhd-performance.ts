// Vite Plugin for ADHD Performance Monitoring
// Analyzes build performance and provides ADHD-specific optimizations

import type { Plugin, ResolvedConfig } from 'vite';
import { writeFileSync } from 'fs';
import { join } from 'path';

interface ADHDPerformanceOptions {
  enabled?: boolean;
  cognitiveLoadThreshold?: number;
  bundleSizeThreshold?: number;
  reportPath?: string;
  enableRealTimeMonitoring?: boolean;
}

interface PerformanceMetrics {
  buildTime: number;
  bundleSize: number;
  chunkCount: number;
  cognitiveLoadScore: number;
  adhdOptimizationScore: number;
  recommendations: string[];
  warnings: string[];
}

interface ChunkAnalysis {
  name: string;
  size: number;
  cognitiveComplexity: number;
  adhdFriendly: boolean;
  recommendations: string[];
}

// Helper functions
function calculateCognitiveComplexity(code: string): number {
  let complexity = 0;

  // Count decision points (higher cognitive load)
  complexity += (code.match(/if\s*\(/g) || []).length * 2;
  complexity += (code.match(/\?\s*:/g) || []).length * 1.5;
  complexity += (code.match(/switch\s*\(/g) || []).length * 3;
  complexity += (code.match(/for\s*\(/g) || []).length * 1;
  complexity += (code.match(/while\s*\(/g) || []).length * 1;

  // Count async operations (can cause waiting/frustration)
  complexity += (code.match(/async\s+/g) || []).length * 1;
  complexity += (code.match(/await\s+/g) || []).length * 1;
  complexity += (code.match(/\.then\(/g) || []).length * 1;

  // Count DOM manipulations (visual complexity)
  complexity += (code.match(/document\./g) || []).length * 0.5;
  complexity += (code.match(/querySelector/g) || []).length * 0.5;

  // Count state management (cognitive overhead)
  complexity += (code.match(/useState|useReducer|useEffect/g) || []).length * 1;

  return Math.round(complexity);
}

function calculateADHDScore(metrics: PerformanceMetrics, bundleSizeThreshold: number): number {
  let score = 100;

  // Penalize large bundle size
  if (metrics.bundleSize > bundleSizeThreshold) {
    score -= 20;
  }

  // Penalize high cognitive load
  if (metrics.cognitiveLoadScore > 100) {
    score -= 15;
  }

  // Penalize slow build times (developer experience affects ADHD users)
  if (metrics.buildTime > 30000) { // 30 seconds
    score -= 10;
  }

  // Penalize warnings
  score -= metrics.warnings.length * 5;

  return Math.max(0, Math.round(score));
}

function getADHDGrade(score: number): string {
  if (score >= 90) return 'A+ (Excellent ADHD optimization)';
  if (score >= 80) return 'A (Good ADHD optimization)';
  if (score >= 70) return 'B (Acceptable ADHD optimization)';
  if (score >= 60) return 'C (Needs ADHD improvements)';
  return 'D (Poor ADHD optimization)';
}

export function adhdPerformancePlugin(options: ADHDPerformanceOptions = {}): Plugin {
  const {
    enabled = true,
    cognitiveLoadThreshold = 100000, // 100KB
    bundleSizeThreshold = 500000,    // 500KB
    reportPath = './adhd-performance-report.json',
    enableRealTimeMonitoring = true
  } = options;

  let config: ResolvedConfig;
  let buildStartTime: number;
  let metrics: PerformanceMetrics;

  return {
    name: 'adhd-performance',
    configResolved(resolvedConfig) {
      config = resolvedConfig;
    },

    buildStart() {
      if (!enabled) return;
      
      buildStartTime = Date.now();
      console.log('🧠 Starting ADHD-optimized build analysis...');
      
      metrics = {
        buildTime: 0,
        bundleSize: 0,
        chunkCount: 0,
        cognitiveLoadScore: 0,
        adhdOptimizationScore: 100,
        recommendations: [],
        warnings: []
      };
    },

    generateBundle(options, bundle) {
      if (!enabled) return;

      const chunks: ChunkAnalysis[] = [];
      let totalSize = 0;
      let totalCognitiveLoad = 0;

      // Analyze each chunk
      for (const [fileName, chunk] of Object.entries(bundle)) {
        if (chunk.type === 'chunk') {
          const chunkSize = chunk.code.length;
          const cognitiveComplexity = calculateCognitiveComplexity(chunk.code);
          
          totalSize += chunkSize;
          totalCognitiveLoad += cognitiveComplexity;

          const chunkAnalysis: ChunkAnalysis = {
            name: fileName,
            size: chunkSize,
            cognitiveComplexity,
            adhdFriendly: chunkSize < cognitiveLoadThreshold && cognitiveComplexity < 50,
            recommendations: []
          };

          chunks.push(chunkAnalysis);

          // Real-time warnings for large chunks
          if (chunkSize > cognitiveLoadThreshold) {
            const sizeKB = Math.round(chunkSize / 1024);
            console.warn(`⚠️  Large chunk detected: ${fileName} (${sizeKB}KB)`);
            console.warn('   This may impact ADHD users with slower connections');
            metrics.warnings.push(`Large chunk: ${fileName} (${sizeKB}KB)`);
            metrics.adhdOptimizationScore -= 10;
          }

          // Check for ADHD-unfriendly patterns (simplified)
          if (chunk.code.includes('setInterval') && !chunk.code.includes('clearInterval')) {
            console.warn(`⚠️  Potential memory leak in ${fileName} - missing clearInterval`);
            metrics.warnings.push(`Potential memory leak in ${fileName}`);
            metrics.adhdOptimizationScore -= 5;
          }
        }
      }

      // Update metrics
      metrics.bundleSize = totalSize;
      metrics.chunkCount = chunks.length;
      metrics.cognitiveLoadScore = totalCognitiveLoad;

      // Generate overall recommendations
      if (totalSize > bundleSizeThreshold) {
        metrics.recommendations.push('Overall bundle size is large - consider lazy loading');
        metrics.recommendations.push('Implement progressive loading for better ADHD user experience');
      }

      // Log immediate feedback
      const totalSizeKB = Math.round(totalSize / 1024);
      console.log(`📦 Total bundle size: ${totalSizeKB}KB`);
      console.log(`🧩 Number of chunks: ${chunks.length}`);

      const adhdFriendlyChunks = chunks.filter(chunk => chunk.adhdFriendly).length;
      const adhdFriendlyPercentage = Math.round((adhdFriendlyChunks / chunks.length) * 100);
      console.log(`🧠 ADHD-friendly chunks: ${adhdFriendlyChunks}/${chunks.length} (${adhdFriendlyPercentage}%)`);

      if (adhdFriendlyPercentage < 80) {
        console.warn('⚠️  Consider optimizing chunks for better ADHD user experience');
      }
    },

    buildEnd() {
      if (!enabled) return;

      metrics.buildTime = Date.now() - buildStartTime;

      // Calculate final ADHD optimization score
      metrics.adhdOptimizationScore = calculateADHDScore(metrics, bundleSizeThreshold);

      // Generate and save report
      const report = {
        timestamp: new Date().toISOString(),
        adhdOptimizationScore: metrics.adhdOptimizationScore,
        metrics: {
          buildTime: `${metrics.buildTime}ms`,
          bundleSize: `${Math.round(metrics.bundleSize / 1024)}KB`,
          chunkCount: metrics.chunkCount,
          cognitiveLoadScore: metrics.cognitiveLoadScore
        },
        adhdOptimizations: {
          recommendations: metrics.recommendations,
          warnings: metrics.warnings,
          score: metrics.adhdOptimizationScore,
          grade: getADHDGrade(metrics.adhdOptimizationScore)
        }
      };

      try {
        writeFileSync(join(config.root, reportPath), JSON.stringify(report, null, 2));
        console.log(`📄 ADHD performance report saved to: ${reportPath}`);
      } catch (error) {
        console.warn('⚠️  Could not save ADHD performance report:', error);
      }

      console.log('✅ ADHD performance analysis complete!');
      console.log(`📊 ADHD Optimization Score: ${metrics.adhdOptimizationScore}/100`);

      if (metrics.adhdOptimizationScore < 70) {
        console.warn('⚠️  Consider implementing ADHD optimizations for better user experience');
      }
    }
  };
}
