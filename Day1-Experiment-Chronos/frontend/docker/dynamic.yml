# Dynamic Traefik Configuration for ADHD Optimizations
# This file defines middleware and other dynamic configurations

http:
  middlewares:
    # ADHD Development Headers
    adhd-dev-headers:
      headers:
        customRequestHeaders:
          X-ADHD-Dev: "true"
          X-Cognitive-Load: "development"
          X-Environment: "dev"
        customResponseHeaders:
          X-ADHD-Optimized: "true"
          X-Performance-Mode: "development"
          X-Cache-Strategy: "no-cache"

    # ADHD Production Security Headers
    adhd-prod-security:
      headers:
        frameDeny: true
        contentTypeNosniff: true
        browserXssFilter: true
        referrerPolicy: "strict-origin-when-cross-origin"
        stsSeconds: 31536000
        stsIncludeSubdomains: true
        stsPreload: true
        customRequestHeaders:
          X-ADHD-Secure: "true"
        customResponseHeaders:
          X-ADHD-Optimized: "true"
          X-Security-Level: "high"

    # ADHD Performance Headers
    adhd-prod-headers:
      headers:
        customResponseHeaders:
          X-ADHD-Optimized: "true"
          X-Cognitive-Load: "managed"
          X-Response-Time-Optimized: "true"
          X-Performance-Mode: "production"
          # Cache control for static assets
          Cache-Control: "public, max-age=31536000, immutable"
          # Preload hints for better performance
          Link: "</static/css/main.css>; rel=preload; as=style, </static/js/main.js>; rel=preload; as=script"

    # Compression for faster loading (ADHD users benefit from speed)
    adhd-compression:
      compress:
        excludedContentTypes:
          - "text/event-stream"
        minResponseBodyBytes: 1024

    # Rate limiting optimized for ADHD users
    adhd-rate-limit:
      rateLimit:
        average: 100
        burst: 200
        period: "1m"
        sourceCriterion:
          ipStrategy:
            depth: 1

    # ADHD-friendly CORS for development
    adhd-dev-cors:
      headers:
        accessControlAllowMethods:
          - "GET"
          - "OPTIONS"
          - "PUT"
          - "POST"
          - "DELETE"
          - "PATCH"
        accessControlAllowHeaders:
          - "*"
        accessControlAllowOriginList:
          - "*"
        accessControlMaxAge: 100
        addVaryHeader: true

    # Circuit breaker to prevent overwhelming ADHD users
    adhd-circuit-breaker:
      circuitBreaker:
        expression: "NetworkErrorRatio() > 0.3"
        checkPeriod: "10s"
        fallbackDuration: "30s"
        recoveryDuration: "30s"

    # Retry middleware with ADHD-friendly settings
    adhd-retry:
      retry:
        attempts: 3
        initialInterval: "100ms"

    # IP whitelist for admin access
    adhd-admin-whitelist:
      ipWhiteList:
        sourceRange:
          - "127.0.0.1/32"
          - "10.0.0.0/8"
          - "**********/12"
          - "***********/16"

    # Redirect to HTTPS
    https-redirect:
      redirectScheme:
        scheme: "https"
        permanent: true

    # Strip prefix for API routing
    api-strip-prefix:
      stripPrefix:
        prefixes:
          - "/api"

    # Add prefix for legacy support
    add-api-prefix:
      addPrefix:
        prefix: "/api/v1"

  # Services (if needed for load balancing)
  services:
    # Example service for multiple backend instances
    chronos-api-lb:
      loadBalancer:
        servers:
          - url: "http://chronos-api-1:8000"
          - url: "http://chronos-api-2:8000"
        healthCheck:
          path: "/health"
          interval: "30s"
          timeout: "10s"
          scheme: "http"

# TLS configuration
tls:
  options:
    # Modern TLS configuration
    modern:
      minVersion: "VersionTLS12"
      cipherSuites:
        - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
        - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
        - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
        - "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256"
      curvePreferences:
        - "CurveP521"
        - "CurveP384"
      sniStrict: true

    # Default TLS configuration
    default:
      minVersion: "VersionTLS12"
      sniStrict: false

  # Certificate stores
  stores:
    default:
      defaultCertificate:
        certFile: "/certs/default.crt"
        keyFile: "/certs/default.key"

# TCP services (for WebSocket connections)
tcp:
  services:
    chronos-websocket:
      loadBalancer:
        servers:
          - address: "chronos-ws:8001"
