# Production Dockerfile for ADHD-optimized React frontend
# Multi-stage build for optimal performance

# Build stage
FROM node:18-alpine AS builder

# Install pnpm globally (use version 9 to match lockfile)
RUN npm install -g pnpm@9

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Set production environment variables for ADHD optimization
ENV NODE_ENV=production
ENV VITE_ADHD_OPTIMIZED=true
ENV VITE_COGNITIVE_LOAD_TRACKING=false

# Build the application with ADHD optimizations
RUN pnpm build

# Production stage
FROM nginx:alpine

# Install curl for health checks
RUN apk add --no-cache curl

# Copy built application from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy ADHD-optimized nginx configuration
COPY docker/nginx-adhd.conf /etc/nginx/conf.d/default.conf

# Create health check endpoint
RUN echo '{"status":"healthy","timestamp":"'$(date -Iseconds)'","adhd_optimized":true}' > /usr/share/nginx/html/health.json

# Create non-root user for security
RUN addgroup -g 1001 -S nginx
RUN adduser -S chronos -u 1001 -G nginx
RUN chown -R chronos:nginx /usr/share/nginx/html
RUN chown -R chronos:nginx /var/cache/nginx
RUN chown -R chronos:nginx /var/log/nginx
RUN chown -R chronos:nginx /etc/nginx/conf.d
RUN touch /var/run/nginx.pid
RUN chown -R chronos:nginx /var/run/nginx.pid

# Switch to non-root user
USER chronos

# Expose port 80
EXPOSE 80

# Health check for production monitoring
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/health.json || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
