# Development Dockerfile for ADHD-optimized React frontend
FROM node:18-alpine

# Install pnpm globally (use version 9 to match lockfile)
RUN npm install -g pnpm@9

# Set working directory
WORKDIR /app

# Install curl for health checks
RUN apk add --no-cache curl

# Copy package files first for better caching
COPY package.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S chronos -u 1001
RUN chown -R chronos:nodejs /app
USER chronos

# Expose ports for development server and HMR
EXPOSE 3000 3001

# Health check for ADHD performance monitoring
# Checks both the main app and HMR endpoints
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || curl -f http://localhost:3000/ || exit 1

# Set environment variables for ADHD optimization
ENV NODE_ENV=development
ENV VITE_ADHD_DEBUG=true
ENV VITE_COGNITIVE_LOAD_TRACKING=true

# Start development server with ADHD-optimized settings
CMD ["pnpm", "dev"]
