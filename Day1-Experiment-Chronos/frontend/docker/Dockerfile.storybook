# Storybook Dockerfile for ADHD component development
FROM node:18-alpine

# Install pnpm globally (use version 9 to match lockfile)
RUN npm install -g pnpm@9

# Set working directory
WORKDIR /app

# Install curl for health checks
RUN apk add --no-cache curl

# Copy package files first for better caching
COPY package.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Initialize Storybook if not already done
RUN if [ ! -d ".storybook" ]; then pnpm dlx storybook@latest init --yes; fi

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S chronos -u 1001
RUN chown -R chronos:nodejs /app
USER chronos

# Expose Storybook port
EXPOSE 6006

# Health check for Storybook
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
  CMD curl -f http://localhost:6006/ || exit 1

# Set environment variables for ADHD component development
ENV NODE_ENV=development
ENV STORYBOOK_ADHD_MODE=true

# Start Storybook with ADHD-optimized settings
CMD ["pnpm", "storybook"]
