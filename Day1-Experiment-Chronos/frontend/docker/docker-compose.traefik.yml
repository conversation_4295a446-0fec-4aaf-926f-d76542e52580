# Traefik Docker Compose for ADHD-Optimized Project Chronos
# This sets up Traefik reverse proxy with ADHD-specific optimizations

version: '3.8'

services:
  traefik:
    image: traefik:v3.0
    container_name: traefik
    restart: unless-stopped
    command:
      # Enable Docker provider
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --providers.docker.network=traefik

      # Enable file provider for dynamic configuration
      - --providers.file.filename=/etc/traefik/dynamic.yml
      - --providers.file.watch=true

      # Entry points
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443

      # Redirect HTTP to HTTPS
      - --entrypoints.web.http.redirections.entrypoint.to=websecure
      - --entrypoints.web.http.redirections.entrypoint.scheme=https
      - --entrypoints.web.http.redirections.entrypoint.permanent=true

      # Certificate resolver for Let's Encrypt
      - --certificatesresolvers.letsencrypt.acme.httpchallenge=true
      - --certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json

      # API and dashboard
      - --api.dashboard=true
      - --api.debug=true

      # Logging
      - --log.level=INFO
      - --log.format=json
      - --accesslog=true
      - --accesslog.format=json

      # Metrics
      - --metrics.prometheus=true
      - --metrics.prometheus.addEntryPointsLabels=true
      - --metrics.prometheus.addServicesLabels=true

      # Global configuration
      - --global.checknewversion=false
      - --global.sendanonymoususage=false

    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard

    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik.yml:/etc/traefik/traefik.yml:ro
      - ./dynamic.yml:/etc/traefik/dynamic.yml:ro
      - traefik-letsencrypt:/letsencrypt
      - traefik-certs:/certs

    networks:
      - traefik

    labels:
      - "traefik.enable=true"

      # Dashboard routing
      - "traefik.http.routers.traefik-dashboard.rule=Host(`traefik.autism.localhost`)"
      - "traefik.http.routers.traefik-dashboard.entrypoints=websecure"
      - "traefik.http.routers.traefik-dashboard.tls.certresolver=letsencrypt"
      - "traefik.http.routers.traefik-dashboard.service=api@internal"

      # Dashboard authentication (change credentials!)
      - "traefik.http.routers.traefik-dashboard.middlewares=traefik-auth"
      - "traefik.http.middlewares.traefik-auth.basicauth.users=admin:$$2y$$10$$K8QVw4.rZ7VdJKqz5.Zt0eJ7VdJKqz5.Zt0eJ7VdJKqz5.Zt0eJ7Vd"

      # ADHD-specific headers for dashboard
      - "traefik.http.middlewares.adhd-dashboard-headers.headers.customresponseheaders.X-ADHD-Dashboard=true"
      - "traefik.http.middlewares.adhd-dashboard-headers.headers.customresponseheaders.X-Cognitive-Load=low"

    environment:
      - TRAEFIK_LOG_LEVEL=INFO

    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Whoami service for testing
  whoami:
    image: traefik/whoami
    container_name: whoami
    restart: unless-stopped
    networks:
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.whoami.rule=Host(`whoami.autism.localhost`)"
      - "traefik.http.routers.whoami.entrypoints=websecure"
      - "traefik.http.routers.whoami.tls.certresolver=letsencrypt"
      - "traefik.http.services.whoami.loadbalancer.server.port=80"

      # ADHD-specific middleware for testing
      - "traefik.http.routers.whoami.middlewares=adhd-dev-headers"

networks:
  traefik:
    external: true

volumes:
  traefik-letsencrypt:
    driver: local
    labels:
      - "traefik.volume=letsencrypt"
  traefik-certs:
    driver: local
    labels:
      - "traefik.volume=certificates"