import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import { adhdPerformancePlugin } from './scripts/vite-plugin-adhd-performance'

export default defineConfig({
  plugins: [
    react(),
    // ADHD Performance Monitoring Plugin
    adhdPerformancePlugin({
      enabled: true,
      cognitiveLoadThreshold: 100000, // 100KB
      bundleSizeThreshold: 500000,    // 500KB
      reportPath: './adhd-performance-report.json',
      enableRealTimeMonitoring: true
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@/components': resolve(__dirname, './src/components'),
      '@/adhd': resolve(__dirname, './src/components/adhd'),
      '@/hooks': resolve(__dirname, './src/hooks'),
      '@/stores': resolve(__dirname, './src/stores'),
      '@/services': resolve(__dirname, './src/services'),
      '@/utils': resolve(__dirname, './src/utils'),
      '@/types': resolve(__dirname, './src/types')
    }
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    hmr: {
      port: 3001
    }
  },
  build: {
    target: 'esnext',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          'vendor': ['react', 'react-dom']
        }
      }
    }
  },
  define: {
    __ADHD_OPTIMIZED__: true,
    __COGNITIVE_LOAD_TRACKING__: process.env.NODE_ENV === 'development'
  }
})
