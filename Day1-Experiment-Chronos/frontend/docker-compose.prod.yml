# Docker Compose for ADHD-optimized frontend production
version: '3.8'

services:
  # Production frontend server
  chronos-frontend:
    build:
      context: .
      dockerfile: docker/Dockerfile.prod
      args:
        - NODE_ENV=production
        - VITE_ADHD_OPTIMIZED=true
    container_name: chronos-frontend
    restart: unless-stopped
    networks:
      - traefik
    environment:
      - NODE_ENV=production
      - NGINX_WORKER_PROCESSES=auto
      - NGINX_WORKER_CONNECTIONS=1024
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik"
      
      # Production routing
      - "traefik.http.routers.chronos-app.rule=Host(`app.autism.localhost`)"
      - "traefik.http.routers.chronos-app.entrypoints=websecure"
      - "traefik.http.routers.chronos-app.tls.certresolver=letsencrypt"
      - "traefik.http.services.chronos-app.loadbalancer.server.port=80"
      
      # ADHD-optimized middleware chain
      - "traefik.http.routers.chronos-app.middlewares=adhd-prod-security,adhd-prod-headers,adhd-prod-compression,adhd-prod-ratelimit"
      
      # Security middleware
      - "traefik.http.middlewares.adhd-prod-security.headers.frameDeny=true"
      - "traefik.http.middlewares.adhd-prod-security.headers.contentTypeNosniff=true"
      - "traefik.http.middlewares.adhd-prod-security.headers.browserXssFilter=true"
      - "traefik.http.middlewares.adhd-prod-security.headers.referrerPolicy=strict-origin-when-cross-origin"
      - "traefik.http.middlewares.adhd-prod-security.headers.customrequestheaders.X-ADHD-Secure=true"
      
      # ADHD-specific headers
      - "traefik.http.middlewares.adhd-prod-headers.headers.customresponseheaders.X-ADHD-Optimized=true"
      - "traefik.http.middlewares.adhd-prod-headers.headers.customresponseheaders.X-Cognitive-Load=managed"
      - "traefik.http.middlewares.adhd-prod-headers.headers.customresponseheaders.X-Response-Time-Optimized=true"
      - "traefik.http.middlewares.adhd-prod-headers.headers.customresponseheaders.Cache-Control=public, max-age=31536000, immutable"
      
      # Compression middleware for faster loading
      - "traefik.http.middlewares.adhd-prod-compression.compress=true"
      
      # Rate limiting to prevent overwhelming ADHD users
      - "traefik.http.middlewares.adhd-prod-ratelimit.ratelimit.average=100"
      - "traefik.http.middlewares.adhd-prod-ratelimit.ratelimit.burst=200"
      - "traefik.http.middlewares.adhd-prod-ratelimit.ratelimit.period=1m"
    
    # Resource limits optimized for ADHD performance
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health.json"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    
    # Logging configuration for ADHD performance monitoring
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "adhd.service=frontend,adhd.environment=production"

networks:
  traefik:
    external: true
