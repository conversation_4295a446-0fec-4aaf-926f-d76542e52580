// ADHD Performance Dashboard
// Comprehensive performance monitoring dashboard for development

import React, { useState, useEffect } from 'react';

interface PerformanceData {
  timestamp: number;
  renderTime: number;
  memoryUsage: number;
  cognitiveLoad: number;
  userInteractions: number;
  errorCount: number;
  attentionScore: number;
}

interface ADHDPerformanceDashboardProps {
  isVisible?: boolean;
  onClose?: () => void;
  maxDataPoints?: number;
}

export const ADHDPerformanceDashboard: React.FC<ADHDPerformanceDashboardProps> = ({
  isVisible = false,
  onClose,
  maxDataPoints = 50
}) => {
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([]);
  const [isRecording, setIsRecording] = useState(true);
  // const [selectedMetric] = useState<keyof PerformanceData>('renderTime');

  // Collect performance data
  useEffect(() => {
    if (!isRecording) return;

    const collectData = () => {
      const now = Date.now();
      const memory = (performance as any).memory;
      
      const newData: PerformanceData = {
        timestamp: now,
        renderTime: performance.now() % 100, // Simplified for demo
        memoryUsage: memory ? (memory.usedJSHeapSize / memory.jsHeapSizeLimit * 100) : 0,
        cognitiveLoad: Math.random() * 100, // Would be calculated from actual metrics
        userInteractions: Math.floor(Math.random() * 10),
        errorCount: 0,
        attentionScore: 100 - (Math.random() * 30) // Simplified attention tracking
      };

      setPerformanceData(prev => {
        const updated = [...prev, newData];
        return updated.slice(-maxDataPoints); // Keep only recent data
      });
    };

    const interval = setInterval(collectData, 1000);
    return () => clearInterval(interval);
  }, [isRecording, maxDataPoints]);

  // Calculate statistics
  const getStats = (metric: keyof PerformanceData) => {
    if (performanceData.length === 0) return { avg: 0, min: 0, max: 0 };
    
    const values = performanceData.map(d => d[metric] as number);
    return {
      avg: values.reduce((a, b) => a + b, 0) / values.length,
      min: Math.min(...values),
      max: Math.max(...values)
    };
  };

  // Get ADHD optimization recommendations
  const getRecommendations = () => {
    const recommendations: string[] = [];
    const stats = getStats('renderTime');
    
    if (stats.avg > 16) {
      recommendations.push('🐌 Render times are slow - consider React.memo() or useMemo()');
    }
    
    if (getStats('memoryUsage').avg > 70) {
      recommendations.push('🧠 High memory usage - check for memory leaks');
    }
    
    if (getStats('cognitiveLoad').avg > 60) {
      recommendations.push('🤯 High cognitive load - simplify UI components');
    }
    
    if (getStats('attentionScore').avg < 50) {
      recommendations.push('😵 Low attention scores - reduce distractions');
    }

    if (recommendations.length === 0) {
      recommendations.push('✅ Performance looks good for ADHD users!');
    }

    return recommendations;
  };

  // Simple chart component
  const MiniChart: React.FC<{ data: number[]; color: string; label: string }> = ({ 
    data, color, label 
  }) => {
    const max = Math.max(...data, 1);
    const points = data.map((value, index) => {
      const x = (index / (data.length - 1)) * 100;
      const y = 100 - (value / max) * 100;
      return `${x},${y}`;
    }).join(' ');

    return (
      <div style={{ marginBottom: '16px' }}>
        <div style={{ fontSize: '12px', marginBottom: '4px', color: '#666' }}>
          {label}
        </div>
        <svg width="100%" height="60" style={{ border: '1px solid #e5e7eb' }}>
          <polyline
            fill="none"
            stroke={color}
            strokeWidth="2"
            points={points}
          />
        </svg>
      </div>
    );
  };

  if (!isVisible) return null;

  const currentData = performanceData[performanceData.length - 1];
  const renderTimes = performanceData.map(d => d.renderTime);
  const memoryUsages = performanceData.map(d => d.memoryUsage);
  const cognitiveLoads = performanceData.map(d => d.cognitiveLoad);
  const attentionScores = performanceData.map(d => d.attentionScore);

  return (
    <div style={{
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      width: '800px',
      height: '600px',
      background: 'white',
      border: '1px solid #d1d5db',
      borderRadius: '12px',
      boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
      zIndex: 10000,
      overflow: 'hidden'
    }}>
      {/* Header */}
      <div style={{
        padding: '16px 24px',
        borderBottom: '1px solid #e5e7eb',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        background: '#f9fafb'
      }}>
        <h2 style={{ margin: 0, fontSize: '18px', fontWeight: 'bold' }}>
          🧠 ADHD Performance Dashboard
        </h2>
        <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
          <button
            onClick={() => setIsRecording(!isRecording)}
            style={{
              padding: '6px 12px',
              background: isRecording ? '#ef4444' : '#10b981',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            {isRecording ? '⏸️ Pause' : '▶️ Record'}
          </button>
          <button
            onClick={() => setPerformanceData([])}
            style={{
              padding: '6px 12px',
              background: '#6b7280',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            🗑️ Clear
          </button>
          <button
            onClick={onClose}
            style={{
              padding: '6px 12px',
              background: '#374151',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            ✕ Close
          </button>
        </div>
      </div>

      <div style={{ display: 'flex', height: 'calc(100% - 73px)' }}>
        {/* Sidebar with current metrics */}
        <div style={{
          width: '250px',
          padding: '16px',
          borderRight: '1px solid #e5e7eb',
          background: '#f9fafb'
        }}>
          <h3 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: 'bold' }}>
            Current Metrics
          </h3>
          
          {currentData && (
            <div style={{ fontSize: '12px', lineHeight: '1.5' }}>
              <div style={{ marginBottom: '8px' }}>
                <strong>Render Time:</strong> {currentData.renderTime.toFixed(2)}ms
                <div style={{
                  width: '100%',
                  height: '4px',
                  background: '#e5e7eb',
                  borderRadius: '2px',
                  marginTop: '2px'
                }}>
                  <div style={{
                    width: `${Math.min(currentData.renderTime / 16 * 100, 100)}%`,
                    height: '100%',
                    background: currentData.renderTime > 16 ? '#ef4444' : '#10b981',
                    borderRadius: '2px'
                  }} />
                </div>
              </div>

              <div style={{ marginBottom: '8px' }}>
                <strong>Memory:</strong> {currentData.memoryUsage.toFixed(1)}%
                <div style={{
                  width: '100%',
                  height: '4px',
                  background: '#e5e7eb',
                  borderRadius: '2px',
                  marginTop: '2px'
                }}>
                  <div style={{
                    width: `${currentData.memoryUsage}%`,
                    height: '100%',
                    background: currentData.memoryUsage > 70 ? '#ef4444' : '#10b981',
                    borderRadius: '2px'
                  }} />
                </div>
              </div>

              <div style={{ marginBottom: '8px' }}>
                <strong>Cognitive Load:</strong> {currentData.cognitiveLoad.toFixed(0)}%
                <div style={{
                  width: '100%',
                  height: '4px',
                  background: '#e5e7eb',
                  borderRadius: '2px',
                  marginTop: '2px'
                }}>
                  <div style={{
                    width: `${currentData.cognitiveLoad}%`,
                    height: '100%',
                    background: currentData.cognitiveLoad > 60 ? '#ef4444' : '#10b981',
                    borderRadius: '2px'
                  }} />
                </div>
              </div>

              <div style={{ marginBottom: '8px' }}>
                <strong>Attention:</strong> {currentData.attentionScore.toFixed(0)}%
                <div style={{
                  width: '100%',
                  height: '4px',
                  background: '#e5e7eb',
                  borderRadius: '2px',
                  marginTop: '2px'
                }}>
                  <div style={{
                    width: `${currentData.attentionScore}%`,
                    height: '100%',
                    background: currentData.attentionScore < 50 ? '#ef4444' : '#10b981',
                    borderRadius: '2px'
                  }} />
                </div>
              </div>
            </div>
          )}

          <h3 style={{ margin: '24px 0 12px 0', fontSize: '14px', fontWeight: 'bold' }}>
            ADHD Recommendations
          </h3>
          <div style={{ fontSize: '11px', lineHeight: '1.4' }}>
            {getRecommendations().map((rec, index) => (
              <div key={index} style={{ 
                marginBottom: '8px', 
                padding: '6px', 
                background: 'white', 
                borderRadius: '4px',
                border: '1px solid #e5e7eb'
              }}>
                {rec}
              </div>
            ))}
          </div>
        </div>

        {/* Main chart area */}
        <div style={{ flex: 1, padding: '16px' }}>
          <h3 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: 'bold' }}>
            Performance Trends
          </h3>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <MiniChart 
              data={renderTimes} 
              color="#3b82f6" 
              label="Render Time (ms)" 
            />
            <MiniChart 
              data={memoryUsages} 
              color="#ef4444" 
              label="Memory Usage (%)" 
            />
            <MiniChart 
              data={cognitiveLoads} 
              color="#f59e0b" 
              label="Cognitive Load (%)" 
            />
            <MiniChart 
              data={attentionScores} 
              color="#10b981" 
              label="Attention Score (%)" 
            />
          </div>

          <div style={{ marginTop: '24px' }}>
            <h4 style={{ margin: '0 0 12px 0', fontSize: '12px', fontWeight: 'bold' }}>
              Statistics
            </h4>
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(4, 1fr)', 
              gap: '12px',
              fontSize: '11px'
            }}>
              {(['renderTime', 'memoryUsage', 'cognitiveLoad', 'attentionScore'] as const).map(metric => {
                const stats = getStats(metric);
                return (
                  <div key={metric} style={{ 
                    padding: '8px', 
                    background: '#f9fafb', 
                    borderRadius: '6px',
                    border: '1px solid #e5e7eb'
                  }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                      {metric.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </div>
                    <div>Avg: {stats.avg.toFixed(1)}</div>
                    <div>Min: {stats.min.toFixed(1)}</div>
                    <div>Max: {stats.max.toFixed(1)}</div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ADHDPerformanceDashboard;
