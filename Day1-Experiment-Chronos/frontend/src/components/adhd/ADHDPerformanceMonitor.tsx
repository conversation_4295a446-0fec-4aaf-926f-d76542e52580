// ADHD Performance Monitor Component
// Real-time performance monitoring for ADHD-optimized user experience

import React, { useState, useEffect, useCallback } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  cognitiveLoad: 'low' | 'medium' | 'high';
  attentionSpan: number;
  interactionDelay: number;
  errorCount: number;
}

interface ADHDPerformanceMonitorProps {
  enabled?: boolean;
  showDebugInfo?: boolean;
  onPerformanceIssue?: (issue: string) => void;
  cognitiveLoadThreshold?: number;
  children?: React.ReactNode;
}

declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}

export const ADHDPerformanceMonitor: React.FC<ADHDPerformanceMonitorProps> = ({
  enabled = import.meta.env.DEV,
  showDebugInfo = false,
  onPerformanceIssue,
  cognitiveLoadThreshold = 16, // 16ms for 60fps
  children
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    memoryUsage: 0,
    cognitiveLoad: 'low',
    attentionSpan: 100,
    interactionDelay: 0,
    errorCount: 0
  });

  const [isMonitoring, setIsMonitoring] = useState(enabled);

  // Performance observer for measuring render times
  useEffect(() => {
    if (!isMonitoring || !window.PerformanceObserver) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        if (entry.entryType === 'measure' && entry.name.includes('React')) {
          const renderTime = entry.duration;
          
          setMetrics(prev => ({
            ...prev,
            renderTime,
            cognitiveLoad: renderTime > cognitiveLoadThreshold ? 'high' : 
                          renderTime > cognitiveLoadThreshold / 2 ? 'medium' : 'low'
          }));

          // Alert on performance issues
          if (renderTime > cognitiveLoadThreshold && onPerformanceIssue) {
            onPerformanceIssue(`Slow render detected: ${renderTime.toFixed(2)}ms`);
          }
        }
      });
    });

    observer.observe({ entryTypes: ['measure', 'navigation', 'paint'] });

    return () => observer.disconnect();
  }, [isMonitoring, cognitiveLoadThreshold, onPerformanceIssue]);

  // Memory usage monitoring
  useEffect(() => {
    if (!isMonitoring) return;

    const checkMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const memoryUsage = memory.usedJSHeapSize / memory.jsHeapSizeLimit * 100;
        
        setMetrics(prev => ({
          ...prev,
          memoryUsage
        }));

        // Alert on high memory usage (can cause frustration for ADHD users)
        if (memoryUsage > 80 && onPerformanceIssue) {
          onPerformanceIssue(`High memory usage: ${memoryUsage.toFixed(1)}%`);
        }
      }
    };

    const interval = setInterval(checkMemory, 5000); // Check every 5 seconds
    return () => clearInterval(interval);
  }, [isMonitoring, onPerformanceIssue]);

  // Interaction delay monitoring
  const measureInteractionDelay = useCallback((_event: Event) => {
    if (!isMonitoring) return;

    const startTime = performance.now();
    
    // Use requestAnimationFrame to measure actual response time
    requestAnimationFrame(() => {
      const endTime = performance.now();
      const delay = endTime - startTime;
      
      setMetrics(prev => ({
        ...prev,
        interactionDelay: delay
      }));

      // ADHD users are particularly sensitive to interaction delays
      if (delay > 100 && onPerformanceIssue) { // 100ms threshold
        onPerformanceIssue(`Slow interaction response: ${delay.toFixed(2)}ms`);
      }
    });
  }, [isMonitoring, onPerformanceIssue]);

  // Attention span tracking (simplified)
  useEffect(() => {
    if (!isMonitoring) return;

    let attentionTimer: number;
    let currentAttention = 100;

    const resetAttention = () => {
      currentAttention = 100;
      setMetrics(prev => ({ ...prev, attentionSpan: currentAttention }));
    };

    const decreaseAttention = () => {
      currentAttention = Math.max(0, currentAttention - 1);
      setMetrics(prev => ({ ...prev, attentionSpan: currentAttention }));
      
      if (currentAttention < 30 && onPerformanceIssue) {
        onPerformanceIssue('Low attention span detected - consider simplifying interface');
      }
    };

    const handleUserActivity = () => {
      resetAttention();
      clearInterval(attentionTimer);
      attentionTimer = setInterval(decreaseAttention, 1000); // Decrease every second
    };

    // Listen for user interactions
    const events = ['click', 'keydown', 'scroll', 'mousemove'];
    events.forEach(event => {
      document.addEventListener(event, handleUserActivity, { passive: true });
    });

    // Start attention tracking
    attentionTimer = setInterval(decreaseAttention, 1000);

    return () => {
      clearInterval(attentionTimer);
      events.forEach(event => {
        document.removeEventListener(event, handleUserActivity);
      });
    };
  }, [isMonitoring, onPerformanceIssue]);

  // Error boundary integration
  useEffect(() => {
    const handleError = () => {
      setMetrics(prev => ({
        ...prev,
        errorCount: prev.errorCount + 1
      }));
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleError);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleError);
    };
  }, []);

  // Add interaction listeners to children
  const enhancedChildren = React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      const childProps = child.props as any;
      return React.cloneElement(child, {
        onClick: (e: Event) => {
          measureInteractionDelay(e);
          if (childProps.onClick) {
            childProps.onClick(e);
          }
        }
      } as any);
    }
    return child;
  });

  // Debug info component
  const DebugInfo = () => {
    if (!showDebugInfo || !isMonitoring) return null;

    const getCognitiveLoadColor = () => {
      switch (metrics.cognitiveLoad) {
        case 'low': return '#4ade80'; // green
        case 'medium': return '#fbbf24'; // yellow
        case 'high': return '#ef4444'; // red
        default: return '#6b7280'; // gray
      }
    };

    return (
      <div style={{
        position: 'fixed',
        top: 10,
        right: 10,
        background: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '10px',
        borderRadius: '8px',
        fontSize: '12px',
        fontFamily: 'monospace',
        zIndex: 9999,
        minWidth: '200px'
      }}>
        <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>
          🧠 ADHD Performance Monitor
        </div>
        <div>Render Time: {metrics.renderTime.toFixed(2)}ms</div>
        <div>Memory Usage: {metrics.memoryUsage.toFixed(1)}%</div>
        <div style={{ color: getCognitiveLoadColor() }}>
          Cognitive Load: {metrics.cognitiveLoad}
        </div>
        <div>Attention Span: {metrics.attentionSpan}%</div>
        <div>Interaction Delay: {metrics.interactionDelay.toFixed(2)}ms</div>
        <div>Errors: {metrics.errorCount}</div>
        <button
          onClick={() => setIsMonitoring(!isMonitoring)}
          style={{
            marginTop: '8px',
            padding: '4px 8px',
            background: isMonitoring ? '#ef4444' : '#4ade80',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '10px'
          }}
        >
          {isMonitoring ? 'Stop' : 'Start'} Monitoring
        </button>
      </div>
    );
  };

  return (
    <>
      {enhancedChildren}
      <DebugInfo />
    </>
  );
};

// Hook for accessing performance metrics
export const useADHDPerformance = () => {
  const [metrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    memoryUsage: 0,
    cognitiveLoad: 'low',
    attentionSpan: 100,
    interactionDelay: 0,
    errorCount: 0
  });

  const reportPerformanceIssue = useCallback((issue: string) => {
    console.warn(`🧠 ADHD Performance Issue: ${issue}`);
    
    // Send to analytics or monitoring service
    if (window.gtag) {
      window.gtag('event', 'adhd_performance_issue', {
        event_category: 'performance',
        event_label: issue,
        value: 1
      });
    }
  }, []);

  return {
    metrics,
    reportPerformanceIssue
  };
};

export default ADHDPerformanceMonitor;
