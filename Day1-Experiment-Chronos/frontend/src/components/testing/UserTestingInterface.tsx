import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Users, 
  ClipboardList, 
  Star, 
  Clock, 
  CheckCircle,
  AlertCircle,
  Brain,
  Heart,
  Lightbulb,
  MessageSquare
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface TestScenario {
  id: string;
  name: string;
  description: string;
  category: string;
  difficulty: string;
  estimatedDuration: number;
  instructions: string[];
  successCriteria: string[];
  adhdConsiderations: string[];
}

interface TestResult {
  scenarioId: string;
  completed: boolean;
  success: boolean;
  difficultyRating: number;
  satisfactionRating: number;
  adhdImpactRating: number;
  cognitiveLoadRating: number;
  positivefeedback: string[];
  negativeFeedback: string[];
  suggestions: string[];
  accessibilityIssues: string[];
  wouldUseAgain: boolean;
  additionalNotes: string;
}

interface UserTestingInterfaceProps {
  userId: string;
  onTestComplete?: (results: TestResult[]) => void;
}

export const UserTestingInterface: React.FC<UserTestingInterfaceProps> = ({
  userId,
  onTestComplete
}) => {
  const [currentStep, setCurrentStep] = useState<'registration' | 'scenarios' | 'testing' | 'feedback' | 'complete'>('registration');
  const [userProfile, setUserProfile] = useState({
    name: '',
    email: '',
    adhdType: '',
    severity: '',
    ageGroup: '',
    techComfort: '',
    assistiveTechExperience: false,
    preferredModes: [] as string[],
    accessibilityNeeds: [] as string[]
  });
  
  const [availableScenarios, setAvailableScenarios] = useState<TestScenario[]>([]);
  const [selectedScenarios, setSelectedScenarios] = useState<string[]>([]);
  const [currentScenarioIndex, setCurrentScenarioIndex] = useState(0);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [currentResult, setCurrentResult] = useState<Partial<TestResult>>({});
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null);
  
  const { toast } = useToast();

  useEffect(() => {
    // Load available test scenarios
    loadTestScenarios();
  }, []);

  const loadTestScenarios = async () => {
    // Mock scenarios - in real implementation, load from API
    const scenarios: TestScenario[] = [
      {
        id: 'voice_synthesis_basic',
        name: 'Basic Voice Synthesis',
        description: 'Test basic text-to-speech functionality with ADHD modes',
        category: 'voice_synthesis',
        difficulty: 'easy',
        estimatedDuration: 10,
        instructions: [
          'Navigate to the Speechbot section',
          'Enter a simple text message',
          'Try different ADHD modes (calm, excited, focused)',
          'Generate and listen to the audio',
          'Rate the voice quality and emotional appropriateness'
        ],
        successCriteria: [
          'Successfully navigate to Speechbot',
          'Generate audio in at least 2 different ADHD modes',
          'Audio plays without technical issues',
          'User can distinguish between different modes'
        ],
        adhdConsiderations: [
          'Clear navigation without overwhelming options',
          'Immediate audio feedback',
          'Visual indicators for different modes',
          'No long waiting times'
        ]
      },
      {
        id: 'voice_profile_creation',
        name: 'Personal Voice Profile Creation',
        description: 'Test voice cloning functionality for self-compassion',
        category: 'voice_synthesis',
        difficulty: 'medium',
        estimatedDuration: 15,
        instructions: [
          'Navigate to Voice Profiles section',
          'Record a 10-second voice sample',
          'Create a voice profile with a meaningful name',
          'Test the created voice profile',
          'Use your voice for a self-compassion message'
        ],
        successCriteria: [
          'Successfully record voice sample',
          'Voice profile created without errors',
          'Generated audio resembles user\'s voice',
          'User feels comfortable with the result'
        ],
        adhdConsiderations: [
          'Clear recording instructions',
          'Visual feedback during recording',
          'Quality assessment and guidance',
          'Emotional safety for self-voice use'
        ]
      },
      {
        id: 'real_time_streaming',
        name: 'Real-Time Audio Streaming',
        description: 'Test advanced real-time streaming synthesis',
        category: 'voice_synthesis',
        difficulty: 'hard',
        estimatedDuration: 18,
        instructions: [
          'Access real-time streaming feature',
          'Start streaming synthesis',
          'Experience immediate audio playback',
          'Test pause/resume functionality',
          'Evaluate streaming quality and responsiveness'
        ],
        successCriteria: [
          'Streaming starts without delay',
          'Audio plays smoothly in real-time',
          'Controls respond immediately',
          'No audio artifacts or interruptions'
        ],
        adhdConsiderations: [
          'Immediate gratification',
          'Reduced waiting anxiety',
          'Smooth, uninterrupted experience',
          'Clear progress indicators'
        ]
      }
    ];
    
    setAvailableScenarios(scenarios);
  };

  const handleRegistrationSubmit = () => {
    if (!userProfile.name || !userProfile.email || !userProfile.adhdType) {
      toast({
        title: "Incomplete Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    setCurrentStep('scenarios');
    toast({
      title: "Registration Complete",
      description: "Thank you for participating in our ADHD user testing!",
    });
  };

  const handleScenarioSelection = () => {
    if (selectedScenarios.length === 0) {
      toast({
        title: "No Scenarios Selected",
        description: "Please select at least one test scenario.",
        variant: "destructive"
      });
      return;
    }

    setCurrentStep('testing');
    setSessionStartTime(new Date());
    setCurrentScenarioIndex(0);
    
    toast({
      title: "Testing Session Started",
      description: `You'll be testing ${selectedScenarios.length} scenarios.`,
    });
  };

  const handleScenarioComplete = () => {
    if (!currentResult.completed) {
      toast({
        title: "Scenario Incomplete",
        description: "Please mark the scenario as completed before proceeding.",
        variant: "destructive"
      });
      return;
    }

    const result: TestResult = {
      scenarioId: selectedScenarios[currentScenarioIndex],
      completed: currentResult.completed || false,
      success: currentResult.success || false,
      difficultyRating: currentResult.difficultyRating || 5,
      satisfactionRating: currentResult.satisfactionRating || 5,
      adhdImpactRating: currentResult.adhdImpactRating || 5,
      cognitiveLoadRating: currentResult.cognitiveLoadRating || 5,
      positivefeedback: currentResult.positivefeedback || [],
      negativeFeedback: currentResult.negativeFeedback || [],
      suggestions: currentResult.suggestions || [],
      accessibilityIssues: currentResult.accessibilityIssues || [],
      wouldUseAgain: currentResult.wouldUseAgain || false,
      additionalNotes: currentResult.additionalNotes || ''
    };

    setTestResults([...testResults, result]);
    setCurrentResult({});

    if (currentScenarioIndex < selectedScenarios.length - 1) {
      setCurrentScenarioIndex(currentScenarioIndex + 1);
      toast({
        title: "Scenario Complete",
        description: "Moving to next scenario...",
      });
    } else {
      setCurrentStep('feedback');
      toast({
        title: "All Scenarios Complete",
        description: "Please provide your overall feedback.",
      });
    }
  };

  const handleTestingComplete = () => {
    setCurrentStep('complete');
    
    if (onTestComplete) {
      onTestComplete(testResults);
    }

    toast({
      title: "Testing Complete! 🎉",
      description: "Thank you for your valuable feedback!",
    });
  };

  const currentScenario = availableScenarios.find(s => s.id === selectedScenarios[currentScenarioIndex]);
  const progress = ((currentScenarioIndex + 1) / selectedScenarios.length) * 100;

  const adhdModes = ['calm', 'excited', 'focused', 'overwhelmed', 'motivated'];
  const accessibilityOptions = [
    'high_contrast', 'large_text', 'clear_navigation', 'voice_guidance',
    'simple_interface', 'reduced_animations', 'keyboard_navigation'
  ];

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-6 w-6 text-blue-600" />
            <span>ADHD User Testing - Speechbot</span>
          </CardTitle>
          <p className="text-gray-600">
            Help us improve Speechbot for the ADHD community by participating in our user testing program.
          </p>
        </CardHeader>
      </Card>

      {/* Registration Step */}
      {currentStep === 'registration' && (
        <Card>
          <CardHeader>
            <CardTitle>User Registration</CardTitle>
            <p className="text-gray-600">
              Please provide some information about yourself to help us understand your needs.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={userProfile.name}
                  onChange={(e) => setUserProfile({...userProfile, name: e.target.value})}
                  placeholder="Your name"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={userProfile.email}
                  onChange={(e) => setUserProfile({...userProfile, email: e.target.value})}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>ADHD Type *</Label>
                <Select value={userProfile.adhdType} onValueChange={(value) => setUserProfile({...userProfile, adhdType: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select ADHD type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="inattentive">Inattentive</SelectItem>
                    <SelectItem value="hyperactive">Hyperactive-Impulsive</SelectItem>
                    <SelectItem value="combined">Combined</SelectItem>
                    <SelectItem value="unsure">Unsure/Self-diagnosed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Severity Level</Label>
                <Select value={userProfile.severity} onValueChange={(value) => setUserProfile({...userProfile, severity: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mild">Mild</SelectItem>
                    <SelectItem value="moderate">Moderate</SelectItem>
                    <SelectItem value="severe">Severe</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Age Group</Label>
                <Select value={userProfile.ageGroup} onValueChange={(value) => setUserProfile({...userProfile, ageGroup: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select age group" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="18-25">18-25</SelectItem>
                    <SelectItem value="26-35">26-35</SelectItem>
                    <SelectItem value="36-45">36-45</SelectItem>
                    <SelectItem value="46+">46+</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Technology Comfort Level</Label>
                <Select value={userProfile.techComfort} onValueChange={(value) => setUserProfile({...userProfile, techComfort: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select comfort level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low - Basic computer use</SelectItem>
                    <SelectItem value="medium">Medium - Comfortable with apps</SelectItem>
                    <SelectItem value="high">High - Tech-savvy</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-3">
              <Label>Preferred ADHD Modes (select all that apply)</Label>
              <div className="grid grid-cols-3 md:grid-cols-5 gap-2">
                {adhdModes.map((mode) => (
                  <div key={mode} className="flex items-center space-x-2">
                    <Checkbox
                      id={mode}
                      checked={userProfile.preferredModes.includes(mode)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setUserProfile({
                            ...userProfile,
                            preferredModes: [...userProfile.preferredModes, mode]
                          });
                        } else {
                          setUserProfile({
                            ...userProfile,
                            preferredModes: userProfile.preferredModes.filter(m => m !== mode)
                          });
                        }
                      }}
                    />
                    <Label htmlFor={mode} className="text-sm capitalize">{mode}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-3">
              <Label>Accessibility Needs (select all that apply)</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {accessibilityOptions.map((option) => (
                  <div key={option} className="flex items-center space-x-2">
                    <Checkbox
                      id={option}
                      checked={userProfile.accessibilityNeeds.includes(option)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setUserProfile({
                            ...userProfile,
                            accessibilityNeeds: [...userProfile.accessibilityNeeds, option]
                          });
                        } else {
                          setUserProfile({
                            ...userProfile,
                            accessibilityNeeds: userProfile.accessibilityNeeds.filter(n => n !== option)
                          });
                        }
                      }}
                    />
                    <Label htmlFor={option} className="text-sm">
                      {option.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="assistive-tech"
                checked={userProfile.assistiveTechExperience}
                onCheckedChange={(checked) => setUserProfile({...userProfile, assistiveTechExperience: !!checked})}
              />
              <Label htmlFor="assistive-tech">
                I have experience with assistive technology
              </Label>
            </div>

            <Button onClick={handleRegistrationSubmit} className="w-full" size="lg">
              <Users className="h-4 w-4 mr-2" />
              Start Testing Session
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Scenario Selection Step */}
      {currentStep === 'scenarios' && (
        <Card>
          <CardHeader>
            <CardTitle>Select Test Scenarios</CardTitle>
            <p className="text-gray-600">
              Choose the scenarios you'd like to test. We recommend starting with easier scenarios.
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {availableScenarios.map((scenario) => (
              <div
                key={scenario.id}
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  selectedScenarios.includes(scenario.id)
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => {
                  if (selectedScenarios.includes(scenario.id)) {
                    setSelectedScenarios(selectedScenarios.filter(id => id !== scenario.id));
                  } else {
                    setSelectedScenarios([...selectedScenarios, scenario.id]);
                  }
                }}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold">{scenario.name}</h3>
                  <div className="flex items-center space-x-2">
                    <Badge variant={scenario.difficulty === 'easy' ? 'default' : scenario.difficulty === 'medium' ? 'secondary' : 'destructive'}>
                      {scenario.difficulty}
                    </Badge>
                    <Badge variant="outline">
                      <Clock className="h-3 w-3 mr-1" />
                      {scenario.estimatedDuration}min
                    </Badge>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-3">{scenario.description}</p>
                <div className="text-xs text-gray-500">
                  <strong>ADHD Considerations:</strong> {scenario.adhdConsiderations.join(', ')}
                </div>
              </div>
            ))}

            <Button 
              onClick={handleScenarioSelection} 
              disabled={selectedScenarios.length === 0}
              className="w-full" 
              size="lg"
            >
              <ClipboardList className="h-4 w-4 mr-2" />
              Begin Testing ({selectedScenarios.length} scenarios)
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Testing Step */}
      {currentStep === 'testing' && currentScenario && (
        <div className="space-y-6">
          {/* Progress */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">
                  Scenario {currentScenarioIndex + 1} of {selectedScenarios.length}
                </span>
                <span className="text-sm text-gray-500">
                  {Math.round(progress)}% Complete
                </span>
              </div>
              <Progress value={progress} className="w-full" />
            </CardContent>
          </Card>

          {/* Current Scenario */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{currentScenario.name}</span>
                <Badge variant={currentScenario.difficulty === 'easy' ? 'default' : currentScenario.difficulty === 'medium' ? 'secondary' : 'destructive'}>
                  {currentScenario.difficulty}
                </Badge>
              </CardTitle>
              <p className="text-gray-600">{currentScenario.description}</p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Instructions */}
              <div>
                <h3 className="font-semibold mb-2 flex items-center">
                  <ClipboardList className="h-4 w-4 mr-2" />
                  Instructions
                </h3>
                <ol className="list-decimal list-inside space-y-1 text-sm">
                  {currentScenario.instructions.map((instruction, index) => (
                    <li key={index}>{instruction}</li>
                  ))}
                </ol>
              </div>

              {/* Success Criteria */}
              <div>
                <h3 className="font-semibold mb-2 flex items-center">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Success Criteria
                </h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {currentScenario.successCriteria.map((criteria, index) => (
                    <li key={index}>{criteria}</li>
                  ))}
                </ul>
              </div>

              {/* ADHD Considerations */}
              <div>
                <h3 className="font-semibold mb-2 flex items-center">
                  <Brain className="h-4 w-4 mr-2" />
                  ADHD-Specific Considerations
                </h3>
                <ul className="list-disc list-inside space-y-1 text-sm text-blue-700">
                  {currentScenario.adhdConsiderations.map((consideration, index) => (
                    <li key={index}>{consideration}</li>
                  ))}
                </ul>
              </div>

              {/* Completion Status */}
              <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="completed"
                    checked={currentResult.completed || false}
                    onCheckedChange={(checked) => setCurrentResult({...currentResult, completed: !!checked})}
                  />
                  <Label htmlFor="completed">I have completed this scenario</Label>
                </div>

                {currentResult.completed && (
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="success"
                      checked={currentResult.success || false}
                      onCheckedChange={(checked) => setCurrentResult({...currentResult, success: !!checked})}
                    />
                    <Label htmlFor="success">I was successful in completing all success criteria</Label>
                  </div>
                )}
              </div>

              <Button 
                onClick={handleScenarioComplete}
                disabled={!currentResult.completed}
                className="w-full" 
                size="lg"
              >
                {currentScenarioIndex < selectedScenarios.length - 1 ? 'Next Scenario' : 'Complete Testing'}
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Completion Step */}
      {currentStep === 'complete' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="h-6 w-6 text-green-600" />
              <span>Testing Complete!</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <Heart className="h-4 w-4" />
              <AlertDescription>
                Thank you for participating in our ADHD user testing program! Your feedback is invaluable 
                in helping us create better assistive technology for the ADHD community.
              </AlertDescription>
            </Alert>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{testResults.length}</div>
                <div className="text-sm text-blue-800">Scenarios Tested</div>
              </div>
              <div className="p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {testResults.filter(r => r.success).length}
                </div>
                <div className="text-sm text-green-800">Successful Completions</div>
              </div>
              <div className="p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {sessionStartTime ? Math.round((Date.now() - sessionStartTime.getTime()) / 60000) : 0}
                </div>
                <div className="text-sm text-purple-800">Minutes Spent</div>
              </div>
            </div>

            <div className="text-center">
              <p className="text-gray-600 mb-4">
                Your feedback has been recorded and will help improve Speechbot for the ADHD community.
              </p>
              <Button onClick={() => window.location.href = '/speechbot'} size="lg">
                <Lightbulb className="h-4 w-4 mr-2" />
                Continue Using Speechbot
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
