import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Play, 
  Pause, 
  Download, 
  Mic, 
  Volume2, 
  Clock,
  Zap,
  Heart,
  Brain,
  Target,
  Shield
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface SpeechSynthesizerProps {
  userId: string;
  userPreferences: any;
  onPreferencesUpdate: (preferences: any) => void;
}

export const SpeechSynthesizer: React.FC<SpeechSynthesizerProps> = ({
  userId,
  userPreferences,
  onPreferencesUpdate
}) => {
  const [text, setText] = useState('');
  const [selectedVoice, setSelectedVoice] = useState('default');
  const [adhdMode, setAdhdMode] = useState(userPreferences?.default_adhd_mode || 'calm');
  const [includeNonverbals, setIncludeNonverbals] = useState(userPreferences?.enable_nonverbals ?? true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [generationStats, setGenerationStats] = useState<any>(null);
  const [voiceProfiles, setVoiceProfiles] = useState<any[]>([]);
  
  const audioRef = useRef<HTMLAudioElement>(null);
  const { toast } = useToast();

  useEffect(() => {
    loadVoiceProfiles();
  }, [userId]);

  useEffect(() => {
    if (userPreferences) {
      setAdhdMode(userPreferences.default_adhd_mode || 'calm');
      setIncludeNonverbals(userPreferences.enable_nonverbals ?? true);
    }
  }, [userPreferences]);

  const loadVoiceProfiles = async () => {
    try {
      const response = await fetch(`/api/v1/voice-profiles/list/${userId}`);
      const profiles = await response.json();
      setVoiceProfiles(profiles);
      
      // Set default voice if user has one
      if (userPreferences?.default_voice_profile_id) {
        setSelectedVoice(userPreferences.default_voice_profile_id);
      }
    } catch (error) {
      console.error('Failed to load voice profiles:', error);
    }
  };

  const handleSynthesize = async () => {
    if (!text.trim()) {
      toast({
        title: "No Text",
        description: "Please enter some text to synthesize.",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);
    setAudioUrl(null);
    setGenerationStats(null);

    // Simulate progress
    const progressInterval = setInterval(() => {
      setGenerationProgress(prev => Math.min(prev + 10, 90));
    }, 200);

    try {
      const startTime = Date.now();
      
      const response = await fetch('/api/v1/tts/synthesize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          voice_profile: selectedVoice,
          adhd_mode: adhdMode,
          include_nonverbals: includeNonverbals,
          format: 'wav'
        }),
      });

      clearInterval(progressInterval);
      setGenerationProgress(100);

      if (!response.ok) {
        throw new Error('Synthesis failed');
      }

      const audioBlob = await response.blob();
      const url = URL.createObjectURL(audioBlob);
      setAudioUrl(url);

      // Extract stats from headers
      const duration = response.headers.get('X-Audio-Duration');
      const realtimeFactor = response.headers.get('X-Realtime-Factor');
      const generationTime = (Date.now() - startTime) / 1000;

      setGenerationStats({
        textLength: text.length,
        audioDuration: parseFloat(duration || '0'),
        generationTime,
        realtimeFactor: parseFloat(realtimeFactor || '0'),
        adhdMode,
        voiceProfile: selectedVoice
      });

      toast({
        title: "Speech Generated",
        description: `Generated ${duration}s of audio in ${generationTime.toFixed(1)}s`,
      });

    } catch (error) {
      console.error('Synthesis failed:', error);
      toast({
        title: "Synthesis Failed",
        description: "Failed to generate speech. Please try again.",
        variant: "destructive"
      });
    } finally {
      clearInterval(progressInterval);
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  };

  const handlePlay = () => {
    if (audioRef.current && audioUrl) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleDownload = () => {
    if (audioUrl) {
      const a = document.createElement('a');
      a.href = audioUrl;
      a.download = `speechbot_${adhdMode}_${Date.now()}.wav`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const adhdModeIcons = {
    calm: Heart,
    excited: Zap,
    focused: Target,
    overwhelmed: Shield,
    motivated: Brain
  };

  const adhdModeColors = {
    calm: 'bg-blue-100 text-blue-800',
    excited: 'bg-yellow-100 text-yellow-800',
    focused: 'bg-green-100 text-green-800',
    overwhelmed: 'bg-purple-100 text-purple-800',
    motivated: 'bg-orange-100 text-orange-800'
  };

  const sampleTexts = [
    "You're doing great work today! Take your time and focus on one task at a time.",
    "Remember to take breaks when you need them. Your mental health is important.",
    "Let's break this big task down into smaller, manageable steps.",
    "You've accomplished more than you realize. Give yourself credit for your progress.",
    "It's okay to feel overwhelmed sometimes. Take a deep breath and start with just one small thing."
  ];

  return (
    <div className="space-y-6">
      {/* Main Synthesis Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Mic className="h-5 w-5" />
            <span>Text-to-Speech Synthesis</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Text Input */}
          <div className="space-y-2">
            <Label htmlFor="text-input">Text to Synthesize</Label>
            <Textarea
              id="text-input"
              placeholder="Enter the text you'd like to convert to speech..."
              value={text}
              onChange={(e) => setText(e.target.value)}
              rows={4}
              className="resize-none"
            />
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>{text.length} characters</span>
              <span>Max: 5,000 characters</span>
            </div>
          </div>

          {/* Sample Texts */}
          <div className="space-y-2">
            <Label>ADHD-Friendly Sample Texts</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {sampleTexts.map((sample, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  className="text-left h-auto p-3 justify-start"
                  onClick={() => setText(sample)}
                >
                  <span className="text-xs line-clamp-2">{sample}</span>
                </Button>
              ))}
            </div>
          </div>

          {/* Voice and Mode Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Voice Profile</Label>
              <Select value={selectedVoice} onValueChange={setSelectedVoice}>
                <SelectTrigger>
                  <SelectValue placeholder="Select voice profile" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">Default Voice</SelectItem>
                  {voiceProfiles.map((profile) => (
                    <SelectItem key={profile.profile_id} value={profile.profile_id}>
                      {profile.name} (Quality: {(profile.quality_score * 100).toFixed(0)}%)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>ADHD Mode</Label>
              <Select value={adhdMode} onValueChange={setAdhdMode}>
                <SelectTrigger>
                  <SelectValue placeholder="Select ADHD mode" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="calm">😌 Calm - Relaxed, steady pace</SelectItem>
                  <SelectItem value="excited">⚡ Excited - Energetic, upbeat</SelectItem>
                  <SelectItem value="focused">🎯 Focused - Clear, direct</SelectItem>
                  <SelectItem value="overwhelmed">🛡️ Overwhelmed - Gentle, supportive</SelectItem>
                  <SelectItem value="motivated">🧠 Motivated - Encouraging, confident</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Options */}
          <div className="flex items-center space-x-2">
            <Switch
              id="nonverbals"
              checked={includeNonverbals}
              onCheckedChange={setIncludeNonverbals}
            />
            <Label htmlFor="nonverbals">
              Include nonverbal sounds (laughs, sighs, etc.)
            </Label>
          </div>

          {/* Generate Button */}
          <div className="space-y-4">
            <Button
              onClick={handleSynthesize}
              disabled={isGenerating || !text.trim()}
              className="w-full"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Generating Speech...
                </>
              ) : (
                <>
                  <Volume2 className="h-4 w-4 mr-2" />
                  Generate Speech
                </>
              )}
            </Button>

            {isGenerating && (
              <div className="space-y-2">
                <Progress value={generationProgress} className="w-full" />
                <p className="text-sm text-center text-gray-600">
                  Processing with {adhdMode} mode...
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Audio Player */}
      {audioUrl && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Volume2 className="h-5 w-5" />
              <span>Generated Audio</span>
              {adhdMode && (
                <Badge className={adhdModeColors[adhdMode as keyof typeof adhdModeColors]}>
                  {adhdMode}
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <audio
              ref={audioRef}
              src={audioUrl}
              onEnded={() => setIsPlaying(false)}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
              className="w-full"
              controls
            />

            <div className="flex space-x-2">
              <Button onClick={handlePlay} variant="outline">
                {isPlaying ? (
                  <>
                    <Pause className="h-4 w-4 mr-2" />
                    Pause
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Play
                  </>
                )}
              </Button>
              <Button onClick={handleDownload} variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </div>

            {/* Generation Stats */}
            {generationStats && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {generationStats.textLength}
                  </div>
                  <div className="text-sm text-gray-600">Characters</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {generationStats.audioDuration.toFixed(1)}s
                  </div>
                  <div className="text-sm text-gray-600">Audio Duration</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {generationStats.generationTime.toFixed(1)}s
                  </div>
                  <div className="text-sm text-gray-600">Generation Time</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {generationStats.realtimeFactor.toFixed(1)}x
                  </div>
                  <div className="text-sm text-gray-600">Realtime Factor</div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Quick Tips */}
      <Card className="bg-blue-50">
        <CardContent className="p-4">
          <h3 className="font-semibold text-blue-900 mb-2">💡 ADHD Tips for Better Synthesis</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Use "calm" mode when feeling overwhelmed or need to focus</li>
            <li>• Try "excited" mode for motivation and energy boosts</li>
            <li>• "Focused" mode works great for step-by-step instructions</li>
            <li>• Nonverbal sounds make speech more engaging and natural</li>
            <li>• Create personal voice profiles for self-compassion exercises</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};
