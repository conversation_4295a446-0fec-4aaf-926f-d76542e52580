import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Mic, 
  Play, 
  Pause, 
  Download, 
  Settings, 
  Users, 
  BarChart3,
  Heart,
  Brain,
  Zap,
  Shield,
  Target
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { VoiceProfileManager } from './VoiceProfileManager';
import { DialogueGenerator } from './DialogueGenerator';
import { ADHDModeSelector } from './ADHDModeSelector';
import { SpeechSynthesizer } from './SpeechSynthesizer';
import { BodyDoublingSession } from './BodyDoublingSession';
import { UserPreferences } from './UserPreferences';
import { AnalyticsDashboard } from './AnalyticsDashboard';
import { RealTimeStreaming } from './RealTimeStreaming';

interface SpeechbotDashboardProps {
  userId: string;
}

export const SpeechbotDashboard: React.FC<SpeechbotDashboardProps> = ({ userId }) => {
  const [activeTab, setActiveTab] = useState('synthesize');
  const [isLoading, setIsLoading] = useState(false);
  const [capabilities, setCapabilities] = useState<any>(null);
  const [userPreferences, setUserPreferences] = useState<any>(null);
  const { toast } = useToast();

  useEffect(() => {
    loadCapabilities();
    loadUserPreferences();
  }, [userId]);

  const loadCapabilities = async () => {
    try {
      const response = await fetch('/api/v1/tts/capabilities');
      const data = await response.json();
      setCapabilities(data);
    } catch (error) {
      console.error('Failed to load capabilities:', error);
      toast({
        title: "Connection Error",
        description: "Failed to connect to Speechbot. Please try again.",
        variant: "destructive"
      });
    }
  };

  const loadUserPreferences = async () => {
    try {
      const response = await fetch(`/api/v1/preferences/${userId}`);
      const data = await response.json();
      setUserPreferences(data);
    } catch (error) {
      console.error('Failed to load user preferences:', error);
    }
  };

  const adhdModes = [
    {
      id: 'calm',
      name: 'Calm',
      icon: Heart,
      color: 'bg-blue-100 text-blue-800',
      description: 'Relaxed, steady pace for focused work'
    },
    {
      id: 'excited',
      name: 'Excited',
      icon: Zap,
      color: 'bg-yellow-100 text-yellow-800',
      description: 'Energetic, enthusiastic tone for motivation'
    },
    {
      id: 'focused',
      name: 'Focused',
      icon: Target,
      color: 'bg-green-100 text-green-800',
      description: 'Clear, direct communication for task focus'
    },
    {
      id: 'overwhelmed',
      name: 'Overwhelmed',
      icon: Shield,
      color: 'bg-purple-100 text-purple-800',
      description: 'Gentle, supportive tone for difficult moments'
    },
    {
      id: 'motivated',
      name: 'Motivated',
      icon: Brain,
      color: 'bg-orange-100 text-orange-800',
      description: 'Encouraging, confident tone for goal achievement'
    }
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            🎭 Speechbot
          </h1>
          <p className="text-gray-600 mt-1">
            ADHD-optimized voice assistant with personalized synthesis
          </p>
        </div>
        
        {capabilities && (
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              {capabilities.engine}
            </Badge>
            <Badge variant="outline">
              {capabilities.model_size}
            </Badge>
          </div>
        )}
      </div>

      {/* ADHD Mode Quick Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-5 w-5" />
            <span>ADHD Emotional Modes</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
            {adhdModes.map((mode) => {
              const Icon = mode.icon;
              const isActive = userPreferences?.default_adhd_mode === mode.id;
              
              return (
                <div
                  key={mode.id}
                  className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                    isActive 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => {
                    // Update default mode
                    setUserPreferences(prev => ({
                      ...prev,
                      default_adhd_mode: mode.id
                    }));
                  }}
                >
                  <div className="flex items-center space-x-2 mb-2">
                    <Icon className="h-4 w-4" />
                    <span className="font-medium text-sm">{mode.name}</span>
                  </div>
                  <p className="text-xs text-gray-600">{mode.description}</p>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="synthesize" className="flex items-center space-x-2">
            <Mic className="h-4 w-4" />
            <span className="hidden sm:inline">Synthesize</span>
          </TabsTrigger>
          <TabsTrigger value="streaming" className="flex items-center space-x-2">
            <Zap className="h-4 w-4" />
            <span className="hidden sm:inline">Streaming</span>
          </TabsTrigger>
          <TabsTrigger value="voices" className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span className="hidden sm:inline">Voices</span>
          </TabsTrigger>
          <TabsTrigger value="dialogue" className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span className="hidden sm:inline">Dialogue</span>
          </TabsTrigger>
          <TabsTrigger value="body-doubling" className="flex items-center space-x-2">
            <Heart className="h-4 w-4" />
            <span className="hidden sm:inline">Body Doubling</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span className="hidden sm:inline">Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span className="hidden sm:inline">Settings</span>
          </TabsTrigger>
        </TabsList>

        {/* Speech Synthesis Tab */}
        <TabsContent value="synthesize">
          <SpeechSynthesizer
            userId={userId}
            userPreferences={userPreferences}
            onPreferencesUpdate={setUserPreferences}
          />
        </TabsContent>

        {/* Real-Time Streaming Tab */}
        <TabsContent value="streaming">
          <RealTimeStreaming
            userId={userId}
            userPreferences={userPreferences}
          />
        </TabsContent>

        {/* Voice Profiles Tab */}
        <TabsContent value="voices">
          <VoiceProfileManager 
            userId={userId}
            onProfileCreated={() => {
              toast({
                title: "Voice Profile Created",
                description: "Your personalized voice is ready to use!",
              });
            }}
          />
        </TabsContent>

        {/* Dialogue Generation Tab */}
        <TabsContent value="dialogue">
          <DialogueGenerator 
            userId={userId}
            userPreferences={userPreferences}
          />
        </TabsContent>

        {/* Body Doubling Tab */}
        <TabsContent value="body-doubling">
          <BodyDoublingSession 
            userId={userId}
            userPreferences={userPreferences}
          />
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics">
          <AnalyticsDashboard 
            userId={userId}
            userPreferences={userPreferences}
          />
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings">
          <UserPreferences 
            userId={userId}
            preferences={userPreferences}
            onPreferencesUpdate={setUserPreferences}
          />
        </TabsContent>
      </Tabs>

      {/* Quick Actions Footer */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-gray-900">Quick Actions</h3>
              <p className="text-sm text-gray-600">
                Common ADHD support features
              </p>
            </div>
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setActiveTab('body-doubling')}
              >
                Start Body Doubling
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setActiveTab('synthesize')}
              >
                Quick TTS
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setActiveTab('voices')}
              >
                Create Voice
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
