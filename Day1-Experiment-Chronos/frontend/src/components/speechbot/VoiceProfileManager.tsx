import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Mic, 
  Upload, 
  Play, 
  Pause, 
  Trash2, 
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  Volume2,
  Download
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface VoiceProfileManagerProps {
  userId: string;
  onProfileCreated: () => void;
}

interface VoiceProfile {
  profile_id: string;
  name: string;
  duration: number;
  quality_score: number;
  status: string;
  created_at: number;
}

export const VoiceProfileManager: React.FC<VoiceProfileManagerProps> = ({
  userId,
  onProfileCreated
}) => {
  const [profiles, setProfiles] = useState<VoiceProfile[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [profileName, setProfileName] = useState('');
  const [profileDescription, setProfileDescription] = useState('');
  const [playingProfile, setPlayingProfile] = useState<string | null>(null);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const recordingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  useEffect(() => {
    loadVoiceProfiles();
  }, [userId]);

  const loadVoiceProfiles = async () => {
    try {
      const response = await fetch(`/api/v1/voice-profiles/list/${userId}`);
      const data = await response.json();
      setProfiles(data);
    } catch (error) {
      console.error('Failed to load voice profiles:', error);
      toast({
        title: "Loading Error",
        description: "Failed to load voice profiles.",
        variant: "destructive"
      });
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          sampleRate: 24000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      });
      
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      audioChunksRef.current = [];
      
      mediaRecorderRef.current.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };
      
      mediaRecorderRef.current.onstop = () => {
        const blob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        setAudioBlob(blob);
        stream.getTracks().forEach(track => track.stop());
      };
      
      mediaRecorderRef.current.start();
      setIsRecording(true);
      setRecordingTime(0);
      
      recordingIntervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
      
    } catch (error) {
      console.error('Failed to start recording:', error);
      toast({
        title: "Recording Error",
        description: "Failed to access microphone. Please check permissions.",
        variant: "destructive"
      });
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current);
      }
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 50 * 1024 * 1024) { // 50MB limit
        toast({
          title: "File Too Large",
          description: "Please select a file smaller than 50MB.",
          variant: "destructive"
        });
        return;
      }
      
      setAudioBlob(file);
      setProfileName(file.name.replace(/\.[^/.]+$/, ""));
    }
  };

  const createVoiceProfile = async () => {
    if (!audioBlob || !profileName.trim()) {
      toast({
        title: "Missing Information",
        description: "Please provide audio and a profile name.",
        variant: "destructive"
      });
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('audio_file', audioBlob, 'voice_sample.webm');
      formData.append('name', profileName);
      formData.append('user_id', userId);
      if (profileDescription) {
        formData.append('description', profileDescription);
      }

      const response = await fetch('/api/v1/voice-profiles/create', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to create voice profile');
      }

      const result = await response.json();
      
      toast({
        title: "Voice Profile Created",
        description: `"${profileName}" is ready to use!`,
      });

      // Reset form
      setAudioBlob(null);
      setProfileName('');
      setProfileDescription('');
      setRecordingTime(0);
      
      // Reload profiles
      await loadVoiceProfiles();
      onProfileCreated();

    } catch (error) {
      console.error('Failed to create voice profile:', error);
      toast({
        title: "Creation Failed",
        description: "Failed to create voice profile. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  const testVoiceProfile = async (profileId: string) => {
    try {
      setPlayingProfile(profileId);
      
      const response = await fetch(`/api/v1/voice-profiles/${profileId}/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          test_text: "Hello! This is a test of your voice profile. How does it sound?",
          adhd_mode: "calm"
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to test voice profile');
      }

      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);
      
      const audio = new Audio(audioUrl);
      audio.onended = () => {
        setPlayingProfile(null);
        URL.revokeObjectURL(audioUrl);
      };
      
      await audio.play();

    } catch (error) {
      console.error('Failed to test voice profile:', error);
      setPlayingProfile(null);
      toast({
        title: "Test Failed",
        description: "Failed to test voice profile.",
        variant: "destructive"
      });
    }
  };

  const deleteVoiceProfile = async (profileId: string) => {
    try {
      const response = await fetch(`/api/v1/voice-profiles/${profileId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete voice profile');
      }

      toast({
        title: "Profile Deleted",
        description: "Voice profile has been removed.",
      });

      await loadVoiceProfiles();

    } catch (error) {
      console.error('Failed to delete voice profile:', error);
      toast({
        title: "Deletion Failed",
        description: "Failed to delete voice profile.",
        variant: "destructive"
      });
    }
  };

  const getQualityColor = (score: number) => {
    if (score >= 0.8) return 'bg-green-100 text-green-800';
    if (score >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getQualityLabel = (score: number) => {
    if (score >= 0.8) return 'Excellent';
    if (score >= 0.6) return 'Good';
    return 'Fair';
  };

  const formatDuration = (seconds: number) => {
    return `${seconds.toFixed(1)}s`;
  };

  const sampleTexts = [
    "Hello, my name is [Your Name]. I'm creating a voice profile for my ADHD productivity assistant. The quick brown fox jumps over the lazy dog. I enjoy using technology to help manage my daily tasks and stay organized.",
    "Hi there! I'm recording this sample to create my personal voice assistant. I have ADHD and I'm excited to use this technology to support my productivity. I hope this voice will help me stay on track with my goals.",
    "You've got this! I believe in your ability to accomplish your goals. Take things one step at a time, and remember that progress is more important than perfection. Every small step forward is worth celebrating."
  ];

  return (
    <div className="space-y-6">
      {/* Create New Profile */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Mic className="h-5 w-5" />
            <span>Create Voice Profile</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Recording Guidelines */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Recording Tips:</strong> Record 8-12 seconds of clear speech in a quiet environment. 
              Speak naturally and include varied intonation. Use one of the sample texts below for best results.
            </AlertDescription>
          </Alert>

          {/* Sample Texts */}
          <div className="space-y-2">
            <Label>Recommended Sample Texts</Label>
            <div className="space-y-2">
              {sampleTexts.map((text, index) => (
                <div key={index} className="p-3 bg-gray-50 rounded-lg text-sm">
                  <strong>Sample {index + 1}:</strong> {text}
                </div>
              ))}
            </div>
          </div>

          {/* Recording Controls */}
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <Button
                onClick={isRecording ? stopRecording : startRecording}
                variant={isRecording ? "destructive" : "default"}
                size="lg"
              >
                <Mic className="h-4 w-4 mr-2" />
                {isRecording ? 'Stop Recording' : 'Start Recording'}
              </Button>
              
              <Button
                onClick={() => fileInputRef.current?.click()}
                variant="outline"
                size="lg"
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload File
              </Button>
              
              <input
                ref={fileInputRef}
                type="file"
                accept="audio/*"
                onChange={handleFileUpload}
                className="hidden"
              />
            </div>

            {isRecording && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" />
                  <span className="text-sm font-medium">Recording: {recordingTime}s</span>
                </div>
                <Progress 
                  value={Math.min((recordingTime / 12) * 100, 100)} 
                  className="w-full"
                />
                <p className="text-xs text-gray-600">
                  Recommended: 8-12 seconds
                </p>
              </div>
            )}
          </div>

          {/* Audio Preview */}
          {audioBlob && (
            <div className="space-y-4 p-4 bg-green-50 rounded-lg">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-800">Audio Ready</span>
              </div>
              
              <audio 
                controls 
                src={URL.createObjectURL(audioBlob)}
                className="w-full"
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="profile-name">Profile Name *</Label>
                  <Input
                    id="profile-name"
                    value={profileName}
                    onChange={(e) => setProfileName(e.target.value)}
                    placeholder="My Voice"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="profile-description">Description</Label>
                  <Input
                    id="profile-description"
                    value={profileDescription}
                    onChange={(e) => setProfileDescription(e.target.value)}
                    placeholder="Recorded when feeling calm"
                  />
                </div>
              </div>
              
              <Button
                onClick={createVoiceProfile}
                disabled={isUploading || !profileName.trim()}
                className="w-full"
                size="lg"
              >
                {isUploading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Creating Profile...
                  </>
                ) : (
                  <>
                    <Star className="h-4 w-4 mr-2" />
                    Create Voice Profile
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Existing Profiles */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Volume2 className="h-5 w-5" />
              <span>Your Voice Profiles</span>
            </div>
            <Badge variant="secondary">{profiles.length} profiles</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {profiles.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Mic className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No voice profiles yet. Create your first one above!</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {profiles.map((profile) => (
                <div
                  key={profile.profile_id}
                  className="p-4 border rounded-lg space-y-3"
                >
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold">{profile.name}</h3>
                    <Badge className={getQualityColor(profile.quality_score)}>
                      {getQualityLabel(profile.quality_score)}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{formatDuration(profile.duration)}</span>
                    </div>
                    <div>
                      Quality: {(profile.quality_score * 100).toFixed(0)}%
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => testVoiceProfile(profile.profile_id)}
                      disabled={playingProfile === profile.profile_id}
                      variant="outline"
                      size="sm"
                    >
                      {playingProfile === profile.profile_id ? (
                        <>
                          <Pause className="h-4 w-4 mr-1" />
                          Playing...
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4 mr-1" />
                          Test
                        </>
                      )}
                    </Button>
                    
                    <Button
                      onClick={() => deleteVoiceProfile(profile.profile_id)}
                      variant="outline"
                      size="sm"
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tips */}
      <Card className="bg-blue-50">
        <CardContent className="p-4">
          <h3 className="font-semibold text-blue-900 mb-2">💡 Voice Cloning Tips</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Record in a quiet environment with minimal background noise</li>
            <li>• Speak naturally - don't try to sound "perfect"</li>
            <li>• Include varied intonation and emotion in your sample</li>
            <li>• 8-12 seconds is the sweet spot for quality vs. convenience</li>
            <li>• Create different profiles for different moods or contexts</li>
            <li>• Use your own voice for self-compassion and motivation</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};
