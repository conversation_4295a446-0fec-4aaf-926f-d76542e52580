import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Play, 
  Pause, 
  Square, 
  Clock, 
  Heart, 
  Users, 
  Coffee,
  CheckCircle,
  Timer,
  Volume2
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface BodyDoublingSessionProps {
  userId: string;
  userPreferences: any;
}

interface SessionState {
  isActive: boolean;
  isPaused: boolean;
  startTime: number;
  duration: number;
  task: string;
  encouragementFreq: string;
  companionVoice: string;
}

export const BodyDoublingSession: React.FC<BodyDoublingSessionProps> = ({
  userId,
  userPreferences
}) => {
  const [sessionState, setSessionState] = useState<SessionState>({
    isActive: false,
    isPaused: false,
    startTime: 0,
    duration: 25,
    task: '',
    encouragementFreq: userPreferences?.default_encouragement_frequency || 'medium',
    companionVoice: 'default'
  });
  
  const [elapsedTime, setElapsedTime] = useState(0);
  const [isGeneratingCompanion, setIsGeneratingCompanion] = useState(false);
  const [companionAudio, setCompanionAudio] = useState<string | null>(null);
  const [voiceProfiles, setVoiceProfiles] = useState<any[]>([]);
  const [sessionHistory, setSessionHistory] = useState<any[]>([]);
  
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const encouragementRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const { toast } = useToast();

  useEffect(() => {
    loadVoiceProfiles();
    loadSessionHistory();
  }, [userId]);

  useEffect(() => {
    if (sessionState.isActive && !sessionState.isPaused) {
      timerRef.current = setInterval(() => {
        setElapsedTime(prev => {
          const newTime = prev + 1;
          
          // Check if session is complete
          if (newTime >= sessionState.duration * 60) {
            completeSession();
            return newTime;
          }
          
          return newTime;
        });
      }, 1000);
      
      // Schedule encouragement
      scheduleEncouragement();
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (encouragementRef.current) {
        clearTimeout(encouragementRef.current);
      }
    }

    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
      if (encouragementRef.current) clearTimeout(encouragementRef.current);
    };
  }, [sessionState.isActive, sessionState.isPaused]);

  const loadVoiceProfiles = async () => {
    try {
      const response = await fetch(`/api/v1/voice-profiles/list/${userId}`);
      const profiles = await response.json();
      setVoiceProfiles(profiles);
    } catch (error) {
      console.error('Failed to load voice profiles:', error);
    }
  };

  const loadSessionHistory = async () => {
    try {
      const response = await fetch(`/api/v1/preferences/${userId}/analytics?days=7`);
      const data = await response.json();
      // Filter for body doubling sessions
      setSessionHistory(data.analytics?.recent_sessions?.filter(
        (session: any) => session.session_type === 'body_doubling'
      ) || []);
    } catch (error) {
      console.error('Failed to load session history:', error);
    }
  };

  const scheduleEncouragement = () => {
    const frequencies = {
      low: 15 * 60 * 1000,    // 15 minutes
      medium: 8 * 60 * 1000,  // 8 minutes
      high: 5 * 60 * 1000     // 5 minutes
    };
    
    const delay = frequencies[sessionState.encouragementFreq as keyof typeof frequencies];
    
    encouragementRef.current = setTimeout(() => {
      generateEncouragement();
      scheduleEncouragement(); // Schedule next one
    }, delay);
  };

  const generateEncouragement = async () => {
    const encouragements = [
      "You're doing great! Keep up the excellent work.",
      "I can see your focus and dedication. You've got this!",
      "Making steady progress - every step counts.",
      "Your concentration is impressive. Stay with it!",
      "You're in a good flow state. Trust your process.",
      "Remember to breathe and stay present with your task.",
      "I'm here with you. You're not working alone.",
      "Your effort is paying off. Keep going!"
    ];
    
    const randomEncouragement = encouragements[Math.floor(Math.random() * encouragements.length)];
    
    try {
      const response = await fetch('/api/v1/tts/quick', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: randomEncouragement,
          voice: sessionState.companionVoice,
          mode: 'supportive'
        }),
      });

      if (response.ok) {
        const audioBlob = await response.blob();
        const audioUrl = URL.createObjectURL(audioBlob);
        
        if (audioRef.current) {
          audioRef.current.src = audioUrl;
          audioRef.current.play().catch(console.error);
        }
      }
    } catch (error) {
      console.error('Failed to generate encouragement:', error);
    }
  };

  const startSession = async () => {
    if (!sessionState.task.trim()) {
      toast({
        title: "Task Required",
        description: "Please describe what you'll be working on.",
        variant: "destructive"
      });
      return;
    }

    setIsGeneratingCompanion(true);

    try {
      // Generate opening companion message
      const response = await fetch('/api/v1/dialogue/body-doubling', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_task: sessionState.task,
          session_duration: sessionState.duration,
          encouragement_frequency: sessionState.encouragementFreq,
          companion_voice_profile: sessionState.companionVoice
        }),
      });

      if (response.ok) {
        const audioBlob = await response.blob();
        const audioUrl = URL.createObjectURL(audioBlob);
        setCompanionAudio(audioUrl);
        
        // Play opening message
        if (audioRef.current) {
          audioRef.current.src = audioUrl;
          audioRef.current.play();
        }
      }

      setSessionState(prev => ({
        ...prev,
        isActive: true,
        startTime: Date.now()
      }));
      
      setElapsedTime(0);

      toast({
        title: "Session Started",
        description: `Working on: ${sessionState.task}`,
      });

    } catch (error) {
      console.error('Failed to start session:', error);
      toast({
        title: "Session Failed",
        description: "Failed to start body doubling session.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingCompanion(false);
    }
  };

  const pauseSession = () => {
    setSessionState(prev => ({ ...prev, isPaused: !prev.isPaused }));
  };

  const stopSession = () => {
    setSessionState(prev => ({
      ...prev,
      isActive: false,
      isPaused: false
    }));
    setElapsedTime(0);
    
    toast({
      title: "Session Stopped",
      description: "Great work! Take a well-deserved break.",
    });
  };

  const completeSession = () => {
    setSessionState(prev => ({
      ...prev,
      isActive: false,
      isPaused: false
    }));

    // Generate completion message
    const completionMessages = [
      "Congratulations! You completed your focus session. That's a real achievement!",
      "Amazing work! You stayed focused for the entire session. You should be proud!",
      "Session complete! You demonstrated excellent concentration and dedication.",
      "Well done! You've accomplished something meaningful today."
    ];
    
    const message = completionMessages[Math.floor(Math.random() * completionMessages.length)];
    
    toast({
      title: "Session Complete! 🎉",
      description: message,
    });

    // Play completion audio
    generateCompletionAudio(message);
  };

  const generateCompletionAudio = async (message: string) => {
    try {
      const response = await fetch('/api/v1/tts/quick', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: message,
          voice: sessionState.companionVoice,
          mode: 'motivated'
        }),
      });

      if (response.ok) {
        const audioBlob = await response.blob();
        const audioUrl = URL.createObjectURL(audioBlob);
        
        if (audioRef.current) {
          audioRef.current.src = audioUrl;
          audioRef.current.play();
        }
      }
    } catch (error) {
      console.error('Failed to generate completion audio:', error);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgressPercentage = () => {
    return Math.min((elapsedTime / (sessionState.duration * 60)) * 100, 100);
  };

  const sessionTemplates = [
    { name: "Deep Work", duration: 90, task: "Complex project work requiring sustained attention" },
    { name: "Pomodoro", duration: 25, task: "Focused work session with break to follow" },
    { name: "Quick Task", duration: 15, task: "Small task or administrative work" },
    { name: "Creative Work", duration: 45, task: "Creative project or brainstorming session" },
    { name: "Study Session", duration: 60, task: "Learning new material or reviewing concepts" }
  ];

  return (
    <div className="space-y-6">
      {/* Session Setup */}
      {!sessionState.isActive && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Start Body Doubling Session</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Task Description */}
            <div className="space-y-2">
              <Label htmlFor="task">What will you be working on?</Label>
              <Input
                id="task"
                value={sessionState.task}
                onChange={(e) => setSessionState(prev => ({ ...prev, task: e.target.value }))}
                placeholder="Describe your task or project..."
              />
            </div>

            {/* Session Templates */}
            <div className="space-y-2">
              <Label>Quick Templates</Label>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                {sessionTemplates.map((template) => (
                  <Button
                    key={template.name}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSessionState(prev => ({
                        ...prev,
                        duration: template.duration,
                        task: template.task
                      }));
                    }}
                    className="h-auto p-3 flex flex-col items-center"
                  >
                    <div className="font-medium">{template.name}</div>
                    <div className="text-xs text-gray-500">{template.duration}m</div>
                  </Button>
                ))}
              </div>
            </div>

            {/* Session Settings */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Duration (minutes)</Label>
                <Input
                  type="number"
                  min="5"
                  max="180"
                  value={sessionState.duration}
                  onChange={(e) => setSessionState(prev => ({ 
                    ...prev, 
                    duration: parseInt(e.target.value) || 25 
                  }))}
                />
              </div>

              <div className="space-y-2">
                <Label>Encouragement Frequency</Label>
                <Select 
                  value={sessionState.encouragementFreq} 
                  onValueChange={(value) => setSessionState(prev => ({ 
                    ...prev, 
                    encouragementFreq: value 
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low (every 15 min)</SelectItem>
                    <SelectItem value="medium">Medium (every 8 min)</SelectItem>
                    <SelectItem value="high">High (every 5 min)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Companion Voice</Label>
                <Select 
                  value={sessionState.companionVoice} 
                  onValueChange={(value) => setSessionState(prev => ({ 
                    ...prev, 
                    companionVoice: value 
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">Default Companion</SelectItem>
                    {voiceProfiles.map((profile) => (
                      <SelectItem key={profile.profile_id} value={profile.profile_id}>
                        {profile.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Button
              onClick={startSession}
              disabled={isGeneratingCompanion || !sessionState.task.trim()}
              className="w-full"
              size="lg"
            >
              {isGeneratingCompanion ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Preparing Companion...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Start Session
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Active Session */}
      {sessionState.isActive && (
        <Card className="bg-gradient-to-r from-blue-50 to-green-50">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Heart className="h-5 w-5 text-red-500" />
                <span>Body Doubling Session</span>
                {sessionState.isPaused && (
                  <Badge variant="secondary">Paused</Badge>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4" />
                <span className="text-2xl font-mono">
                  {formatTime(elapsedTime)}
                </span>
                <span className="text-sm text-gray-500">
                  / {sessionState.duration}m
                </span>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Progress */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{Math.round(getProgressPercentage())}%</span>
              </div>
              <Progress value={getProgressPercentage()} className="w-full" />
            </div>

            {/* Current Task */}
            <div className="p-4 bg-white rounded-lg">
              <h3 className="font-medium mb-2">Current Task:</h3>
              <p className="text-gray-700">{sessionState.task}</p>
            </div>

            {/* Session Controls */}
            <div className="flex justify-center space-x-4">
              <Button
                onClick={pauseSession}
                variant="outline"
                size="lg"
              >
                {sessionState.isPaused ? (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Resume
                  </>
                ) : (
                  <>
                    <Pause className="h-4 w-4 mr-2" />
                    Pause
                  </>
                )}
              </Button>
              
              <Button
                onClick={stopSession}
                variant="destructive"
                size="lg"
              >
                <Square className="h-4 w-4 mr-2" />
                Stop Session
              </Button>
            </div>

            {/* Companion Audio */}
            <audio ref={audioRef} className="hidden" />
          </CardContent>
        </Card>
      )}

      {/* Session History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5" />
            <span>Recent Sessions</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {sessionHistory.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Timer className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No recent sessions. Start your first body doubling session!</p>
            </div>
          ) : (
            <div className="space-y-3">
              {sessionHistory.slice(0, 5).map((session, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium">{session.task || 'Focus Session'}</div>
                    <div className="text-sm text-gray-600">
                      {new Date(session.created_at).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{Math.round(session.audio_duration / 60)}m</div>
                    <div className="text-sm text-gray-600">
                      {session.adhd_mode} mode
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tips */}
      <Card className="bg-purple-50">
        <CardContent className="p-4">
          <h3 className="font-semibold text-purple-900 mb-2">💡 Body Doubling Tips</h3>
          <ul className="text-sm text-purple-800 space-y-1">
            <li>• Body doubling provides accountability without social pressure</li>
            <li>• Your virtual companion offers gentle encouragement and presence</li>
            <li>• Adjust encouragement frequency based on your needs</li>
            <li>• Use your own voice profile for self-compassion exercises</li>
            <li>• Take breaks when needed - the companion will wait for you</li>
            <li>• Celebrate completing sessions, no matter how short</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};
