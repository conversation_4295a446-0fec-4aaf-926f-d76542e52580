# 🚀 Project Chronos + Speechbot Deployment

**The World's Most Advanced ADHD-Optimized Voice Assistant Platform**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)
[![ADHD Optimized](https://img.shields.io/badge/ADHD-Optimized-purple.svg)](https://github.com/forkrul/day1-idea)

## 🌟 Overview

Project Chronos + Speechbot is a revolutionary ADHD-optimized productivity platform featuring:

- **🎭 Advanced Voice Synthesis** with 5 ADHD emotional modes
- **🧠 Real-Time Emotion Detection** for automatic mode selection
- **🤝 Virtual Body Doubling** companions for work sessions
- **🎤 Personal Voice Cloning** for self-compassion exercises
- **📊 ADHD Analytics** with usage insights and recommendations
- **⚡ Real-Time Streaming** for immediate audio feedback

## ⚡ Quick Start

### 1. One-Command Setup

```bash
# Clone repository
git clone https://github.com/forkrul/day1-idea.git
cd day1-idea

# Run automated setup
./setup.sh
```

### 2. Manual Setup

```bash
# Install Docker and Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Copy environment configuration
cp .env.example .env
# Edit .env with your settings

# Start platform
docker-compose up -d

# Check health
./health_check.sh
```

## 🔧 System Requirements

### Minimum Requirements
- **OS**: Linux (Ubuntu 20.04+), macOS, or Windows with WSL2
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 50GB free space
- **CPU**: 4 cores minimum

### Recommended for Production
- **RAM**: 32GB+ for optimal performance
- **GPU**: NVIDIA RTX 3080/4080 or better
- **Storage**: 200GB+ SSD storage
- **Network**: Stable internet for model downloads

## 🌐 Access Points

Once deployed, access the platform at:

| Service | URL | Description |
|---------|-----|-------------|
| **🏠 Main Platform** | http://chronos.autism.localhost:8090 | Primary user interface |
| **🎭 Speechbot API** | http://speechbot.autism.localhost:8090 | Voice synthesis service |
| **📊 API Docs** | http://api.autism.localhost:8090/docs | Interactive API documentation |
| **📈 Grafana** | http://grafana.autism.localhost:8090 | Monitoring dashboard |
| **🔍 Prometheus** | http://prometheus.autism.localhost:8090 | Metrics collection |
| **📧 Mailhog** | http://mail.autism.localhost:8090 | Email testing |
| **🗄️ MinIO** | http://minio.autism.localhost:8090 | Object storage |
| **🔀 Traefik** | http://traefik.autism.localhost:8091 | Load balancer dashboard |

## 🔐 Security Configuration

### Development Setup
```bash
# Use provided defaults for local development
cp .env.example .env
```

### Production Setup
```bash
# Use production template
cp .env.production.example .env

# Generate secure secrets
openssl rand -base64 32  # For JWT_SECRET_KEY
openssl rand -base64 32  # For SECRET_KEY

# Update all passwords and domains in .env
```

## 🧪 Health Monitoring

### Automated Health Checks
```bash
# Full comprehensive health check
./health_check.sh

# Quick status check
./health_check.sh --quick

# Continuous monitoring
watch -n 30 './health_check.sh --quick'
```

### Manual Testing
```bash
# Test voice synthesis
curl -X POST "http://speechbot.autism.localhost:8090/api/v1/tts/quick" \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello ADHD community!", "mode": "calm"}' \
  --output test.wav

# Test API health
curl http://api.autism.localhost:8090/health

# Check service logs
docker-compose logs -f speechbot
```

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Database
DATABASE_URL=postgresql+asyncpg://chronos:password@postgres:5432/chronos

# Security (CHANGE IN PRODUCTION!)
JWT_SECRET_KEY=your-jwt-secret-key
SECRET_KEY=your-app-secret-key

# Domains
DOMAIN=autism.localhost
CORS_ORIGINS=http://chronos.autism.localhost:8090

# Speechbot
SPEECHBOT_URL=http://speechbot:8001
DEFAULT_ADHD_MODE=calm
ENABLE_EMOTION_DETECTION=true

# AI/ML
HF_TOKEN=your_huggingface_token  # Optional but recommended
```

### GPU Configuration

For NVIDIA GPU support:
```bash
# Install NVIDIA Container Toolkit
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
sudo apt-get update && sudo apt-get install -y nvidia-container-toolkit
sudo systemctl restart docker

# Verify GPU access
docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi
```

## 🚨 Troubleshooting

### Common Issues

**Services won't start:**
```bash
# Check Docker daemon
sudo systemctl status docker

# Check resources
docker system df
docker system prune -f

# Restart services
docker-compose down && docker-compose up -d
```

**Database connection issues:**
```bash
# Check PostgreSQL logs
docker-compose logs postgres

# Reset database
docker-compose down -v
docker-compose up -d postgres
```

**Speechbot not responding:**
```bash
# Check GPU availability
nvidia-smi

# Check Speechbot logs
docker-compose logs speechbot

# Restart Speechbot
docker-compose restart speechbot
```

**Memory issues:**
```bash
# Check memory usage
docker stats

# Increase swap space
sudo fallocate -l 8G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### Log Analysis
```bash
# View all logs
docker-compose logs -f

# Filter errors
docker-compose logs | grep -i error

# Service-specific logs
docker-compose logs -f speechbot
docker-compose logs -f chronos-api
```

## 📊 Performance Optimization

### Production Optimizations

**Database tuning:**
```sql
-- PostgreSQL optimizations
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
SELECT pg_reload_conf();
```

**Speechbot GPU optimization:**
```yaml
# In docker-compose.yml
environment:
  - CUDA_VISIBLE_DEVICES=0
  - DIA_BATCH_SIZE=4
  - DIA_PRECISION=fp16
```

**Resource limits:**
```yaml
# In docker-compose.yml
deploy:
  resources:
    limits:
      memory: 8G
      cpus: '4'
```

## 🔄 Updates and Maintenance

### Updating the Platform
```bash
# Pull latest changes
git pull origin master

# Update containers
docker-compose pull
docker-compose up -d --build

# Run health check
./health_check.sh
```

### Backup Strategy
```bash
# Database backup
docker-compose exec postgres pg_dump -U chronos chronos > backup_$(date +%Y%m%d).sql

# Voice profiles backup
docker-compose exec minio mc cp --recursive minio/voice-samples ./backups/

# Configuration backup
cp .env ./backups/.env.$(date +%Y%m%d)
```

### Security Updates
```bash
# Update dependencies
docker-compose exec chronos-ui npm audit fix
docker-compose exec speechbot pip install --upgrade -r requirements.txt

# Rebuild with updates
docker-compose build --no-cache
docker-compose up -d
```

## 🎯 ADHD-Specific Features

### Voice Modes
- **😌 Calm**: Relaxed, steady pace for focused work
- **⚡ Excited**: Energetic, upbeat for motivation
- **🎯 Focused**: Clear, direct for task concentration
- **🛡️ Overwhelmed**: Gentle, supportive for difficult moments
- **🧠 Motivated**: Encouraging, confident for goal achievement

### Body Doubling
- Virtual companions for accountability
- Customizable encouragement frequency
- Session templates for different work types
- Progress tracking and celebration

### Accessibility
- High contrast mode support
- Reduced motion options
- Clear navigation patterns
- Keyboard accessibility
- Screen reader compatibility

## 📞 Support

### Getting Help
- **📖 Documentation**: Check DEPLOYMENT_GUIDE.md for detailed setup
- **🐛 Issues**: Create GitHub issue with logs and system info
- **💬 Community**: Join ADHD tech community discussions
- **📧 Contact**: Reach out to development team

### Useful Commands
```bash
# Service management
docker-compose up -d          # Start all services
docker-compose down           # Stop all services
docker-compose restart        # Restart all services
docker-compose ps             # Check service status

# Monitoring
./health_check.sh             # Full health check
docker-compose logs -f        # View logs
docker stats                  # Resource usage

# Maintenance
docker system prune -f        # Clean up unused resources
docker-compose pull           # Update images
```

## 🎉 Success!

**Congratulations! You've deployed the world's most advanced ADHD-optimized voice assistant platform!**

The platform is now ready to:
- 🎭 Generate personalized voice synthesis with ADHD modes
- 🧠 Detect emotions and adapt automatically
- 🤝 Provide virtual body doubling companions
- 📊 Track usage patterns and provide insights
- ⚡ Stream audio in real-time for immediate feedback

**Welcome to the future of ADHD assistive technology!** 🌟

---

*Project Chronos + Speechbot - Empowering the ADHD community through innovative voice technology*
