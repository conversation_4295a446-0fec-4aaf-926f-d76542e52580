# Agent 7: Notifications & Background Tasks Agent PRD

## Agent Overview

**Agent Name**: Notifications & Background Tasks Agent  
**Primary Responsibility**: Notification system, Celery workers, background task processing  
**Dependencies**: Agent 1 (Core Infrastructure), Agent 2 (Authentication), Agent 3 (Task Management), Agent 4 (Time Blocking)  
**Deliverables**: Persistent reminder system, background job processing, notification scheduling, email/SMS integration

## Mission Statement

Provide intelligent, persistent notification system that respects ADHD attention patterns while ensuring important reminders aren't missed. Handle background processing to maintain app responsiveness and deliver timely, contextual notifications.

## Technical Specifications

### Technology Stack
- **Task Queue**: Celery with Redis broker
- **Scheduling**: Celery Beat for periodic tasks
- **Email**: SMTP integration with HTML templates
- **SMS**: Twilio integration for critical notifications
- **Push Notifications**: Firebase Cloud Messaging (FCM)
- **Background Processing**: Async task processing with retry logic

### Core Responsibilities

#### 1. Notification Management
```python
# app/services/notification_service.py - Notification business logic
# app/workers/notification_worker.py - Background notification processing
# app/utils/notification_utils.py - Notification utilities and templates
# app/schemas/notification.py - Notification Pydantic schemas
```

#### 2. Background Task Processing
```python
# app/workers/celery_app.py - Celery configuration and setup
# app/workers/task_worker.py - Background task processing
# app/services/background_service.py - Background job coordination
```

#### 3. Reminder System
```python
# app/services/reminder_service.py - Intelligent reminder scheduling
# app/utils/reminder_utils.py - Reminder calculation utilities
# app/models/reminder.py - Reminder database models
```

## Key Features & User Stories

### Feature 1: Persistent & Staggered Reminders
**User Story**: "As a user who impulsively dismisses notifications, I want a reminder that won't go away until I properly acknowledge it, ensuring I don't forget important tasks or appointments."

**Technical Requirements**:
- Multiple reminder stages (1 hour, 15 min, 5 min before)
- Persistent notifications that require explicit acknowledgment
- Escalating reminder intensity (email → SMS → phone call)
- Smart snooze options with context-aware suggestions
- Integration with focus modes to respect concentration periods

### Feature 2: Context-Aware Notification Timing
**User Story**: "As a user whose attention fluctuates throughout the day, I want notifications that arrive when I'm most likely to act on them, not when they'll be ignored or cause overwhelm."

**Technical Requirements**:
- Learning user attention patterns and optimal notification times
- Integration with focus sessions to avoid interruptions
- Batching non-urgent notifications for designated review times
- Energy level consideration for notification delivery
- Location-based notification triggering

### Feature 3: ADHD-Optimized Notification Content
**User Story**: "As a user who gets overwhelmed by complex notifications, I want clear, actionable reminders that tell me exactly what to do next without requiring additional decision-making."

**Technical Requirements**:
- Clear, concise notification text with specific actions
- Visual indicators for urgency and type
- Quick action buttons (Complete, Snooze, Reschedule)
- Context preservation (what was I supposed to do?)
- Minimal cognitive load design

## Notification Service Implementation

### Core Notification Service
```python
# app/services/notification_service.py
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID
from enum import Enum

class NotificationType(str, Enum):
    """Types of notifications."""
    TASK_REMINDER = "task_reminder"
    DEADLINE_WARNING = "deadline_warning"
    FOCUS_BREAK = "focus_break"
    BODY_DOUBLING_INVITE = "body_doubling_invite"
    ACHIEVEMENT = "achievement"
    DAILY_REVIEW = "daily_review"

class NotificationPriority(str, Enum):
    """Notification priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

class NotificationService:
    """Service for managing ADHD-optimized notifications."""
    
    def __init__(self, db: AsyncSession, celery_app):
        self.db = db
        self.celery = celery_app
        self.redis = Redis()
    
    async def schedule_notification(
        self,
        user_id: UUID,
        notification_type: NotificationType,
        title: str,
        message: str,
        scheduled_for: datetime,
        priority: NotificationPriority = NotificationPriority.NORMAL,
        is_persistent: bool = False,
        related_task_id: Optional[UUID] = None,
        delivery_channels: List[str] = ["push"],
        reminder_stages: Optional[List[int]] = None
    ) -> Notification:
        """
        Schedule a notification with ADHD-optimized features.
        
        Args:
            user_id: Target user
            notification_type: Type of notification
            title: Notification title
            message: Notification content
            scheduled_for: When to deliver notification
            priority: Notification priority level
            is_persistent: Whether notification requires acknowledgment
            related_task_id: Associated task (if applicable)
            delivery_channels: How to deliver (push, email, sms)
            reminder_stages: Minutes before event for staggered reminders
            
        Returns:
            Created notification record
        """
        
        # Create notification record
        notification = Notification(
            user_id=user_id,
            type=notification_type,
            title=title,
            message=message,
            scheduled_for=scheduled_for,
            priority=priority,
            is_persistent=is_persistent,
            task_id=related_task_id,
            delivery_channels=delivery_channels
        )
        
        self.db.add(notification)
        await self.db.commit()
        await self.db.refresh(notification)
        
        # Schedule background delivery
        await self._schedule_delivery(notification, reminder_stages)
        
        return notification
    
    async def _schedule_delivery(
        self,
        notification: Notification,
        reminder_stages: Optional[List[int]] = None
    ):
        """Schedule notification delivery with staggered reminders."""
        
        if reminder_stages:
            # Schedule multiple reminders
            for minutes_before in reminder_stages:
                delivery_time = notification.scheduled_for - timedelta(minutes=minutes_before)
                
                if delivery_time > datetime.utcnow():
                    self.celery.send_task(
                        'deliver_notification',
                        args=[str(notification.id), f"reminder_{minutes_before}min"],
                        eta=delivery_time
                    )
        else:
            # Schedule single notification
            self.celery.send_task(
                'deliver_notification',
                args=[str(notification.id), "primary"],
                eta=notification.scheduled_for
            )
    
    async def deliver_notification(
        self,
        notification_id: UUID,
        delivery_stage: str
    ):
        """Deliver notification through configured channels."""
        
        notification = await self.get_notification(notification_id)
        if not notification:
            return
        
        # Check if user is in focus mode
        if await self._user_in_focus_mode(notification.user_id):
            if notification.priority != NotificationPriority.URGENT:
                # Defer non-urgent notifications
                await self._defer_notification(notification, minutes=30)
                return
        
        # Deliver through each channel
        for channel in notification.delivery_channels:
            if channel == "push":
                await self._send_push_notification(notification)
            elif channel == "email":
                await self._send_email_notification(notification)
            elif channel == "sms":
                await self._send_sms_notification(notification)
        
        # Update delivery status
        notification.sent_at = datetime.utcnow()
        await self.db.commit()
        
        # Schedule persistence check if needed
        if notification.is_persistent:
            self.celery.send_task(
                'check_notification_acknowledgment',
                args=[str(notification.id)],
                countdown=300  # Check after 5 minutes
            )
    
    async def acknowledge_notification(
        self,
        user_id: UUID,
        notification_id: UUID,
        action: str = "acknowledged"
    ):
        """Acknowledge notification to stop persistence."""
        
        notification = await self.get_notification(notification_id)
        if notification and notification.user_id == user_id:
            notification.acknowledged_at = datetime.utcnow()
            notification.acknowledgment_action = action
            await self.db.commit()
            
            # Cancel any pending persistence checks
            self.celery.control.revoke(f"check_ack_{notification_id}")
```

### Background Workers
```python
# app/workers/notification_worker.py
from celery import Celery
from app.services.notification_service import NotificationService

@celery_app.task(name='deliver_notification')
def deliver_notification_task(notification_id: str, delivery_stage: str):
    """Background task for notification delivery."""
    
    async def _deliver():
        async with get_db_session() as db:
            notification_service = NotificationService(db, celery_app)
            await notification_service.deliver_notification(
                UUID(notification_id), 
                delivery_stage
            )
    
    asyncio.run(_deliver())

@celery_app.task(name='check_notification_acknowledgment')
def check_notification_acknowledgment_task(notification_id: str):
    """Check if persistent notification was acknowledged."""
    
    async def _check():
        async with get_db_session() as db:
            notification_service = NotificationService(db, celery_app)
            notification = await notification_service.get_notification(UUID(notification_id))
            
            if notification and not notification.acknowledged_at:
                # Escalate notification
                await notification_service.escalate_notification(notification)
    
    asyncio.run(_check())

@celery_app.task(name='process_daily_reminders')
def process_daily_reminders_task():
    """Daily task to process upcoming reminders."""
    
    async def _process():
        async with get_db_session() as db:
            reminder_service = ReminderService(db, celery_app)
            await reminder_service.process_daily_reminders()
    
    asyncio.run(_process())

@celery_app.task(name='cleanup_old_notifications')
def cleanup_old_notifications_task():
    """Weekly cleanup of old notifications."""
    
    async def _cleanup():
        async with get_db_session() as db:
            notification_service = NotificationService(db, celery_app)
            await notification_service.cleanup_old_notifications(days=30)
    
    asyncio.run(_cleanup())
```

## API Endpoints

### Notification Management Endpoints
```python
# app/api/v1/notifications.py
@router.get("/", response_model=List[NotificationResponse])
async def get_notifications(
    unread_only: bool = False,
    limit: int = 50,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user notifications with filtering."""

@router.post("/{notification_id}/acknowledge")
async def acknowledge_notification(
    notification_id: UUID,
    action: str = "acknowledged",
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Acknowledge notification to stop persistence."""

@router.post("/{notification_id}/snooze")
async def snooze_notification(
    notification_id: UUID,
    snooze_data: NotificationSnoozeRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Snooze notification with smart suggestions."""

@router.put("/preferences", response_model=NotificationPreferencesResponse)
async def update_notification_preferences(
    preferences: NotificationPreferencesUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update user notification preferences."""
```

## Implementation Plan

### Phase 1: Core Notification System (Week 1)
1. Set up Celery with Redis broker
2. Implement basic notification scheduling and delivery
3. Create notification database models and schemas
4. Set up email and push notification integration

### Phase 2: Persistent Reminders (Week 2)
1. Implement persistent notification system
2. Add staggered reminder functionality
3. Create acknowledgment and snooze features
4. Develop escalation mechanisms

### Phase 3: Context-Aware Features (Week 3)
1. Add focus mode integration for notification timing
2. Implement user pattern learning for optimal delivery
3. Create batching for non-urgent notifications
4. Add location-based notification triggers

### Phase 4: Advanced Features & Testing (Week 4)
1. Implement notification analytics and insights
2. Add comprehensive background task monitoring
3. Create extensive testing suite
4. Optimize performance and reliability

## Testing Strategy

### Unit Tests
```python
# tests/unit/test_services/test_notification_service.py
class TestNotificationService:
    async def test_schedule_notification_with_reminders(self):
        """Test notification scheduling with staggered reminders."""
    
    async def test_persistent_notification_acknowledgment(self):
        """Test persistent notification acknowledgment flow."""
    
    async def test_focus_mode_notification_deferral(self):
        """Test notification deferral during focus modes."""

# tests/unit/test_workers/test_notification_worker.py
class TestNotificationWorker:
    def test_deliver_notification_task(self):
        """Test background notification delivery."""
    
    def test_notification_escalation(self):
        """Test notification escalation for unacknowledged reminders."""
```

### Integration Tests
```python
# tests/integration/test_notifications/test_notification_flow.py
class TestNotificationFlow:
    async def test_complete_reminder_flow(self):
        """Test complete reminder creation and delivery flow."""
    
    async def test_persistent_notification_cycle(self):
        """Test persistent notification until acknowledgment."""
```

### BDD Scenarios
```gherkin
Feature: Persistent Reminders
  Scenario: Staggered task reminders
    Given I have a task due in 2 hours
    When I enable staggered reminders
    Then I should receive reminders at 1 hour, 15 minutes, and 5 minutes before
    And each reminder should persist until acknowledged

Feature: Context-Aware Notifications
  Scenario: Focus mode notification deferral
    Given I am in an active focus session
    When a non-urgent notification is scheduled
    Then the notification should be deferred until my focus session ends
    And urgent notifications should still be delivered immediately
```

## Quality Standards

### Performance Requirements
- Notification delivery within 30 seconds of scheduled time
- Background task processing < 5 seconds average
- Support for 10,000+ scheduled notifications
- 99.9% notification delivery reliability

### User Experience Requirements
- Clear, actionable notification content
- Minimal cognitive load design
- Respectful of user attention patterns
- Effective persistence without annoyance

## Success Metrics

### Feature Adoption
- 80% of users enable persistent reminders
- 60% of users customize notification preferences
- 70% of notifications are acknowledged within 5 minutes
- 40% reduction in missed appointments/deadlines

### System Performance
- 99.9% notification delivery success rate
- < 1% notification delivery delays
- 95% user satisfaction with notification timing
- 50% reduction in notification dismissal without action

## Deliverables

1. **Notification System**: Complete notification scheduling and delivery
2. **Persistent Reminders**: Multi-stage reminder system with acknowledgment
3. **Background Processing**: Celery-based task queue with monitoring
4. **Context Awareness**: Focus mode integration and pattern learning
5. **Multi-Channel Delivery**: Push, email, and SMS notification support
6. **Test Suite**: Comprehensive testing including background tasks
7. **Documentation**: Notification system and background processing guides

## Integration Points

### Provides to Other Agents
- Notification scheduling and delivery services
- Background task processing infrastructure
- Reminder system for time-sensitive events
- User attention pattern data

### Requires from Other Agents
- User preferences and authentication (Agent 2)
- Task and deadline data (Agent 3)
- Time block and scheduling data (Agent 4)
- Focus session status (Agent 5)

## Commit Strategy

Notification-focused commits with clear background processing:
- `feat(notifications): Implement persistent reminder system with staggered delivery`
- `feat(celery): Add background task processing with Celery and Redis`
- `feat(context): Add focus mode integration for notification timing`
- `feat(channels): Implement multi-channel notification delivery (push/email/SMS)`
- `test(notifications): Add comprehensive notification and background task tests`
- `docs(notifications): Add notification system and background processing documentation`

This notification system will ensure users with ADHD receive timely, persistent reminders that respect their attention patterns while providing the accountability needed to stay on track with tasks and appointments.
