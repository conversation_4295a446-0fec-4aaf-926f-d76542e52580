# Agent 6: Real-time & WebSocket Agent PRD

## Agent Overview

**Agent Name**: Real-time & WebSocket Agent  
**Primary Responsibility**: WebSocket connections, real-time updates, body doubling sessions  
**Dependencies**: Agent 1 (Core Infrastructure), Agent 2 (Authentication), Agent 5 (Focus Sessions)  
**Deliverables**: Virtual body doubling, real-time collaboration, live updates, WebSocket infrastructure

## Mission Statement

Enable virtual body doubling and real-time collaboration features that provide the social accountability and shared focus that many users with ADHD need to overcome task initiation barriers and maintain concentration.

## Technical Specifications

### Technology Stack
- **WebSocket Framework**: FastAPI WebSocket support with connection management
- **Real-time State**: Redis for session state and message broadcasting
- **Connection Pooling**: Custom WebSocket connection manager
- **Message Queue**: Redis Pub/Sub for real-time message distribution
- **Session Management**: Persistent session storage with automatic cleanup

### Core Responsibilities

#### 1. WebSocket Infrastructure
```python
# app/api/websockets/manager.py - WebSocket connection management
# app/services/websocket_service.py - WebSocket business logic
# app/utils/websocket_utils.py - WebSocket utilities and helpers
# app/middleware/websocket_auth.py - WebSocket authentication
```

#### 2. Body Doubling Sessions
```python
# app/services/body_doubling_service.py - Body doubling session management
# app/models/body_doubling.py - Body doubling database models
# app/schemas/body_doubling.py - Body doubling Pydantic schemas
# app/api/v1/body_doubling.py - Body doubling endpoints
```

#### 3. Real-time Updates
```python
# app/services/realtime_service.py - Real-time update coordination
# app/workers/realtime_worker.py - Background real-time processing
# app/utils/broadcast_utils.py - Message broadcasting utilities
```

## Key Features & User Stories

### Feature 1: Integrated Virtual Body Doubling
**User Story**: "As a user who finds it easier to work when someone else is present, I want to start a virtual 'body doubling' session to feel a sense of shared focus and accountability, helping me to start and complete my tasks."

**Technical Requirements**:
- Create and join body doubling sessions with up to 4 participants
- Real-time presence indicators showing who's actively working
- Shared timer synchronization for group focus sessions
- Optional video/audio integration for enhanced presence
- Privacy controls and anonymous participation options

### Feature 2: Real-time Task Progress Sharing
**User Story**: "As a user in a body doubling session, I want to share my task progress with others in real-time, so we can encourage each other and maintain accountability."

**Technical Requirements**:
- Live task status updates within body doubling sessions
- Progress sharing with privacy controls
- Encouragement and reaction system
- Session-based chat for coordination and support
- Achievement celebrations and milestone sharing

### Feature 3: Synchronized Focus Sessions
**User Story**: "As a user who works better with others, I want to synchronize my focus sessions with my body doubling partners, so we can take breaks together and maintain shared rhythm."

**Technical Requirements**:
- Group focus session coordination
- Synchronized break timers
- Flexible session management (join/leave mid-session)
- Different focus modes within the same session
- Session recording and analytics for group productivity

## WebSocket Infrastructure

### Connection Manager
```python
# app/api/websockets/manager.py
from typing import Dict, List, Set
from uuid import UUID
from fastapi import WebSocket, WebSocketDisconnect
import json
import asyncio

class WebSocketManager:
    """Centralized WebSocket connection management for Project Chronos."""
    
    def __init__(self):
        # User connections: user_id -> WebSocket
        self.user_connections: Dict[UUID, WebSocket] = {}
        
        # Session connections: session_id -> Set[user_id]
        self.session_participants: Dict[UUID, Set[UUID]] = {}
        
        # Connection metadata
        self.connection_metadata: Dict[UUID, Dict] = {}
        
        self.redis = Redis()
    
    async def connect_user(
        self,
        websocket: WebSocket,
        user_id: UUID,
        connection_type: str = "general"
    ):
        """Connect user to WebSocket with authentication."""
        await websocket.accept()
        self.user_connections[user_id] = websocket
        self.connection_metadata[user_id] = {
            "connected_at": datetime.utcnow(),
            "connection_type": connection_type,
            "last_activity": datetime.utcnow()
        }
        
        # Notify about user coming online
        await self._broadcast_user_status(user_id, "online")
    
    async def disconnect_user(self, user_id: UUID):
        """Disconnect user and cleanup resources."""
        if user_id in self.user_connections:
            # Remove from all sessions
            for session_id, participants in self.session_participants.items():
                if user_id in participants:
                    participants.remove(user_id)
                    await self._broadcast_to_session(
                        session_id,
                        {
                            "type": "participant_left",
                            "user_id": str(user_id),
                            "timestamp": datetime.utcnow().isoformat()
                        }
                    )
            
            # Cleanup connections
            del self.user_connections[user_id]
            del self.connection_metadata[user_id]
            
            # Notify about user going offline
            await self._broadcast_user_status(user_id, "offline")
    
    async def join_session(
        self,
        user_id: UUID,
        session_id: UUID,
        session_type: str = "body_doubling"
    ):
        """Add user to a real-time session."""
        if session_id not in self.session_participants:
            self.session_participants[session_id] = set()
        
        self.session_participants[session_id].add(user_id)
        
        # Notify other participants
        await self._broadcast_to_session(
            session_id,
            {
                "type": "participant_joined",
                "user_id": str(user_id),
                "session_type": session_type,
                "timestamp": datetime.utcnow().isoformat()
            },
            exclude_user=user_id
        )
    
    async def leave_session(self, user_id: UUID, session_id: UUID):
        """Remove user from a real-time session."""
        if session_id in self.session_participants:
            self.session_participants[session_id].discard(user_id)
            
            # Notify other participants
            await self._broadcast_to_session(
                session_id,
                {
                    "type": "participant_left",
                    "user_id": str(user_id),
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
    
    async def broadcast_to_user(
        self,
        user_id: UUID,
        message: Dict
    ):
        """Send message to specific user."""
        if user_id in self.user_connections:
            websocket = self.user_connections[user_id]
            try:
                await websocket.send_text(json.dumps(message))
                self.connection_metadata[user_id]["last_activity"] = datetime.utcnow()
            except Exception as e:
                # Connection lost, cleanup
                await self.disconnect_user(user_id)
    
    async def _broadcast_to_session(
        self,
        session_id: UUID,
        message: Dict,
        exclude_user: Optional[UUID] = None
    ):
        """Broadcast message to all session participants."""
        if session_id in self.session_participants:
            participants = self.session_participants[session_id]
            for user_id in participants:
                if user_id != exclude_user:
                    await self.broadcast_to_user(user_id, message)
```

### Body Doubling Service
```python
# app/services/body_doubling_service.py
class BodyDoublingService:
    """Service for managing virtual body doubling sessions."""
    
    def __init__(self, db: AsyncSession, websocket_manager: WebSocketManager):
        self.db = db
        self.websocket_manager = websocket_manager
        self.redis = Redis()
    
    async def create_session(
        self,
        host_user_id: UUID,
        session_data: BodyDoublingSessionCreate
    ) -> BodyDoublingSession:
        """
        Create a new body doubling session.
        
        Args:
            host_user_id: User creating the session
            session_data: Session configuration data
            
        Returns:
            Created body doubling session
            
        Raises:
            ValidationError: If session data is invalid
        """
        
    async def join_session(
        self,
        user_id: UUID,
        session_id: UUID
    ) -> BodyDoublingParticipant:
        """
        Join an existing body doubling session.
        
        Args:
            user_id: User joining the session
            session_id: Session to join
            
        Returns:
            Participant record
            
        Raises:
            SessionFullError: If session is at capacity
            SessionNotFoundError: If session doesn't exist
        """
        
    async def leave_session(
        self,
        user_id: UUID,
        session_id: UUID
    ):
        """Leave a body doubling session."""
        
    async def start_group_focus(
        self,
        session_id: UUID,
        focus_duration: int,
        break_duration: int = 5
    ) -> GroupFocusSession:
        """
        Start synchronized focus session for all participants.
        
        Args:
            session_id: Body doubling session ID
            focus_duration: Focus period in minutes
            break_duration: Break period in minutes
            
        Returns:
            Group focus session details
        """
        
    async def share_task_progress(
        self,
        user_id: UUID,
        session_id: UUID,
        task_update: TaskProgressUpdate
    ):
        """
        Share task progress with session participants.
        
        Args:
            user_id: User sharing progress
            session_id: Session to share with
            task_update: Progress update details
        """
        
        # Broadcast to session participants
        await self.websocket_manager._broadcast_to_session(
            session_id,
            {
                "type": "task_progress",
                "user_id": str(user_id),
                "task_update": task_update.dict(),
                "timestamp": datetime.utcnow().isoformat()
            }
        )
    
    async def send_encouragement(
        self,
        from_user_id: UUID,
        to_user_id: UUID,
        session_id: UUID,
        encouragement_type: str,
        message: Optional[str] = None
    ):
        """Send encouragement to another session participant."""
        
        encouragement_data = {
            "type": "encouragement",
            "from_user_id": str(from_user_id),
            "encouragement_type": encouragement_type,
            "message": message,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.websocket_manager.broadcast_to_user(
            to_user_id,
            encouragement_data
        )
```

## API Endpoints

### Body Doubling Endpoints
```python
# app/api/v1/body_doubling.py
@router.post("/sessions", response_model=BodyDoublingSessionResponse)
async def create_body_doubling_session(
    session_data: BodyDoublingSessionCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new body doubling session."""

@router.get("/sessions/public", response_model=List[BodyDoublingSessionResponse])
async def get_public_sessions(
    limit: int = 10,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get available public body doubling sessions."""

@router.post("/sessions/{session_id}/join", response_model=ParticipantResponse)
async def join_body_doubling_session(
    session_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Join an existing body doubling session."""

@router.post("/sessions/{session_id}/leave")
async def leave_body_doubling_session(
    session_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Leave a body doubling session."""

@router.post("/sessions/{session_id}/focus", response_model=GroupFocusResponse)
async def start_group_focus_session(
    session_id: UUID,
    focus_data: GroupFocusRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Start synchronized focus session for all participants."""
```

### WebSocket Endpoints
```python
# app/api/websockets/body_doubling.py
@app.websocket("/ws/body-doubling/{session_id}")
async def body_doubling_websocket(
    websocket: WebSocket,
    session_id: UUID,
    token: str = Query(...),
    websocket_manager: WebSocketManager = Depends(get_websocket_manager)
):
    """WebSocket endpoint for body doubling sessions."""
    
    # Authenticate user from token
    user = await authenticate_websocket_token(token)
    if not user:
        await websocket.close(code=4001, reason="Authentication failed")
        return
    
    try:
        # Connect user to WebSocket
        await websocket_manager.connect_user(
            websocket, 
            user.id, 
            connection_type="body_doubling"
        )
        
        # Join body doubling session
        await websocket_manager.join_session(
            user.id, 
            session_id, 
            session_type="body_doubling"
        )
        
        # Handle incoming messages
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            await handle_body_doubling_message(
                user.id,
                session_id,
                message,
                websocket_manager
            )
            
    except WebSocketDisconnect:
        await websocket_manager.disconnect_user(user.id)
    except Exception as e:
        logger.error(f"WebSocket error for user {user.id}: {e}")
        await websocket_manager.disconnect_user(user.id)
```

## Implementation Plan

### Phase 1: WebSocket Infrastructure (Week 1)
1. Implement WebSocket connection manager
2. Set up user authentication for WebSocket connections
3. Create message broadcasting system
4. Implement connection cleanup and error handling

### Phase 2: Body Doubling Core (Week 2)
1. Develop body doubling session management
2. Implement session join/leave functionality
3. Create participant management system
4. Add session state synchronization

### Phase 3: Real-time Features (Week 3)
1. Implement task progress sharing
2. Add encouragement and reaction system
3. Create synchronized focus sessions
4. Develop session chat functionality

### Phase 4: Advanced Features & Testing (Week 4)
1. Add session analytics and insights
2. Implement privacy controls and moderation
3. Create comprehensive testing suite
4. Add monitoring and performance optimization

## Testing Strategy

### Unit Tests
```python
# tests/unit/test_services/test_websocket_manager.py
class TestWebSocketManager:
    async def test_user_connection_management(self):
        """Test user connection and disconnection."""
    
    async def test_session_participant_management(self):
        """Test adding/removing users from sessions."""
    
    async def test_message_broadcasting(self):
        """Test message broadcasting to session participants."""

# tests/unit/test_services/test_body_doubling_service.py
class TestBodyDoublingService:
    async def test_create_body_doubling_session(self):
        """Test body doubling session creation."""
    
    async def test_join_session_capacity_limits(self):
        """Test session capacity enforcement."""
```

### Integration Tests
```python
# tests/integration/test_websockets/test_body_doubling_flow.py
class TestBodyDoublingFlow:
    async def test_complete_body_doubling_session(self):
        """Test complete body doubling session flow."""
    
    async def test_real_time_progress_sharing(self):
        """Test real-time task progress sharing."""
    
    async def test_synchronized_focus_sessions(self):
        """Test group focus session synchronization."""
```

### BDD Scenarios
```gherkin
Feature: Virtual Body Doubling
  Scenario: Creating and joining a body doubling session
    Given I want to create a body doubling session
    When I create a session for 4 participants
    Then other users should be able to find and join my session
    And we should all see each other's presence in real-time

Feature: Synchronized Focus Sessions
  Scenario: Group focus session with synchronized breaks
    Given I am in a body doubling session with 3 other users
    When the host starts a 25-minute focus session
    Then all participants should see the synchronized timer
    And we should all receive break notifications together

Feature: Real-time Progress Sharing
  Scenario: Sharing task completion with session participants
    Given I am in an active body doubling session
    When I complete a task
    Then other participants should see my progress update
    And they should be able to send encouragement
```

## Quality Standards

### Performance Requirements
- WebSocket message delivery < 100ms
- Support for 1000+ concurrent connections
- Session state synchronization < 200ms
- Connection recovery within 5 seconds

### Reliability Requirements
- 99.9% WebSocket uptime
- Automatic connection recovery
- Graceful handling of network interruptions
- Data consistency across all participants

## Success Metrics

### Feature Adoption
- 40% of users try body doubling within first month
- 25% of users regularly participate in body doubling sessions
- 60% of body doubling sessions result in task completion
- 30% of users create their own sessions

### User Impact
- 50% increase in task initiation rates during body doubling
- 40% improvement in focus session completion
- 70% user satisfaction with body doubling experience
- 35% reduction in procrastination incidents

## Deliverables

1. **WebSocket Infrastructure**: Robust connection management system
2. **Body Doubling Platform**: Complete virtual body doubling functionality
3. **Real-time Updates**: Live progress sharing and synchronization
4. **Group Focus Sessions**: Synchronized focus and break timers
5. **Communication System**: Session chat and encouragement features
6. **Test Suite**: Comprehensive WebSocket and real-time testing
7. **Documentation**: WebSocket API and body doubling guides

## Integration Points

### Provides to Other Agents
- Real-time communication infrastructure
- WebSocket connection management
- Live update broadcasting capabilities
- Session coordination services

### Requires from Other Agents
- User authentication and authorization (Agent 2)
- Database models and operations (Agent 1)
- Focus session integration (Agent 5)
- Task progress data (Agent 3)

## Commit Strategy

Real-time focused commits with clear WebSocket functionality:
- `feat(websocket): Implement WebSocket connection manager`
- `feat(body-doubling): Add virtual body doubling session management`
- `feat(realtime): Implement real-time progress sharing`
- `feat(sync): Add synchronized group focus sessions`
- `test(websocket): Add comprehensive WebSocket test suite`
- `docs(realtime): Add WebSocket and body doubling documentation`

This real-time system will provide the social accountability and shared focus that many users with ADHD need to overcome task initiation barriers and maintain concentration through virtual body doubling sessions.
