# PRD: Speechbot Frontend Components - React Implementation

**Project:** Chronos Speechbot Frontend Integration  
**Version:** 1.0  
**Date:** June 18, 2025  
**Status:** Draft  
**Parent PRD:** PRD-Speechbot-ADHD-Voice-Assistant.md

## Executive Summary

This PRD details the frontend React components and user interface implementation for Speechbot integration within Project Chronos. It focuses on creating ADHD-optimized voice interaction components that seamlessly integrate with the existing Chronos frontend architecture while providing intuitive voice controls and adaptive user experiences.

## Problem Statement

ADHD users need intuitive, non-overwhelming voice interface controls that:
- **Minimize Cognitive Load**: Simple, clear voice controls without complex menus
- **Provide Immediate Feedback**: Visual and audio confirmation of voice interactions
- **Adapt to User State**: UI that changes based on ADHD cognitive state
- **Integrate Seamlessly**: Voice controls that feel natural within existing workflows
- **Support Accessibility**: Full keyboard navigation and screen reader compatibility

## Solution Overview

The Speechbot frontend components will provide:
- Intuitive voice control interfaces with ADHD-optimized design
- Real-time voice adaptation controls and feedback
- Seamless integration with existing Chronos React components
- Comprehensive accessibility features
- Progressive enhancement for voice features

## Component Architecture

### Core Component Structure

```
src/components/speechbot/
├── providers/
│   ├── SpeechbotProvider.tsx          # Main context provider
│   ├── VoiceStateProvider.tsx         # Voice state management
│   └── ADHDAdaptationProvider.tsx     # ADHD adaptation context
├── controls/
│   ├── VoiceToggle.tsx                # Master voice on/off
│   ├── VoiceVolumeControl.tsx         # Volume adjustment
│   ├── EnergyLevelSelector.tsx        # Energy level input
│   ├── FocusModeSelector.tsx          # Focus mode selection
│   └── QuickVoiceSettings.tsx         # Quick access settings
├── output/
│   ├── SpeechOutput.tsx               # Audio playback component
│   ├── VoiceVisualizer.tsx            # Audio waveform display
│   ├── SpeechTranscript.tsx           # Text display of speech
│   └── VoiceStatusIndicator.tsx       # Current voice status
├── profiles/
│   ├── VoiceProfileManager.tsx        # Profile management UI
│   ├── VoiceProfileCard.tsx           # Individual profile display
│   ├── VoiceCloneWizard.tsx           # Voice cloning setup
│   └── ProfileSettings.tsx            # Profile configuration
├── adaptation/
│   ├── ADHDVoiceAdapter.tsx           # ADHD-specific controls
│   ├── CognitiveLoadIndicator.tsx     # Cognitive load display
│   ├── EnergyMatcher.tsx              # Energy level matching
│   └── SensitivityControls.tsx        # Sensory sensitivity settings
├── dashboard/
│   ├── SpeechbotDashboard.tsx         # Main dashboard
│   ├── VoiceAnalytics.tsx             # Usage analytics
│   ├── AdaptationHistory.tsx          # Adaptation tracking
│   └── VoiceQualityMetrics.tsx        # Quality monitoring
└── accessibility/
    ├── VoiceKeyboardShortcuts.tsx     # Keyboard navigation
    ├── ScreenReaderAnnouncements.tsx  # Screen reader support
    └── HighContrastVoiceUI.tsx        # High contrast mode
```

## Key Components Specification

### 1. SpeechbotProvider - Main Context Provider

```typescript
interface SpeechbotContextType {
  // Voice state
  isVoiceEnabled: boolean;
  currentVoiceProfile: VoiceProfile | null;
  voiceConfig: VoiceConfig;
  adhdState: ADHDState;
  
  // Voice controls
  toggleVoice: () => Promise<void>;
  synthesizeSpeech: (text: string, options?: SynthesisOptions) => Promise<void>;
  updateVoiceConfig: (config: Partial<VoiceConfig>) => Promise<void>;
  updateADHDState: (state: Partial<ADHDState>) => Promise<void>;
  
  // Profile management
  voiceProfiles: VoiceProfile[];
  switchVoiceProfile: (profileId: string) => Promise<void>;
  createVoiceProfile: (profileData: VoiceProfileCreate) => Promise<VoiceProfile>;
  
  // Real-time adaptation
  isAdaptationEnabled: boolean;
  adaptationHistory: AdaptationEvent[];
  
  // Status and feedback
  synthesisStatus: 'idle' | 'synthesizing' | 'playing' | 'error';
  lastError: string | null;
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
}
```

### 2. VoiceToggle - Master Voice Control

```typescript
interface VoiceToggleProps {
  size?: 'small' | 'medium' | 'large';
  showLabel?: boolean;
  position?: 'fixed' | 'relative';
  adhdOptimized?: boolean;
  quickAccess?: boolean;
}

const VoiceToggle: React.FC<VoiceToggleProps> = ({
  size = 'medium',
  showLabel = true,
  position = 'relative',
  adhdOptimized = true,
  quickAccess = false
}) => {
  const { isVoiceEnabled, toggleVoice, synthesisStatus } = useSpeechbot();
  const { cognitiveLoad } = useADHDState();
  
  // ADHD-optimized styling based on cognitive load
  const getToggleStyle = () => {
    if (!adhdOptimized) return 'default';
    
    if (cognitiveLoad > 0.7) return 'high-contrast-simple';
    if (cognitiveLoad > 0.4) return 'medium-emphasis';
    return 'standard';
  };
  
  return (
    <button
      className={`voice-toggle ${getToggleStyle()} ${size}`}
      onClick={toggleVoice}
      disabled={synthesisStatus === 'synthesizing'}
      aria-label={isVoiceEnabled ? 'Disable voice' : 'Enable voice'}
      aria-pressed={isVoiceEnabled}
    >
      {/* Voice icon with status indicator */}
      <VoiceIcon 
        enabled={isVoiceEnabled} 
        status={synthesisStatus}
        size={size}
      />
      {showLabel && (
        <span className="voice-toggle-label">
          {isVoiceEnabled ? 'Voice On' : 'Voice Off'}
        </span>
      )}
    </button>
  );
};
```

### 3. ADHDVoiceAdapter - ADHD-Specific Controls

```typescript
interface ADHDVoiceAdapterProps {
  autoAdapt?: boolean;
  showAdvancedControls?: boolean;
  compactMode?: boolean;
}

const ADHDVoiceAdapter: React.FC<ADHDVoiceAdapterProps> = ({
  autoAdapt = true,
  showAdvancedControls = false,
  compactMode = false
}) => {
  const { adhdState, updateADHDState, voiceConfig, updateVoiceConfig } = useSpeechbot();
  const [isExpanded, setIsExpanded] = useState(!compactMode);
  
  const handleEnergyLevelChange = async (energyLevel: number) => {
    await updateADHDState({ energyLevel });
    
    if (autoAdapt) {
      // Automatically adapt voice to new energy level
      const adaptedConfig = calculateVoiceAdaptation(energyLevel, adhdState);
      await updateVoiceConfig(adaptedConfig);
    }
  };
  
  return (
    <div className={`adhd-voice-adapter ${compactMode ? 'compact' : 'full'}`}>
      <div className="adapter-header">
        <h3>Voice Adaptation</h3>
        {compactMode && (
          <button 
            onClick={() => setIsExpanded(!isExpanded)}
            aria-expanded={isExpanded}
          >
            {isExpanded ? 'Collapse' : 'Expand'}
          </button>
        )}
      </div>
      
      {isExpanded && (
        <div className="adapter-controls">
          {/* Energy Level Selector */}
          <EnergyLevelSelector
            value={adhdState.energyLevel}
            onChange={handleEnergyLevelChange}
            adhdOptimized={true}
          />
          
          {/* Cognitive Load Indicator */}
          <CognitiveLoadIndicator
            value={adhdState.cognitiveLoad}
            onChange={(load) => updateADHDState({ cognitiveLoad: load })}
            readOnly={autoAdapt}
          />
          
          {/* Focus Mode Selector */}
          <FocusModeSelector
            value={adhdState.focusMode}
            onChange={(mode) => updateADHDState({ focusMode: mode })}
          />
          
          {showAdvancedControls && (
            <div className="advanced-controls">
              <SensitivityControls />
              <EmotionalRegulationControls />
              <VoicePersonalizationControls />
            </div>
          )}
        </div>
      )}
    </div>
  );
};
```

## ADHD-Optimized Design Patterns

### 1. Cognitive Load Responsive Design

```typescript
// Hook for cognitive load responsive styling
const useCognitiveLoadStyles = () => {
  const { adhdState } = useSpeechbot();
  
  return useMemo(() => {
    const { cognitiveLoad } = adhdState;
    
    if (cognitiveLoad > 0.8) {
      return {
        complexity: 'minimal',
        colors: 'high-contrast',
        animations: 'none',
        textSize: 'large',
        spacing: 'generous'
      };
    } else if (cognitiveLoad > 0.5) {
      return {
        complexity: 'simplified',
        colors: 'medium-contrast',
        animations: 'subtle',
        textSize: 'medium',
        spacing: 'comfortable'
      };
    } else {
      return {
        complexity: 'full',
        colors: 'standard',
        animations: 'normal',
        textSize: 'standard',
        spacing: 'compact'
      };
    }
  }, [adhdState.cognitiveLoad]);
};
```

### 2. Energy Level Visual Feedback

```typescript
const EnergyLevelSelector: React.FC<EnergyLevelSelectorProps> = ({
  value,
  onChange,
  adhdOptimized = true
}) => {
  const energyColors = {
    1: '#ff6b6b', // Low energy - warm red
    2: '#ff8e53', // 
    3: '#ff9f43', // 
    4: '#feca57', // 
    5: '#48dbfb', // Medium - cool blue
    6: '#0abde3', // 
    7: '#00d2d3', // 
    8: '#1dd1a1', // 
    9: '#55a3ff', // 
    10: '#5f27cd' // High energy - vibrant purple
  };
  
  return (
    <div className="energy-level-selector">
      <label htmlFor="energy-slider">Energy Level: {value}/10</label>
      <div className="energy-slider-container">
        <input
          id="energy-slider"
          type="range"
          min="1"
          max="10"
          value={value}
          onChange={(e) => onChange(parseInt(e.target.value))}
          className="energy-slider"
          style={{
            '--slider-color': energyColors[value as keyof typeof energyColors]
          }}
        />
        <div className="energy-indicators">
          {Array.from({ length: 10 }, (_, i) => (
            <div
              key={i + 1}
              className={`energy-dot ${value >= i + 1 ? 'active' : ''}`}
              style={{
                backgroundColor: value >= i + 1 ? energyColors[(i + 1) as keyof typeof energyColors] : '#e0e0e0'
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
```

### 3. Focus Mode Visual States

```typescript
const FocusModeSelector: React.FC<FocusModeSelectorProps> = ({
  value,
  onChange
}) => {
  const focusModes = [
    {
      id: 'normal',
      label: 'Normal',
      icon: '🎯',
      description: 'Balanced voice for regular tasks',
      color: '#3498db'
    },
    {
      id: 'deep_work',
      label: 'Deep Work',
      icon: '🧠',
      description: 'Minimal, clear voice for concentration',
      color: '#2c3e50'
    },
    {
      id: 'creative',
      label: 'Creative',
      icon: '🎨',
      description: 'Energetic voice for creative tasks',
      color: '#e74c3c'
    },
    {
      id: 'administrative',
      label: 'Admin',
      icon: '📋',
      description: 'Structured voice for routine tasks',
      color: '#27ae60'
    },
    {
      id: 'break',
      label: 'Break',
      icon: '☕',
      description: 'Relaxed voice for rest periods',
      color: '#f39c12'
    }
  ];
  
  return (
    <div className="focus-mode-selector">
      <label>Focus Mode</label>
      <div className="focus-mode-grid">
        {focusModes.map((mode) => (
          <button
            key={mode.id}
            className={`focus-mode-card ${value === mode.id ? 'active' : ''}`}
            onClick={() => onChange(mode.id)}
            style={{
              '--mode-color': mode.color,
              borderColor: value === mode.id ? mode.color : '#e0e0e0'
            }}
          >
            <div className="mode-icon">{mode.icon}</div>
            <div className="mode-label">{mode.label}</div>
            <div className="mode-description">{mode.description}</div>
          </button>
        ))}
      </div>
    </div>
  );
};
```

## Integration with Existing Chronos Components

### 1. Task Management Integration

```typescript
// Enhanced TaskCard with voice integration
const TaskCard: React.FC<TaskCardProps> = ({ task, ...props }) => {
  const { synthesizeSpeech, isVoiceEnabled } = useSpeechbot();
  const { adhdState } = useADHDState();

  const handleTaskAnnouncement = async () => {
    if (!isVoiceEnabled) return;

    const announcement = generateTaskAnnouncement(task, adhdState);
    await synthesizeSpeech(announcement, {
      priority: 'medium',
      adhdContext: adhdState
    });
  };

  return (
    <div className="task-card enhanced-voice">
      {/* Existing task card content */}
      <TaskCardContent task={task} {...props} />

      {/* Voice integration controls */}
      {isVoiceEnabled && (
        <div className="voice-controls">
          <button
            onClick={handleTaskAnnouncement}
            className="voice-announce-btn"
            aria-label="Announce task details"
          >
            🔊
          </button>
        </div>
      )}
    </div>
  );
};
```

### 2. Focus Session Integration

```typescript
// Enhanced FocusSession with voice cues
const FocusSessionTimer: React.FC<FocusSessionTimerProps> = ({
  session,
  onSessionEnd
}) => {
  const { synthesizeSpeech, isVoiceEnabled, voiceConfig } = useSpeechbot();
  const [sessionPhase, setSessionPhase] = useState<'focus' | 'break'>('focus');

  useEffect(() => {
    if (!isVoiceEnabled) return;

    const announcePhaseChange = async () => {
      const announcement = sessionPhase === 'focus'
        ? "Starting your focus session. I'll keep my voice minimal and clear."
        : "Time for a break! You've earned it.";

      await synthesizeSpeech(announcement, {
        adhdContext: {
          focusMode: sessionPhase === 'focus' ? 'deep_work' : 'break'
        }
      });
    };

    announcePhaseChange();
  }, [sessionPhase, isVoiceEnabled, synthesizeSpeech]);

  return (
    <div className="focus-session-timer voice-enhanced">
      {/* Existing timer content */}
      <TimerDisplay session={session} />

      {/* Voice-specific controls */}
      <VoiceFocusControls
        sessionPhase={sessionPhase}
        onPhaseChange={setSessionPhase}
      />
    </div>
  );
};
```

### 3. Notification Integration

```typescript
// Enhanced notifications with voice output
const NotificationCenter: React.FC = () => {
  const { notifications } = useNotifications();
  const { synthesizeSpeech, isVoiceEnabled } = useSpeechbot();
  const { adhdState } = useADHDState();

  const handleVoiceNotification = async (notification: Notification) => {
    if (!isVoiceEnabled || !notification.voiceEnabled) return;

    const voiceText = adaptNotificationForVoice(notification, adhdState);
    await synthesizeSpeech(voiceText, {
      priority: notification.priority,
      adhdContext: adhdState,
      interruptible: notification.priority !== 'urgent'
    });
  };

  return (
    <div className="notification-center">
      {notifications.map((notification) => (
        <NotificationCard
          key={notification.id}
          notification={notification}
          onVoicePlay={() => handleVoiceNotification(notification)}
          voiceEnabled={isVoiceEnabled}
        />
      ))}
    </div>
  );
};
```

## Accessibility Implementation

### 1. Keyboard Navigation

```typescript
const VoiceKeyboardShortcuts: React.FC = () => {
  const { toggleVoice, synthesizeSpeech } = useSpeechbot();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+Shift+V: Toggle voice
      if (event.ctrlKey && event.shiftKey && event.key === 'V') {
        event.preventDefault();
        toggleVoice();
      }

      // Ctrl+Shift+S: Speak current focus
      if (event.ctrlKey && event.shiftKey && event.key === 'S') {
        event.preventDefault();
        speakCurrentFocus();
      }

      // Ctrl+Shift+R: Repeat last speech
      if (event.ctrlKey && event.shiftKey && event.key === 'R') {
        event.preventDefault();
        repeatLastSpeech();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [toggleVoice, synthesizeSpeech]);

  const speakCurrentFocus = async () => {
    const focusedElement = document.activeElement;
    if (focusedElement) {
      const text = extractTextFromElement(focusedElement);
      if (text) {
        await synthesizeSpeech(text);
      }
    }
  };

  return null; // This component only handles keyboard events
};
```

### 2. Screen Reader Integration

```typescript
const ScreenReaderAnnouncements: React.FC = () => {
  const { synthesisStatus, lastSynthesizedText } = useSpeechbot();
  const [announcements, setAnnouncements] = useState<string[]>([]);

  useEffect(() => {
    // Announce voice status changes to screen readers
    let announcement = '';

    switch (synthesisStatus) {
      case 'synthesizing':
        announcement = 'Voice synthesis in progress';
        break;
      case 'playing':
        announcement = `Now speaking: ${lastSynthesizedText}`;
        break;
      case 'error':
        announcement = 'Voice synthesis error occurred';
        break;
    }

    if (announcement) {
      setAnnouncements(prev => [...prev, announcement]);

      // Remove announcement after it's been read
      setTimeout(() => {
        setAnnouncements(prev => prev.slice(1));
      }, 3000);
    }
  }, [synthesisStatus, lastSynthesizedText]);

  return (
    <div aria-live="polite" aria-atomic="false" className="sr-only">
      {announcements.map((announcement, index) => (
        <div key={index}>{announcement}</div>
      ))}
    </div>
  );
};
```

### 3. High Contrast Mode

```typescript
const HighContrastVoiceUI: React.FC<{ children: React.ReactNode }> = ({
  children
}) => {
  const { isHighContrast } = useAccessibility();
  const { adhdState } = useADHDState();

  const getContrastClass = () => {
    if (!isHighContrast) return '';

    // Extra high contrast for high cognitive load
    if (adhdState.cognitiveLoad > 0.7) {
      return 'voice-ui-ultra-contrast';
    }

    return 'voice-ui-high-contrast';
  };

  return (
    <div className={`voice-ui-container ${getContrastClass()}`}>
      {children}
    </div>
  );
};
```

## Voice Profile Management UI

### 1. Voice Profile Manager

```typescript
const VoiceProfileManager: React.FC = () => {
  const { voiceProfiles, createVoiceProfile, deleteVoiceProfile } = useSpeechbot();
  const [isCreating, setIsCreating] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState<string | null>(null);

  return (
    <div className="voice-profile-manager">
      <div className="profile-header">
        <h2>Voice Profiles</h2>
        <button
          onClick={() => setIsCreating(true)}
          className="create-profile-btn"
          disabled={voiceProfiles.length >= 5} // Limit profiles
        >
          Create New Profile
        </button>
      </div>

      <div className="profile-grid">
        {voiceProfiles.map((profile) => (
          <VoiceProfileCard
            key={profile.id}
            profile={profile}
            isSelected={selectedProfile === profile.id}
            onSelect={() => setSelectedProfile(profile.id)}
            onDelete={() => deleteVoiceProfile(profile.id)}
          />
        ))}
      </div>

      {isCreating && (
        <VoiceCloneWizard
          onComplete={() => setIsCreating(false)}
          onCancel={() => setIsCreating(false)}
        />
      )}
    </div>
  );
};
```

### 2. Voice Clone Wizard

```typescript
const VoiceCloneWizard: React.FC<VoiceCloneWizardProps> = ({
  onComplete,
  onCancel
}) => {
  const [step, setStep] = useState(1);
  const [profileData, setProfileData] = useState<VoiceProfileCreate>({
    name: '',
    audioSamples: [],
    adhdPreferences: {}
  });
  const [isRecording, setIsRecording] = useState(false);
  const [recordingProgress, setRecordingProgress] = useState(0);

  const steps = [
    { id: 1, title: 'Profile Setup', component: ProfileSetupStep },
    { id: 2, title: 'Voice Recording', component: VoiceRecordingStep },
    { id: 3, title: 'ADHD Preferences', component: ADHDPreferencesStep },
    { id: 4, title: 'Testing & Finalization', component: TestingStep }
  ];

  const CurrentStepComponent = steps[step - 1].component;

  return (
    <div className="voice-clone-wizard">
      <div className="wizard-header">
        <h3>Create Voice Profile</h3>
        <div className="step-indicator">
          {steps.map((s) => (
            <div
              key={s.id}
              className={`step ${step >= s.id ? 'completed' : ''} ${step === s.id ? 'active' : ''}`}
            >
              {s.title}
            </div>
          ))}
        </div>
      </div>

      <div className="wizard-content">
        <CurrentStepComponent
          profileData={profileData}
          onUpdate={setProfileData}
          onNext={() => setStep(step + 1)}
          onPrevious={() => setStep(step - 1)}
          isRecording={isRecording}
          setIsRecording={setIsRecording}
          recordingProgress={recordingProgress}
          setRecordingProgress={setRecordingProgress}
        />
      </div>

      <div className="wizard-actions">
        <button onClick={onCancel} className="cancel-btn">
          Cancel
        </button>
        {step > 1 && (
          <button onClick={() => setStep(step - 1)} className="back-btn">
            Back
          </button>
        )}
        {step < steps.length ? (
          <button onClick={() => setStep(step + 1)} className="next-btn">
            Next
          </button>
        ) : (
          <button onClick={onComplete} className="finish-btn">
            Create Profile
          </button>
        )}
      </div>
    </div>
  );
};
```

## Performance Optimization

### 1. Audio Caching Strategy

```typescript
const useAudioCache = () => {
  const [audioCache, setAudioCache] = useState<Map<string, AudioBuffer>>(new Map());
  const maxCacheSize = 50; // Maximum cached audio files

  const cacheAudio = useCallback(async (text: string, audioBlob: Blob) => {
    const audioBuffer = await audioBlob.arrayBuffer();
    const buffer = await new AudioContext().decodeAudioData(audioBuffer);

    setAudioCache(prev => {
      const newCache = new Map(prev);

      // Remove oldest entries if cache is full
      if (newCache.size >= maxCacheSize) {
        const firstKey = newCache.keys().next().value;
        newCache.delete(firstKey);
      }

      newCache.set(text, buffer);
      return newCache;
    });
  }, [maxCacheSize]);

  const getCachedAudio = useCallback((text: string): AudioBuffer | null => {
    return audioCache.get(text) || null;
  }, [audioCache]);

  return { cacheAudio, getCachedAudio };
};
```

### 2. Lazy Loading Components

```typescript
// Lazy load heavy voice components
const VoiceProfileManager = lazy(() => import('./VoiceProfileManager'));
const VoiceCloneWizard = lazy(() => import('./VoiceCloneWizard'));
const SpeechbotDashboard = lazy(() => import('./SpeechbotDashboard'));

const LazyVoiceComponent: React.FC<{ component: string }> = ({ component }) => {
  const { adhdState } = useADHDState();

  // Show simpler loading for high cognitive load
  const LoadingComponent = adhdState.cognitiveLoad > 0.7
    ? SimpleSpinner
    : DetailedLoadingScreen;

  return (
    <Suspense fallback={<LoadingComponent />}>
      {component === 'profile-manager' && <VoiceProfileManager />}
      {component === 'clone-wizard' && <VoiceCloneWizard />}
      {component === 'dashboard' && <SpeechbotDashboard />}
    </Suspense>
  );
};
```

## Testing Strategy

### 1. Component Testing

```typescript
// Example test for VoiceToggle component
describe('VoiceToggle', () => {
  it('should toggle voice state when clicked', async () => {
    const mockToggleVoice = jest.fn();
    const mockContext = {
      isVoiceEnabled: false,
      toggleVoice: mockToggleVoice,
      synthesisStatus: 'idle'
    };

    render(
      <SpeechbotContext.Provider value={mockContext}>
        <VoiceToggle />
      </SpeechbotContext.Provider>
    );

    const toggleButton = screen.getByRole('button', { name: /enable voice/i });
    await userEvent.click(toggleButton);

    expect(mockToggleVoice).toHaveBeenCalledTimes(1);
  });

  it('should adapt styling based on cognitive load', () => {
    const highCognitiveLoadContext = {
      adhdState: { cognitiveLoad: 0.9 }
    };

    render(
      <ADHDStateContext.Provider value={highCognitiveLoadContext}>
        <VoiceToggle adhdOptimized={true} />
      </ADHDStateContext.Provider>
    );

    const toggleButton = screen.getByRole('button');
    expect(toggleButton).toHaveClass('high-contrast-simple');
  });
});
```

### 2. Accessibility Testing

```typescript
// Accessibility test suite
describe('Voice Components Accessibility', () => {
  it('should support keyboard navigation', async () => {
    render(<VoiceControlPanel />);

    // Tab through all interactive elements
    await userEvent.tab();
    expect(screen.getByRole('button', { name: /toggle voice/i })).toHaveFocus();

    await userEvent.tab();
    expect(screen.getByRole('slider', { name: /energy level/i })).toHaveFocus();

    // Test keyboard shortcuts
    await userEvent.keyboard('{Control>}{Shift>}V{/Shift}{/Control}');
    // Verify voice toggle was triggered
  });

  it('should provide proper ARIA labels', () => {
    render(<EnergyLevelSelector value={5} onChange={jest.fn()} />);

    const slider = screen.getByRole('slider');
    expect(slider).toHaveAttribute('aria-label', expect.stringContaining('Energy Level'));
    expect(slider).toHaveAttribute('aria-valuemin', '1');
    expect(slider).toHaveAttribute('aria-valuemax', '10');
    expect(slider).toHaveAttribute('aria-valuenow', '5');
  });
});
```

### 3. ADHD-Specific Testing

```typescript
describe('ADHD Adaptations', () => {
  it('should simplify UI for high cognitive load', () => {
    const highCognitiveLoadState = {
      cognitiveLoad: 0.9,
      energyLevel: 3,
      focusMode: 'overwhelmed'
    };

    render(
      <ADHDStateProvider initialState={highCognitiveLoadState}>
        <VoiceControlPanel />
      </ADHDStateProvider>
    );

    // Check for simplified UI elements
    expect(screen.queryByText('Advanced Settings')).not.toBeInTheDocument();
    expect(screen.getByTestId('simple-controls')).toBeInTheDocument();
  });

  it('should adapt voice parameters based on energy level', async () => {
    const mockUpdateVoiceConfig = jest.fn();

    render(
      <SpeechbotProvider>
        <EnergyLevelSelector value={8} onChange={jest.fn()} />
      </SpeechbotProvider>
    );

    // Simulate energy level change
    const slider = screen.getByRole('slider');
    await userEvent.type(slider, '3');

    // Verify voice config adaptation
    expect(mockUpdateVoiceConfig).toHaveBeenCalledWith(
      expect.objectContaining({
        pace: expect.any(Number),
        energy: expect.any(Number)
      })
    );
  });
});
```

## Styling and Theming

### 1. ADHD-Optimized CSS Variables

```css
/* Voice component CSS variables */
:root {
  /* Cognitive load responsive colors */
  --voice-primary-low-load: #3498db;
  --voice-primary-medium-load: #2980b9;
  --voice-primary-high-load: #1a1a1a;

  /* Energy level colors */
  --energy-low: #ff6b6b;
  --energy-medium: #48dbfb;
  --energy-high: #5f27cd;

  /* Focus mode colors */
  --focus-normal: #3498db;
  --focus-deep-work: #2c3e50;
  --focus-creative: #e74c3c;
  --focus-admin: #27ae60;
  --focus-break: #f39c12;

  /* Accessibility */
  --voice-high-contrast-bg: #000000;
  --voice-high-contrast-text: #ffffff;
  --voice-focus-outline: #0066cc;

  /* Spacing for cognitive load */
  --spacing-low-load: 0.5rem;
  --spacing-medium-load: 0.75rem;
  --spacing-high-load: 1rem;
}

/* Cognitive load responsive classes */
.voice-ui-container.high-cognitive-load {
  --primary-color: var(--voice-primary-high-load);
  --spacing: var(--spacing-high-load);
  font-size: 1.2em;
  line-height: 1.6;
}

.voice-ui-container.medium-cognitive-load {
  --primary-color: var(--voice-primary-medium-load);
  --spacing: var(--spacing-medium-load);
  font-size: 1.1em;
  line-height: 1.5;
}

.voice-ui-container.low-cognitive-load {
  --primary-color: var(--voice-primary-low-load);
  --spacing: var(--spacing-low-load);
  font-size: 1em;
  line-height: 1.4;
}
```

### 2. Component-Specific Styles

```css
/* Voice Toggle Button */
.voice-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  padding: var(--spacing) calc(var(--spacing) * 1.5);
  border: 2px solid var(--primary-color);
  border-radius: 8px;
  background: transparent;
  color: var(--primary-color);
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
}

.voice-toggle:hover {
  background: var(--primary-color);
  color: white;
}

.voice-toggle:focus {
  outline: 3px solid var(--voice-focus-outline);
  outline-offset: 2px;
}

.voice-toggle.high-contrast-simple {
  border-width: 3px;
  font-size: 1.2em;
  padding: 1rem 1.5rem;
}

/* Energy Level Selector */
.energy-level-selector {
  margin: var(--spacing) 0;
}

.energy-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #e0e0e0;
  outline: none;
  -webkit-appearance: none;
}

.energy-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--slider-color);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.energy-indicators {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
}

.energy-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

/* Focus Mode Selector */
.focus-mode-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing);
  margin-top: var(--spacing);
}

.focus-mode-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing);
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.focus-mode-card:hover {
  border-color: var(--mode-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.focus-mode-card.active {
  border-color: var(--mode-color);
  background: var(--mode-color);
  color: white;
}

.mode-icon {
  font-size: 2em;
  margin-bottom: 0.5rem;
}

.mode-label {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.mode-description {
  font-size: 0.85em;
  text-align: center;
  opacity: 0.8;
}
```

## Implementation Roadmap

### Phase 1: Core Components (Week 1-2)
- [ ] SpeechbotProvider context setup
- [ ] VoiceToggle component
- [ ] Basic SpeechOutput component
- [ ] EnergyLevelSelector component
- [ ] Integration with existing Chronos components

### Phase 2: ADHD Adaptations (Week 3-4)
- [ ] ADHDVoiceAdapter component
- [ ] Cognitive load responsive styling
- [ ] FocusModeSelector component
- [ ] CognitiveLoadIndicator component
- [ ] Real-time adaptation logic

### Phase 3: Advanced Features (Week 5-6)
- [ ] VoiceProfileManager component
- [ ] VoiceCloneWizard component
- [ ] Audio caching implementation
- [ ] Performance optimizations
- [ ] Advanced accessibility features

### Phase 4: Testing & Polish (Week 7-8)
- [ ] Comprehensive component testing
- [ ] Accessibility testing
- [ ] ADHD user testing
- [ ] Performance testing
- [ ] Documentation and examples

## Error Handling and User Feedback

### 1. ADHD-Friendly Error States

```typescript
interface ErrorState {
  type: 'network' | 'validation' | 'permission' | 'synthesis' | 'unknown';
  message: string;
  suggestions: string[];
  recoveryActions: RecoveryAction[];
  isRetryable: boolean;
  helpUrl?: string;
}

const ErrorBoundary: React.FC<ErrorBoundaryProps> = ({ children }) => {
  const [error, setError] = useState<ErrorState | null>(null);
  const { adhdState } = useADHDState();

  const handleError = (error: Error, errorInfo: ErrorInfo) => {
    const errorState = analyzeError(error, adhdState);
    setError(errorState);

    // Log error with ADHD context
    logErrorWithContext(error, errorInfo, adhdState);
  };

  const analyzeError = (error: Error, adhdState: ADHDState): ErrorState => {
    // Network errors
    if (error.message.includes('fetch')) {
      return {
        type: 'network',
        message: "Having trouble connecting right now",
        suggestions: [
          "Check your internet connection",
          "Try refreshing the page",
          "The issue might resolve itself in a moment"
        ],
        recoveryActions: [
          { label: "Try Again", action: () => window.location.reload() },
          { label: "Go Back", action: () => window.history.back() }
        ],
        isRetryable: true,
        helpUrl: "https://docs.chronos.app/troubleshooting/connection"
      };
    }

    // Voice synthesis errors
    if (error.message.includes('synthesis')) {
      const suggestions = ["Let's try that again"];

      if (adhdState.cognitive_load > 0.7) {
        suggestions.push("This might be a good time for a short break");
      }

      return {
        type: 'synthesis',
        message: "Voice creation didn't work this time",
        suggestions,
        recoveryActions: [
          { label: "Try Again", action: () => retryLastAction() },
          { label: "Use Different Text", action: () => clearCurrentText() }
        ],
        isRetryable: true
      };
    }

    // Default error handling
    return {
      type: 'unknown',
      message: "Something unexpected happened",
      suggestions: [
        "This isn't your fault - it's a technical issue",
        "Try refreshing the page",
        "If it keeps happening, let us know"
      ],
      recoveryActions: [
        { label: "Refresh Page", action: () => window.location.reload() },
        { label: "Report Issue", action: () => openSupportChat() }
      ],
      isRetryable: true
    };
  };

  if (error) {
    return (
      <div className="error-boundary">
        <div className="error-content">
          <div className="error-icon">😔</div>
          <h2>Oops! {error.message}</h2>

          <div className="error-suggestions">
            <h3>Here's what you can try:</h3>
            <ul>
              {error.suggestions.map((suggestion, index) => (
                <li key={index}>{suggestion}</li>
              ))}
            </ul>
          </div>

          <div className="error-actions">
            {error.recoveryActions.map((action, index) => (
              <button
                key={index}
                onClick={action.action}
                className="recovery-action"
              >
                {action.label}
              </button>
            ))}
          </div>

          {error.helpUrl && (
            <a href={error.helpUrl} className="help-link">
              Get more help →
            </a>
          )}
        </div>
      </div>
    );
  }

  return <>{children}</>;
};
```

### 2. Loading States with ADHD Considerations

```typescript
const LoadingState: React.FC<LoadingStateProps> = ({
  type,
  message,
  progress,
  adhdOptimized = true
}) => {
  const { adhdState } = useADHDState();
  const [encouragementMessage, setEncouragementMessage] = useState('');

  useEffect(() => {
    if (adhdOptimized && adhdState.cognitive_load > 0.6) {
      // Show encouraging messages for high cognitive load
      const messages = [
        "Taking a moment to process...",
        "Almost there! Your patience is appreciated.",
        "Working on it... feel free to take a deep breath.",
        "Processing... you're doing great!"
      ];

      const interval = setInterval(() => {
        setEncouragementMessage(
          messages[Math.floor(Math.random() * messages.length)]
        );
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [adhdOptimized, adhdState.cognitive_load]);

  const getLoadingAnimation = () => {
    if (adhdState.sensory_sensitivity_level > 7) {
      return 'minimal'; // Subtle animation for sensory sensitive users
    }
    return 'standard';
  };

  return (
    <div className={`loading-state ${getLoadingAnimation()}`}>
      <div className="loading-content">
        {type === 'synthesis' && (
          <div className="synthesis-loading">
            <div className="voice-wave">
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className="wave-bar"
                  style={{
                    animationDelay: `${i * 0.1}s`,
                    animationDuration: adhdState.sensory_sensitivity_level > 7 ? '2s' : '1s'
                  }}
                />
              ))}
            </div>
            <p>Creating your voice...</p>
          </div>
        )}

        {type === 'training' && (
          <div className="training-loading">
            <div className="progress-circle">
              <svg viewBox="0 0 36 36">
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#e0e0e0"
                  strokeWidth="2"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#3498db"
                  strokeWidth="2"
                  strokeDasharray={`${progress || 0}, 100`}
                />
              </svg>
              <div className="progress-text">{Math.round(progress || 0)}%</div>
            </div>
            <p>Training your voice model...</p>
          </div>
        )}

        <p className="loading-message">{message}</p>

        {encouragementMessage && (
          <p className="encouragement-message">{encouragementMessage}</p>
        )}
      </div>
    </div>
  );
};
```

### 3. Form Validation with ADHD Support

```typescript
const useADHDFormValidation = () => {
  const { adhdState } = useADHDState();

  const validateField = (
    fieldName: string,
    value: any,
    rules: ValidationRule[]
  ): ValidationResult => {
    const errors: string[] = [];
    const suggestions: string[] = [];

    for (const rule of rules) {
      const result = rule.validate(value);

      if (!result.valid) {
        // Generate ADHD-friendly error message
        const friendlyMessage = generateFriendlyMessage(
          fieldName,
          rule.type,
          result.message,
          adhdState
        );

        errors.push(friendlyMessage);

        // Add helpful suggestions
        const fieldSuggestions = getFieldSuggestions(fieldName, rule.type);
        suggestions.push(...fieldSuggestions);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      suggestions: [...new Set(suggestions)] // Remove duplicates
    };
  };

  const generateFriendlyMessage = (
    fieldName: string,
    ruleType: string,
    originalMessage: string,
    adhdState: ADHDState
  ): string => {
    const fieldDisplayName = fieldName.replace(/([A-Z])/g, ' $1').toLowerCase();

    const friendlyMessages = {
      required: `We need the ${fieldDisplayName} to continue.`,
      minLength: `The ${fieldDisplayName} needs to be a bit longer.`,
      maxLength: `The ${fieldDisplayName} is too long. Try keeping it shorter.`,
      email: `The email format doesn't look quite right.`,
      pattern: `The ${fieldDisplayName} format isn't what we expected.`
    };

    let message = friendlyMessages[ruleType] || originalMessage;

    // Add encouraging note for high cognitive load
    if (adhdState.cognitive_load > 0.7) {
      message += " Take your time - you've got this!";
    }

    return message;
  };

  return { validateField };
};
```

## Security Considerations

### 1. Voice Data Protection

```typescript
class VoiceDataSecurity {
  private encryptionKey: string;

  constructor() {
    this.encryptionKey = process.env.VOICE_ENCRYPTION_KEY || '';
  }

  async encryptVoiceData(audioData: ArrayBuffer, userId: string): Promise<ArrayBuffer> {
    // Client-side encryption before upload
    const key = await this.deriveUserKey(userId);
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv: crypto.getRandomValues(new Uint8Array(12)) },
      key,
      audioData
    );

    return encrypted;
  }

  async validateAudioUpload(file: File): Promise<AudioValidationResult> {
    // Validate file type
    const allowedTypes = ['audio/wav', 'audio/mp3', 'audio/flac', 'audio/m4a'];
    if (!allowedTypes.includes(file.type)) {
      throw new Error('Audio format not supported. Please use WAV, MP3, FLAC, or M4A.');
    }

    // Validate file size (50MB limit)
    if (file.size > 50 * 1024 * 1024) {
      throw new Error('Audio file is too large. Please keep recordings under 50MB.');
    }

    // Basic audio validation
    const audioContext = new AudioContext();
    const arrayBuffer = await file.arrayBuffer();

    try {
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

      return {
        valid: true,
        duration: audioBuffer.duration,
        sampleRate: audioBuffer.sampleRate,
        channels: audioBuffer.numberOfChannels
      };
    } catch (error) {
      throw new Error('Audio file appears to be corrupted. Please try recording again.');
    }
  }

  private async deriveUserKey(userId: string): Promise<CryptoKey> {
    const encoder = new TextEncoder();
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      encoder.encode(this.encryptionKey + userId),
      { name: 'PBKDF2' },
      false,
      ['deriveKey']
    );

    return crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: encoder.encode(userId),
        iterations: 100000,
        hash: 'SHA-256'
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    );
  }
}
```

### 2. Content Security Policy

```typescript
// CSP configuration for voice components
const voiceCSPConfig = {
  'default-src': ["'self'"],
  'script-src': ["'self'", "'unsafe-inline'", 'https://cdn.chronos.app'],
  'connect-src': [
    "'self'",
    'https://api.chronos.app',
    'wss://api.chronos.app',
    'https://cdn.chronos.app'
  ],
  'media-src': ["'self'", 'blob:', 'https://cdn.chronos.app'],
  'worker-src': ["'self'", 'blob:'],
  'frame-src': ["'none'"],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"]
};
```

## Performance Monitoring

### 1. Component Performance Tracking

```typescript
const usePerformanceMonitoring = () => {
  const { adhdState } = useADHDState();

  const trackComponentRender = useCallback((componentName: string, renderTime: number) => {
    // Track render performance
    if (renderTime > 100) { // Slow render threshold
      console.warn(`Slow render detected: ${componentName} took ${renderTime}ms`);

      // Send performance data to analytics
      analytics.track('component_performance', {
        component: componentName,
        render_time: renderTime,
        cognitive_load: adhdState.cognitive_load,
        energy_level: adhdState.energy_level,
        timestamp: Date.now()
      });
    }
  }, [adhdState]);

  const trackUserInteraction = useCallback((
    action: string,
    component: string,
    responseTime: number
  ) => {
    analytics.track('user_interaction', {
      action,
      component,
      response_time: responseTime,
      adhd_context: adhdState,
      timestamp: Date.now()
    });
  }, [adhdState]);

  return { trackComponentRender, trackUserInteraction };
};
```

## Conclusion

This frontend components PRD provides a comprehensive blueprint for implementing ADHD-optimized voice interface components within the Chronos React application. The components are designed with cognitive load awareness, accessibility, and seamless integration with existing Chronos features.

Key highlights include:
- **ADHD-Responsive Design**: Components that adapt to cognitive load and energy levels
- **Comprehensive Accessibility**: Full keyboard navigation and screen reader support
- **Performance Optimization**: Audio caching and lazy loading strategies
- **Seamless Integration**: Natural integration with existing Chronos components
- **Extensive Testing**: Component and accessibility testing strategies
- **ADHD-Friendly Error Handling**: Supportive error states and recovery actions
- **Security-First Design**: Client-side encryption and validation
- **Performance Monitoring**: Real-time tracking of component performance

The implementation follows React best practices while prioritizing ADHD user needs and maintaining consistency with the existing Chronos design system. The phased approach ensures manageable development while delivering value early in the process.
