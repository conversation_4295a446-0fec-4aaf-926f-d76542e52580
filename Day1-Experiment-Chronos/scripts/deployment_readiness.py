#!/usr/bin/env python3
"""
Deployment readiness checker for Project Chronos.

This script performs comprehensive checks to ensure the ADHD-focused
productivity platform is ready for production deployment.
"""

import asyncio
import subprocess
import sys
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
import argparse
from dataclasses import dataclass
from enum import Enum


class CheckCategory(Enum):
    """Categories of deployment readiness checks."""
    CODE_QUALITY = "code_quality"
    SECURITY = "security"
    TESTING = "testing"
    ADHD_FEATURES = "adhd_features"
    PERFORMANCE = "performance"
    DOCUMENTATION = "documentation"
    INFRASTRUCTURE = "infrastructure"


@dataclass
class CheckResult:
    """Result of a deployment readiness check."""
    category: CheckCategory
    name: str
    success: bool
    score: float  # 0-100
    message: str
    details: List[str] = None
    recommendations: List[str] = None


class DeploymentReadinessChecker:
    """Comprehensive deployment readiness checker for ADHD-focused platform."""
    
    def __init__(self, project_root: Path, verbose: bool = False):
        self.project_root = project_root
        self.verbose = verbose
        self.results: List[CheckResult] = []
        self.start_time = time.time()
    
    def log(self, message: str, level: str = "INFO"):
        """Log with deployment-focused formatting."""
        timestamp = time.strftime("%H:%M:%S")
        
        icons = {
            "INFO": "ℹ️",
            "SUCCESS": "✅",
            "WARNING": "⚠️",
            "ERROR": "❌",
            "CHECKING": "🔍"
        }
        
        icon = icons.get(level, "📝")
        print(f"[{timestamp}] {icon} {message}")
    
    def run_command(self, command: List[str], timeout: int = 300) -> tuple[bool, str]:
        """Run command and return success status and output."""
        try:
            result = subprocess.run(
                command,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result.returncode == 0, result.stdout + result.stderr
        except subprocess.TimeoutExpired:
            return False, f"Command timed out after {timeout}s"
        except Exception as e:
            return False, f"Command failed: {e}"
    
    def check_code_quality(self) -> List[CheckResult]:
        """Check code quality standards."""
        self.log("Checking code quality standards...", "CHECKING")
        results = []
        
        # Black formatting
        success, output = self.run_command(["poetry", "run", "black", "--check", "."])
        results.append(CheckResult(
            category=CheckCategory.CODE_QUALITY,
            name="Code Formatting (Black)",
            success=success,
            score=100 if success else 0,
            message="Code formatting is consistent" if success else "Code formatting issues found",
            details=[output] if not success else None,
            recommendations=["Run 'poetry run black .' to fix formatting"] if not success else None
        ))
        
        # Linting
        success, output = self.run_command(["poetry", "run", "flake8", "app/", "tests/"])
        results.append(CheckResult(
            category=CheckCategory.CODE_QUALITY,
            name="Code Linting (Flake8)",
            success=success,
            score=100 if success else 0,
            message="No linting issues found" if success else "Linting issues detected",
            details=[output] if not success else None,
            recommendations=["Fix linting issues before deployment"] if not success else None
        ))
        
        # Type checking
        success, output = self.run_command(["poetry", "run", "mypy", "app/"])
        results.append(CheckResult(
            category=CheckCategory.CODE_QUALITY,
            name="Type Checking (MyPy)",
            success=success,
            score=100 if success else 70,  # Type issues are warnings, not blockers
            message="Type checking passed" if success else "Type checking issues found",
            details=[output] if not success else None,
            recommendations=["Address type checking issues for better maintainability"] if not success else None
        ))
        
        return results
    
    def check_security(self) -> List[CheckResult]:
        """Check security standards."""
        self.log("Checking security standards...", "CHECKING")
        results = []
        
        # Security scanning with Bandit
        success, output = self.run_command(["poetry", "run", "bandit", "-r", "app/", "-f", "json"])
        
        # Parse bandit results
        high_severity_issues = 0
        medium_severity_issues = 0
        
        if not success and output:
            try:
                bandit_data = json.loads(output)
                for issue in bandit_data.get("results", []):
                    severity = issue.get("issue_severity", "").upper()
                    if severity == "HIGH":
                        high_severity_issues += 1
                    elif severity == "MEDIUM":
                        medium_severity_issues += 1
            except json.JSONDecodeError:
                pass
        
        # Score based on severity
        if high_severity_issues == 0 and medium_severity_issues == 0:
            score = 100
            message = "No security issues found"
        elif high_severity_issues == 0:
            score = 80
            message = f"{medium_severity_issues} medium severity issues found"
        else:
            score = 0
            message = f"{high_severity_issues} high severity issues found"
        
        results.append(CheckResult(
            category=CheckCategory.SECURITY,
            name="Security Scanning (Bandit)",
            success=high_severity_issues == 0,
            score=score,
            message=message,
            details=[f"High: {high_severity_issues}, Medium: {medium_severity_issues}"],
            recommendations=["Address high severity security issues before deployment"] if high_severity_issues > 0 else None
        ))
        
        # Dependency vulnerabilities
        success, output = self.run_command(["poetry", "run", "safety", "check", "--json"])
        
        vulnerability_count = 0
        if not success and output:
            try:
                safety_data = json.loads(output)
                vulnerability_count = len(safety_data)
            except json.JSONDecodeError:
                pass
        
        results.append(CheckResult(
            category=CheckCategory.SECURITY,
            name="Dependency Vulnerabilities (Safety)",
            success=vulnerability_count == 0,
            score=100 if vulnerability_count == 0 else max(0, 100 - vulnerability_count * 20),
            message=f"No dependency vulnerabilities found" if vulnerability_count == 0 else f"{vulnerability_count} vulnerabilities found",
            recommendations=["Update vulnerable dependencies"] if vulnerability_count > 0 else None
        ))
        
        return results
    
    def check_testing(self) -> List[CheckResult]:
        """Check testing coverage and quality."""
        self.log("Checking test coverage and quality...", "CHECKING")
        results = []
        
        # Unit test coverage
        success, output = self.run_command([
            "poetry", "run", "pytest", "tests/unit/", "--cov=app", 
            "--cov-report=term", "--cov-fail-under=90", "-q"
        ])
        
        # Extract coverage percentage
        coverage = 0
        for line in output.split('\n'):
            if 'TOTAL' in line and '%' in line:
                try:
                    coverage = int(line.split()[-1].replace('%', ''))
                    break
                except (ValueError, IndexError):
                    pass
        
        results.append(CheckResult(
            category=CheckCategory.TESTING,
            name="Unit Test Coverage",
            success=coverage >= 90,
            score=coverage,
            message=f"Test coverage: {coverage}%",
            recommendations=["Increase test coverage to at least 90%"] if coverage < 90 else None
        ))
        
        # ADHD-specific tests
        success, output = self.run_command([
            "poetry", "run", "pytest", "tests/", "-m", "adhd_feature", "-q"
        ])
        
        results.append(CheckResult(
            category=CheckCategory.TESTING,
            name="ADHD Feature Tests",
            success=success,
            score=100 if success else 0,
            message="ADHD feature tests passed" if success else "ADHD feature tests failed",
            recommendations=["Fix ADHD feature test failures"] if not success else None
        ))
        
        return results
    
    def check_adhd_features(self) -> List[CheckResult]:
        """Check ADHD-specific feature completeness."""
        self.log("Checking ADHD feature completeness...", "CHECKING")
        results = []
        
        # Check for required ADHD models and services
        required_files = [
            "app/models/user.py",
            "app/models/task.py",
            "app/models/time_block.py",
            "app/models/focus_session.py",
            "app/models/integration.py",
            "app/services/ai_service.py",
            "app/services/task_service.py",
            "app/services/time_block_service.py",
            "app/services/focus_session_service.py",
            "app/services/integration_service.py"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                missing_files.append(file_path)
        
        completeness_score = ((len(required_files) - len(missing_files)) / len(required_files)) * 100
        
        results.append(CheckResult(
            category=CheckCategory.ADHD_FEATURES,
            name="Core ADHD Components",
            success=len(missing_files) == 0,
            score=completeness_score,
            message=f"ADHD components: {completeness_score:.0f}% complete",
            details=[f"Missing: {', '.join(missing_files)}"] if missing_files else None,
            recommendations=["Implement missing ADHD components"] if missing_files else None
        ))
        
        # Check for ADHD-specific fields in models
        adhd_features_found = 0
        total_adhd_features = 5
        
        user_model = self.project_root / "app/models/user.py"
        if user_model.exists():
            content = user_model.read_text()
            if "adhd_diagnosed" in content:
                adhd_features_found += 1
            if "preferences" in content:
                adhd_features_found += 1
        
        task_model = self.project_root / "app/models/task.py"
        if task_model.exists():
            content = task_model.read_text()
            if "energy_level" in content:
                adhd_features_found += 1
            if "complexity" in content:
                adhd_features_found += 1
        
        time_block_model = self.project_root / "app/models/time_block.py"
        if time_block_model.exists():
            content = time_block_model.read_text()
            if "buffer_before" in content and "buffer_after" in content:
                adhd_features_found += 1
        
        feature_score = (adhd_features_found / total_adhd_features) * 100
        
        results.append(CheckResult(
            category=CheckCategory.ADHD_FEATURES,
            name="ADHD Model Features",
            success=adhd_features_found >= 4,
            score=feature_score,
            message=f"ADHD model features: {feature_score:.0f}% implemented",
            recommendations=["Implement missing ADHD-specific model fields"] if adhd_features_found < 4 else None
        ))
        
        return results
    
    def check_documentation(self) -> List[CheckResult]:
        """Check documentation completeness."""
        self.log("Checking documentation completeness...", "CHECKING")
        results = []
        
        # Check for key documentation files
        doc_files = [
            "README.md",
            "docs/index.rst",
            "docs/features/integrations.rst",
            "docs/api/integrations.rst",
            "docs/development/integration-development.rst"
        ]
        
        missing_docs = []
        for doc_file in doc_files:
            if not (self.project_root / doc_file).exists():
                missing_docs.append(doc_file)
        
        doc_score = ((len(doc_files) - len(missing_docs)) / len(doc_files)) * 100
        
        results.append(CheckResult(
            category=CheckCategory.DOCUMENTATION,
            name="Documentation Completeness",
            success=len(missing_docs) == 0,
            score=doc_score,
            message=f"Documentation: {doc_score:.0f}% complete",
            details=[f"Missing: {', '.join(missing_docs)}"] if missing_docs else None,
            recommendations=["Complete missing documentation"] if missing_docs else None
        ))
        
        return results
    
    def check_infrastructure(self) -> List[CheckResult]:
        """Check infrastructure readiness."""
        self.log("Checking infrastructure readiness...", "CHECKING")
        results = []
        
        # Check for required configuration files
        config_files = [
            "pyproject.toml",
            ".github/workflows/comprehensive-ci.yml",
            "docker-compose.yml",
            "Dockerfile"
        ]
        
        missing_configs = []
        for config_file in config_files:
            if not (self.project_root / config_file).exists():
                missing_configs.append(config_file)
        
        config_score = ((len(config_files) - len(missing_configs)) / len(config_files)) * 100
        
        results.append(CheckResult(
            category=CheckCategory.INFRASTRUCTURE,
            name="Infrastructure Configuration",
            success=len(missing_configs) <= 1,  # Allow one missing file
            score=config_score,
            message=f"Infrastructure config: {config_score:.0f}% complete",
            details=[f"Missing: {', '.join(missing_configs)}"] if missing_configs else None,
            recommendations=["Complete infrastructure configuration"] if len(missing_configs) > 1 else None
        ))
        
        return results
    
    def run_all_checks(self) -> Dict[str, Any]:
        """Run all deployment readiness checks."""
        self.log("🚀 Starting deployment readiness assessment for Project Chronos", "INFO")
        self.log("🧠 Ensuring ADHD-focused platform is production-ready!", "INFO")
        
        # Run all check categories
        check_functions = [
            self.check_code_quality,
            self.check_security,
            self.check_testing,
            self.check_adhd_features,
            self.check_documentation,
            self.check_infrastructure
        ]
        
        for check_func in check_functions:
            try:
                category_results = check_func()
                self.results.extend(category_results)
            except Exception as e:
                self.log(f"Error in {check_func.__name__}: {e}", "ERROR")
        
        return self.generate_report()
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive deployment readiness report."""
        total_duration = time.time() - self.start_time
        
        # Calculate scores by category
        category_scores = {}
        for category in CheckCategory:
            category_results = [r for r in self.results if r.category == category]
            if category_results:
                avg_score = sum(r.score for r in category_results) / len(category_results)
                category_scores[category.value] = {
                    "score": avg_score,
                    "checks": len(category_results),
                    "passed": sum(1 for r in category_results if r.success),
                    "failed": sum(1 for r in category_results if not r.success)
                }
        
        # Calculate overall readiness
        overall_score = sum(scores["score"] for scores in category_scores.values()) / len(category_scores)
        
        # Determine readiness level
        if overall_score >= 95:
            readiness_level = "PRODUCTION_READY"
            readiness_message = "🌟 Excellent! Ready for production deployment"
        elif overall_score >= 85:
            readiness_level = "MOSTLY_READY"
            readiness_message = "✅ Good! Minor improvements recommended"
        elif overall_score >= 70:
            readiness_level = "NEEDS_WORK"
            readiness_message = "⚠️ Needs work before deployment"
        else:
            readiness_level = "NOT_READY"
            readiness_message = "❌ Significant issues must be addressed"
        
        return {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "duration_seconds": total_duration,
            "readiness_level": readiness_level,
            "readiness_message": readiness_message,
            "overall_score": overall_score,
            "category_scores": category_scores,
            "total_checks": len(self.results),
            "passed_checks": sum(1 for r in self.results if r.success),
            "failed_checks": sum(1 for r in self.results if not r.success),
            "critical_issues": [
                {
                    "category": r.category.value,
                    "name": r.name,
                    "message": r.message,
                    "recommendations": r.recommendations
                }
                for r in self.results if not r.success and r.score < 50
            ],
            "recommendations": [
                rec for r in self.results if r.recommendations
                for rec in r.recommendations
            ]
        }
    
    def print_report(self, report: Dict[str, Any]):
        """Print deployment readiness report."""
        print("\n" + "="*80)
        print("🚀 PROJECT CHRONOS DEPLOYMENT READINESS REPORT")
        print("="*80)
        
        print(f"\n{report['readiness_message']}")
        print(f"📊 Overall Score: {report['overall_score']:.1f}/100")
        print(f"⏱️ Assessment Duration: {report['duration_seconds']:.1f} seconds")
        print(f"✅ Passed Checks: {report['passed_checks']}/{report['total_checks']}")
        
        print("\n📋 Category Breakdown:")
        for category, scores in report["category_scores"].items():
            score = scores["score"]
            status = "✅" if score >= 80 else "⚠️" if score >= 60 else "❌"
            print(f"  {status} {category.replace('_', ' ').title()}: {score:.1f}/100 "
                  f"({scores['passed']}/{scores['checks']} passed)")
        
        if report["critical_issues"]:
            print("\n🚨 Critical Issues:")
            for issue in report["critical_issues"]:
                print(f"  ❌ {issue['category']}: {issue['name']}")
                print(f"     {issue['message']}")
                if issue["recommendations"]:
                    for rec in issue["recommendations"]:
                        print(f"     → {rec}")
        
        if report["recommendations"]:
            print("\n💡 Recommendations:")
            for i, rec in enumerate(set(report["recommendations"]), 1):
                print(f"  {i}. {rec}")
        
        print("\n" + "="*80)
        
        if report["readiness_level"] == "PRODUCTION_READY":
            print("🎉 Project Chronos is ready to help ADHD users be more productive!")
        elif report["readiness_level"] == "MOSTLY_READY":
            print("👍 Almost there! Address the recommendations for optimal deployment.")
        else:
            print("🔧 Keep working! Each improvement makes the platform better for ADHD users.")
        
        print("="*80)


def main():
    """Main deployment readiness checker."""
    parser = argparse.ArgumentParser(
        description="Check deployment readiness for Project Chronos"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--report", "-r",
        type=str,
        help="Save detailed report to JSON file"
    )
    
    args = parser.parse_args()
    
    project_root = Path(__file__).parent.parent
    checker = DeploymentReadinessChecker(project_root, verbose=args.verbose)
    
    try:
        report = checker.run_all_checks()
        checker.print_report(report)
        
        if args.report:
            with open(args.report, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"\n📄 Detailed report saved to: {args.report}")
        
        # Exit with appropriate code
        if report["readiness_level"] in ["PRODUCTION_READY", "MOSTLY_READY"]:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ Deployment readiness check interrupted")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Deployment readiness check failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
