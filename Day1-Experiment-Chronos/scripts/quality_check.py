#!/usr/bin/env python3
"""
Comprehensive quality checking script for Project Chronos.

This script runs all quality checks including code formatting, linting,
type checking, security scanning, and ADHD-specific feature validation.
"""

import subprocess
import sys
import json
import os
from pathlib import Path
from typing import Dict, List, Tuple, Any
import argparse


class QualityChecker:
    """
    Comprehensive quality checker for ADHD-focused codebase.
    
    Runs multiple quality checks and provides detailed reporting
    with special attention to ADHD-specific code patterns.
    """
    
    def __init__(self, project_root: Path):
        """Initialize quality checker."""
        self.project_root = project_root
        self.results: Dict[str, Any] = {}
        self.errors: List[str] = []
        self.warnings: List[str] = []
    
    def run_command(self, command: List[str], check_name: str) -> Tuple[bool, str]:
        """
        Run a command and capture output.
        
        Args:
            command: Command to run
            check_name: Name of the check for reporting
            
        Returns:
            Tuple of (success, output)
        """
        try:
            result = subprocess.run(
                command,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            success = result.returncode == 0
            output = result.stdout + result.stderr
            
            self.results[check_name] = {
                "success": success,
                "output": output,
                "return_code": result.returncode
            }
            
            if not success:
                self.errors.append(f"{check_name} failed with code {result.returncode}")
            
            return success, output
            
        except subprocess.TimeoutExpired:
            error_msg = f"{check_name} timed out after 5 minutes"
            self.errors.append(error_msg)
            self.results[check_name] = {
                "success": False,
                "output": error_msg,
                "return_code": -1
            }
            return False, error_msg
        
        except Exception as e:
            error_msg = f"{check_name} failed with exception: {str(e)}"
            self.errors.append(error_msg)
            self.results[check_name] = {
                "success": False,
                "output": error_msg,
                "return_code": -1
            }
            return False, error_msg
    
    def check_code_formatting(self) -> bool:
        """Check code formatting with Black."""
        print("🔍 Checking code formatting with Black...")
        
        success, output = self.run_command(
            ["poetry", "run", "black", "--check", "--diff", "."],
            "black_formatting"
        )
        
        if success:
            print("✅ Code formatting is correct")
        else:
            print("❌ Code formatting issues found")
            print(output)
        
        return success
    
    def check_import_sorting(self) -> bool:
        """Check import sorting with isort."""
        print("🔍 Checking import sorting with isort...")
        
        success, output = self.run_command(
            ["poetry", "run", "isort", "--check-only", "--diff", "."],
            "isort_imports"
        )
        
        if success:
            print("✅ Import sorting is correct")
        else:
            print("❌ Import sorting issues found")
            print(output)
        
        return success
    
    def check_linting(self) -> bool:
        """Check code linting with Flake8."""
        print("🔍 Running linting checks with Flake8...")
        
        success, output = self.run_command(
            ["poetry", "run", "flake8", "app/", "tests/"],
            "flake8_linting"
        )
        
        if success:
            print("✅ No linting issues found")
        else:
            print("❌ Linting issues found")
            print(output)
        
        return success
    
    def check_type_hints(self) -> bool:
        """Check type hints with MyPy."""
        print("🔍 Checking type hints with MyPy...")
        
        success, output = self.run_command(
            ["poetry", "run", "mypy", "app/"],
            "mypy_types"
        )
        
        if success:
            print("✅ Type checking passed")
        else:
            print("❌ Type checking issues found")
            print(output)
        
        return success
    
    def check_docstrings(self) -> bool:
        """Check docstring quality with pydocstyle."""
        print("🔍 Checking docstring quality with pydocstyle...")
        
        success, output = self.run_command(
            ["poetry", "run", "pydocstyle", "app/"],
            "pydocstyle_docs"
        )
        
        if success:
            print("✅ Docstring quality is good")
        else:
            print("❌ Docstring issues found")
            print(output)
        
        return success
    
    def check_security(self) -> bool:
        """Check security with Bandit."""
        print("🔍 Running security checks with Bandit...")
        
        success, output = self.run_command(
            ["poetry", "run", "bandit", "-r", "app/", "-f", "json"],
            "bandit_security"
        )
        
        if success:
            print("✅ No security issues found")
        else:
            print("❌ Security issues found")
            # Parse JSON output for better reporting
            try:
                bandit_results = json.loads(output)
                if bandit_results.get("results"):
                    for issue in bandit_results["results"]:
                        severity = issue.get("issue_severity", "UNKNOWN")
                        confidence = issue.get("issue_confidence", "UNKNOWN")
                        test_name = issue.get("test_name", "Unknown test")
                        filename = issue.get("filename", "Unknown file")
                        line_number = issue.get("line_number", "Unknown line")
                        
                        print(f"  {severity}/{confidence}: {test_name} in {filename}:{line_number}")
            except json.JSONDecodeError:
                print(output)
        
        return success
    
    def check_dependencies(self) -> bool:
        """Check dependency vulnerabilities with Safety."""
        print("🔍 Checking dependency vulnerabilities with Safety...")
        
        success, output = self.run_command(
            ["poetry", "run", "safety", "check", "--json"],
            "safety_dependencies"
        )
        
        if success:
            print("✅ No dependency vulnerabilities found")
        else:
            print("❌ Dependency vulnerabilities found")
            # Parse JSON output for better reporting
            try:
                safety_results = json.loads(output)
                for vuln in safety_results:
                    package = vuln.get("package", "Unknown package")
                    installed = vuln.get("installed_version", "Unknown version")
                    vulnerability = vuln.get("vulnerability", "Unknown vulnerability")
                    
                    print(f"  {package} {installed}: {vulnerability}")
            except json.JSONDecodeError:
                print(output)
        
        return success
    
    def check_adhd_specific_patterns(self) -> bool:
        """Check ADHD-specific code patterns and requirements."""
        print("🔍 Checking ADHD-specific code patterns...")

        adhd_checks = []

        # Check for ADHD-specific model fields
        user_model_path = self.project_root / "app" / "models" / "user.py"
        if user_model_path.exists():
            with open(user_model_path, 'r') as f:
                user_content = f.read()
                if "adhd_diagnosed" in user_content:
                    adhd_checks.append("✅ User model has ADHD diagnosis field")
                else:
                    adhd_checks.append("❌ User model missing ADHD diagnosis field")

                if "preferences" in user_content:
                    adhd_checks.append("✅ User model has preferences field")
                else:
                    adhd_checks.append("❌ User model missing preferences field")

        # Check for ADHD-specific task fields
        task_model_path = self.project_root / "app" / "models" / "task.py"
        if task_model_path.exists():
            with open(task_model_path, 'r') as f:
                task_content = f.read()
                if "energy_level" in task_content:
                    adhd_checks.append("✅ Task model has energy_level field")
                else:
                    adhd_checks.append("❌ Task model missing energy_level field")

                if "context_tags" in task_content:
                    adhd_checks.append("✅ Task model has context_tags field")
                else:
                    adhd_checks.append("❌ Task model missing context_tags field")

        # Check for AI chunking service
        ai_service_path = self.project_root / "app" / "services" / "ai_service.py"
        if ai_service_path.exists():
            adhd_checks.append("✅ AI chunking service exists")
        else:
            adhd_checks.append("❌ AI chunking service missing")

        # Check for ADHD-specific tests
        adhd_test_path = self.project_root / "tests" / "integration" / "test_adhd_features"
        if adhd_test_path.exists():
            adhd_checks.append("✅ ADHD-specific integration tests exist")
        else:
            adhd_checks.append("❌ ADHD-specific integration tests missing")

        # Check for focus session models
        focus_model_path = self.project_root / "app" / "models" / "focus_session.py"
        if focus_model_path.exists():
            adhd_checks.append("✅ Focus session model exists")
        else:
            adhd_checks.append("❌ Focus session model missing")

        # Check for time block models with ADHD features
        time_block_path = self.project_root / "app" / "models" / "time_block.py"
        if time_block_path.exists():
            with open(time_block_path, 'r') as f:
                content = f.read()
                if "buffer_before" in content and "buffer_after" in content:
                    adhd_checks.append("✅ Time block model has buffer time fields")
                else:
                    adhd_checks.append("❌ Time block model missing buffer time fields")

        # Check for integration models
        integration_path = self.project_root / "app" / "models" / "integration.py"
        if integration_path.exists():
            adhd_checks.append("✅ Integration model exists")
        else:
            adhd_checks.append("❌ Integration model missing")

        # Check for comprehensive test coverage
        test_files = [
            "tests/unit/test_adhd_features.py",
            "tests/integration/test_external_integrations.py",
            "tests/performance/test_adhd_performance.py",
            "tests/accessibility/test_adhd_accessibility.py",
            "tests/e2e/test_adhd_user_workflows.py"
        ]

        for test_file in test_files:
            if (self.project_root / test_file).exists():
                adhd_checks.append(f"✅ {test_file.split('/')[-1]} exists")
            else:
                adhd_checks.append(f"❌ {test_file.split('/')[-1]} missing")

        # Report results
        for check in adhd_checks:
            print(f"  {check}")

        # Consider it successful if most checks pass
        success_count = sum(1 for check in adhd_checks if check.startswith("✅"))
        total_checks = len(adhd_checks)
        success_rate = success_count / total_checks if total_checks > 0 else 0

        success = success_rate >= 0.8  # 80% of checks must pass

        self.results["adhd_patterns"] = {
            "success": success,
            "checks": adhd_checks,
            "success_rate": success_rate
        }

        if success:
            print("✅ ADHD-specific patterns check passed")
        else:
            print("❌ ADHD-specific patterns check failed")

        return success

    def run_adhd_tests(self) -> bool:
        """Run ADHD-specific test suites."""
        print("🔍 Running ADHD-specific test suites...")

        success, output = self.run_command(
            ["poetry", "run", "pytest", "tests/", "-v", "-m", "adhd_feature", "--tb=short"],
            "adhd_tests"
        )

        if success:
            print("✅ ADHD feature tests passed")
        else:
            print("❌ ADHD feature tests failed")
            print(output)

        return success
    
    def run_all_checks(self, skip_slow: bool = False) -> bool:
        """
        Run all quality checks.
        
        Args:
            skip_slow: Skip slow checks like security scanning
            
        Returns:
            True if all checks pass
        """
        print("🚀 Starting comprehensive quality checks for Project Chronos...")
        print("=" * 60)
        
        checks = [
            ("Code Formatting", self.check_code_formatting),
            ("Import Sorting", self.check_import_sorting),
            ("Linting", self.check_linting),
            ("Type Hints", self.check_type_hints),
            ("Docstrings", self.check_docstrings),
            ("ADHD Patterns", self.check_adhd_specific_patterns),
        ]
        
        if not skip_slow:
            checks.extend([
                ("Security", self.check_security),
                ("Dependencies", self.check_dependencies),
            ])
        
        all_passed = True
        
        for check_name, check_func in checks:
            print(f"\n📋 {check_name}")
            print("-" * 40)
            
            try:
                passed = check_func()
                if not passed:
                    all_passed = False
            except Exception as e:
                print(f"❌ {check_name} failed with exception: {e}")
                all_passed = False
        
        print("\n" + "=" * 60)
        
        if all_passed:
            print("🎉 All quality checks passed!")
        else:
            print("💥 Some quality checks failed!")
            print("\nErrors:")
            for error in self.errors:
                print(f"  - {error}")
        
        return all_passed
    
    def generate_report(self, output_file: Path = None) -> Dict[str, Any]:
        """
        Generate detailed quality report.
        
        Args:
            output_file: Optional file to write report to
            
        Returns:
            Quality report dictionary
        """
        report = {
            "timestamp": subprocess.run(
                ["date", "-Iseconds"], 
                capture_output=True, 
                text=True
            ).stdout.strip(),
            "project_root": str(self.project_root),
            "results": self.results,
            "errors": self.errors,
            "warnings": self.warnings,
            "summary": {
                "total_checks": len(self.results),
                "passed_checks": sum(1 for r in self.results.values() if r.get("success", False)),
                "failed_checks": sum(1 for r in self.results.values() if not r.get("success", True)),
                "overall_success": len(self.errors) == 0
            }
        }
        
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"📄 Quality report written to {output_file}")
        
        return report


def main():
    """Main entry point for quality checking."""
    parser = argparse.ArgumentParser(
        description="Run comprehensive quality checks for Project Chronos"
    )
    parser.add_argument(
        "--skip-slow",
        action="store_true",
        help="Skip slow checks like security scanning"
    )
    parser.add_argument(
        "--report",
        type=Path,
        help="Output file for detailed quality report"
    )
    parser.add_argument(
        "--project-root",
        type=Path,
        default=Path.cwd(),
        help="Project root directory"
    )
    
    args = parser.parse_args()
    
    # Ensure we're in a Poetry project
    if not (args.project_root / "pyproject.toml").exists():
        print("❌ No pyproject.toml found. Please run from project root.")
        sys.exit(1)
    
    checker = QualityChecker(args.project_root)
    
    try:
        success = checker.run_all_checks(skip_slow=args.skip_slow)
        
        if args.report:
            checker.generate_report(args.report)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n🛑 Quality check interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"💥 Quality check failed with unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
