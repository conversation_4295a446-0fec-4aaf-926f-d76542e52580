#!/bin/bash

# Chronos Development Environment Startup Script
# This script starts all services for local development

set -e

echo "🚀 Starting Chronos Development Environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker compose is available
if ! docker compose version &> /dev/null; then
    echo "❌ docker compose is not available. Please install Docker Compose first."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
    echo "✅ .env file created. Please review and update the configuration if needed."
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p monitoring/grafana/dashboards
mkdir -p monitoring/grafana/provisioning/datasources
mkdir -p monitoring/grafana/provisioning/dashboards
mkdir -p database
mkdir -p logs

# Note about local domain names
echo "🌐 Local domain setup..."
echo "ℹ️  For best experience, add these entries to your /etc/hosts file:"
echo "   127.0.0.1 chronos.autism.localhost"
echo "   127.0.0.1 api.autism.localhost"
echo "   127.0.0.1 traefik.autism.localhost"
echo "   127.0.0.1 grafana.autism.localhost"
echo "   127.0.0.1 prometheus.autism.localhost"
echo "   127.0.0.1 mail.autism.localhost"
echo "   127.0.0.1 minio.autism.localhost"
echo "   (You can also access services via localhost with port numbers)"
echo ""

# Pull latest images
echo "📦 Pulling latest Docker images..."
docker compose pull

# Build and start services
echo "🏗️  Building and starting services..."
docker compose up --build -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check service health
echo "🔍 Checking service health..."

services=("postgres" "redis" "chronos-api")
for service in "${services[@]}"; do
    echo "Checking $service..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker compose ps $service | grep -q "healthy\|Up"; then
            echo "✅ $service is ready"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        echo "⚠️  $service is taking longer than expected to start"
    fi
done

# Show service URLs
echo ""
echo "🎉 Chronos Development Environment is ready!"
echo ""
echo "📱 Application URLs:"
echo "   • Chronos UI:      http://chronos.autism.localhost"
echo "   • Chronos API:     http://api.autism.localhost"
echo "   • API Docs:        http://localhost:8000/docs"
echo ""
echo "🛠️  Development Tools:"
echo "   • Traefik Dashboard: http://traefik.autism.localhost"
echo "   • Grafana:          http://grafana.autism.localhost (admin/admin)"
echo "   • Prometheus:       http://prometheus.autism.localhost"
echo "   • Mailhog:          http://mail.autism.localhost"
echo "   • MinIO Console:    http://minio.autism.localhost (chronos/chronos123)"
echo ""
echo "🗄️  Database Access:"
echo "   • PostgreSQL:       localhost:5432 (chronos/chronos_dev)"
echo "   • Redis:            localhost:6379"
echo ""
echo "📊 Demo Account:"
echo "   • Email:            <EMAIL>"
echo "   • Password:         demo123"
echo ""
echo "📝 Logs:"
echo "   • View all logs:    docker compose logs -f"
echo "   • View API logs:    docker compose logs -f chronos-api"
echo "   • View UI logs:     docker compose logs -f chronos-ui"
echo ""
echo "🛑 To stop all services: docker compose down"
echo "🔄 To restart services: docker compose restart"
echo "🧹 To clean up everything: docker compose down -v --remove-orphans"
echo ""

# Show running containers
echo "🐳 Running containers:"
docker compose ps

echo ""
echo "✨ Happy coding! The ADHD-optimized task management system is ready for development."
