#!/usr/bin/env bash

# NixOS AMD GPU Setup for Project Chronos
# Automated setup script for NixOS with AMD Radeon RX 7900 XT/XTX

set -e

echo "🔥 NixOS AMD GPU Setup for Project Chronos"
echo "==========================================="
echo ""

# Check if running on NixOS
if [ ! -f /etc/NIXOS ]; then
    echo "❌ This script is designed for NixOS only"
    echo "For other distributions, use ./setup_amd_gpu.sh"
    exit 1
fi

echo "✅ Running on NixOS"
echo ""

# Check current user
USER_NAME=$(whoami)
echo "Current user: $USER_NAME"

# Check if user has sudo access
if ! sudo -n true 2>/dev/null; then
    echo "❌ This script requires sudo access"
    echo "Please ensure your user is in the 'wheel' group"
    exit 1
fi

echo "✅ Sudo access confirmed"
echo ""

# Backup current configuration
echo "📋 Backing up current NixOS configuration..."
if [ -f /etc/nixos/configuration.nix ]; then
    sudo cp /etc/nixos/configuration.nix /etc/nixos/configuration.nix.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ Configuration backed up"
else
    echo "⚠️ No existing configuration found"
fi

# Check if AMD GPU configuration already exists
if grep -q "hardware.opengl" /etc/nixos/configuration.nix 2>/dev/null; then
    echo "⚠️ AMD GPU configuration may already exist"
    echo "Please review the configuration manually"
else
    echo "📝 Adding AMD GPU configuration..."
    
    # Create AMD GPU configuration snippet
    cat > /tmp/amd-gpu-config.nix << 'EOF'

  # AMD GPU Support for RX 7900 XT/XTX
  hardware.opengl = {
    enable = true;
    driSupport = true;
    driSupport32Bit = true;
    extraPackages = with pkgs; [
      rocm-opencl-icd
      rocm-opencl-runtime
      amdvlk
    ];
  };

  # Docker with GPU support
  virtualisation.docker = {
    enable = true;
    enableOnBoot = true;
  };

  # User groups for GPU access
  users.users.${USER_NAME} = {
    extraGroups = [ "docker" "video" "render" "audio" ];
  };

  # System packages for AMD GPU development
  environment.systemPackages = with pkgs; [
    rocminfo
    rocm-smi
    hip
    docker
    docker-compose
    git
    curl
    wget
  ];

  # Environment variables for AMD GPU
  environment.variables = {
    ROC_ENABLE_PRE_VEGA = "1";
    HSA_OVERRIDE_GFX_VERSION = "11.0.0";
    PYTORCH_ROCM_ARCH = "gfx1100;gfx1101;gfx1102";
  };

  # Kernel modules and parameters
  boot.kernelModules = [ "amdgpu" ];
  boot.kernelParams = [
    "amdgpu.si_support=1"
    "amdgpu.cik_support=1"
  ];

  # Udev rules for GPU access
  services.udev.extraRules = ''
    SUBSYSTEM=="drm", KERNEL=="card*", GROUP="video", MODE="0664"
    SUBSYSTEM=="drm", KERNEL=="renderD*", GROUP="render", MODE="0664"
    SUBSYSTEM=="kfd", KERNEL=="kfd", TAG+="uaccess", GROUP="render", MODE="0666"
  '';

  # Networking for development
  networking.firewall.allowedTCPPorts = [ 8090 8001 8002 8003 3000 9090 9000 ];

EOF

    echo "✅ AMD GPU configuration created"
fi

echo ""
echo "🔧 Configuration Options"
echo "======================="
echo ""
echo "Choose your setup method:"
echo "1. Manual configuration (recommended)"
echo "2. Automatic configuration (experimental)"
echo "3. Flake-based setup (modern)"
echo ""
read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        echo ""
        echo "📋 Manual Configuration Instructions"
        echo "===================================="
        echo ""
        echo "1. Edit your NixOS configuration:"
        echo "   sudo nano /etc/nixos/configuration.nix"
        echo ""
        echo "2. Add the AMD GPU configuration from:"
        echo "   cat nixos-amd-gpu-config.nix"
        echo ""
        echo "3. Rebuild your system:"
        echo "   sudo nixos-rebuild switch"
        echo ""
        echo "4. Reboot to activate all changes:"
        echo "   sudo reboot"
        echo ""
        ;;
    2)
        echo ""
        echo "⚠️ Automatic configuration is experimental"
        read -p "Continue? (y/N): " confirm
        if [[ $confirm =~ ^[Yy]$ ]]; then
            echo "🔧 Applying automatic configuration..."
            # This would need careful implementation
            echo "❌ Automatic configuration not yet implemented"
            echo "Please use manual configuration instead"
        fi
        ;;
    3)
        echo ""
        echo "🚀 Flake-based Setup"
        echo "==================="
        echo ""
        echo "1. Enable flakes in your configuration:"
        echo "   nix.settings.experimental-features = [ \"nix-command\" \"flakes\" ];"
        echo ""
        echo "2. Rebuild with flakes enabled:"
        echo "   sudo nixos-rebuild switch"
        echo ""
        echo "3. Use the development shell:"
        echo "   nix develop"
        echo ""
        echo "4. Import the NixOS module in your configuration:"
        echo "   imports = [ ./flake.nix#nixosModules.amd-gpu ];"
        echo ""
        ;;
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac

echo ""
echo "🧪 Testing Commands"
echo "=================="
echo ""
echo "After configuration and reboot, test with:"
echo ""
echo "# Check AMD GPU detection"
echo "rocm-smi --showproductname"
echo ""
echo "# Check GPU devices"
echo "ls -la /dev/kfd /dev/dri"
echo ""
echo "# Test Docker GPU access"
echo "docker run --rm --device=/dev/kfd --device=/dev/dri ubuntu:22.04 ls -la /dev/kfd /dev/dri"
echo ""
echo "# Enter development environment"
echo "nix-shell  # or 'nix develop' for flakes"
echo ""

echo ""
echo "📚 Additional Resources"
echo "======================"
echo ""
echo "• NixOS AMD GPU Wiki: https://nixos.wiki/wiki/AMD_GPU"
echo "• ROCm on NixOS: https://nixos.wiki/wiki/ROCm"
echo "• Docker on NixOS: https://nixos.wiki/wiki/Docker"
echo ""

echo "🎯 Next Steps"
echo "============="
echo ""
echo "1. Configure NixOS with AMD GPU support"
echo "2. Reboot your system"
echo "3. Enter development environment: nix-shell"
echo "4. Build Project Chronos: docker build -t chronos-speechbot-rocm ."
echo "5. Test AMD GPU acceleration"
echo ""
echo "🔥 Your RX 7900 XT will provide incredible performance for voice synthesis!"
