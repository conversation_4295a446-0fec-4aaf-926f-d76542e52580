/* 
ADHD-Optimized Dark Mode Swagger UI Styles
Designed for cognitive accessibility and reduced visual overwhelm
*/

:root {
  /* ADHD-friendly color palette */
  --adhd-bg-primary: #1a1a1a;
  --adhd-bg-secondary: #2d2d2d;
  --adhd-bg-tertiary: #3a3a3a;
  --adhd-text-primary: #e8e8e8;
  --adhd-text-secondary: #b8b8b8;
  --adhd-accent-calm: #4a90e2;
  --adhd-accent-excited: #f5a623;
  --adhd-accent-focused: #7ed321;
  --adhd-accent-overwhelmed: #bd10e0;
  --adhd-accent-motivated: #ff6b6b;
  --adhd-border: #4a4a4a;
  --adhd-hover: #404040;
  --adhd-success: #27ae60;
  --adhd-warning: #f39c12;
  --adhd-error: #e74c3c;
}

/* Base dark theme overrides */
.swagger-ui {
  background-color: var(--adhd-bg-primary) !important;
  color: var(--adhd-text-primary) !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif !important;
  line-height: 1.6 !important;
}

/* Reduce visual noise */
.swagger-ui .topbar {
  background-color: var(--adhd-bg-secondary) !important;
  border-bottom: 2px solid var(--adhd-border) !important;
  padding: 1rem !important;
}

.swagger-ui .topbar .download-url-wrapper {
  display: none !important; /* Reduce clutter */
}

/* ADHD-friendly headers */
.swagger-ui h1, .swagger-ui h2, .swagger-ui h3, .swagger-ui h4, .swagger-ui h5 {
  color: var(--adhd-text-primary) !important;
  font-weight: 600 !important;
  margin-bottom: 1rem !important;
  margin-top: 2rem !important;
}

.swagger-ui .info .title {
  color: var(--adhd-accent-calm) !important;
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 0.5rem !important;
}

/* Clear section separation */
.swagger-ui .scheme-container {
  background-color: var(--adhd-bg-secondary) !important;
  border: 2px solid var(--adhd-border) !important;
  border-radius: 8px !important;
  padding: 1.5rem !important;
  margin-bottom: 2rem !important;
}

/* Operation blocks with ADHD mode colors */
.swagger-ui .opblock {
  background-color: var(--adhd-bg-secondary) !important;
  border: 2px solid var(--adhd-border) !important;
  border-radius: 8px !important;
  margin-bottom: 1.5rem !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.swagger-ui .opblock.opblock-get {
  border-left: 6px solid var(--adhd-accent-calm) !important;
}

.swagger-ui .opblock.opblock-post {
  border-left: 6px solid var(--adhd-accent-excited) !important;
}

.swagger-ui .opblock.opblock-put {
  border-left: 6px solid var(--adhd-accent-focused) !important;
}

.swagger-ui .opblock.opblock-delete {
  border-left: 6px solid var(--adhd-error) !important;
}

/* Clear operation headers */
.swagger-ui .opblock .opblock-summary {
  background-color: var(--adhd-bg-tertiary) !important;
  border-bottom: 1px solid var(--adhd-border) !important;
  padding: 1rem 1.5rem !important;
}

.swagger-ui .opblock .opblock-summary-method {
  font-weight: 700 !important;
  font-size: 0.9rem !important;
  padding: 0.5rem 1rem !important;
  border-radius: 4px !important;
  margin-right: 1rem !important;
}

.swagger-ui .opblock .opblock-summary-path {
  color: var(--adhd-text-primary) !important;
  font-family: 'JetBrains Mono', 'Fira Code', monospace !important;
  font-size: 1rem !important;
  font-weight: 500 !important;
}

/* Enhanced readability for descriptions */
.swagger-ui .opblock .opblock-description-wrapper p {
  color: var(--adhd-text-secondary) !important;
  font-size: 1rem !important;
  line-height: 1.7 !important;
  margin-bottom: 1rem !important;
}

/* ADHD-friendly form elements */
.swagger-ui .parameters-col_description input[type="text"],
.swagger-ui .parameters-col_description textarea,
.swagger-ui .parameters-col_description select {
  background-color: var(--adhd-bg-primary) !important;
  border: 2px solid var(--adhd-border) !important;
  border-radius: 6px !important;
  color: var(--adhd-text-primary) !important;
  font-size: 1rem !important;
  padding: 0.75rem !important;
  transition: border-color 0.2s ease !important;
}

.swagger-ui .parameters-col_description input[type="text"]:focus,
.swagger-ui .parameters-col_description textarea:focus,
.swagger-ui .parameters-col_description select:focus {
  border-color: var(--adhd-accent-calm) !important;
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2) !important;
}

/* Large, accessible buttons */
.swagger-ui .btn {
  background-color: var(--adhd-accent-calm) !important;
  border: none !important;
  border-radius: 6px !important;
  color: white !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  padding: 0.75rem 1.5rem !important;
  min-height: 44px !important; /* WCAG touch target size */
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.swagger-ui .btn:hover {
  background-color: #3a7bc8 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3) !important;
}

.swagger-ui .btn.execute {
  background-color: var(--adhd-accent-excited) !important;
}

.swagger-ui .btn.execute:hover {
  background-color: #e09612 !important;
}

/* Clear response sections */
.swagger-ui .responses-wrapper {
  background-color: var(--adhd-bg-secondary) !important;
  border: 1px solid var(--adhd-border) !important;
  border-radius: 6px !important;
  margin-top: 1rem !important;
  padding: 1rem !important;
}

.swagger-ui .response-col_status {
  color: var(--adhd-text-primary) !important;
  font-weight: 600 !important;
}

.swagger-ui .response-col_description {
  color: var(--adhd-text-secondary) !important;
}

/* Code blocks with better contrast */
.swagger-ui .highlight-code {
  background-color: var(--adhd-bg-primary) !important;
  border: 1px solid var(--adhd-border) !important;
  border-radius: 6px !important;
  padding: 1rem !important;
}

.swagger-ui .highlight-code pre {
  color: var(--adhd-text-primary) !important;
  font-family: 'JetBrains Mono', 'Fira Code', monospace !important;
  font-size: 0.9rem !important;
  line-height: 1.5 !important;
}

/* Model sections */
.swagger-ui .model-box {
  background-color: var(--adhd-bg-secondary) !important;
  border: 1px solid var(--adhd-border) !important;
  border-radius: 6px !important;
  padding: 1rem !important;
}

.swagger-ui .model .property {
  color: var(--adhd-text-primary) !important;
  padding: 0.5rem 0 !important;
  border-bottom: 1px solid var(--adhd-border) !important;
}

.swagger-ui .model .property:last-child {
  border-bottom: none !important;
}

/* ADHD-specific enhancements */
.swagger-ui .info .description {
  background-color: var(--adhd-bg-secondary) !important;
  border-left: 4px solid var(--adhd-accent-calm) !important;
  border-radius: 0 6px 6px 0 !important;
  padding: 1.5rem !important;
  margin: 2rem 0 !important;
}

.swagger-ui .info .description h1,
.swagger-ui .info .description h2,
.swagger-ui .info .description h3 {
  color: var(--adhd-accent-calm) !important;
  margin-top: 1.5rem !important;
}

.swagger-ui .info .description h1:first-child,
.swagger-ui .info .description h2:first-child,
.swagger-ui .info .description h3:first-child {
  margin-top: 0 !important;
}

/* Tag sections with clear separation */
.swagger-ui .opblock-tag {
  background-color: var(--adhd-bg-secondary) !important;
  border: 2px solid var(--adhd-border) !important;
  border-radius: 8px !important;
  margin-bottom: 2rem !important;
  padding: 1rem !important;
}

.swagger-ui .opblock-tag-section h3 {
  color: var(--adhd-accent-calm) !important;
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 1rem !important;
  padding-bottom: 0.5rem !important;
  border-bottom: 2px solid var(--adhd-border) !important;
}

/* Reduce motion for ADHD users */
@media (prefers-reduced-motion: reduce) {
  .swagger-ui * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --adhd-bg-primary: #000000;
    --adhd-bg-secondary: #1a1a1a;
    --adhd-text-primary: #ffffff;
    --adhd-border: #666666;
  }
}

/* Focus indicators for keyboard navigation */
.swagger-ui *:focus {
  outline: 3px solid var(--adhd-accent-calm) !important;
  outline-offset: 2px !important;
}

/* Loading states */
.swagger-ui .loading {
  background-color: var(--adhd-bg-secondary) !important;
  border: 2px solid var(--adhd-border) !important;
  border-radius: 6px !important;
  padding: 2rem !important;
  text-align: center !important;
  color: var(--adhd-text-secondary) !important;
}

/* Error states */
.swagger-ui .errors-wrapper {
  background-color: rgba(231, 76, 60, 0.1) !important;
  border: 2px solid var(--adhd-error) !important;
  border-radius: 6px !important;
  padding: 1rem !important;
  margin-top: 1rem !important;
}

.swagger-ui .errors-wrapper .error-wrapper {
  color: var(--adhd-error) !important;
  font-weight: 600 !important;
}

/* Success states */
.swagger-ui .response.highlighted {
  background-color: rgba(39, 174, 96, 0.1) !important;
  border: 2px solid var(--adhd-success) !important;
}

/* Scrollbar styling */
.swagger-ui ::-webkit-scrollbar {
  width: 12px !important;
  height: 12px !important;
}

.swagger-ui ::-webkit-scrollbar-track {
  background-color: var(--adhd-bg-secondary) !important;
  border-radius: 6px !important;
}

.swagger-ui ::-webkit-scrollbar-thumb {
  background-color: var(--adhd-border) !important;
  border-radius: 6px !important;
  border: 2px solid var(--adhd-bg-secondary) !important;
}

.swagger-ui ::-webkit-scrollbar-thumb:hover {
  background-color: var(--adhd-accent-calm) !important;
}
