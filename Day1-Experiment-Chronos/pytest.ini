[tool:pytest]
# Pytest configuration for Project Chronos ADHD-focused testing

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options for ADHD-developer-friendly testing
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --color=yes
    --disable-warnings
    --asyncio-mode=auto
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=80

# Test markers for ADHD-specific categorization
markers =
    # Core test types
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    behavior: Behavior-driven development tests using Gherkin
    performance: Performance and load tests
    api: API endpoint tests
    slow: Slow-running tests (excluded from quick runs)

    # ADHD-specific feature markers
    adhd_critical: Tests for ADHD-critical features that must work perfectly
    adhd_workflow: Tests for complete ADHD user workflows
    adhd_feature: Tests for ADHD-specific features

    # Specific ADHD feature areas
    hyperfocus: Tests for hyperfocus protection features
    energy_management: Tests for energy tracking and optimization
    task_chunking: Tests for AI task chunking functionality
    gentle_ui: Tests for gentle, supportive user interface elements
    time_blindness: Tests for time blindness support features
    dopamine_optimization: Tests for dopamine-friendly gamification
    executive_function: Tests for executive function support
    working_memory: Tests for working memory assistance
    attention_management: Tests for attention and focus management

    # User experience markers
    accessibility: Tests for accessibility and neurodivergent accommodations
    visual_design: Tests for ADHD-friendly visual design
    notification_system: Tests for gentle notification systems
    social_features: Tests for body doubling and accountability

    # Technical markers
    database: Tests requiring database operations
    external_api: Tests involving external API calls
    auth: Tests for authentication and authorization
    security: Tests for security features
    ai: Tests requiring AI services

    # Development workflow markers
    smoke: Smoke tests for basic functionality
    regression: Regression tests for bug prevention
    edge_case: Tests for edge cases and error conditions

# Async test configuration
asyncio_mode = auto
