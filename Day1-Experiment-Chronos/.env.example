# Project Chronos Environment Configuration

# Application Settings
DEBUG=true
TESTING=false
LOG_LEVEL=INFO

# Security
SECRET_KEY=your-secret-key-here-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=15
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database Configuration
POSTGRES_SERVER=localhost
POSTGRES_USER=chronos
POSTGRES_PASSWORD=chronos_dev
POSTGRES_DB=chronos
POSTGRES_PORT=5432
# DATABASE_URL=postgresql+asyncpg://chronos:chronos_dev@localhost:5432/chronos

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
# REDIS_PASSWORD=
# REDIS_URL=redis://localhost:6379/0

# CORS Origins (comma-separated)
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Email Configuration (optional)
# SMTP_TLS=true
# SMTP_PORT=587
# SMTP_HOST=smtp.gmail.com
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password
# EMAILS_FROM_EMAIL=<EMAIL>
# EMAILS_FROM_NAME=Project Chronos

# External APIs (optional)
# OPENAI_API_KEY=your-openai-api-key
# ANTHROPIC_API_KEY=your-anthropic-api-key

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# ADHD-Specific Settings
DEFAULT_FOCUS_DURATION=25
DEFAULT_BREAK_DURATION=5
MAX_TASK_CHUNK_SIZE=30
NOTIFICATION_PERSISTENCE_HOURS=24
