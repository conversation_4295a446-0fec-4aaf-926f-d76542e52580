"""Test configuration and fixtures for Project Chronos.

This module provides pytest fixtures and configuration for testing the
authentication system and other components.
"""

import asyncio
from typing import AsyncGenerator, Generator

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.pool import StaticPool

from app.core.config import settings
from app.core.database import get_db
from app.main import app
from app.models.base import Base

# Set testing mode
settings.TESTING = True

# Test database URL (in-memory SQLite for speed)
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def test_db() -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session.
    
    Yields:
        AsyncSession: Test database session
    """
    # Create test engine
    engine = create_async_engine(
        TEST_DATABASE_URL,
        echo=False,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
    )
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create session
    async with AsyncSession(engine) as session:
        yield session
    
    # Clean up
    await engine.dispose()


@pytest.fixture
def client(test_db: AsyncSession) -> TestClient:
    """Create a test client with database dependency override.
    
    Args:
        test_db: Test database session
        
    Returns:
        TestClient: FastAPI test client
    """
    async def override_get_db() -> AsyncSession:
        return test_db
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    # Clean up
    app.dependency_overrides.clear()


@pytest.fixture
def test_user_data() -> dict:
    """Test user registration data.
    
    Returns:
        dict: Test user data for registration
    """
    return {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "full_name": "Test User",
        "has_adhd_diagnosis": True,
    }


@pytest.fixture
def test_login_data() -> dict:
    """Test user login data.
    
    Returns:
        dict: Test user data for login
    """
    return {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "remember_me": False,
    }
