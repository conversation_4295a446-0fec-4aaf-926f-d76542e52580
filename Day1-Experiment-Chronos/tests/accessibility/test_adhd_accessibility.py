"""
Accessibility tests for ADHD users.

This module tests accessibility features specifically designed for ADHD users,
including visual accommodations, cognitive load reduction, and sensory considerations.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import patch

from app.services.task_service import TaskService
from app.services.time_block_service import TimeBlockService
from app.services.notification_service import NotificationService
from tests.factories import UserFactory, TaskFactory, TimeBlockFactory


@pytest.mark.accessibility
class TestVisualAccessibility:
    """Test visual accessibility features for ADHD users."""
    
    async def test_color_coding_consistency(self, db_session):
        """Test consistent color coding across the application."""
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "visual_preferences": {
                    "color_scheme": "high_contrast",
                    "energy_colors": {
                        "low": "#10B981",    # Green
                        "medium": "#F59E0B", # Amber
                        "high": "#EF4444"   # Red
                    }
                }
            }
        )
        
        # Create tasks with different energy levels
        tasks = []
        for energy in ["low", "medium", "high"]:
            task = await TaskFactory.create(
                db_session,
                user_id=user.id,
                energy_level=energy,
                title=f"Task with {energy} energy"
            )
            tasks.append(task)
        
        task_service = TaskService(db_session)
        
        # Get tasks with visual metadata
        task_list = await task_service.get_user_tasks(
            user_id=user.id,
            include_visual_metadata=True
        )
        
        # Verify consistent color coding
        for task in task_list:
            expected_color = user.preferences["visual_preferences"]["energy_colors"][task.energy_level]
            assert task.visual_metadata["color"] == expected_color
            assert task.visual_metadata["contrast_ratio"] >= 4.5  # WCAG AA compliance
    
    async def test_visual_hierarchy_clarity(self, db_session):
        """Test clear visual hierarchy for ADHD attention patterns."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        
        # Create tasks with different priorities
        high_priority_task = await TaskFactory.create(
            db_session,
            user_id=user.id,
            priority="high",
            title="Urgent important task"
        )
        
        medium_priority_task = await TaskFactory.create(
            db_session,
            user_id=user.id,
            priority="medium",
            title="Regular task"
        )
        
        low_priority_task = await TaskFactory.create(
            db_session,
            user_id=user.id,
            priority="low",
            title="Low priority task"
        )
        
        task_service = TaskService(db_session)
        
        # Get tasks with visual hierarchy
        tasks = await task_service.get_user_tasks(
            user_id=user.id,
            include_visual_metadata=True,
            sort_by="priority"
        )
        
        # Verify visual hierarchy
        high_task = next(t for t in tasks if t.priority == "high")
        medium_task = next(t for t in tasks if t.priority == "medium")
        low_task = next(t for t in tasks if t.priority == "low")
        
        # High priority should have strongest visual emphasis
        assert high_task.visual_metadata["font_weight"] == "bold"
        assert high_task.visual_metadata["border_width"] >= 2
        assert high_task.visual_metadata["opacity"] == 1.0
        
        # Medium priority should have moderate emphasis
        assert medium_task.visual_metadata["font_weight"] == "medium"
        assert medium_task.visual_metadata["border_width"] == 1
        
        # Low priority should have subtle emphasis
        assert low_task.visual_metadata["opacity"] <= 0.8
    
    async def test_progress_visualization_clarity(self, db_session):
        """Test clear progress visualization for ADHD users."""
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "visual_preferences": {
                    "progress_style": "circular",
                    "show_percentages": True,
                    "animate_progress": False  # Reduce distraction
                }
            }
        )
        
        # Create task with subtasks
        main_task = await TaskFactory.create(
            db_session,
            user_id=user.id,
            title="Main project task",
            estimated_duration=120
        )
        
        # Create subtasks with various completion states
        subtasks = []
        for i, status in enumerate(["completed", "completed", "in_progress", "pending"]):
            subtask = await TaskFactory.create(
                db_session,
                user_id=user.id,
                parent_task_id=main_task.id,
                title=f"Subtask {i+1}",
                status=status,
                estimated_duration=30
            )
            subtasks.append(subtask)
        
        task_service = TaskService(db_session)
        
        # Get progress visualization
        progress_data = await task_service.get_task_progress(
            user_id=user.id,
            task_id=main_task.id,
            visual_format="circular"
        )
        
        # Verify ADHD-friendly progress visualization
        assert progress_data["completion_percentage"] == 50  # 2 of 4 subtasks completed
        assert progress_data["visual_format"] == "circular"
        assert progress_data["show_percentage_text"] == True
        assert progress_data["animation_enabled"] == False
        assert "color_segments" in progress_data  # Different colors for different states


@pytest.mark.accessibility
class TestCognitiveLoadReduction:
    """Test cognitive load reduction features."""
    
    async def test_information_chunking(self, db_session):
        """Test information is presented in digestible chunks."""
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "cognitive_preferences": {
                    "max_items_per_view": 7,  # Miller's rule: 7±2 items
                    "group_similar_items": True,
                    "show_context_gradually": True
                }
            }
        )
        
        # Create many tasks
        tasks = []
        for i in range(20):
            task = await TaskFactory.create(
                db_session,
                user_id=user.id,
                title=f"Task {i+1}",
                energy_level=["low", "medium", "high"][i % 3]
            )
            tasks.append(task)
        
        task_service = TaskService(db_session)
        
        # Get paginated task list
        task_page = await task_service.get_user_tasks(
            user_id=user.id,
            page=1,
            page_size=None,  # Should use user preference
            group_by="energy_level"
        )
        
        # Verify cognitive load reduction
        assert len(task_page.items) <= 7  # Respects max items preference
        assert task_page.grouped_by == "energy_level"
        assert task_page.total_pages > 1  # Multiple pages to reduce cognitive load
        
        # Verify grouping reduces cognitive load
        energy_groups = {}
        for task in task_page.items:
            energy = task.energy_level
            if energy not in energy_groups:
                energy_groups[energy] = []
            energy_groups[energy].append(task)
        
        # Should have clear grouping
        assert len(energy_groups) <= 3  # Max 3 energy levels
        for group in energy_groups.values():
            assert len(group) <= 5  # Small groups within each energy level
    
    async def test_progressive_disclosure(self, db_session):
        """Test progressive disclosure of task details."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        
        task = await TaskFactory.create(
            db_session,
            user_id=user.id,
            title="Complex task with many details",
            description="This is a very detailed description with lots of information that might overwhelm ADHD users if shown all at once",
            estimated_duration=120,
            energy_level="high",
            complexity="high"
        )
        
        task_service = TaskService(db_session)
        
        # Get task with progressive disclosure
        task_summary = await task_service.get_task_summary(
            user_id=user.id,
            task_id=task.id,
            detail_level="minimal"
        )
        
        # Minimal view should show only essential information
        assert "title" in task_summary
        assert "estimated_duration" in task_summary
        assert "energy_level" in task_summary
        assert "description" not in task_summary  # Hidden in minimal view
        assert "complexity" not in task_summary  # Hidden in minimal view
        
        # Full view should show all details
        task_full = await task_service.get_task_summary(
            user_id=user.id,
            task_id=task.id,
            detail_level="full"
        )
        
        assert "description" in task_full
        assert "complexity" in task_full
        assert len(task_full.keys()) > len(task_summary.keys())
    
    async def test_context_switching_minimization(self, db_session):
        """Test features that minimize context switching."""
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "workflow_preferences": {
                    "minimize_context_switches": True,
                    "batch_similar_tasks": True,
                    "suggest_task_grouping": True
                }
            }
        )
        
        # Create tasks with different contexts
        email_tasks = []
        coding_tasks = []
        meeting_tasks = []
        
        for i in range(3):
            email_tasks.append(await TaskFactory.create(
                db_session,
                user_id=user.id,
                title=f"Email task {i+1}",
                tags=["email", "communication"],
                energy_level="low"
            ))
            
            coding_tasks.append(await TaskFactory.create(
                db_session,
                user_id=user.id,
                title=f"Coding task {i+1}",
                tags=["coding", "development"],
                energy_level="high"
            ))
            
            meeting_tasks.append(await TaskFactory.create(
                db_session,
                user_id=user.id,
                title=f"Meeting {i+1}",
                tags=["meeting", "collaboration"],
                energy_level="medium"
            ))
        
        task_service = TaskService(db_session)
        
        # Get task suggestions that minimize context switching
        suggestions = await task_service.get_task_suggestions(
            user_id=user.id,
            optimize_for="context_switching"
        )
        
        # Verify tasks are grouped by context
        assert "grouped_suggestions" in suggestions
        groups = suggestions["grouped_suggestions"]
        
        # Should suggest batching similar tasks
        email_group = next((g for g in groups if "email" in g["context_type"]), None)
        assert email_group is not None
        assert len(email_group["tasks"]) == 3
        
        coding_group = next((g for g in groups if "coding" in g["context_type"]), None)
        assert coding_group is not None
        assert len(coding_group["tasks"]) == 3


@pytest.mark.accessibility
class TestSensoryConsiderations:
    """Test sensory considerations for ADHD users."""
    
    async def test_notification_sensory_settings(self, db_session):
        """Test notification respects sensory preferences."""
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "sensory_preferences": {
                    "sound_notifications": False,  # Sound sensitive
                    "visual_notifications": True,
                    "vibration_notifications": False,
                    "notification_intensity": "gentle"
                }
            }
        )
        
        notification_service = NotificationService(db_session)
        
        # Create notification
        notification = await notification_service.create_notification(
            user_id=user.id,
            notification_data={
                "type": "task_reminder",
                "title": "Task Due Soon",
                "message": "Your task is due in 15 minutes",
                "priority": "medium"
            }
        )
        
        # Verify sensory accommodations
        delivery_settings = notification.delivery_settings
        
        assert delivery_settings["sound_enabled"] == False
        assert delivery_settings["visual_enabled"] == True
        assert delivery_settings["vibration_enabled"] == False
        assert delivery_settings["intensity"] == "gentle"
        
        # Gentle intensity should affect visual presentation
        visual_settings = delivery_settings["visual_settings"]
        assert visual_settings["flash_duration"] <= 500  # Short, gentle flash
        assert visual_settings["color_intensity"] <= 0.7  # Muted colors
        assert visual_settings["animation_speed"] == "slow"
    
    async def test_visual_distraction_reduction(self, db_session):
        """Test reduction of visual distractions."""
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "visual_preferences": {
                    "reduce_animations": True,
                    "minimize_visual_clutter": True,
                    "focus_mode_styling": True
                }
            }
        )
        
        time_block_service = TimeBlockService(db_session)
        
        # Create time blocks
        today = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        time_blocks = []
        
        for hour in range(9, 17):
            time_block = await TimeBlockFactory.create(
                db_session,
                user_id=user.id,
                start_time=today.replace(hour=hour),
                end_time=today.replace(hour=hour + 1),
                block_type="work"
            )
            time_blocks.append(time_block)
        
        # Get schedule with distraction reduction
        schedule_data = await time_block_service.get_visual_schedule(
            user_id=user.id,
            date=today.date(),
            apply_user_preferences=True
        )
        
        # Verify distraction reduction
        visual_settings = schedule_data["visual_settings"]
        
        assert visual_settings["animations_enabled"] == False
        assert visual_settings["transition_duration"] <= 200  # Quick transitions
        assert visual_settings["visual_effects"] == "minimal"
        
        # Focus mode styling should be applied
        assert visual_settings["focus_mode"] == True
        assert visual_settings["background_opacity"] <= 0.1  # Minimal background
        assert visual_settings["border_style"] == "clean"
    
    async def test_attention_span_accommodations(self, db_session):
        """Test accommodations for varying attention spans."""
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "attention_preferences": {
                    "short_attention_mode": True,
                    "auto_save_frequency": "high",  # Every 30 seconds
                    "session_reminders": True,
                    "break_suggestions": True
                }
            }
        )
        
        task_service = TaskService(db_session)
        
        # Create a task and start working on it
        task = await TaskFactory.create(
            db_session,
            user_id=user.id,
            title="Task requiring sustained attention",
            estimated_duration=60
        )
        
        # Start task
        started_task = await task_service.start_task(
            user_id=user.id,
            task_id=task.id
        )
        
        # Verify attention span accommodations
        session_settings = started_task.session_settings
        
        assert session_settings["auto_save_interval"] == 30  # 30 seconds
        assert session_settings["attention_reminders"] == True
        assert session_settings["break_suggestions"] == True
        
        # Should suggest breaks for long tasks
        if task.estimated_duration > 45:
            assert session_settings["suggested_break_interval"] <= 25  # Pomodoro-style
        
        # Should provide attention anchoring
        assert "attention_anchors" in session_settings
        anchors = session_settings["attention_anchors"]
        assert len(anchors) > 0
        assert all("reminder_text" in anchor for anchor in anchors)


@pytest.mark.accessibility
class TestKeyboardAndNavigationAccessibility:
    """Test keyboard navigation and accessibility for ADHD users."""
    
    async def test_keyboard_shortcuts_consistency(self, client, authenticated_user):
        """Test consistent keyboard shortcuts across the application."""
        # This would test keyboard navigation in a real frontend
        # For API testing, we verify that shortcuts are documented and consistent
        
        response = await client.get(
            "/api/v1/user/keyboard-shortcuts",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        shortcuts = response.json()["data"]
        
        # Verify ADHD-friendly shortcut design
        assert "quick_task_creation" in shortcuts
        assert shortcuts["quick_task_creation"]["key"] == "Ctrl+N"
        assert shortcuts["quick_task_creation"]["description"] is not None
        
        # Should have shortcuts for common ADHD needs
        assert "focus_mode_toggle" in shortcuts
        assert "energy_level_quick_set" in shortcuts
        assert "break_timer_start" in shortcuts
        
        # Shortcuts should be memorable and logical
        for shortcut_name, shortcut_data in shortcuts.items():
            assert len(shortcut_data["key"]) <= 10  # Not too complex
            assert "description" in shortcut_data
            assert "category" in shortcut_data  # Grouped for easier learning
    
    async def test_navigation_breadcrumbs(self, client, authenticated_user):
        """Test clear navigation breadcrumbs for ADHD users."""
        # Create a task to navigate to
        task_response = await client.post(
            "/api/v1/tasks",
            headers=authenticated_user["headers"],
            json={
                "title": "Test Task for Navigation",
                "estimated_duration": 60
            }
        )
        
        task_id = task_response.json()["data"]["id"]
        
        # Get task details with navigation context
        response = await client.get(
            f"/api/v1/tasks/{task_id}",
            headers=authenticated_user["headers"],
            params={"include_navigation": "true"}
        )
        
        assert response.status_code == 200
        task_data = response.json()["data"]
        
        # Verify navigation breadcrumbs
        assert "navigation" in task_data
        navigation = task_data["navigation"]
        
        assert "breadcrumbs" in navigation
        breadcrumbs = navigation["breadcrumbs"]
        
        # Should provide clear path back
        assert len(breadcrumbs) >= 2  # At least "Tasks" > "Task Details"
        assert breadcrumbs[0]["label"] == "Tasks"
        assert breadcrumbs[-1]["label"] == "Task Details"
        
        # Each breadcrumb should have clear navigation
        for breadcrumb in breadcrumbs:
            assert "label" in breadcrumb
            assert "url" in breadcrumb or "action" in breadcrumb
            assert "accessible_description" in breadcrumb
