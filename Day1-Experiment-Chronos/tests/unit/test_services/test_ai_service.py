"""
Unit tests for AIChunkingService with ADHD-specific functionality.

This module tests the AI-powered task chunking service's ability to
break down overwhelming tasks into manageable, ADHD-friendly subtasks.
"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List

from app.services.ai_service import AIChunkingService
from app.core.exceptions import AIServiceException, AIServiceUnavailableError, AIChunkingError
from tests.utils import MockADHDScenarios


class TestAIChunkingServiceInitialization:
    """Test suite for AIChunkingService initialization and setup."""
    
    def test_service_initialization_with_openai(self):
        """Test service initialization with OpenAI API key."""
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.openai_api_key = "test-openai-key"
            mock_settings.anthropic_api_key = None
            mock_settings.redis_url = "redis://localhost:6379"
            
            service = AIChunkingService()
            
            assert service.openai_client is not None
            assert service.anthropic_client is None
    
    def test_service_initialization_with_anthropic(self):
        """Test service initialization with Anthropic API key."""
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.openai_api_key = None
            mock_settings.anthropic_api_key = "test-anthropic-key"
            mock_settings.redis_url = "redis://localhost:6379"
            
            service = AIChunkingService()
            
            assert service.openai_client is None
            assert service.anthropic_client is not None
    
    def test_service_initialization_no_api_keys(self):
        """Test service initialization without API keys."""
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.openai_api_key = None
            mock_settings.anthropic_api_key = None
            mock_settings.redis_url = "redis://localhost:6379"
            
            service = AIChunkingService()
            
            assert service.openai_client is None
            assert service.anthropic_client is None


class TestAIChunkingServiceCore:
    """Test suite for core AI chunking functionality."""
    
    @pytest.fixture
    def ai_service(self):
        """Create AIChunkingService instance for testing."""
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.openai_api_key = "test-key"
            mock_settings.anthropic_api_key = "test-key"
            mock_settings.redis_url = "redis://localhost:6379"
            return AIChunkingService()
    
    async def test_chunk_task_basic_functionality(self, ai_service):
        """Test basic task chunking functionality."""
        # Mock AI response
        mock_chunks = [
            {
                "title": "Research Phase",
                "description": "Gather requirements and research solutions",
                "estimated_duration": 45,
                "energy_level": "medium",
                "context_tags": ["computer", "research"]
            },
            {
                "title": "Implementation Phase",
                "description": "Implement the solution based on research",
                "estimated_duration": 60,
                "energy_level": "high", 
                "context_tags": ["computer", "coding"]
            }
        ]
        
        # Mock OpenAI client
        with patch.object(ai_service, '_generate_chunks_openai', return_value=mock_chunks):
            with patch.object(ai_service, '_get_cached_result', return_value=None):
                with patch.object(ai_service, '_cache_result', return_value=None):
                    
                    result = await ai_service.chunk_task(
                        title="Large Development Task",
                        description="Build a complex feature",
                        chunk_size="small"
                    )
                    
                    assert len(result) == 2
                    assert result[0]["title"] == "Research Phase"
                    assert result[1]["title"] == "Implementation Phase"
                    assert all(chunk["estimated_duration"] <= 60 for chunk in result)
    
    async def test_chunk_task_with_adhd_preferences(self, ai_service):
        """Test task chunking with ADHD-specific user preferences."""
        adhd_preferences = {
            "chunking_preferences": {
                "default_size": "small",
                "max_subtasks": 5,
                "auto_chunk_threshold": 30
            },
            "energy_patterns": {
                "morning": "high",
                "afternoon": "medium",
                "evening": "low"
            }
        }
        
        # Mock ADHD-optimized chunks
        mock_chunks = [
            {
                "title": "Quick Setup",
                "description": "Initial setup - 15 minutes max",
                "estimated_duration": 15,
                "energy_level": "low",
                "context_tags": ["computer", "setup"]
            },
            {
                "title": "Core Work",
                "description": "Main work session - 25 minutes",
                "estimated_duration": 25,
                "energy_level": "high",
                "context_tags": ["computer", "focus"]
            }
        ]
        
        with patch.object(ai_service, '_generate_chunks_openai', return_value=mock_chunks):
            with patch.object(ai_service, '_get_cached_result', return_value=None):
                with patch.object(ai_service, '_cache_result', return_value=None):
                    
                    result = await ai_service.chunk_task(
                        title="ADHD-Friendly Task",
                        description="Task optimized for ADHD users",
                        chunk_size="small",
                        user_preferences=adhd_preferences
                    )
                    
                    # Verify ADHD optimizations
                    assert len(result) <= 5  # Max subtasks preference
                    assert all(chunk["estimated_duration"] <= 30 for chunk in result)
                    assert any(chunk["energy_level"] == "low" for chunk in result)
    
    async def test_chunk_task_caching(self, ai_service):
        """Test caching functionality for repeated requests."""
        cached_result = [
            {
                "title": "Cached Chunk",
                "description": "Previously generated chunk",
                "estimated_duration": 30,
                "energy_level": "medium",
                "context_tags": ["cached"]
            }
        ]
        
        # Mock cache hit
        with patch.object(ai_service, '_get_cached_result', return_value=cached_result):
            result = await ai_service.chunk_task(
                title="Cached Task",
                description="This should return cached result"
            )
            
            assert result == cached_result
            assert result[0]["title"] == "Cached Chunk"
    
    async def test_chunk_task_no_ai_service_available(self, ai_service):
        """Test error handling when no AI service is available."""
        # Remove AI clients
        ai_service.openai_client = None
        ai_service.anthropic_client = None
        
        with patch.object(ai_service, '_get_cached_result', return_value=None):
            with pytest.raises(AIServiceUnavailableError):
                await ai_service.chunk_task(
                    title="Task Without AI",
                    description="This should fail"
                )


class TestAIChunkingServiceOpenAI:
    """Test suite for OpenAI-specific functionality."""
    
    @pytest.fixture
    def ai_service(self):
        """Create AIChunkingService with OpenAI."""
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.openai_api_key = "test-openai-key"
            mock_settings.anthropic_api_key = None
            mock_settings.redis_url = "redis://localhost:6379"
            return AIChunkingService()
    
    async def test_generate_chunks_openai_success(self, ai_service):
        """Test successful chunk generation with OpenAI."""
        # Mock OpenAI response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = json.dumps({
            "subtasks": [
                {
                    "title": "OpenAI Generated Task",
                    "description": "Task generated by OpenAI",
                    "estimated_duration": 30,
                    "energy_level": "medium",
                    "context_tags": ["computer"]
                }
            ]
        })
        
        # Mock OpenAI client
        ai_service.openai_client = AsyncMock()
        ai_service.openai_client.chat.completions.create = AsyncMock(return_value=mock_response)
        
        # Test chunk generation
        result = await ai_service._generate_chunks_openai(
            prompt="Test prompt for ADHD task chunking",
            chunk_size="small"
        )
        
        assert len(result) == 1
        assert result[0]["title"] == "OpenAI Generated Task"
        assert result[0]["estimated_duration"] == 30
    
    async def test_generate_chunks_openai_error(self, ai_service):
        """Test error handling in OpenAI chunk generation."""
        # Mock OpenAI client to raise exception
        ai_service.openai_client = AsyncMock()
        ai_service.openai_client.chat.completions.create = AsyncMock(
            side_effect=Exception("OpenAI API Error")
        )
        
        # Test error handling
        with pytest.raises(AIServiceException):
            await ai_service._generate_chunks_openai(
                prompt="Test prompt",
                chunk_size="small"
            )


class TestAIChunkingServiceAnthropic:
    """Test suite for Anthropic-specific functionality."""
    
    @pytest.fixture
    def ai_service(self):
        """Create AIChunkingService with Anthropic."""
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.openai_api_key = None
            mock_settings.anthropic_api_key = "test-anthropic-key"
            mock_settings.redis_url = "redis://localhost:6379"
            return AIChunkingService()
    
    async def test_generate_chunks_anthropic_success(self, ai_service):
        """Test successful chunk generation with Anthropic."""
        # Mock Anthropic response
        mock_response = MagicMock()
        mock_response.content = [MagicMock()]
        mock_response.content[0].text = json.dumps({
            "subtasks": [
                {
                    "title": "Anthropic Generated Task",
                    "description": "Task generated by Anthropic Claude",
                    "estimated_duration": 25,
                    "energy_level": "low",
                    "context_tags": ["computer", "writing"]
                }
            ]
        })
        
        # Mock Anthropic client
        ai_service.anthropic_client = AsyncMock()
        ai_service.anthropic_client.messages.create = AsyncMock(return_value=mock_response)
        
        # Test chunk generation
        result = await ai_service._generate_chunks_anthropic(
            prompt="Test prompt for ADHD task chunking",
            chunk_size="small"
        )
        
        assert len(result) == 1
        assert result[0]["title"] == "Anthropic Generated Task"
        assert result[0]["estimated_duration"] == 25


class TestAIChunkingServiceADHDOptimization:
    """Test suite for ADHD-specific optimizations."""
    
    @pytest.fixture
    def ai_service(self):
        """Create AIChunkingService for ADHD testing."""
        with patch('app.core.config.settings') as mock_settings:
            mock_settings.openai_api_key = "test-key"
            mock_settings.anthropic_api_key = None
            mock_settings.redis_url = "redis://localhost:6379"
            return AIChunkingService()
    
    def test_build_chunking_prompt_adhd_context(self, ai_service):
        """Test ADHD-specific prompt building."""
        adhd_preferences = {
            "chunking_preferences": {
                "default_size": "small",
                "max_subtasks": 5
            }
        }
        
        prompt = ai_service._build_chunking_prompt(
            title="Overwhelming Project",
            description="Large project causing task paralysis",
            chunk_size="small",
            context="User has ADHD and struggles with large tasks",
            user_preferences=adhd_preferences
        )
        
        # Verify ADHD-specific elements in prompt
        assert "ADHD" in prompt
        assert "small" in prompt
        assert "task paralysis" in prompt or "overwhelming" in prompt
        assert "5" in prompt  # max_subtasks
    
    async def test_validate_and_format_chunks_adhd_compliance(self, ai_service):
        """Test chunk validation for ADHD compliance."""
        raw_chunks = [
            {
                "title": "ADHD-Friendly Task",
                "description": "Clear, actionable task description",
                "estimated_duration": 25,
                "energy_level": "medium",
                "context_tags": ["computer", "focus"]
            },
            {
                "title": "Too Long Task",
                "description": "This task is too long for ADHD users",
                "estimated_duration": 180,  # 3 hours - too long
                "energy_level": "high",
                "context_tags": ["computer"]
            }
        ]
        
        validated_chunks = ai_service._validate_and_format_chunks(raw_chunks)
        
        # Should filter out or adjust overly long tasks
        adhd_friendly_chunks = [
            chunk for chunk in validated_chunks 
            if chunk["estimated_duration"] <= 90
        ]
        
        assert len(adhd_friendly_chunks) >= 1
        assert all(
            chunk["estimated_duration"] <= 90 
            for chunk in adhd_friendly_chunks
        )
    
    async def test_chunk_task_time_blindness_scenario(self, ai_service):
        """Test chunking for time blindness scenario."""
        scenario = MockADHDScenarios.create_time_blindness_scenario()
        
        # Mock chunks optimized for time blindness
        mock_chunks = [
            {
                "title": "Timed Task - 15 minutes",
                "description": "Task with explicit time limit for time awareness",
                "estimated_duration": 15,
                "energy_level": "low",
                "context_tags": ["timed", "computer"],
                "time_awareness_cues": True
            }
        ]
        
        with patch.object(ai_service, '_generate_chunks_openai', return_value=mock_chunks):
            with patch.object(ai_service, '_get_cached_result', return_value=None):
                with patch.object(ai_service, '_cache_result', return_value=None):
                    
                    result = await ai_service.chunk_task(
                        title=scenario["test_tasks"][0]["title"],
                        description="Task for user with time blindness",
                        chunk_size="small",
                        context="User struggles with time estimation"
                    )
                    
                    # Verify time blindness optimizations
                    assert len(result) == 1
                    assert result[0]["estimated_duration"] <= 30
                    assert "15 minutes" in result[0]["title"]  # Explicit time cue
    
    async def test_chunk_task_paralysis_scenario(self, ai_service):
        """Test chunking for task paralysis scenario."""
        scenario = MockADHDScenarios.create_task_paralysis_scenario()
        
        # Mock chunks optimized for task paralysis
        mock_chunks = [
            {
                "title": "Step 1: Simple Start",
                "description": "Easy first step to overcome paralysis",
                "estimated_duration": 10,
                "energy_level": "low",
                "context_tags": ["easy", "start"]
            },
            {
                "title": "Step 2: Build Momentum",
                "description": "Second step building on first success",
                "estimated_duration": 20,
                "energy_level": "medium",
                "context_tags": ["momentum", "progress"]
            }
        ]
        
        with patch.object(ai_service, '_generate_chunks_openai', return_value=mock_chunks):
            with patch.object(ai_service, '_get_cached_result', return_value=None):
                with patch.object(ai_service, '_cache_result', return_value=None):
                    
                    result = await ai_service.chunk_task(
                        title=scenario["test_tasks"][0]["title"],
                        description="Overwhelming project causing paralysis",
                        chunk_size="small",
                        context="User experiences task paralysis with large projects"
                    )
                    
                    # Verify task paralysis optimizations
                    assert len(result) == 2
                    assert result[0]["estimated_duration"] <= 15  # Very small first step
                    assert "Simple" in result[0]["title"] or "Easy" in result[0]["title"]
                    assert result[0]["energy_level"] == "low"  # Low barrier to entry
