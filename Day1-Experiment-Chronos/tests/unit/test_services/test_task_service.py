"""
Unit tests for TaskService with ADHD-specific functionality.

This module tests the TaskService's ADHD-focused features including
CRUD operations, AI chunking integration, and adaptive filtering.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
from datetime import datetime, timedelta

from app.services.task_service import TaskService
from app.models.task import Task
from app.models.user import User
from app.schemas.task import TaskCreate, TaskUpdate, TaskFilterRequest
from app.core.exceptions import TaskNotFoundError, TaskValidationError, AuthorizationError
from tests.factories import TaskFactory, UserFactory
from tests.utils import ADHDTestAssertions


class TestTaskServiceBasicOperations:
    """Test suite for basic TaskService CRUD operations."""
    
    @pytest.fixture
    def task_service(self):
        """Create TaskService instance for testing."""
        return TaskService()
    
    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        return AsyncMock()
    
    @pytest.fixture
    def sample_user_id(self):
        """Create sample user ID."""
        return uuid4()
    
    @pytest.fixture
    def sample_task_data(self):
        """Create sample task creation data."""
        return TaskCreate(
            title="Test ADHD Task",
            description="A test task with ADHD-optimized features",
            priority="medium",
            energy_level="low",
            estimated_duration=30,
            context_tags=["home", "computer"],
            due_date=datetime.utcnow() + timedelta(days=1)
        )
    
    async def test_create_task_success(
        self, 
        task_service: TaskService, 
        mock_db_session, 
        sample_user_id, 
        sample_task_data
    ):
        """Test successful task creation with ADHD features."""
        # Mock database operations
        mock_db_session.add = MagicMock()
        mock_db_session.commit = AsyncMock()
        mock_db_session.refresh = AsyncMock()
        
        # Create task
        result = await task_service.create_task(
            db=mock_db_session,
            task_data=sample_task_data,
            user_id=sample_user_id
        )
        
        # Verify task creation
        assert result.title == sample_task_data.title
        assert result.energy_level == sample_task_data.energy_level
        assert result.estimated_duration == sample_task_data.estimated_duration
        assert result.context_tags == sample_task_data.context_tags
        assert result.user_id == sample_user_id
        
        # Verify database operations
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()
    
    async def test_get_task_by_id_success(
        self, 
        task_service: TaskService, 
        mock_db_session, 
        sample_user_id
    ):
        """Test successful task retrieval by ID."""
        task_id = uuid4()
        mock_task = Task(
            id=task_id,
            user_id=sample_user_id,
            title="Test Task",
            energy_level="medium"
        )
        
        # Mock database query
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_task
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        # Get task
        result = await task_service.get_task_by_id(
            db=mock_db_session,
            task_id=task_id,
            user_id=sample_user_id
        )
        
        # Verify result
        assert result == mock_task
        assert result.id == task_id
        assert result.user_id == sample_user_id
    
    async def test_get_task_by_id_not_found(
        self, 
        task_service: TaskService, 
        mock_db_session, 
        sample_user_id
    ):
        """Test task retrieval when task doesn't exist."""
        task_id = uuid4()
        
        # Mock database query returning None
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        # Expect TaskNotFoundError
        with pytest.raises(TaskNotFoundError):
            await task_service.get_task_by_id(
                db=mock_db_session,
                task_id=task_id,
                user_id=sample_user_id
            )
    
    async def test_get_task_authorization_error(
        self, 
        task_service: TaskService, 
        mock_db_session
    ):
        """Test task retrieval with wrong user ID."""
        task_id = uuid4()
        owner_id = uuid4()
        wrong_user_id = uuid4()
        
        mock_task = Task(
            id=task_id,
            user_id=owner_id,
            title="Test Task"
        )
        
        # Mock database query
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_task
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        # Expect AuthorizationError
        with pytest.raises(AuthorizationError):
            await task_service.get_task_by_id(
                db=mock_db_session,
                task_id=task_id,
                user_id=wrong_user_id
            )


class TestTaskServiceADHDFeatures:
    """Test suite for ADHD-specific TaskService features."""
    
    @pytest.fixture
    def task_service(self):
        """Create TaskService instance for testing."""
        return TaskService()
    
    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        return AsyncMock()
    
    async def test_get_user_tasks_with_energy_filter(
        self, 
        task_service: TaskService, 
        mock_db_session
    ):
        """Test task filtering by energy level."""
        user_id = uuid4()
        
        # Create mock tasks with different energy levels
        low_energy_task = Task(
            id=uuid4(),
            user_id=user_id,
            title="Low Energy Task",
            energy_level="low",
            estimated_duration=15
        )
        
        high_energy_task = Task(
            id=uuid4(),
            user_id=user_id,
            title="High Energy Task",
            energy_level="high",
            estimated_duration=90
        )
        
        # Mock database query
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [low_energy_task]
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        # Filter for low energy tasks
        filters = TaskFilterRequest(energy_level="low")
        result = await task_service.get_user_tasks(
            db=mock_db_session,
            user_id=user_id,
            filters=filters
        )
        
        # Verify filtering
        assert len(result) == 1
        assert result[0].energy_level == "low"
        assert result[0].estimated_duration <= 30  # ADHD-friendly duration
    
    async def test_get_user_tasks_with_context_filter(
        self, 
        task_service: TaskService, 
        mock_db_session
    ):
        """Test task filtering by context tags."""
        user_id = uuid4()
        
        # Create mock task with context tags
        home_task = Task(
            id=uuid4(),
            user_id=user_id,
            title="Home Task",
            context_tags=["home", "computer"]
        )
        
        # Mock database query
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [home_task]
        mock_db_session.execute = AsyncMock(return_value=mock_result)
        
        # Filter for home context
        filters = TaskFilterRequest(context_tags=["home"])
        result = await task_service.get_user_tasks(
            db=mock_db_session,
            user_id=user_id,
            filters=filters
        )
        
        # Verify filtering
        assert len(result) == 1
        assert "home" in result[0].context_tags
    
    async def test_update_task_with_status_change(
        self, 
        task_service: TaskService, 
        mock_db_session
    ):
        """Test task update with status change tracking."""
        user_id = uuid4()
        task_id = uuid4()
        
        # Create mock existing task
        existing_task = Task(
            id=task_id,
            user_id=user_id,
            title="Test Task",
            status="pending"
        )
        
        # Mock get_task_by_id
        with patch.object(task_service, 'get_task_by_id', return_value=existing_task):
            # Mock database operations
            mock_db_session.commit = AsyncMock()
            mock_db_session.refresh = AsyncMock()
            
            # Update task to completed
            update_data = TaskUpdate(status="completed")
            result = await task_service.update_task(
                db=mock_db_session,
                task_id=task_id,
                task_data=update_data,
                user_id=user_id
            )
            
            # Verify status change
            assert result.status == "completed"
            mock_db_session.commit.assert_called_once()
    
    async def test_soft_delete_task(
        self, 
        task_service: TaskService, 
        mock_db_session
    ):
        """Test soft delete functionality for ADHD users."""
        user_id = uuid4()
        task_id = uuid4()
        
        # Create mock task
        mock_task = Task(
            id=task_id,
            user_id=user_id,
            title="Task to Delete"
        )
        
        # Mock get_task_by_id
        with patch.object(task_service, 'get_task_by_id', return_value=mock_task):
            # Mock database operations
            mock_db_session.commit = AsyncMock()
            
            # Soft delete task
            await task_service.soft_delete_task(
                db=mock_db_session,
                task_id=task_id,
                user_id=user_id
            )
            
            # Verify soft delete
            assert mock_task.is_deleted is True
            assert mock_task.deleted_at is not None
            mock_db_session.commit.assert_called_once()


class TestTaskServiceAIChunking:
    """Test suite for AI chunking functionality."""
    
    @pytest.fixture
    def task_service(self):
        """Create TaskService instance for testing."""
        return TaskService()
    
    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        return AsyncMock()
    
    async def test_chunk_task_success(
        self, 
        task_service: TaskService, 
        mock_db_session
    ):
        """Test successful task chunking with AI."""
        user_id = uuid4()
        task_id = uuid4()
        
        # Create mock large task
        large_task = Task(
            id=task_id,
            user_id=user_id,
            title="Large Project",
            description="Complex project requiring chunking",
            estimated_duration=240,  # 4 hours
            is_chunked=False
        )
        
        # Mock AI service response
        mock_chunks = [
            {
                "title": "Research Phase",
                "description": "Research requirements and gather information",
                "estimated_duration": 60,
                "energy_level": "medium",
                "context_tags": ["computer", "research"]
            },
            {
                "title": "Planning Phase", 
                "description": "Create detailed project plan",
                "estimated_duration": 45,
                "energy_level": "high",
                "context_tags": ["computer", "planning"]
            }
        ]
        
        # Mock dependencies
        with patch.object(task_service, 'get_task_by_id', return_value=large_task):
            with patch.object(task_service.ai_service, 'chunk_task', return_value=mock_chunks):
                # Mock database operations
                mock_db_session.add = MagicMock()
                mock_db_session.commit = AsyncMock()
                mock_db_session.refresh = AsyncMock()
                
                # Chunk the task
                result = await task_service.chunk_task(
                    db=mock_db_session,
                    task_id=task_id,
                    user_id=user_id,
                    chunk_size="small"
                )
                
                # Verify chunking results
                assert len(result) == 2
                assert result[0].title == "Research Phase"
                assert result[1].title == "Planning Phase"
                assert all(subtask.estimated_duration <= 60 for subtask in result)
                assert large_task.is_chunked is True
    
    async def test_chunk_already_chunked_task(
        self, 
        task_service: TaskService, 
        mock_db_session
    ):
        """Test chunking a task that's already chunked."""
        user_id = uuid4()
        task_id = uuid4()
        
        # Create mock already-chunked task
        chunked_task = Task(
            id=task_id,
            user_id=user_id,
            title="Already Chunked Task",
            is_chunked=True,
            subtasks=["Existing subtask"]
        )
        
        # Mock get_task_by_id
        with patch.object(task_service, 'get_task_by_id', return_value=chunked_task):
            # Attempt to chunk again
            result = await task_service.chunk_task(
                db=mock_db_session,
                task_id=task_id,
                user_id=user_id
            )
            
            # Should return existing subtasks
            assert result == chunked_task.subtasks
    
    async def test_chunk_task_with_adhd_preferences(
        self, 
        task_service: TaskService, 
        mock_db_session
    ):
        """Test task chunking with ADHD-specific preferences."""
        user_id = uuid4()
        task_id = uuid4()
        
        # Create mock task
        task = Task(
            id=task_id,
            user_id=user_id,
            title="ADHD-Optimized Task",
            estimated_duration=120,
            is_chunked=False
        )
        
        # ADHD user preferences
        adhd_preferences = {
            "chunking_preferences": {
                "default_size": "small",
                "max_subtasks": 5,
                "auto_chunk_threshold": 45
            },
            "energy_patterns": {
                "morning": "high",
                "afternoon": "medium",
                "evening": "low"
            }
        }
        
        # Mock AI service with ADHD-optimized chunks
        mock_chunks = [
            {
                "title": "Quick Setup",
                "description": "Initial setup tasks",
                "estimated_duration": 30,  # ADHD-friendly duration
                "energy_level": "low",
                "context_tags": ["computer", "setup"]
            }
        ]
        
        # Mock dependencies
        with patch.object(task_service, 'get_task_by_id', return_value=task):
            with patch.object(task_service.ai_service, 'chunk_task', return_value=mock_chunks) as mock_ai:
                # Mock database operations
                mock_db_session.add = MagicMock()
                mock_db_session.commit = AsyncMock()
                mock_db_session.refresh = AsyncMock()
                
                # Chunk with ADHD preferences
                result = await task_service.chunk_task(
                    db=mock_db_session,
                    task_id=task_id,
                    user_id=user_id,
                    chunk_size="small",
                    user_preferences=adhd_preferences
                )
                
                # Verify AI service was called with preferences
                mock_ai.assert_called_once()
                call_args = mock_ai.call_args
                assert call_args[1]["user_preferences"] == adhd_preferences
                
                # Verify ADHD-optimized results
                assert len(result) == 1
                assert result[0].estimated_duration <= 30  # ADHD-friendly
                ADHDTestAssertions.assert_task_duration_appropriate_for_adhd(
                    result[0], 
                    max_duration=45
                )
