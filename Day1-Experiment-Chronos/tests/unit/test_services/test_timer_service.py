"""
Unit tests for TimerService.

This module tests the real-time timer functionality including
Redis-based state persistence and timer accuracy.
"""

import asyncio
import pytest
from datetime import datetime, timedelta
from uuid import uuid4

from chronos.app.core.exceptions import TimerError, TimerNotFoundError
from chronos.app.services.timer_service import Timer, TimerService
from tests.conftest import MockRedis


class TestTimer:
    """Test cases for Timer class."""
    
    def test_timer_initialization(self):
        """Test timer initialization."""
        session_id = uuid4()
        timer = Timer(
            session_id=session_id,
            duration=25,
            timer_type="focus"
        )
        
        assert timer.session_id == session_id
        assert timer.duration == 25
        assert timer.timer_type == "focus"
        assert timer.is_active is True
        assert timer.is_paused is False
        assert timer.paused_duration == 0
    
    def test_timer_elapsed_time(self):
        """Test timer elapsed time calculation."""
        timer = Timer(
            session_id=uuid4(),
            duration=25,
            start_time=datetime.utcnow() - timedelta(minutes=5)
        )
        
        elapsed = timer.get_elapsed_seconds()
        # Should be approximately 5 minutes (300 seconds), allow some tolerance
        assert 295 <= elapsed <= 305
    
    def test_timer_remaining_time(self):
        """Test timer remaining time calculation."""
        timer = Timer(
            session_id=uuid4(),
            duration=25,  # 25 minutes = 1500 seconds
            start_time=datetime.utcnow() - timedelta(minutes=5)
        )
        
        remaining = timer.get_remaining_seconds()
        # Should be approximately 20 minutes (1200 seconds)
        assert 1195 <= remaining <= 1205
    
    def test_timer_progress_percentage(self):
        """Test timer progress percentage calculation."""
        timer = Timer(
            session_id=uuid4(),
            duration=25,
            start_time=datetime.utcnow() - timedelta(minutes=5)
        )
        
        progress = timer.get_progress_percentage()
        # Should be approximately 20% (5/25)
        assert 0.18 <= progress <= 0.22
    
    def test_timer_pause_resume(self):
        """Test timer pause and resume functionality."""
        import time

        timer = Timer(
            session_id=uuid4(),
            duration=25,
            start_time=datetime.utcnow() - timedelta(minutes=5)
        )

        # Pause timer
        timer.pause()
        assert timer.is_paused is True
        assert timer.paused_at is not None

        # Wait a bit and resume
        time.sleep(0.1)  # Small delay to accumulate paused time
        timer.resume()
        assert timer.is_paused is False
        assert timer.paused_at is None
        assert timer.paused_duration >= 0  # Should be >= 0 after resume
    
    def test_timer_extend(self):
        """Test timer extension."""
        timer = Timer(
            session_id=uuid4(),
            duration=25
        )
        
        timer.extend(15)
        assert timer.duration == 40
    
    def test_timer_stop(self):
        """Test timer stop functionality."""
        timer = Timer(
            session_id=uuid4(),
            duration=25
        )
        
        timer.stop()
        assert timer.is_active is False
        assert timer.is_paused is False
    
    def test_timer_serialization(self):
        """Test timer to_dict and from_dict."""
        session_id = uuid4()
        timer = Timer(
            session_id=session_id,
            duration=25,
            timer_type="focus"
        )
        
        # Convert to dict
        timer_dict = timer.to_dict()
        assert timer_dict["session_id"] == str(session_id)
        assert timer_dict["duration"] == 25
        assert timer_dict["timer_type"] == "focus"
        
        # Convert back from dict
        restored_timer = Timer.from_dict(timer_dict)
        assert restored_timer.session_id == session_id
        assert restored_timer.duration == 25
        assert restored_timer.timer_type == "focus"


class TestTimerService:
    """Test cases for TimerService."""
    
    @pytest.mark.asyncio
    async def test_create_timer(self, mock_redis):
        """Test timer creation."""
        timer_service = TimerService()
        timer_service.redis_client = mock_redis
        
        session_id = uuid4()
        timer = await timer_service.create_timer(
            session_id=session_id,
            duration=25,
            timer_type="focus"
        )
        
        assert timer.session_id == session_id
        assert timer.duration == 25
        assert timer.timer_type == "focus"
        assert timer.is_active is True
        
        # Check if timer is stored in memory
        assert str(session_id) in timer_service.active_timers
    
    @pytest.mark.asyncio
    async def test_get_timer_state(self, mock_redis):
        """Test getting timer state."""
        timer_service = TimerService()
        timer_service.redis_client = mock_redis
        
        session_id = uuid4()
        await timer_service.create_timer(
            session_id=session_id,
            duration=25
        )
        
        timer_state = await timer_service.get_timer_state(session_id)
        
        assert timer_state.session_id == session_id
        assert timer_state.status == "active"
        assert timer_state.elapsed_time >= 0
        assert timer_state.remaining_time <= 25
        assert timer_state.progress_percentage >= 0.0
        assert timer_state.is_paused is False
    
    @pytest.mark.asyncio
    async def test_pause_timer(self, mock_redis):
        """Test pausing timer."""
        timer_service = TimerService()
        timer_service.redis_client = mock_redis
        
        session_id = uuid4()
        await timer_service.create_timer(session_id=session_id, duration=25)
        
        timer_state = await timer_service.pause_timer(session_id)
        
        assert timer_state.status == "paused"
        assert timer_state.is_paused is True
    
    @pytest.mark.asyncio
    async def test_resume_timer(self, mock_redis):
        """Test resuming timer."""
        timer_service = TimerService()
        timer_service.redis_client = mock_redis
        
        session_id = uuid4()
        await timer_service.create_timer(session_id=session_id, duration=25)
        await timer_service.pause_timer(session_id)
        
        timer_state = await timer_service.resume_timer(session_id)
        
        assert timer_state.status == "active"
        assert timer_state.is_paused is False
    
    @pytest.mark.asyncio
    async def test_extend_timer(self, mock_redis):
        """Test extending timer."""
        timer_service = TimerService()
        timer_service.redis_client = mock_redis
        
        session_id = uuid4()
        await timer_service.create_timer(session_id=session_id, duration=25)
        
        timer_state = await timer_service.extend_timer(session_id, 15)
        
        # Get the timer to check duration
        timer = await timer_service._get_timer(session_id)
        assert timer.duration == 40
    
    @pytest.mark.asyncio
    async def test_stop_timer(self, mock_redis):
        """Test stopping timer."""
        timer_service = TimerService()
        timer_service.redis_client = mock_redis
        
        session_id = uuid4()
        await timer_service.create_timer(session_id=session_id, duration=25)
        
        timer_state = await timer_service.stop_timer(session_id)
        
        assert timer_state.status == "stopped"
        
        # Timer should be removed from memory
        assert str(session_id) not in timer_service.active_timers
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_timer_fails(self, mock_redis):
        """Test that getting nonexistent timer fails."""
        timer_service = TimerService()
        timer_service.redis_client = mock_redis
        
        with pytest.raises(TimerNotFoundError):
            await timer_service.get_timer_state(uuid4())
    
    @pytest.mark.asyncio
    async def test_timer_persistence_redis(self, mock_redis):
        """Test timer persistence in Redis."""
        timer_service = TimerService()
        timer_service.redis_client = mock_redis
        
        session_id = uuid4()
        await timer_service.create_timer(session_id=session_id, duration=25)
        
        # Remove from memory to test Redis retrieval
        del timer_service.active_timers[str(session_id)]
        
        # Should still be able to get timer from Redis
        timer_state = await timer_service.get_timer_state(session_id)
        assert timer_state.session_id == session_id
    
    @pytest.mark.asyncio
    async def test_timer_accuracy(self, mock_redis):
        """Test timer accuracy over time."""
        timer_service = TimerService()
        timer_service.redis_client = mock_redis
        
        session_id = uuid4()
        start_time = datetime.utcnow()
        
        await timer_service.create_timer(session_id=session_id, duration=1)  # 1 minute
        
        # Wait a short time
        await asyncio.sleep(0.1)  # 100ms
        
        timer_state = await timer_service.get_timer_state(session_id)
        
        # Check that elapsed time is reasonable
        assert timer_state.elapsed_time == 0  # Should still be 0 minutes
        assert timer_state.remaining_time == 1  # Should still be 1 minute
        assert 0.0 <= timer_state.progress_percentage <= 0.1  # Very small progress
    
    @pytest.mark.asyncio
    async def test_cleanup_expired_timers(self, mock_redis):
        """Test cleanup of expired timers."""
        timer_service = TimerService()
        timer_service.redis_client = mock_redis
        
        # Create timer with old start time
        session_id = uuid4()
        timer = Timer(
            session_id=session_id,
            duration=25,
            start_time=datetime.utcnow() - timedelta(hours=25)  # 25 hours ago
        )
        
        # Manually add to Redis
        timer_key = timer_service._get_timer_key(session_id)
        await mock_redis.setex(timer_key, timedelta(hours=24), timer.to_dict())
        
        # Run cleanup
        await timer_service.cleanup_expired_timers()
        
        # Timer should be removed
        assert await mock_redis.get(timer_key) is None
    
    @pytest.mark.asyncio
    async def test_timer_service_close(self, mock_redis):
        """Test timer service cleanup."""
        timer_service = TimerService()
        timer_service.redis_client = mock_redis
        
        await timer_service.close()
        # Should not raise any exceptions
