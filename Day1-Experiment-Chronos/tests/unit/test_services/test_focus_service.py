"""
Unit tests for FocusSessionService.

This module tests the focus session business logic including
ADHD-specific features like hyperfocus detection and gentle interventions.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4

from chronos.app.core.exceptions import (
    FocusSessionError,
    FocusSessionNotFoundError,
    InvalidFocusSessionStateError,
)
from chronos.app.models.focus import FocusSession
from chronos.app.schemas.focus import FocusSessionCreate
from chronos.app.services.focus_service import FocusSessionService
from tests.conftest import assert_session_state


class TestFocusSessionService:
    """Test cases for FocusSessionService."""
    
    @pytest.mark.asyncio
    async def test_create_focus_session(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_session_data: FocusSessionCreate
    ):
        """Test focus session creation."""
        session = await focus_service.create_focus_session(
            user_id=test_user.id,
            session_data=sample_session_data
        )
        
        assert session.id is not None
        assert session.user_id == test_user.id
        assert session.session_type == "pomodoro"
        assert session.planned_duration == 25
        assert session.break_duration == 5
        assert session.status == "planned"
        assert session.focus_mode_settings["gentle_reminders"] is True
        assert session.started_at is None
        assert session.completed_at is None
    
    @pytest.mark.asyncio
    async def test_create_session_with_active_session_fails(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_session_data: FocusSessionCreate
    ):
        """Test that creating a session fails when user has active session."""
        # Create and start first session
        session1 = await focus_service.create_focus_session(
            user_id=test_user.id,
            session_data=sample_session_data
        )
        await focus_service.start_focus_session(test_user.id, session1.id)
        
        # Try to create second session - should fail
        with pytest.raises(FocusSessionError) as exc_info:
            await focus_service.create_focus_session(
                user_id=test_user.id,
                session_data=sample_session_data
            )
        
        assert "Cannot create new session while another session is active" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_start_focus_session(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_focus_session: FocusSession
    ):
        """Test starting a planned focus session."""
        session = await focus_service.start_focus_session(
            user_id=test_user.id,
            session_id=sample_focus_session.id
        )
        
        assert_session_state(session, "active")
        assert session.started_at is not None
        assert session.started_at <= datetime.utcnow()
    
    @pytest.mark.asyncio
    async def test_start_non_planned_session_fails(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_focus_session: FocusSession
    ):
        """Test that starting non-planned session fails."""
        # First start the session
        await focus_service.start_focus_session(test_user.id, sample_focus_session.id)
        
        # Try to start again - should fail
        with pytest.raises(InvalidFocusSessionStateError):
            await focus_service.start_focus_session(test_user.id, sample_focus_session.id)
    
    @pytest.mark.asyncio
    async def test_pause_focus_session(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_focus_session: FocusSession
    ):
        """Test pausing an active focus session."""
        # Start session first
        await focus_service.start_focus_session(test_user.id, sample_focus_session.id)
        
        # Pause session
        session = await focus_service.pause_focus_session(
            user_id=test_user.id,
            session_id=sample_focus_session.id,
            pause_reason="Need a quick break"
        )
        
        assert_session_state(session, "paused")
        assert session.paused_at is not None
        assert session.focus_mode_settings.get("last_pause_reason") == "Need a quick break"
    
    @pytest.mark.asyncio
    async def test_pause_non_active_session_fails(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_focus_session: FocusSession
    ):
        """Test that pausing non-active session fails."""
        with pytest.raises(InvalidFocusSessionStateError):
            await focus_service.pause_focus_session(
                user_id=test_user.id,
                session_id=sample_focus_session.id
            )
    
    @pytest.mark.asyncio
    async def test_resume_focus_session(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_focus_session: FocusSession
    ):
        """Test resuming a paused focus session."""
        # Start and pause session
        await focus_service.start_focus_session(test_user.id, sample_focus_session.id)
        await focus_service.pause_focus_session(test_user.id, sample_focus_session.id)
        
        # Resume session
        session = await focus_service.resume_focus_session(
            user_id=test_user.id,
            session_id=sample_focus_session.id
        )
        
        assert_session_state(session, "active")
        assert session.paused_at is None
    
    @pytest.mark.asyncio
    async def test_resume_non_paused_session_fails(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_focus_session: FocusSession
    ):
        """Test that resuming non-paused session fails."""
        with pytest.raises(InvalidFocusSessionStateError):
            await focus_service.resume_focus_session(
                user_id=test_user.id,
                session_id=sample_focus_session.id
            )
    
    @pytest.mark.asyncio
    async def test_complete_focus_session(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_focus_session: FocusSession
    ):
        """Test completing a focus session."""
        # Start session
        await focus_service.start_focus_session(test_user.id, sample_focus_session.id)
        
        # Complete session
        session = await focus_service.complete_focus_session(
            user_id=test_user.id,
            session_id=sample_focus_session.id,
            actual_duration=30,
            completion_notes="Great session!"
        )
        
        assert_session_state(session, "completed")
        assert session.completed_at is not None
        assert session.actual_duration == 30
        assert session.focus_mode_settings.get("completion_notes") == "Great session!"
    
    @pytest.mark.asyncio
    async def test_complete_non_active_session_fails(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_focus_session: FocusSession
    ):
        """Test that completing non-active session fails."""
        with pytest.raises(InvalidFocusSessionStateError):
            await focus_service.complete_focus_session(
                user_id=test_user.id,
                session_id=sample_focus_session.id
            )
    
    @pytest.mark.asyncio
    async def test_get_session_by_id(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_focus_session: FocusSession
    ):
        """Test getting session by ID."""
        session = await focus_service.get_session_by_id(
            user_id=test_user.id,
            session_id=sample_focus_session.id
        )
        
        assert session.id == sample_focus_session.id
        assert session.user_id == test_user.id
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_session_fails(
        self,
        focus_service: FocusSessionService,
        test_user
    ):
        """Test that getting nonexistent session fails."""
        with pytest.raises(FocusSessionNotFoundError):
            await focus_service.get_session_by_id(
                user_id=test_user.id,
                session_id=uuid4()
            )
    
    @pytest.mark.asyncio
    async def test_get_active_session(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_focus_session: FocusSession
    ):
        """Test getting active session."""
        # No active session initially
        active_session = await focus_service.get_active_session(test_user.id)
        assert active_session is None
        
        # Start session
        await focus_service.start_focus_session(test_user.id, sample_focus_session.id)
        
        # Should find active session
        active_session = await focus_service.get_active_session(test_user.id)
        assert active_session is not None
        assert active_session.id == sample_focus_session.id
        assert active_session.status == "active"
    
    @pytest.mark.asyncio
    async def test_get_user_sessions(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_session_data: FocusSessionCreate
    ):
        """Test getting user sessions with pagination."""
        # Create multiple sessions
        sessions = []
        for i in range(5):
            session = await focus_service.create_focus_session(
                user_id=test_user.id,
                session_data=sample_session_data
            )
            sessions.append(session)
        
        # Get all sessions
        user_sessions = await focus_service.get_user_sessions(
            user_id=test_user.id,
            limit=10,
            offset=0
        )
        
        assert len(user_sessions) >= 5  # At least the ones we created
        
        # Test pagination
        first_page = await focus_service.get_user_sessions(
            user_id=test_user.id,
            limit=3,
            offset=0
        )
        assert len(first_page) == 3
        
        second_page = await focus_service.get_user_sessions(
            user_id=test_user.id,
            limit=3,
            offset=3
        )
        assert len(second_page) >= 2  # At least 2 more
    
    @pytest.mark.asyncio
    async def test_extend_session(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_focus_session: FocusSession
    ):
        """Test extending session duration."""
        # Start session
        await focus_service.start_focus_session(test_user.id, sample_focus_session.id)
        
        original_duration = sample_focus_session.planned_duration
        
        # Extend session
        session = await focus_service.extend_session(
            user_id=test_user.id,
            session_id=sample_focus_session.id,
            additional_minutes=15
        )
        
        assert session.planned_duration == original_duration + 15
    
    @pytest.mark.asyncio
    async def test_extend_inactive_session_fails(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_focus_session: FocusSession
    ):
        """Test that extending inactive session fails."""
        with pytest.raises(InvalidFocusSessionStateError):
            await focus_service.extend_session(
                user_id=test_user.id,
                session_id=sample_focus_session.id,
                additional_minutes=15
            )
    
    @pytest.mark.asyncio
    async def test_check_hyperfocus(
        self,
        focus_service: FocusSessionService,
        test_user,
        sample_focus_session: FocusSession
    ):
        """Test hyperfocus detection."""
        # Start session
        await focus_service.start_focus_session(test_user.id, sample_focus_session.id)
        
        # Simulate long session by modifying started_at
        sample_focus_session.started_at = datetime.utcnow() - timedelta(hours=2, minutes=30)
        
        # Check hyperfocus
        hyperfocus_detected = await focus_service.check_hyperfocus(
            user_id=test_user.id,
            session_id=sample_focus_session.id
        )
        
        assert hyperfocus_detected is True
        
        # Refresh session to check if hyperfocus was marked
        session = await focus_service.get_session_by_id(test_user.id, sample_focus_session.id)
        assert session.hyperfocus_detected is True
        assert session.hyperfocus_duration is not None
