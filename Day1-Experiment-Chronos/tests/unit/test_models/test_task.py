"""
Unit tests for Task model with ADHD-specific features.

This module tests the Task model's ADHD-focused functionality including
energy levels, context tags, time estimation, and hierarchical relationships.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4

from chronos.app.models.task import Task
from app.models.task import Task as AppTask


class TestTaskModel:
    """Test Task model functionality."""

    def test_task_creation(self):
        """Test basic task creation."""
        task = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="Test Task",
            description="A test task"
        )

        assert task.title == "Test Task"
        assert task.description == "A test task"
        assert task.status == "pending"
        assert task.priority == "medium"
        assert task.energy_level == "medium"
        assert task.context_tags == []
        assert task.is_chunked is False
        assert task.parent_task_id is None
    
    def test_adhd_specific_fields(self):
        """Test ADHD-specific task fields."""
        task = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="ADHD Task",
            energy_level="low",
            estimated_duration=15,
            context_tags=["home", "quick"]
        )
        
        assert task.energy_level == "low"
        assert task.estimated_duration == 15
        assert "home" in task.context_tags
        assert "quick" in task.context_tags
    
    def test_is_overdue(self):
        """Test overdue detection."""
        # Task with future due date
        future_task = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="Future Task",
            due_date=datetime.utcnow() + timedelta(hours=1)
        )
        assert future_task.is_overdue() is False
        
        # Task with past due date
        past_task = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="Past Task",
            due_date=datetime.utcnow() - timedelta(hours=1)
        )
        assert past_task.is_overdue() is True
        
        # Completed task (not overdue even if past due date)
        completed_task = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="Completed Task",
            status="completed",
            due_date=datetime.utcnow() - timedelta(hours=1)
        )
        assert completed_task.is_overdue() is False
    
    def test_subtask_relationships(self):
        """Test parent-child task relationships."""
        parent_task = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="Parent Task",
            is_chunked=True
        )
        
        subtask = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="Subtask",
            parent_task_id=parent_task.id
        )
        
        assert parent_task.has_subtasks() is False  # No subtasks added yet
        assert subtask.is_subtask() is True
        assert parent_task.is_subtask() is False
    
    def test_context_tag_management(self):
        """Test context tag operations."""
        task = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="Test Task"
        )
        
        # Add context tags
        task.add_context_tag("home")
        task.add_context_tag("computer")
        assert "home" in task.context_tags
        assert "computer" in task.context_tags
        
        # Don't add duplicates
        task.add_context_tag("home")
        assert task.context_tags.count("home") == 1
        
        # Remove context tag
        task.remove_context_tag("home")
        assert "home" not in task.context_tags
        assert "computer" in task.context_tags
    
    def test_context_matching(self):
        """Test context tag matching."""
        task = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="Test Task",
            context_tags=["home", "computer", "quiet"]
        )
        
        # Should match if all required tags are present
        assert task.matches_context(["home"]) is True
        assert task.matches_context(["home", "computer"]) is True
        
        # Should not match if any required tag is missing
        assert task.matches_context(["home", "office"]) is False
        assert task.matches_context(["office"]) is False
        
        # Empty requirements should match
        assert task.matches_context([]) is True
    
    def test_energy_level_suitability(self):
        """Test energy level matching."""
        low_energy_task = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="Low Energy Task",
            energy_level="low"
        )
        
        high_energy_task = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="High Energy Task",
            energy_level="high"
        )
        
        # Low energy task suitable for all energy levels
        assert low_energy_task.is_suitable_for_energy_level("low") is True
        assert low_energy_task.is_suitable_for_energy_level("medium") is True
        assert low_energy_task.is_suitable_for_energy_level("high") is True
        
        # High energy task only suitable for high energy
        assert high_energy_task.is_suitable_for_energy_level("low") is False
        assert high_energy_task.is_suitable_for_energy_level("medium") is False
        assert high_energy_task.is_suitable_for_energy_level("high") is True
    
    def test_urgency_score(self):
        """Test urgency score calculation."""
        # Base priority scores
        low_priority_task = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="Low Priority",
            priority="low"
        )
        assert low_priority_task.get_urgency_score() == 1
        
        urgent_task = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="Urgent Task",
            priority="urgent"
        )
        assert urgent_task.get_urgency_score() == 4
        
        # Due date affects urgency
        overdue_task = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="Overdue Task",
            priority="medium",
            due_date=datetime.utcnow() - timedelta(hours=1)
        )
        assert overdue_task.get_urgency_score() == 7  # 2 + 5 for overdue
        
        due_today_task = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="Due Today",
            priority="medium",
            due_date=datetime.utcnow() + timedelta(hours=2)
        )
        assert due_today_task.get_urgency_score() == 5  # 2 + 3 for due today
    
    def test_task_representation(self):
        """Test task string representation."""
        task = Task(
            user_id="550e8400-e29b-41d4-a716-************",
            title="Test Task",
            status="pending",
            energy_level="medium"
        )
        
        repr_str = repr(task)
        assert "Test Task" in repr_str
        assert "pending" in repr_str
        assert "medium" in repr_str
=======
from uuid import uuid4

from app.models.task import Task
from tests.utils import ADHDTestAssertions, TestDataValidator


class TestTaskModel:
    """Test suite for Task model basic functionality."""
    
    def test_task_creation_with_basic_fields(self):
        """Test creating a task with basic required fields."""
        user_id = uuid4()
        task = Task(
            user_id=user_id,
            title="Test Task",
            description="A test task for ADHD users",
            priority="medium"
        )
        
        assert task.user_id == user_id
        assert task.title == "Test Task"
        assert task.description == "A test task for ADHD users"
        assert task.priority == "medium"
        assert task.status == "pending"  # Default value
    
    def test_task_default_values(self):
        """Test that task model has appropriate default values."""
        task = Task(
            user_id=uuid4(),
            title="Default Values Task"
        )
        
        # Test ADHD-specific defaults
        assert task.energy_level == "medium"
        assert task.status == "pending"
        assert task.priority == "medium"
        assert task.is_chunked is False
        
        # Test optional fields
        assert task.estimated_duration is None
        assert task.actual_duration is None
        assert task.context_tags is None
    
    def test_task_id_generation(self):
        """Test that task ID is automatically generated as UUID."""
        task = Task(
            user_id=uuid4(),
            title="UUID Test Task"
        )
        
        assert task.id is not None
        assert isinstance(task.id, type(uuid4()))
    
    def test_task_timestamps(self):
        """Test that timestamps are properly set."""
        task = Task(
            user_id=uuid4(),
            title="Timestamp Test Task"
        )
        
        # Timestamps should be set automatically
        assert task.created_at is not None
        assert task.updated_at is not None
        assert isinstance(task.created_at, datetime)
        assert isinstance(task.updated_at, datetime)


class TestTaskADHDFeatures:
    """Test suite for ADHD-specific Task model features."""
    
    def test_energy_level_field(self):
        """Test energy level field functionality."""
        valid_levels = ["low", "medium", "high"]
        
        for level in valid_levels:
            task = Task(
                user_id=uuid4(),
                title=f"Energy {level} Task",
                energy_level=level
            )
            assert task.energy_level == level
    
    def test_context_tags_functionality(self):
        """Test context tags for ADHD filtering."""
        context_tags = ["home", "computer", "phone", "creative"]
        
        task = Task(
            user_id=uuid4(),
            title="Context Tags Task",
            context_tags=context_tags
        )
        
        assert task.context_tags == context_tags
        assert "home" in task.context_tags
        assert "computer" in task.context_tags
        assert len(task.context_tags) == 4
    
    def test_time_estimation_fields(self):
        """Test time estimation fields for ADHD time tracking."""
        task = Task(
            user_id=uuid4(),
            title="Time Estimation Task",
            estimated_duration=60,  # 1 hour
            actual_duration=90      # 1.5 hours (common underestimation)
        )
        
        assert task.estimated_duration == 60
        assert task.actual_duration == 90
        
        # Test time estimation accuracy calculation
        accuracy = task.actual_duration / task.estimated_duration
        assert accuracy == 1.5  # 50% underestimation
    
    def test_priority_levels(self):
        """Test priority level functionality."""
        valid_priorities = ["low", "medium", "high", "urgent"]
        
        for priority in valid_priorities:
            task = Task(
                user_id=uuid4(),
                title=f"Priority {priority} Task",
                priority=priority
            )
            assert task.priority == priority
    
    def test_task_status_progression(self):
        """Test task status progression through workflow."""
        task = Task(
            user_id=uuid4(),
            title="Status Progression Task"
        )
        
        # Initial status
        assert task.status == "pending"
        
        # Progress through statuses
        valid_statuses = ["pending", "in_progress", "completed", "cancelled"]
        for status in valid_statuses:
            task.status = status
            assert task.status == status
    
    def test_chunking_functionality(self):
        """Test AI chunking support for large tasks."""
        large_task = Task(
            user_id=uuid4(),
            title="Large Project Task",
            estimated_duration=240,  # 4 hours
            is_chunked=False
        )
        
        assert large_task.is_chunked is False
        assert large_task.estimated_duration == 240
        
        # Simulate chunking
        large_task.is_chunked = True
        assert large_task.is_chunked is True


class TestTaskADHDValidation:
    """Test suite for ADHD-specific task validation."""
    
    def test_task_duration_adhd_appropriateness(self):
        """Test task duration validation for ADHD users."""
        # Short task (ADHD-friendly)
        short_task = Task(
            user_id=uuid4(),
            title="Short Task",
            estimated_duration=30
        )
        
        ADHDTestAssertions.assert_task_duration_appropriate_for_adhd(
            short_task, 
            max_duration=90
        )
        
        # Long task that should be chunked
        long_task = Task(
            user_id=uuid4(),
            title="Long Task",
            estimated_duration=120,
            is_chunked=True,
            subtasks=["Subtask 1", "Subtask 2"]  # Mock subtasks
        )
        
        # This should pass because task is chunked
        ADHDTestAssertions.assert_task_duration_appropriate_for_adhd(
            long_task,
            chunking_threshold=60
        )
    
    def test_energy_level_matching(self):
        """Test energy level matching for ADHD task selection."""
        low_energy_task = Task(
            user_id=uuid4(),
            title="Low Energy Task",
            energy_level="low",
            estimated_duration=15
        )
        
        high_energy_task = Task(
            user_id=uuid4(),
            title="High Energy Task", 
            energy_level="high",
            estimated_duration=90
        )
        
        # Test energy level appropriateness
        ADHDTestAssertions.assert_energy_level_appropriate(
            low_energy_task, 
            "low", 
            tolerance="strict"
        )
        
        ADHDTestAssertions.assert_energy_level_appropriate(
            high_energy_task, 
            "high", 
            tolerance="strict"
        )
    
    def test_time_estimation_tracking(self):
        """Test time estimation tracking for ADHD learning."""
        task = Task(
            user_id=uuid4(),
            title="Time Tracking Task",
            estimated_duration=45
        )
        
        # Test initial estimation tracking
        ADHDTestAssertions.assert_time_estimation_tracking(task)
        
        # Test with actual duration (underestimation scenario)
        actual_duration = 75  # 75 minutes actual vs 45 estimated
        ADHDTestAssertions.assert_time_estimation_tracking(
            task, 
            actual_duration
        )
        
        # Verify accuracy calculation
        expected_accuracy = actual_duration / task.estimated_duration
        assert task.time_estimation_accuracy == expected_accuracy
    
    def test_task_adhd_compliance_validation(self):
        """Test comprehensive ADHD compliance validation."""
        # Create a well-formed ADHD task
        good_task = Task(
            user_id=uuid4(),
            title="Well-Formed ADHD Task",
            description="Clear, actionable task description",
            estimated_duration=45,
            energy_level="medium",
            priority="medium",
            context_tags=["computer", "focus"]
        )
        
        issues = TestDataValidator.validate_task_adhd_compliance(good_task)
        assert len(issues) == 0  # Should have no issues
        
        # Create a poorly-formed task
        bad_task = Task(
            user_id=uuid4(),
            title="Poorly-Formed Task",
            estimated_duration=300,  # 5 hours - too long
            # Missing energy_level, context_tags, priority
        )
        
        issues = TestDataValidator.validate_task_adhd_compliance(bad_task)
        assert len(issues) > 0  # Should have multiple issues
        assert any("duration exceeds" in issue for issue in issues)


class TestTaskModelMethods:
    """Test suite for Task model methods and properties."""
    
    def test_task_string_representation(self):
        """Test task model string representation."""
        task = Task(
            user_id=uuid4(),
            title="Representation Test Task",
            priority="high"
        )
        
        repr_str = repr(task)
        assert "Task" in repr_str
        assert str(task.id) in repr_str
    
    def test_task_soft_delete_functionality(self):
        """Test soft delete functionality for ADHD users."""
        task = Task(
            user_id=uuid4(),
            title="Soft Delete Test Task"
        )
        
        # Initially not deleted
        assert task.is_deleted is False
        assert task.deleted_at is None
        
        # Soft delete
        task.soft_delete()
        assert task.is_deleted is True
        assert task.deleted_at is not None
        
        # Restore
        task.restore()
        assert task.is_deleted is False
        assert task.deleted_at is None
    
    def test_urgency_score_calculation(self):
        """Test urgency score calculation for ADHD prioritization."""
        # High priority, due soon
        urgent_task = Task(
            user_id=uuid4(),
            title="Urgent Task",
            priority="urgent",
            due_date=datetime.utcnow() + timedelta(hours=2)
        )
        
        urgency_score = urgent_task.calculate_urgency_score()
        assert urgency_score > 3.0  # Should be high urgency
        
        # Low priority, due later
        low_urgency_task = Task(
            user_id=uuid4(),
            title="Low Urgency Task",
            priority="low",
            due_date=datetime.utcnow() + timedelta(days=7)
        )
        
        low_urgency_score = low_urgency_task.calculate_urgency_score()
        assert low_urgency_score < urgency_score  # Should be lower
    
    def test_duration_accuracy_property(self):
        """Test duration accuracy property for ADHD learning."""
        task = Task(
            user_id=uuid4(),
            title="Duration Accuracy Task",
            estimated_duration=60,
            actual_duration=90
        )
        
        accuracy = task.duration_accuracy
        assert accuracy == 0.67  # 60/90 = 0.67 (overestimated)
        
        # Test with no actual duration
        incomplete_task = Task(
            user_id=uuid4(),
            title="Incomplete Task",
            estimated_duration=30
        )
        
        assert incomplete_task.duration_accuracy is None


class TestTaskADHDIntegration:
    """Integration tests for ADHD-specific task features."""
    
    def test_complete_adhd_task_profile(self):
        """Test creating a complete ADHD-optimized task."""
        adhd_task = Task(
            user_id=uuid4(),
            title="Complete ADHD Task",
            description="Detailed task description with clear action steps",
            priority="medium",
            energy_level="medium",
            estimated_duration=45,
            context_tags=["computer", "focus", "creative"],
            due_date=datetime.utcnow() + timedelta(days=2),
            is_chunked=False
        )
        
        # Validate complete task profile
        assert adhd_task.title == "Complete ADHD Task"
        assert adhd_task.energy_level == "medium"
        assert adhd_task.estimated_duration == 45
        assert "computer" in adhd_task.context_tags
        assert adhd_task.priority == "medium"
        
        # Validate ADHD compliance
        issues = TestDataValidator.validate_task_adhd_compliance(adhd_task)
        assert len(issues) == 0  # Should be fully compliant
    
    def test_task_chunking_scenario(self):
        """Test task chunking scenario for large projects."""
        large_project = Task(
            user_id=uuid4(),
            title="Large Project - Website Redesign",
            description="Complete website redesign with multiple phases",
            estimated_duration=240,  # 4 hours
            energy_level="high",
            priority="high",
            context_tags=["computer", "creative", "focus"],
            is_chunked=True
        )
        
        # Simulate chunked subtasks
        subtasks = [
            "Research design trends",
            "Create wireframes", 
            "Design mockups",
            "Implement frontend",
            "Test and refine"
        ]
        
        # Validate chunking
        assert large_project.is_chunked is True
        assert large_project.estimated_duration > 120  # Requires chunking
        assert len(subtasks) <= 5  # ADHD-friendly chunk count
>>>>>>> origin/master
