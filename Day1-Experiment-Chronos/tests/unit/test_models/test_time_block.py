"""
Unit tests for TimeBlock model.

Tests ADHD-specific time blocking features including visual scheduling,
time blindness support, and energy-aware time allocation.
"""

import pytest
from datetime import datetime, timedelta, time
from unittest.mock import patch

from app.models.time_block import TimeBlock, BlockType, BlockStatus
from app.models.user import User
from app.models.task import Task
from tests.factories import UserFactory, TaskFactory, TimeBlockFactory


@pytest.mark.unit
class TestTimeBlockModel:
    """Test TimeBlock model functionality."""

    async def test_create_time_block(self, db_session):
        """Test creating a basic time block."""
        user = await UserFactory.create(db_session)
        task = await TaskFactory.create(db_session, user_id=user.id)
        
        start_time = datetime.utcnow().replace(hour=9, minute=0, second=0, microsecond=0)
        end_time = start_time + timedelta(hours=1)
        
        time_block = TimeBlock(
            user_id=user.id,
            task_id=task.id,
            title="Morning Focus Block",
            start_time=start_time,
            end_time=end_time,
            block_type=BlockType.FOCUS,
            energy_level="high"
        )
        
        db_session.add(time_block)
        await db_session.commit()
        await db_session.refresh(time_block)
        
        assert time_block.id is not None
        assert time_block.duration_minutes == 60
        assert time_block.block_type == BlockType.FOCUS
        assert time_block.status == BlockStatus.SCHEDULED

    async def test_time_block_validation(self, db_session):
        """Test time block validation rules."""
        user = await UserFactory.create(db_session)
        
        start_time = datetime.utcnow().replace(hour=9, minute=0)
        
        # End time must be after start time
        with pytest.raises(ValueError, match="End time must be after start time"):
            TimeBlock(
                user_id=user.id,
                title="Invalid Block",
                start_time=start_time,
                end_time=start_time - timedelta(hours=1)
            )
        
        # Duration should be reasonable (not too short or too long)
        with pytest.raises(ValueError, match="Time blocks should be between"):
            TimeBlock(
                user_id=user.id,
                title="Too Short Block",
                start_time=start_time,
                end_time=start_time + timedelta(minutes=2)  # Too short
            )

    async def test_energy_level_validation(self, db_session):
        """Test energy level validation and defaults."""
        user = await UserFactory.create(db_session)
        
        # Valid energy levels
        for energy in ["low", "medium", "high"]:
            time_block = TimeBlock(
                user_id=user.id,
                title=f"{energy.title()} Energy Block",
                start_time=datetime.utcnow(),
                end_time=datetime.utcnow() + timedelta(hours=1),
                energy_level=energy
            )
            assert time_block.energy_level == energy
        
        # Invalid energy level
        with pytest.raises(ValueError, match="Energy level must be"):
            TimeBlock(
                user_id=user.id,
                title="Invalid Energy Block",
                start_time=datetime.utcnow(),
                end_time=datetime.utcnow() + timedelta(hours=1),
                energy_level="extreme"
            )

    async def test_conflict_detection(self, db_session):
        """Test time block conflict detection."""
        user = await UserFactory.create(db_session)
        
        # Create first time block
        start_time = datetime.utcnow().replace(hour=9, minute=0)
        existing_block = await TimeBlockFactory.create(
            db_session,
            user_id=user.id,
            start_time=start_time,
            end_time=start_time + timedelta(hours=2)
        )
        
        # Create overlapping block
        overlapping_block = TimeBlock(
            user_id=user.id,
            title="Overlapping Block",
            start_time=start_time + timedelta(minutes=30),
            end_time=start_time + timedelta(hours=3)
        )
        
        conflicts = overlapping_block.check_conflicts([existing_block])
        assert len(conflicts) == 1
        assert conflicts[0].id == existing_block.id

    async def test_buffer_time_calculation(self, db_session):
        """Test buffer time calculation for ADHD users."""
        adhd_user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "buffer_time_percentage": 25,
                "transition_time_minutes": 10
            }
        )
        
        time_block = TimeBlock(
            user_id=adhd_user.id,
            title="Task with Buffer",
            start_time=datetime.utcnow(),
            end_time=datetime.utcnow() + timedelta(hours=1),
            include_buffer=True
        )
        
        buffer_info = time_block.calculate_buffer_time()
        
        assert buffer_info["buffer_minutes"] == 15  # 25% of 60 minutes
        assert buffer_info["transition_minutes"] == 10
        assert buffer_info["total_buffer"] == 25

    async def test_visual_representation(self, db_session):
        """Test visual representation data for ADHD-friendly display."""
        time_block = await TimeBlockFactory.create(
            db_session,
            block_type=BlockType.FOCUS,
            energy_level="high",
            start_time=datetime.utcnow().replace(hour=9, minute=0),
            end_time=datetime.utcnow().replace(hour=10, minute=30)
        )
        
        visual_data = time_block.get_visual_representation()
        
        assert visual_data["color"] is not None
        assert visual_data["height_pixels"] == 90  # 1.5 hours * 60 pixels/hour
        assert visual_data["position_top"] >= 0
        assert "energy_indicator" in visual_data
        assert visual_data["accessibility_label"] is not None

    async def test_time_blindness_support(self, db_session):
        """Test time blindness support features."""
        time_block = await TimeBlockFactory.create(
            db_session,
            start_time=datetime.utcnow() + timedelta(hours=1)
        )
        
        # Test time awareness features
        awareness_data = time_block.get_time_awareness_data()
        
        assert "time_until_start" in awareness_data
        assert "duration_description" in awareness_data
        assert "visual_time_indicator" in awareness_data
        assert awareness_data["time_until_start"]["minutes"] == 60

    async def test_start_time_block(self, db_session):
        """Test starting a time block."""
        time_block = await TimeBlockFactory.create(
            db_session,
            status=BlockStatus.SCHEDULED,
            start_time=datetime.utcnow() - timedelta(minutes=5)
        )
        
        actual_start = datetime.utcnow()
        with patch('app.models.time_block.datetime') as mock_datetime:
            mock_datetime.utcnow.return_value = actual_start
            
            time_block.start()
        
        assert time_block.status == BlockStatus.IN_PROGRESS
        assert time_block.actual_start_time == actual_start

    async def test_complete_time_block(self, db_session):
        """Test completing a time block."""
        start_time = datetime.utcnow() - timedelta(minutes=45)
        time_block = await TimeBlockFactory.create(
            db_session,
            status=BlockStatus.IN_PROGRESS,
            actual_start_time=start_time,
            end_time=start_time + timedelta(hours=1)
        )
        
        completion_time = start_time + timedelta(minutes=50)
        with patch('app.models.time_block.datetime') as mock_datetime:
            mock_datetime.utcnow.return_value = completion_time
            
            result = time_block.complete(completion_notes="Finished early, felt productive")
        
        assert time_block.status == BlockStatus.COMPLETED
        assert time_block.actual_end_time == completion_time
        assert time_block.completion_notes == "Finished early, felt productive"
        assert result["actual_duration"] == 50

    async def test_reschedule_time_block(self, db_session):
        """Test rescheduling a time block."""
        original_start = datetime.utcnow() + timedelta(hours=2)
        time_block = await TimeBlockFactory.create(
            db_session,
            start_time=original_start,
            end_time=original_start + timedelta(hours=1)
        )
        
        new_start = original_start + timedelta(hours=1)
        time_block.reschedule(new_start, new_start + timedelta(hours=1))
        
        assert time_block.start_time == new_start
        assert time_block.end_time == new_start + timedelta(hours=1)
        assert time_block.reschedule_count == 1

    async def test_energy_matching_validation(self, db_session):
        """Test energy level matching with user patterns."""
        user = await UserFactory.create(
            db_session,
            preferences={
                "energy_patterns": {
                    "morning": "high",
                    "afternoon": "medium", 
                    "evening": "low"
                }
            }
        )
        
        # Morning high-energy block (should match)
        morning_block = TimeBlock(
            user_id=user.id,
            title="Morning Focus",
            start_time=datetime.utcnow().replace(hour=9, minute=0),
            end_time=datetime.utcnow().replace(hour=10, minute=0),
            energy_level="high"
        )
        
        validation = morning_block.validate_energy_matching(user.preferences)
        assert validation["is_optimal"] is True
        
        # Evening high-energy block (should warn)
        evening_block = TimeBlock(
            user_id=user.id,
            title="Evening Focus",
            start_time=datetime.utcnow().replace(hour=20, minute=0),
            end_time=datetime.utcnow().replace(hour=21, minute=0),
            energy_level="high"
        )
        
        validation = evening_block.validate_energy_matching(user.preferences)
        assert validation["is_optimal"] is False
        assert "warning" in validation

    async def test_break_recommendations(self, db_session):
        """Test break recommendations for long time blocks."""
        # Long focus block should recommend breaks
        long_block = TimeBlock(
            user_id=(await UserFactory.create(db_session)).id,
            title="Long Focus Session",
            start_time=datetime.utcnow(),
            end_time=datetime.utcnow() + timedelta(hours=3),
            block_type=BlockType.FOCUS
        )
        
        break_recommendations = long_block.get_break_recommendations()
        
        assert len(break_recommendations) > 0
        assert break_recommendations[0]["time"] == datetime.utcnow() + timedelta(hours=1)
        assert break_recommendations[0]["duration"] == 15

    async def test_accessibility_features(self, db_session):
        """Test accessibility features for neurodivergent users."""
        time_block = await TimeBlockFactory.create(
            db_session,
            title="Important Meeting",
            block_type=BlockType.MEETING,
            energy_level="medium"
        )
        
        accessibility = time_block.get_accessibility_features()
        
        assert "screen_reader_text" in accessibility
        assert "high_contrast_colors" in accessibility
        assert "focus_indicators" in accessibility
        assert accessibility["cognitive_load_level"] in ["low", "medium", "high"]
