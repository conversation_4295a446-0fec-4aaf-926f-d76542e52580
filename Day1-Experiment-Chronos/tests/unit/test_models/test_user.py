"""
Unit tests for User model with ADHD-specific features.

This module tests the User model's ADHD-focused functionality including
preferences validation, energy patterns, and neurodivergent-specific features.
"""

import pytest
from datetime import datetime
from uuid import uuid4

from chronos.app.models.user import User
from app.models.user import User as AppUser


class TestUserModel:
    """Test User model functionality."""

    def test_user_creation(self):
        """Test basic user creation."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            full_name="Test User"
        )

        assert user.email == "<EMAIL>"
        assert user.hashed_password == "hashed_password"
        assert user.full_name == "Test User"
        assert user.is_active is True
        assert user.is_verified is False
        assert user.adhd_diagnosed is False
        assert user.timezone == "UTC"
        assert user.preferences == {}
    
    def test_adhd_user_creation(self):
        """Test ADHD user creation with specific fields."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            adhd_diagnosed=True,
            preferences={
                "energy_patterns": {"morning": "high", "afternoon": "low"},
                "notification_preferences": {"persistent": True}
            }
        )
        
        assert user.adhd_diagnosed is True
        assert user.is_adhd_user() is True
        assert user.get_preference("energy_patterns.morning") == "high"
        assert user.get_preference("notification_preferences.persistent") is True
    
    def test_preference_management(self):
        """Test preference getting and setting."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        
        # Test setting nested preference
        user.set_preference("energy_patterns.morning", "high")
        assert user.get_preference("energy_patterns.morning") == "high"
        
        # Test setting top-level preference
        user.set_preference("theme", "dark")
        assert user.get_preference("theme") == "dark"
        
        # Test default value
        assert user.get_preference("nonexistent", "default") == "default"
    
    def test_energy_patterns(self):
        """Test energy pattern management."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        
        # Test setting energy pattern
        user.set_energy_pattern("morning", "high")
        assert user.get_energy_pattern("morning") == "high"
        
        # Test default energy pattern
        assert user.get_energy_pattern("evening") == "medium"
    
    def test_notification_preferences(self):
        """Test ADHD-friendly notification preferences."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        
        prefs = user.get_notification_preferences()
        
        # Check ADHD-friendly defaults
        assert prefs["persistent"] is True
        assert prefs["gentle"] is True
        assert prefs["staggered"] is True
        assert prefs["focus_mode_respect"] is True
        assert prefs["max_per_hour"] == 5
    
    def test_chunking_preferences(self):
        """Test AI chunking preferences."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        
        prefs = user.get_chunking_preferences()
        
        # Check ADHD-friendly defaults
        assert prefs["default_size"] == "small"
        assert prefs["max_subtasks"] == 5
        assert prefs["include_time_estimates"] is True
        assert prefs["include_energy_levels"] is True
    
    def test_focus_preferences(self):
        """Test focus session preferences."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        
        prefs = user.get_focus_preferences()
        
        # Check ADHD-friendly defaults
        assert prefs["default_duration"] == 25
        assert prefs["break_duration"] == 5
        assert prefs["hyperfocus_warning"] == 120
        assert prefs["gentle_transitions"] is True
    
    def test_accessibility_preferences(self):
        """Test accessibility preferences."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        
        prefs = user.get_accessibility_preferences()
        
        # Check ADHD-helpful defaults
        assert prefs["color_coding"] is True
        assert prefs["visual_timers"] is True
        assert prefs["progress_indicators"] is True
    
    def test_is_adhd_user(self):
        """Test ADHD user detection."""
        # User with diagnosis
        adhd_user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            adhd_diagnosed=True
        )
        assert adhd_user.is_adhd_user() is True
        
        # User with ADHD support mode
        support_user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            preferences={"adhd_support_mode": True}
        )
        assert support_user.is_adhd_user() is True
        
        # Regular user
        regular_user = User(
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        assert regular_user.is_adhd_user() is False
    
    def test_user_representation(self):
        """Test user string representation."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            adhd_diagnosed=True
        )
        
        repr_str = repr(user)
        assert "<EMAIL>" in repr_str
        assert "adhd_diagnosed=True" in repr_str
=======
Unit tests for User model with ADHD-specific features.

This module tests the User model's ADHD-focused functionality including
preferences validation, energy patterns, and neurodivergent-specific features.
"""

import pytest
from datetime import datetime
from uuid import uuid4

from app.models.user import User
from tests.utils import TestDataValidator, assert_adhd_feature_enabled


class TestUserModel:
    """Test suite for User model basic functionality."""
    
    def test_user_creation_with_basic_fields(self):
        """Test creating a user with basic required fields."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password_123",
            first_name="Test",
            last_name="User"
        )
        
        assert user.email == "<EMAIL>"
        assert user.hashed_password == "hashed_password_123"
        assert user.first_name == "Test"
        assert user.last_name == "User"
        assert user.is_active is True  # Default value
        assert user.is_verified is False  # Default value
        assert user.adhd_diagnosis is False  # Default value
    
    def test_user_default_values(self):
        """Test that user model has appropriate default values."""
        user = User(
            email="<EMAIL>",
            hashed_password="password"
        )
        
        # Test ADHD-specific defaults
        assert user.preferred_chunk_size == "small"
        assert user.default_energy_level == "medium"
        assert user.adhd_diagnosis is False
        
        # Test account defaults
        assert user.is_active is True
        assert user.is_verified is False
    
    def test_user_id_generation(self):
        """Test that user ID is automatically generated as UUID."""
        user = User(
            email="<EMAIL>",
            hashed_password="password"
        )
        
        assert user.id is not None
        assert isinstance(user.id, type(uuid4()))
    
    def test_user_timestamps(self):
        """Test that timestamps are properly set."""
        user = User(
            email="<EMAIL>",
            hashed_password="password"
        )
        
        # Timestamps should be set automatically
        assert user.created_at is not None
        assert user.updated_at is not None
        assert isinstance(user.created_at, datetime)
        assert isinstance(user.updated_at, datetime)


class TestUserADHDFeatures:
    """Test suite for ADHD-specific User model features."""
    
    def test_adhd_diagnosis_field(self):
        """Test ADHD diagnosis field functionality."""
        # Test user without ADHD diagnosis
        neurotypical_user = User(
            email="<EMAIL>",
            hashed_password="password",
            adhd_diagnosis=False
        )
        assert neurotypical_user.adhd_diagnosis is False
        
        # Test user with ADHD diagnosis
        adhd_user = User(
            email="<EMAIL>",
            hashed_password="password",
            adhd_diagnosis=True
        )
        assert adhd_user.adhd_diagnosis is True
    
    def test_preferred_chunk_size_validation(self):
        """Test preferred chunk size field validation."""
        valid_sizes = ["small", "medium", "large"]
        
        for size in valid_sizes:
            user = User(
                email=f"chunk_{size}@chronos.test",
                hashed_password="password",
                preferred_chunk_size=size
            )
            assert user.preferred_chunk_size == size
    
    def test_energy_level_validation(self):
        """Test energy level field validation."""
        valid_levels = ["low", "medium", "high"]
        
        for level in valid_levels:
            user = User(
                email=f"energy_{level}@chronos.test",
                hashed_password="password",
                default_energy_level=level
            )
            assert user.default_energy_level == level
    
    def test_adhd_user_optimal_defaults(self):
        """Test that ADHD users get optimal default settings."""
        adhd_user = User(
            email="<EMAIL>",
            hashed_password="password",
            adhd_diagnosis=True,
            preferred_chunk_size="small"  # ADHD-optimized
        )
        
        assert adhd_user.adhd_diagnosis is True
        assert adhd_user.preferred_chunk_size == "small"
        # ADHD users benefit from smaller chunks
    
    def test_user_preferences_structure(self):
        """Test user preferences JSON field structure."""
        preferences = {
            "energy_patterns": {
                "morning": "high",
                "afternoon": "medium",
                "evening": "low"
            },
            "notification_preferences": {
                "persistent": True,
                "gentle": True,
                "frequency": "high"
            },
            "chunking_preferences": {
                "default_size": "small",
                "max_subtasks": 5,
                "auto_chunk_threshold": 45
            }
        }
        
        user = User(
            email="<EMAIL>",
            hashed_password="password",
            adhd_diagnosis=True,
            preferences=preferences
        )
        
        assert user.preferences == preferences
        assert TestDataValidator.validate_adhd_user_preferences(preferences)


class TestUserADHDPreferences:
    """Test suite for ADHD-specific user preferences."""
    
    def test_energy_patterns_preferences(self):
        """Test energy patterns preference validation."""
        energy_patterns = {
            "morning": "high",
            "afternoon": "medium", 
            "evening": "low",
            "late_night": "variable"
        }
        
        user = User(
            email="<EMAIL>",
            hashed_password="password",
            preferences={"energy_patterns": energy_patterns}
        )
        
        stored_patterns = user.preferences["energy_patterns"]
        assert stored_patterns["morning"] == "high"
        assert stored_patterns["afternoon"] == "medium"
        assert stored_patterns["evening"] == "low"
    
    def test_notification_preferences_adhd_optimized(self):
        """Test ADHD-optimized notification preferences."""
        notification_prefs = {
            "persistent": True,
            "gentle": True,
            "frequency": "high",
            "sound_enabled": False,
            "visual_emphasis": True
        }
        
        user = User(
            email="<EMAIL>",
            hashed_password="password",
            adhd_diagnosis=True,
            preferences={"notification_preferences": notification_prefs}
        )
        
        # Validate ADHD-specific notification features
        assert_adhd_feature_enabled(
            "persistent_notifications", 
            user.preferences, 
            True
        )
        assert_adhd_feature_enabled(
            "gentle_notifications", 
            user.preferences, 
            True
        )
    
    def test_chunking_preferences_validation(self):
        """Test task chunking preferences for ADHD users."""
        chunking_prefs = {
            "default_size": "small",
            "max_subtasks": 5,
            "auto_chunk_threshold": 30,  # 30 minutes
            "include_time_estimates": True
        }
        
        user = User(
            email="<EMAIL>",
            hashed_password="password",
            adhd_diagnosis=True,
            preferences={"chunking_preferences": chunking_prefs}
        )
        
        stored_prefs = user.preferences["chunking_preferences"]
        assert stored_prefs["default_size"] == "small"
        assert stored_prefs["max_subtasks"] == 5
        assert stored_prefs["auto_chunk_threshold"] == 30
        assert stored_prefs["include_time_estimates"] is True
    
    def test_focus_preferences_adhd_specific(self):
        """Test focus session preferences for ADHD users."""
        focus_prefs = {
            "pomodoro_length": 25,  # Standard for ADHD
            "break_length": 5,
            "long_break_length": 15,
            "auto_start_breaks": True,  # Important for ADHD
            "hyperfocus_protection": True
        }
        
        user = User(
            email="<EMAIL>",
            hashed_password="password",
            adhd_diagnosis=True,
            preferences={"focus_preferences": focus_prefs}
        )
        
        stored_prefs = user.preferences["focus_preferences"]
        assert stored_prefs["pomodoro_length"] == 25
        assert stored_prefs["auto_start_breaks"] is True
        assert stored_prefs["hyperfocus_protection"] is True


class TestUserModelMethods:
    """Test suite for User model methods and properties."""
    
    def test_user_string_representation(self):
        """Test user model string representation."""
        user = User(
            email="<EMAIL>",
            hashed_password="password",
            first_name="Test",
            last_name="User"
        )
        
        repr_str = repr(user)
        assert "User" in repr_str
        assert str(user.id) in repr_str
    
    def test_user_soft_delete_functionality(self):
        """Test soft delete functionality for ADHD users."""
        user = User(
            email="<EMAIL>",
            hashed_password="password"
        )
        
        # Initially not deleted
        assert user.is_deleted is False
        assert user.deleted_at is None
        
        # Soft delete
        user.soft_delete()
        assert user.is_deleted is True
        assert user.deleted_at is not None
        
        # Restore
        user.restore()
        assert user.is_deleted is False
        assert user.deleted_at is None
    
    def test_user_full_name_property(self):
        """Test full name property construction."""
        user = User(
            email="<EMAIL>",
            hashed_password="password",
            first_name="John",
            last_name="Doe"
        )
        
        # Test that we can access individual name components
        assert user.first_name == "John"
        assert user.last_name == "Doe"
    
    def test_user_preferences_default_empty_dict(self):
        """Test that preferences defaults to empty dict."""
        user = User(
            email="<EMAIL>",
            hashed_password="password"
        )
        
        # Should have empty preferences by default
        assert user.preferences == {}
        
        # Should be able to add preferences
        user.preferences = {"test": "value"}
        assert user.preferences["test"] == "value"


class TestUserADHDIntegration:
    """Integration tests for ADHD-specific user features."""
    
    def test_complete_adhd_user_profile(self):
        """Test creating a complete ADHD user profile."""
        complete_preferences = {
            "energy_patterns": {
                "morning": "high",
                "afternoon": "medium",
                "evening": "low"
            },
            "notification_preferences": {
                "persistent": True,
                "gentle": True,
                "frequency": "high"
            },
            "chunking_preferences": {
                "default_size": "small",
                "max_subtasks": 5,
                "auto_chunk_threshold": 45
            },
            "focus_preferences": {
                "pomodoro_length": 25,
                "break_length": 5,
                "auto_start_breaks": True
            },
            "scheduling_preferences": {
                "buffer_time": 15,
                "energy_matching": True,
                "avoid_context_switching": True
            }
        }
        
        adhd_user = User(
            email="<EMAIL>",
            hashed_password="secure_password",
            first_name="ADHD",
            last_name="User",
            adhd_diagnosis=True,
            preferred_chunk_size="small",
            default_energy_level="medium",
            preferences=complete_preferences
        )
        
        # Validate complete profile
        assert adhd_user.adhd_diagnosis is True
        assert adhd_user.preferred_chunk_size == "small"
        assert TestDataValidator.validate_adhd_user_preferences(
            adhd_user.preferences
        )
        
        # Test specific ADHD features
        assert_adhd_feature_enabled(
            "persistent_notifications", 
            adhd_user.preferences
        )
        assert_adhd_feature_enabled(
            "gentle_notifications", 
            adhd_user.preferences
        )
        assert_adhd_feature_enabled(
            "energy_matching", 
            adhd_user.preferences
        )
>>>>>>> origin/master
