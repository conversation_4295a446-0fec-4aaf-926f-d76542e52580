"""
Unit tests for AI utility functions.

This module tests AI prompt templates, response validation,
and ADHD-specific AI processing utilities.
"""

import pytest
from chronos.app.utils.ai_utils import (
    validate_ai_response,
    extract_action_verbs,
    suggest_context_tags,
    estimate_task_complexity,
    CHUNKING_PROMPTS
)
from chronos.app.core.exceptions import ValidationError


class TestAIUtils:
    """Test cases for AI utility functions."""
    
    def test_validate_ai_response_valid(self):
        """Test validation of valid AI response."""
        chunk = {
            "title": "Create presentation outline",
            "description": "Draft the main sections and key points",
            "estimated_duration": 30,
            "energy_level": "medium",
            "context_tags": ["work", "computer"],
            "priority": "high"
        }
        
        validated = validate_ai_response(chunk)
        
        assert validated["title"] == "Create presentation outline"
        assert validated["estimated_duration"] == 30
        assert validated["energy_level"] == "medium"
        assert validated["context_tags"] == ["work", "computer"]
        assert validated["priority"] == "high"
    
    def test_validate_ai_response_missing_title(self):
        """Test validation with missing required title."""
        chunk = {
            "description": "Some description",
            "estimated_duration": 30
        }
        
        with pytest.raises(ValidationError, match="Missing required field: title"):
            validate_ai_response(chunk)
    
    def test_validate_ai_response_empty_title(self):
        """Test validation with empty title."""
        chunk = {
            "title": "",
            "description": "Some description"
        }
        
        with pytest.raises(ValidationError, match="Title cannot be empty"):
            validate_ai_response(chunk)
    
    def test_validate_ai_response_invalid_energy_level(self):
        """Test validation with invalid energy level."""
        chunk = {
            "title": "Test task",
            "energy_level": "extreme"
        }
        
        validated = validate_ai_response(chunk)
        
        # Should default to medium for invalid energy level
        assert validated["energy_level"] == "medium"
    
    def test_validate_ai_response_invalid_duration(self):
        """Test validation with invalid duration."""
        chunk = {
            "title": "Test task",
            "estimated_duration": "not a number"
        }
        
        validated = validate_ai_response(chunk)
        
        # Should default to 15 minutes for invalid duration
        assert validated["estimated_duration"] == 15
    
    def test_validate_ai_response_duration_clamping(self):
        """Test duration clamping to reasonable ranges."""
        # Test too short duration
        chunk1 = {
            "title": "Test task",
            "estimated_duration": -5
        }
        validated1 = validate_ai_response(chunk1)
        assert validated1["estimated_duration"] == 5  # Clamped to minimum
        
        # Test too long duration
        chunk2 = {
            "title": "Test task",
            "estimated_duration": 2000  # Over 24 hours
        }
        validated2 = validate_ai_response(chunk2)
        assert validated2["estimated_duration"] == 1440  # Clamped to 24 hours
    
    def test_validate_ai_response_context_tags_cleanup(self):
        """Test context tags validation and cleanup."""
        chunk = {
            "title": "Test task",
            "context_tags": ["  Work  ", "COMPUTER", "home", "", "phone"]
        }
        
        validated = validate_ai_response(chunk)
        
        # Should be cleaned and lowercased
        assert validated["context_tags"] == ["work", "computer", "home", "phone"]
    
    def test_validate_ai_response_too_many_tags(self):
        """Test context tags limit enforcement."""
        chunk = {
            "title": "Test task",
            "context_tags": [f"tag{i}" for i in range(15)]  # 15 tags, limit is 10
        }
        
        validated = validate_ai_response(chunk)
        
        # Should be limited to 10 tags
        assert len(validated["context_tags"]) == 10
    
    def test_extract_action_verbs(self):
        """Test action verb extraction from task text."""
        text = "Create a presentation and send email to team"
        verbs = extract_action_verbs(text)
        
        assert "create" in verbs
        assert "send" in verbs
        assert "email" in verbs
    
    def test_suggest_context_tags(self):
        """Test context tag suggestions based on task content."""
        title = "Send email to client about project update"
        description = "Use computer to draft and send professional email"
        
        tags = suggest_context_tags(title, description)
        
        assert "email" in tags
        assert "computer" in tags
        assert "work" in tags
    
    def test_suggest_context_tags_home_context(self):
        """Test context tag suggestions for home tasks."""
        title = "Organize home office and file personal documents"
        
        tags = suggest_context_tags(title)
        
        assert "home" in tags
        assert "admin" in tags
    
    def test_estimate_task_complexity_simple(self):
        """Test complexity estimation for simple tasks."""
        title = "Quick email check"
        complexity = estimate_task_complexity(title)
        
        assert complexity == "simple"
    
    def test_estimate_task_complexity_complex(self):
        """Test complexity estimation for complex tasks."""
        title = "Complete comprehensive project analysis and system design"
        description = "This involves multiple phases including research, analysis, design, and documentation"
        
        complexity = estimate_task_complexity(title, description)
        
        assert complexity == "complex"
    
    def test_estimate_task_complexity_moderate(self):
        """Test complexity estimation for moderate tasks."""
        title = "Prepare meeting agenda"
        description = "Review previous notes and create agenda items"
        
        complexity = estimate_task_complexity(title, description)
        
        assert complexity == "moderate"
    
    def test_chunking_prompts_exist(self):
        """Test that all required chunking prompts exist."""
        required_sizes = ["small", "medium", "large"]
        
        for size in required_sizes:
            assert size in CHUNKING_PROMPTS
            assert isinstance(CHUNKING_PROMPTS[size], str)
            assert len(CHUNKING_PROMPTS[size]) > 100  # Substantial prompt
    
    def test_chunking_prompts_contain_placeholders(self):
        """Test that chunking prompts contain required placeholders."""
        required_placeholders = ["{title}", "{description}", "{context}"]
        
        for size, prompt in CHUNKING_PROMPTS.items():
            for placeholder in required_placeholders:
                assert placeholder in prompt, f"Missing {placeholder} in {size} prompt"
    
    def test_chunking_prompts_adhd_optimized(self):
        """Test that chunking prompts are ADHD-optimized."""
        adhd_keywords = ["adhd", "decision", "overwhelm", "specific", "concrete"]
        
        for size, prompt in CHUNKING_PROMPTS.items():
            prompt_lower = prompt.lower()
            # At least some ADHD-specific language should be present
            assert any(keyword in prompt_lower for keyword in adhd_keywords), \
                f"Prompt for {size} chunks lacks ADHD-specific language"
