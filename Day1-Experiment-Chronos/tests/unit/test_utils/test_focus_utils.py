"""
Unit tests for focus utilities.

This module tests ADHD-specific focus utilities including
break calculations, hyperfocus detection, and gentle reminders.
"""

import pytest

from chronos.app.utils.focus_utils import (
    calculate_optimal_break_duration,
    detect_hyperfocus_pattern,
    generate_gentle_reminder_message,
    get_default_focus_modes,
)


class TestFocusUtils:
    """Test cases for focus utility functions."""
    
    def test_calculate_optimal_break_duration_pomodoro(self):
        """Test break duration calculation for pomodoro sessions."""
        break_duration = calculate_optimal_break_duration(
            session_duration=25,
            session_type="pomodoro"
        )
        
        # 25 * 0.2 = 5 minutes
        assert break_duration == 5
    
    def test_calculate_optimal_break_duration_deep_work(self):
        """Test break duration calculation for deep work sessions."""
        break_duration = calculate_optimal_break_duration(
            session_duration=90,
            session_type="deep_work"
        )
        
        # 90 * 0.15 = 13.5, rounded to 13 or 14
        assert 13 <= break_duration <= 14
    
    def test_calculate_optimal_break_duration_with_preferences(self):
        """Test break duration calculation with user preferences."""
        user_preferences = {
            "break_ratio": 0.3,
            "min_break_duration": 10,
            "max_break_duration": 20
        }
        
        break_duration = calculate_optimal_break_duration(
            session_duration=30,
            session_type="custom",
            user_preferences=user_preferences
        )
        
        # 30 * 0.3 = 9, but min is 10
        assert break_duration == 10
    
    def test_calculate_optimal_break_duration_respects_limits(self):
        """Test that break duration respects min/max limits."""
        user_preferences = {
            "min_break_duration": 15,
            "max_break_duration": 20
        }
        
        # Test minimum limit
        short_break = calculate_optimal_break_duration(
            session_duration=10,  # Would normally give 2 minutes
            session_type="pomodoro",
            user_preferences=user_preferences
        )
        assert short_break == 15
        
        # Test maximum limit
        long_break = calculate_optimal_break_duration(
            session_duration=200,  # Would normally give 40 minutes
            session_type="pomodoro",
            user_preferences=user_preferences
        )
        assert long_break == 20
    
    def test_detect_hyperfocus_pattern_basic(self):
        """Test basic hyperfocus detection."""
        result = detect_hyperfocus_pattern(
            session_duration=150,  # 2.5 hours
            planned_duration=60    # 1 hour
        )
        
        assert result["is_hyperfocus"] is True
        assert result["confidence"] > 0.5
        assert "session_overrun" in result["indicators"]
        assert "extended_duration" in result["indicators"]
        assert result["recommended_action"] in ["gentle_reminder", "strong_break_suggestion"]
    
    def test_detect_hyperfocus_pattern_with_history(self):
        """Test hyperfocus detection with user history."""
        user_history = [
            {"duration": 30},
            {"duration": 45},
            {"duration": 25},
            {"duration": 35},
            {"duration": 40}
        ]
        
        result = detect_hyperfocus_pattern(
            session_duration=100,  # Much longer than average
            planned_duration=60,
            user_history=user_history
        )
        
        assert result["is_hyperfocus"] is True
        assert "above_average_pattern" in result["indicators"]
    
    def test_detect_hyperfocus_pattern_consecutive_sessions(self):
        """Test hyperfocus detection with consecutive long sessions."""
        user_history = [
            {"duration": 95},
            {"duration": 100},
            {"duration": 110},
            {"duration": 105},
            {"duration": 90}
        ]
        
        result = detect_hyperfocus_pattern(
            session_duration=80,
            planned_duration=60,
            user_history=user_history
        )
        
        assert "consecutive_long_sessions" in result["indicators"]
    
    def test_detect_hyperfocus_pattern_normal_session(self):
        """Test that normal sessions don't trigger hyperfocus detection."""
        result = detect_hyperfocus_pattern(
            session_duration=30,
            planned_duration=25
        )
        
        assert result["is_hyperfocus"] is False
        assert result["confidence"] < 0.5
        assert result["recommended_action"] == "continue_monitoring"
    
    def test_generate_gentle_reminder_message_break(self):
        """Test generating break reminder messages."""
        message = generate_gentle_reminder_message(
            reminder_type="break",
            session_duration=60
        )
        
        assert message["type"] == "break"
        assert "60" in message["message"]  # Duration should be mentioned
        assert "continue" in message["options"]
        assert "take_break" in message["options"]
        assert message["dismissible"] is True
    
    def test_generate_gentle_reminder_message_hydration(self):
        """Test generating hydration reminder messages."""
        message = generate_gentle_reminder_message(
            reminder_type="hydration",
            session_duration=45
        )
        
        assert message["type"] == "hydration"
        assert "water" in message["message"].lower()
        assert "done" in message["options"]
        assert "remind_later" in message["options"]
    
    def test_generate_gentle_reminder_message_with_preferences(self):
        """Test generating reminders with user preferences."""
        user_preferences = {
            "reminder_tone": "encouraging",
            "dismissible_reminders": False,
            "auto_dismiss_seconds": 60
        }
        
        message = generate_gentle_reminder_message(
            reminder_type="movement",
            session_duration=90,
            user_preferences=user_preferences
        )
        
        assert message["tone"] == "encouraging"
        assert message["dismissible"] is False
        assert message["auto_dismiss_seconds"] == 60
        # Encouraging tone should have more enthusiastic language
        assert any(word in message["message"].lower() for word in ["amazing", "great", "brilliant"])
    
    def test_generate_gentle_reminder_message_minimal_tone(self):
        """Test generating reminders with minimal tone."""
        user_preferences = {
            "reminder_tone": "minimal"
        }
        
        message = generate_gentle_reminder_message(
            reminder_type="eye_strain",
            session_duration=120,
            user_preferences=user_preferences
        )
        
        assert message["tone"] == "minimal"
        # Minimal tone should be brief
        assert len(message["message"]) < 50
    
    def test_generate_gentle_reminder_message_long_session_options(self):
        """Test that long sessions get additional options."""
        message = generate_gentle_reminder_message(
            reminder_type="break",
            session_duration=95  # Over 90 minutes
        )
        
        assert "extend_15min" in message["options"]
    
    def test_get_default_focus_modes(self):
        """Test getting default focus mode templates."""
        modes = get_default_focus_modes()
        
        assert len(modes) == 5
        
        # Check that all expected modes are present
        mode_names = [mode["name"] for mode in modes]
        expected_names = [
            "Pomodoro Classic",
            "Deep Work", 
            "Creative Flow",
            "Study Session",
            "Quick Sprint"
        ]
        
        for expected_name in expected_names:
            assert expected_name in mode_names
        
        # Check that all modes have required fields
        for mode in modes:
            assert "name" in mode
            assert "description" in mode
            assert "settings" in mode
            assert "is_default" in mode
            assert mode["is_default"] is True
            
            # Check required settings
            settings = mode["settings"]
            assert "session_duration" in settings
            assert "break_duration" in settings
            assert "gentle_reminders" in settings
            assert "hyperfocus_threshold" in settings
    
    def test_get_default_focus_modes_pomodoro_settings(self):
        """Test Pomodoro Classic mode settings."""
        modes = get_default_focus_modes()
        pomodoro_mode = next(mode for mode in modes if mode["name"] == "Pomodoro Classic")
        
        settings = pomodoro_mode["settings"]
        assert settings["session_duration"] == 25
        assert settings["break_duration"] == 5
        assert settings["long_break_duration"] == 15
        assert settings["sessions_until_long_break"] == 4
        assert settings["gentle_reminders"] is True
    
    def test_get_default_focus_modes_deep_work_settings(self):
        """Test Deep Work mode settings."""
        modes = get_default_focus_modes()
        deep_work_mode = next(mode for mode in modes if mode["name"] == "Deep Work")
        
        settings = deep_work_mode["settings"]
        assert settings["session_duration"] == 90
        assert settings["break_duration"] == 20
        assert settings["hyperfocus_threshold"] == 150
        assert settings["notification_sounds"] == "minimal"
    
    def test_get_default_focus_modes_creative_flow_settings(self):
        """Test Creative Flow mode settings."""
        modes = get_default_focus_modes()
        creative_mode = next(mode for mode in modes if mode["name"] == "Creative Flow")
        
        settings = creative_mode["settings"]
        assert settings["flexible_duration"] is True
        assert settings["hyperfocus_threshold"] == 180  # Longer threshold for creative work
        assert settings["notification_sounds"] == "ambient"
