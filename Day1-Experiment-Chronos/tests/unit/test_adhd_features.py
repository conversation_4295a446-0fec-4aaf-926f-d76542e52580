"""
Unit tests for ADHD-specific features in Project Chronos.

This module tests the core ADHD accommodations including AI chunking,
energy-aware scheduling, time blindness support, and gentle error handling.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch, MagicMock

from app.services.task_service import TaskService
from app.services.ai_service import AIService
from app.services.time_block_service import TimeBlockService
from app.schemas.task import TaskCreate, TaskChunkRequest
from app.schemas.time_block import TimeBlockCreate
from tests.factories import UserFactory, TaskFactory, TimeBlockFactory


@pytest.mark.adhd_feature
class TestAIChunking:
    """Test AI-powered task chunking for ADHD users."""
    
    async def test_chunk_large_task_creates_manageable_pieces(self, db_session, adhd_test_utils):
        """Test that large tasks are broken into ADHD-friendly chunks."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        task_service = TaskService(db_session)
        
        # Create large complex task
        large_task = await TaskFactory.create(
            db_session,
            user_id=user.id,
            title="Complete comprehensive quarterly business review and strategic planning",
            description="Analyze Q4 performance, create strategic recommendations, prepare presentation",
            estimated_duration=240,  # 4 hours
            energy_level="high",
            complexity="high"
        )
        
        # Mock AI service response
        mock_chunks = [
            {
                "title": "Gather Q4 performance data",
                "description": "Collect metrics and KPIs from all departments",
                "estimated_duration": 45,
                "energy_level": "medium",
                "order": 1
            },
            {
                "title": "Analyze performance trends",
                "description": "Review data for patterns and insights",
                "estimated_duration": 60,
                "energy_level": "high",
                "order": 2
            },
            {
                "title": "Draft strategic recommendations",
                "description": "Create actionable recommendations based on analysis",
                "estimated_duration": 75,
                "energy_level": "high",
                "order": 3
            },
            {
                "title": "Create presentation slides",
                "description": "Design visual presentation of findings",
                "estimated_duration": 60,
                "energy_level": "medium",
                "order": 4
            }
        ]
        
        with patch.object(AIService, 'chunk_task', return_value=mock_chunks):
            chunks = await task_service.chunk_task(
                user_id=user.id,
                task_id=large_task.id,
                chunk_request=TaskChunkRequest(
                    max_chunk_duration=60,
                    energy_preference="mixed",
                    include_breaks=True
                )
            )
        
        # Validate ADHD-friendly chunking
        adhd_test_utils.assert_chunking_quality(chunks, large_task.__dict__)
        
        # Verify chunks respect ADHD patterns
        assert all(chunk["estimated_duration"] <= 75 for chunk in chunks), \
               "All chunks should be manageable duration"
        assert len([c for c in chunks if c["energy_level"] == "high"]) <= 2, \
               "Should limit high-energy chunks to prevent burnout"
    
    async def test_chunk_respects_user_energy_patterns(self, db_session):
        """Test chunking considers user's energy patterns."""
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "energy_patterns": {
                    "morning": "high",
                    "afternoon": "medium",
                    "evening": "low"
                },
                "chunking_preferences": {
                    "max_high_energy_chunks": 2,
                    "prefer_morning_for_complex": True
                }
            }
        )
        
        task_service = TaskService(db_session)
        
        task = await TaskFactory.create(
            db_session,
            user_id=user.id,
            estimated_duration=180,
            energy_level="high"
        )
        
        mock_chunks = [
            {"energy_level": "high", "estimated_duration": 45, "title": "Complex analysis", "description": "Deep work", "order": 1},
            {"energy_level": "medium", "estimated_duration": 60, "title": "Documentation", "description": "Write up findings", "order": 2},
            {"energy_level": "low", "estimated_duration": 30, "title": "Final review", "description": "Quick check", "order": 3}
        ]
        
        with patch.object(AIService, 'chunk_task', return_value=mock_chunks):
            chunks = await task_service.chunk_task(
                user_id=user.id,
                task_id=task.id,
                chunk_request=TaskChunkRequest()
            )
        
        high_energy_chunks = [c for c in chunks if c["energy_level"] == "high"]
        assert len(high_energy_chunks) <= 2, "Should respect user's high energy limits"
    
    async def test_chunk_includes_break_suggestions(self, db_session):
        """Test that chunking includes appropriate break suggestions."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        task_service = TaskService(db_session)
        
        task = await TaskFactory.create(
            db_session,
            user_id=user.id,
            estimated_duration=120
        )
        
        mock_chunks = [
            {"title": "Part 1", "estimated_duration": 45, "energy_level": "high", "description": "First part", "order": 1},
            {"title": "Break", "estimated_duration": 15, "energy_level": "rest", "description": "Take a break", "order": 2, "is_break": True},
            {"title": "Part 2", "estimated_duration": 60, "energy_level": "medium", "description": "Second part", "order": 3}
        ]
        
        with patch.object(AIService, 'chunk_task', return_value=mock_chunks):
            chunks = await task_service.chunk_task(
                user_id=user.id,
                task_id=task.id,
                chunk_request=TaskChunkRequest(include_breaks=True)
            )
        
        break_chunks = [c for c in chunks if c.get("is_break", False)]
        assert len(break_chunks) >= 1, "Should include break suggestions for long tasks"


@pytest.mark.adhd_feature
class TestEnergyAwareScheduling:
    """Test energy-aware scheduling features."""
    
    async def test_schedule_respects_energy_patterns(self, db_session, adhd_test_utils):
        """Test that scheduling respects user energy patterns."""
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "energy_patterns": {
                    "morning": "high",
                    "afternoon": "medium",
                    "evening": "low"
                }
            }
        )
        
        time_block_service = TimeBlockService(db_session)
        
        # Create high-energy task
        high_energy_task = await TaskFactory.create(
            db_session,
            user_id=user.id,
            energy_level="high",
            estimated_duration=60
        )
        
        # Mock current time as afternoon (medium energy)
        with patch('app.services.time_block_service.datetime') as mock_datetime:
            mock_datetime.utcnow.return_value = datetime.utcnow().replace(hour=14)  # 2 PM
            
            suggestions = await time_block_service.suggest_time_slots(
                user_id=user.id,
                task_id=high_energy_task.id,
                duration_minutes=60
            )
        
        # Should suggest morning slots for high-energy tasks
        morning_suggestions = [s for s in suggestions if 6 <= s["start_time"].hour <= 11]
        assert len(morning_suggestions) > 0, "Should suggest morning slots for high-energy tasks"
        
        # Validate energy matching
        for suggestion in suggestions:
            if suggestion["start_time"].hour <= 11:  # Morning
                adhd_test_utils.assert_energy_matching(
                    {"energy_level": "high"}, 
                    user.preferences
                )
    
    async def test_buffer_time_added_automatically(self, db_session):
        """Test that buffer time is automatically added for ADHD users."""
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "time_blocking": {
                    "auto_add_buffers": True,
                    "default_buffer_before": 10,
                    "default_buffer_after": 5
                }
            }
        )
        
        time_block_service = TimeBlockService(db_session)
        
        start_time = datetime.utcnow().replace(hour=14, minute=0, second=0, microsecond=0)
        time_block_data = TimeBlockCreate(
            title="Important Meeting",
            start_time=start_time,
            end_time=start_time + timedelta(hours=1),
            block_type="meeting"
        )
        
        time_block = await time_block_service.create_time_block(
            user_id=user.id,
            time_block_data=time_block_data
        )
        
        assert time_block.buffer_before >= 5, "Should add buffer time before meetings"
        assert time_block.buffer_after >= 5, "Should add buffer time after meetings"
    
    async def test_conflict_detection_with_buffers(self, db_session):
        """Test conflict detection includes buffer times."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        time_block_service = TimeBlockService(db_session)
        
        # Create existing time block with buffers
        start_time = datetime.utcnow().replace(hour=14, minute=0, second=0, microsecond=0)
        existing_block = await TimeBlockFactory.create(
            db_session,
            user_id=user.id,
            start_time=start_time,
            end_time=start_time + timedelta(hours=1),
            buffer_before=10,
            buffer_after=10
        )
        
        # Try to create overlapping block
        new_block_data = TimeBlockCreate(
            title="Conflicting Block",
            start_time=start_time + timedelta(minutes=50),  # Would conflict with buffer
            end_time=start_time + timedelta(hours=1, minutes=50),
            block_type="work"
        )
        
        conflicts = await time_block_service.check_conflicts(
            user_id=user.id,
            start_time=new_block_data.start_time,
            end_time=new_block_data.end_time
        )
        
        assert len(conflicts) > 0, "Should detect conflict with buffer time"
        assert conflicts[0]["type"] == "buffer_overlap", "Should identify buffer overlap"


@pytest.mark.adhd_feature
class TestTimeBlinднessSupport:
    """Test time blindness support features."""
    
    async def test_time_estimation_tracking(self, db_session):
        """Test tracking of time estimation accuracy."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        task_service = TaskService(db_session)
        
        # Create task with estimate
        task = await TaskFactory.create(
            db_session,
            user_id=user.id,
            estimated_duration=60,
            status="pending"
        )
        
        # Start task
        await task_service.start_task(user_id=user.id, task_id=task.id)
        
        # Mock task completion after different duration
        with patch('app.services.task_service.datetime') as mock_datetime:
            start_time = datetime.utcnow()
            completion_time = start_time + timedelta(minutes=90)  # Took longer than estimated
            
            mock_datetime.utcnow.side_effect = [start_time, completion_time]
            
            completed_task = await task_service.complete_task(
                user_id=user.id,
                task_id=task.id
            )
        
        # Check estimation accuracy tracking
        assert completed_task.actual_duration == 90
        assert completed_task.time_estimation_accuracy < 1.0  # Underestimated
        
        # Should provide feedback for future estimates
        assert "estimation_feedback" in completed_task.metadata
    
    async def test_visual_time_indicators(self, db_session):
        """Test visual time representation features."""
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "visual_preferences": {
                    "time_display": "circular",
                    "show_progress_bars": True,
                    "color_code_urgency": True
                }
            }
        )
        
        time_block_service = TimeBlockService(db_session)
        
        # Create time blocks for visual testing
        today = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        blocks = []
        
        for hour in range(9, 17):  # 9 AM to 5 PM
            block = await TimeBlockFactory.create(
                db_session,
                user_id=user.id,
                start_time=today.replace(hour=hour),
                end_time=today.replace(hour=hour + 1),
                energy_level="medium" if hour < 14 else "low"
            )
            blocks.append(block)
        
        # Get visual representation
        visual_data = await time_block_service.get_visual_schedule(
            user_id=user.id,
            date=today.date(),
            view_type="circular"
        )
        
        assert visual_data["view_type"] == "circular"
        assert "time_segments" in visual_data
        assert "current_time_indicator" in visual_data
        assert "energy_color_mapping" in visual_data
        
        # Verify ADHD-friendly visual features
        for segment in visual_data["time_segments"]:
            assert "color" in segment, "Each segment should have color coding"
            assert "progress_percentage" in segment, "Should show progress indicators"
            assert "urgency_level" in segment, "Should indicate urgency visually"


@pytest.mark.adhd_feature
class TestGentleErrorHandling:
    """Test ADHD-friendly error handling and messaging."""
    
    async def test_supportive_error_messages(self, client, authenticated_user):
        """Test that error messages are supportive and helpful."""
        # Try to create task with invalid data
        response = await client.post(
            "/api/v1/tasks",
            headers=authenticated_user["headers"],
            json={
                "title": "",  # Invalid: empty title
                "estimated_duration": -10  # Invalid: negative duration
            }
        )
        
        assert response.status_code == 400
        error_data = response.json()
        
        # Verify ADHD-friendly error structure
        assert "error" in error_data
        error = error_data["error"]
        
        assert "suggestions" in error, "Should provide helpful suggestions"
        assert "help_url" in error, "Should provide help resources"
        
        # Check tone is supportive
        message = error["message"].lower()
        assert not any(word in message for word in ["failed", "invalid", "wrong"]), \
               "Error message should avoid negative language"
        
        # Should provide specific guidance
        suggestions = error["suggestions"]
        assert len(suggestions) > 0, "Should provide actionable suggestions"
        assert any("title" in suggestion.lower() for suggestion in suggestions), \
               "Should provide specific guidance about the title field"
    
    async def test_recovery_assistance(self, client, authenticated_user):
        """Test that errors include recovery assistance."""
        # Simulate a service error
        with patch('app.services.task_service.TaskService.create_task') as mock_create:
            mock_create.side_effect = Exception("Database connection error")
            
            response = await client.post(
                "/api/v1/tasks",
                headers=authenticated_user["headers"],
                json={
                    "title": "Test Task",
                    "estimated_duration": 60
                }
            )
        
        assert response.status_code == 500
        error_data = response.json()
        
        error = error_data["error"]
        assert "recovery_steps" in error, "Should provide recovery steps"
        assert "support_contact" in error, "Should provide support contact"
        
        # Recovery steps should be actionable
        recovery_steps = error["recovery_steps"]
        assert len(recovery_steps) > 0, "Should provide recovery guidance"
        assert all("step" in step and "action" in step for step in recovery_steps), \
               "Recovery steps should be structured and actionable"
    
    async def test_progress_encouragement(self, client, authenticated_user):
        """Test that successful operations include encouragement."""
        response = await client.post(
            "/api/v1/tasks",
            headers=authenticated_user["headers"],
            json={
                "title": "Great Task",
                "description": "A well-planned task",
                "estimated_duration": 60,
                "energy_level": "medium"
            }
        )
        
        assert response.status_code == 201
        success_data = response.json()
        
        # Check for encouraging language
        if "message" in success_data:
            message = success_data["message"].lower()
            encouraging_words = ["great", "excellent", "well done", "success", "awesome"]
            assert any(word in message for word in encouraging_words), \
                   "Success messages should be encouraging"
        
        # Should acknowledge effort
        assert "data" in success_data
        task_data = success_data["data"]
        if "feedback" in task_data:
            feedback = task_data["feedback"]
            assert "effort_acknowledgment" in feedback, \
                   "Should acknowledge user's effort in task creation"
