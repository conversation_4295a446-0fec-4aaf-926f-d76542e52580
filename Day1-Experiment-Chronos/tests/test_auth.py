"""Tests for authentication functionality.

This module tests the authentication system including user registration,
login, token management, and ADHD-specific features.
"""

import pytest
from fastapi.testclient import TestClient


class TestUserRegistration:
    """Test user registration functionality."""
    
    def test_register_user_success(
        self, 
        client: TestClient, 
        test_user_data: dict
    ) -> None:
        """Test successful user registration.
        
        Args:
            client: FastAPI test client
            test_user_data: Test user registration data
        """
        response = client.post("/api/v1/auth/register", json=test_user_data)
        
        assert response.status_code == 201
        data = response.json()
        
        assert data["email"] == test_user_data["email"]
        assert data["full_name"] == test_user_data["full_name"]
        assert data["has_adhd_diagnosis"] == test_user_data["has_adhd_diagnosis"]
        assert data["is_active"] is True
        assert data["is_verified"] is False  # Email verification required
        assert "id" in data
        assert "created_at" in data
        assert "hashed_password" not in data  # Should not expose password
    
    def test_register_user_duplicate_email(
        self, 
        client: TestClient, 
        test_user_data: dict
    ) -> None:
        """Test registration with duplicate email.
        
        Args:
            client: FastAPI test client
            test_user_data: Test user registration data
        """
        # Register first user
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # Try to register with same email
        response = client.post("/api/v1/auth/register", json=test_user_data)
        
        assert response.status_code == 400
        assert "already registered" in response.json()["detail"]
    
    def test_register_user_invalid_password(
        self, 
        client: TestClient, 
        test_user_data: dict
    ) -> None:
        """Test registration with invalid password.
        
        Args:
            client: FastAPI test client
            test_user_data: Test user registration data
        """
        test_user_data["password"] = "weak"  # Too short
        
        response = client.post("/api/v1/auth/register", json=test_user_data)
        
        assert response.status_code == 422  # Validation error
    
    def test_register_user_minimal_data(self, client: TestClient) -> None:
        """Test registration with minimal required data.
        
        Args:
            client: FastAPI test client
        """
        minimal_data = {
            "email": "<EMAIL>",
            "password": "password123",
        }
        
        response = client.post("/api/v1/auth/register", json=minimal_data)
        
        assert response.status_code == 201
        data = response.json()
        
        assert data["email"] == minimal_data["email"]
        assert data["full_name"] is None
        assert data["has_adhd_diagnosis"] is None
        assert data["profile_completion_percentage"] >= 20  # Basic completion


class TestUserLogin:
    """Test user login functionality."""
    
    def test_login_success(
        self, 
        client: TestClient, 
        test_user_data: dict,
        test_login_data: dict
    ) -> None:
        """Test successful user login.
        
        Args:
            client: FastAPI test client
            test_user_data: Test user registration data
            test_login_data: Test user login data
        """
        # Register user first
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # Login
        response = client.post("/api/v1/auth/login", json=test_login_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
        assert data["expires_in"] > 0
    
    def test_login_invalid_credentials(
        self, 
        client: TestClient,
        test_login_data: dict
    ) -> None:
        """Test login with invalid credentials.
        
        Args:
            client: FastAPI test client
            test_login_data: Test user login data
        """
        response = client.post("/api/v1/auth/login", json=test_login_data)
        
        assert response.status_code == 401
        assert "Incorrect email or password" in response.json()["detail"]
    
    def test_login_wrong_password(
        self, 
        client: TestClient, 
        test_user_data: dict
    ) -> None:
        """Test login with wrong password.
        
        Args:
            client: FastAPI test client
            test_user_data: Test user registration data
        """
        # Register user first
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # Login with wrong password
        login_data = {
            "email": test_user_data["email"],
            "password": "wrongpassword",
            "remember_me": False,
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401


class TestTokenManagement:
    """Test token management functionality."""
    
    def test_get_current_user(
        self, 
        client: TestClient, 
        test_user_data: dict,
        test_login_data: dict
    ) -> None:
        """Test getting current user with valid token.
        
        Args:
            client: FastAPI test client
            test_user_data: Test user registration data
            test_login_data: Test user login data
        """
        # Register and login
        client.post("/api/v1/auth/register", json=test_user_data)
        login_response = client.post("/api/v1/auth/login", json=test_login_data)
        
        access_token = login_response.json()["access_token"]
        
        # Get current user
        headers = {"Authorization": f"Bearer {access_token}"}
        response = client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["email"] == test_user_data["email"]
        assert data["full_name"] == test_user_data["full_name"]
    
    def test_refresh_token(
        self, 
        client: TestClient, 
        test_user_data: dict,
        test_login_data: dict
    ) -> None:
        """Test token refresh functionality.
        
        Args:
            client: FastAPI test client
            test_user_data: Test user registration data
            test_login_data: Test user login data
        """
        # Register and login
        client.post("/api/v1/auth/register", json=test_user_data)
        login_response = client.post("/api/v1/auth/login", json=test_login_data)
        
        refresh_token = login_response.json()["refresh_token"]
        
        # Refresh token
        refresh_data = {"refresh_token": refresh_token}
        response = client.post("/api/v1/auth/refresh", json=refresh_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
    
    def test_unauthorized_access(self, client: TestClient) -> None:
        """Test accessing protected endpoint without token.
        
        Args:
            client: FastAPI test client
        """
        response = client.get("/api/v1/auth/me")
        
        assert response.status_code == 401
        assert "Authentication required" in response.json()["detail"]


class TestApplicationEndpoints:
    """Test basic application endpoints."""
    
    def test_root_endpoint(self, client: TestClient) -> None:
        """Test root endpoint.

        Args:
            client: FastAPI test client
        """
        response = client.get("/")

        assert response.status_code == 200
        data = response.json()

        assert "Project Chronos" in data["message"]
        assert "version" in data
    
    def test_health_check(self, client: TestClient) -> None:
        """Test health check endpoint.
        
        Args:
            client: FastAPI test client
        """
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["service"] == "project-chronos"
