"""
Integration tests for focus session API endpoints.

This module tests the complete focus session API workflow including
session management, timer integration, and real-time updates.
"""

import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient
from uuid import uuid4

from chronos.app.api.v1.focus import router
from chronos.app.schemas.focus import FocusSessionCreate


@pytest.fixture
def focus_session_data():
    """Sample focus session data for API tests."""
    return {
        "session_type": "pomodoro",
        "planned_duration": 25,
        "break_duration": 5,
        "focus_mode_settings": {
            "gentle_reminders": True,
            "reminder_threshold": 0.9,
            "hyperfocus_threshold": 120
        }
    }


@pytest.fixture
def deep_work_session_data():
    """Sample deep work session data for API tests."""
    return {
        "session_type": "deep_work",
        "planned_duration": 90,
        "break_duration": 20,
        "focus_mode_settings": {
            "gentle_reminders": True,
            "reminder_threshold": 0.8,
            "hyperfocus_threshold": 150
        }
    }


class TestFocusSessionAPI:
    """Test cases for focus session API endpoints."""
    
    @pytest.mark.asyncio
    async def test_create_focus_session(self, async_client: AsyncClient, focus_session_data):
        """Test creating a focus session via API."""
        response = await async_client.post("/api/v1/focus/sessions", json=focus_session_data)
        
        assert response.status_code == 201
        data = response.json()
        
        assert data["session_type"] == "pomodoro"
        assert data["planned_duration"] == 25
        assert data["break_duration"] == 5
        assert data["status"] == "planned"
        assert data["is_active"] is False
        assert data["elapsed_time"] == 0
        assert data["remaining_time"] == 0
        assert data["progress_percentage"] == 0.0
    
    @pytest.mark.asyncio
    async def test_create_invalid_session_fails(self, async_client: AsyncClient):
        """Test that creating invalid session fails."""
        invalid_data = {
            "session_type": "invalid_type",
            "planned_duration": -5,  # Invalid duration
            "break_duration": 5
        }
        
        response = await async_client.post("/api/v1/focus/sessions", json=invalid_data)
        assert response.status_code == 422  # Validation error
    
    @pytest.mark.asyncio
    async def test_start_focus_session(self, async_client: AsyncClient, focus_session_data):
        """Test starting a focus session via API."""
        # Create session
        create_response = await async_client.post("/api/v1/focus/sessions", json=focus_session_data)
        session_id = create_response.json()["id"]
        
        # Start session
        start_response = await async_client.post(f"/api/v1/focus/sessions/{session_id}/start")
        
        assert start_response.status_code == 200
        data = start_response.json()
        
        assert data["status"] == "active"
        assert data["is_active"] is True
        assert data["started_at"] is not None
    
    @pytest.mark.asyncio
    async def test_pause_and_resume_session(self, async_client: AsyncClient, focus_session_data):
        """Test pausing and resuming a focus session."""
        # Create and start session
        create_response = await async_client.post("/api/v1/focus/sessions", json=focus_session_data)
        session_id = create_response.json()["id"]
        await async_client.post(f"/api/v1/focus/sessions/{session_id}/start")
        
        # Pause session
        pause_data = {"reason": "Quick break needed"}
        pause_response = await async_client.put(
            f"/api/v1/focus/sessions/{session_id}/pause",
            json=pause_data
        )
        
        assert pause_response.status_code == 200
        pause_result = pause_response.json()
        assert pause_result["status"] == "paused"
        assert pause_result["paused_at"] is not None
        
        # Resume session
        resume_response = await async_client.put(f"/api/v1/focus/sessions/{session_id}/resume")
        
        assert resume_response.status_code == 200
        resume_result = resume_response.json()
        assert resume_result["status"] == "active"
        assert resume_result["paused_at"] is None
    
    @pytest.mark.asyncio
    async def test_complete_session(self, async_client: AsyncClient, focus_session_data):
        """Test completing a focus session."""
        # Create and start session
        create_response = await async_client.post("/api/v1/focus/sessions", json=focus_session_data)
        session_id = create_response.json()["id"]
        await async_client.post(f"/api/v1/focus/sessions/{session_id}/start")
        
        # Complete session
        completion_data = {
            "completion_notes": "Great session!",
            "actual_duration": 30
        }
        complete_response = await async_client.post(
            f"/api/v1/focus/sessions/{session_id}/complete",
            json=completion_data
        )
        
        assert complete_response.status_code == 200
        data = complete_response.json()
        
        assert data["status"] == "completed"
        assert data["completed_at"] is not None
        assert data["actual_duration"] == 30
        assert data["is_active"] is False
    
    @pytest.mark.asyncio
    async def test_get_active_session(self, async_client: AsyncClient, focus_session_data):
        """Test getting active session."""
        # No active session initially
        response = await async_client.get("/api/v1/focus/sessions/active")
        assert response.status_code == 200
        assert response.json() is None
        
        # Create and start session
        create_response = await async_client.post("/api/v1/focus/sessions", json=focus_session_data)
        session_id = create_response.json()["id"]
        await async_client.post(f"/api/v1/focus/sessions/{session_id}/start")
        
        # Should now have active session
        active_response = await async_client.get("/api/v1/focus/sessions/active")
        assert active_response.status_code == 200
        active_data = active_response.json()
        
        assert active_data is not None
        assert active_data["id"] == session_id
        assert active_data["status"] == "active"
    
    @pytest.mark.asyncio
    async def test_get_session_by_id(self, async_client: AsyncClient, focus_session_data):
        """Test getting specific session by ID."""
        # Create session
        create_response = await async_client.post("/api/v1/focus/sessions", json=focus_session_data)
        session_id = create_response.json()["id"]
        
        # Get session by ID
        get_response = await async_client.get(f"/api/v1/focus/sessions/{session_id}")
        
        assert get_response.status_code == 200
        data = get_response.json()
        
        assert data["id"] == session_id
        assert data["session_type"] == "pomodoro"
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_session_fails(self, async_client: AsyncClient):
        """Test that getting nonexistent session returns 404."""
        fake_id = str(uuid4())
        response = await async_client.get(f"/api/v1/focus/sessions/{fake_id}")
        
        assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_get_user_sessions(self, async_client: AsyncClient, focus_session_data):
        """Test getting user sessions with pagination."""
        # Create multiple sessions
        session_ids = []
        for i in range(3):
            response = await async_client.post("/api/v1/focus/sessions", json=focus_session_data)
            session_ids.append(response.json()["id"])
        
        # Get all sessions
        response = await async_client.get("/api/v1/focus/sessions")
        
        assert response.status_code == 200
        sessions = response.json()
        
        assert len(sessions) >= 3
        
        # Test pagination
        paginated_response = await async_client.get("/api/v1/focus/sessions?limit=2&offset=0")
        assert paginated_response.status_code == 200
        paginated_sessions = paginated_response.json()
        assert len(paginated_sessions) == 2
    
    @pytest.mark.asyncio
    async def test_get_sessions_with_status_filter(self, async_client: AsyncClient, focus_session_data):
        """Test getting sessions with status filter."""
        # Create and start one session
        create_response = await async_client.post("/api/v1/focus/sessions", json=focus_session_data)
        session_id = create_response.json()["id"]
        await async_client.post(f"/api/v1/focus/sessions/{session_id}/start")
        
        # Create another session but don't start it
        await async_client.post("/api/v1/focus/sessions", json=focus_session_data)
        
        # Filter for active sessions
        active_response = await async_client.get("/api/v1/focus/sessions?status=active")
        assert active_response.status_code == 200
        active_sessions = active_response.json()
        
        assert len(active_sessions) == 1
        assert active_sessions[0]["status"] == "active"
        
        # Filter for planned sessions
        planned_response = await async_client.get("/api/v1/focus/sessions?status=planned")
        assert planned_response.status_code == 200
        planned_sessions = planned_response.json()
        
        assert len(planned_sessions) >= 1
        assert all(session["status"] == "planned" for session in planned_sessions)
    
    @pytest.mark.asyncio
    async def test_extend_session(self, async_client: AsyncClient, focus_session_data):
        """Test extending session duration."""
        # Create and start session
        create_response = await async_client.post("/api/v1/focus/sessions", json=focus_session_data)
        session_id = create_response.json()["id"]
        await async_client.post(f"/api/v1/focus/sessions/{session_id}/start")
        
        # Extend session
        extend_response = await async_client.post(
            f"/api/v1/focus/sessions/{session_id}/extend?additional_minutes=15"
        )
        
        assert extend_response.status_code == 200
        data = extend_response.json()
        
        assert data["planned_duration"] == 40  # 25 + 15
    
    @pytest.mark.asyncio
    async def test_extend_inactive_session_fails(self, async_client: AsyncClient, focus_session_data):
        """Test that extending inactive session fails."""
        # Create session but don't start it
        create_response = await async_client.post("/api/v1/focus/sessions", json=focus_session_data)
        session_id = create_response.json()["id"]
        
        # Try to extend - should fail
        extend_response = await async_client.post(
            f"/api/v1/focus/sessions/{session_id}/extend?additional_minutes=15"
        )
        
        assert extend_response.status_code == 400
    
    @pytest.mark.asyncio
    async def test_get_timer_state(self, async_client: AsyncClient, focus_session_data):
        """Test getting timer state for session."""
        # Create and start session
        create_response = await async_client.post("/api/v1/focus/sessions", json=focus_session_data)
        session_id = create_response.json()["id"]
        await async_client.post(f"/api/v1/focus/sessions/{session_id}/start")
        
        # Get timer state
        timer_response = await async_client.get(f"/api/v1/focus/sessions/{session_id}/timer")
        
        assert timer_response.status_code == 200
        timer_data = timer_response.json()
        
        assert timer_data["session_id"] == session_id
        assert timer_data["status"] in ["active", "paused", "stopped"]
        assert "elapsed_time" in timer_data
        assert "remaining_time" in timer_data
        assert "progress_percentage" in timer_data
        assert "is_paused" in timer_data
        assert "last_updated" in timer_data
    
    @pytest.mark.asyncio
    async def test_get_focus_analytics(self, async_client: AsyncClient, focus_session_data):
        """Test getting focus analytics."""
        # Create and complete a session to have some data
        create_response = await async_client.post("/api/v1/focus/sessions", json=focus_session_data)
        session_id = create_response.json()["id"]
        await async_client.post(f"/api/v1/focus/sessions/{session_id}/start")
        
        completion_data = {"completion_notes": "Test session", "actual_duration": 25}
        await async_client.post(f"/api/v1/focus/sessions/{session_id}/complete", json=completion_data)
        
        # Get analytics
        analytics_response = await async_client.get("/api/v1/focus/analytics?days=7")
        
        assert analytics_response.status_code == 200
        analytics = analytics_response.json()
        
        assert "total_sessions" in analytics
        assert "completed_sessions" in analytics
        assert "total_focus_time" in analytics
        assert "average_session_duration" in analytics
        assert "completion_rate" in analytics
        assert "hyperfocus_incidents" in analytics
        assert "most_productive_hours" in analytics
        assert "session_type_breakdown" in analytics
        assert "weekly_trend" in analytics
        
        assert analytics["total_sessions"] >= 1
        assert analytics["completed_sessions"] >= 1
    
    @pytest.mark.asyncio
    async def test_session_workflow_complete(self, async_client: AsyncClient, focus_session_data):
        """Test complete session workflow from creation to completion."""
        # 1. Create session
        create_response = await async_client.post("/api/v1/focus/sessions", json=focus_session_data)
        assert create_response.status_code == 201
        session_id = create_response.json()["id"]
        
        # 2. Start session
        start_response = await async_client.post(f"/api/v1/focus/sessions/{session_id}/start")
        assert start_response.status_code == 200
        assert start_response.json()["status"] == "active"
        
        # 3. Check active session
        active_response = await async_client.get("/api/v1/focus/sessions/active")
        assert active_response.json()["id"] == session_id
        
        # 4. Get timer state
        timer_response = await async_client.get(f"/api/v1/focus/sessions/{session_id}/timer")
        assert timer_response.status_code == 200
        
        # 5. Pause session
        pause_response = await async_client.put(f"/api/v1/focus/sessions/{session_id}/pause")
        assert pause_response.status_code == 200
        assert pause_response.json()["status"] == "paused"
        
        # 6. Resume session
        resume_response = await async_client.put(f"/api/v1/focus/sessions/{session_id}/resume")
        assert resume_response.status_code == 200
        assert resume_response.json()["status"] == "active"
        
        # 7. Extend session
        extend_response = await async_client.post(
            f"/api/v1/focus/sessions/{session_id}/extend?additional_minutes=10"
        )
        assert extend_response.status_code == 200
        assert extend_response.json()["planned_duration"] == 35
        
        # 8. Complete session
        completion_data = {"completion_notes": "Successful test!", "actual_duration": 35}
        complete_response = await async_client.post(
            f"/api/v1/focus/sessions/{session_id}/complete",
            json=completion_data
        )
        assert complete_response.status_code == 200
        assert complete_response.json()["status"] == "completed"
        
        # 9. Verify no active session
        final_active_response = await async_client.get("/api/v1/focus/sessions/active")
        assert final_active_response.json() is None
