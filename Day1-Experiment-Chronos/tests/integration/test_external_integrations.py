"""
Integration tests for external service integrations.

This module tests the integration with external services like Google Calendar,
Todoist, Slack, and Notion with ADHD-optimized features.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch, MagicMock
from uuid import uuid4

from app.integrations.google_calendar import GoogleCalendarIntegration
from app.integrations.todoist import TodoistIntegration
from app.integrations.slack import SlackIntegration
from app.integrations.notion import NotionIntegration
from app.services.integration_service import IntegrationService
from app.schemas.integration import (
    IntegrationCreate, SyncRequest, CalendarEventSync, TaskSync
)
from app.models.integration import IntegrationType, IntegrationStatus
from tests.factories import UserFactory, IntegrationFactory


@pytest.mark.integration
class TestGoogleCalendarIntegration:
    """Test Google Calendar integration with ADHD features."""
    
    async def test_oauth_authentication_flow(self):
        """Test OAuth 2.0 authentication with Google Calendar."""
        integration = GoogleCalendarIntegration()
        
        # Mock successful OAuth response
        mock_oauth_response = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "expires_in": 3600,
            "token_type": "Bearer",
            "scope": "https://www.googleapis.com/auth/calendar"
        }
        
        with patch.object(integration, '_make_request', return_value=mock_oauth_response):
            tokens = await integration.authenticate({
                "code": "test_authorization_code",
                "redirect_uri": "https://app.chronos.com/oauth/callback"
            })
        
        assert tokens.access_token == "test_access_token"
        assert tokens.refresh_token == "test_refresh_token"
        assert tokens.expires_in == 3600
    
    async def test_calendar_event_import_with_adhd_features(self):
        """Test importing calendar events with ADHD optimizations."""
        integration = GoogleCalendarIntegration()
        
        # Mock Google Calendar API response
        mock_events = {
            "items": [
                {
                    "id": "event_1",
                    "summary": "Deep focus coding session",
                    "description": "Complex algorithm implementation",
                    "start": {"dateTime": "2024-01-15T09:00:00Z"},
                    "end": {"dateTime": "2024-01-15T11:00:00Z"},
                    "location": "Home Office",
                    "attendees": []
                },
                {
                    "id": "event_2", 
                    "summary": "Quick email check",
                    "description": "Review and respond to emails",
                    "start": {"dateTime": "2024-01-15T14:00:00Z"},
                    "end": {"dateTime": "2024-01-15T14:15:00Z"},
                    "location": "",
                    "attendees": []
                },
                {
                    "id": "event_3",
                    "summary": "Team standup meeting",
                    "description": "Daily team sync",
                    "start": {"dateTime": "2024-01-15T10:00:00Z"},
                    "end": {"dateTime": "2024-01-15T10:30:00Z"},
                    "attendees": [{"email": "<EMAIL>"}]
                }
            ]
        }
        
        with patch.object(integration, '_make_request', return_value=mock_events):
            with patch.object(integration, 'get_calendars', return_value=[{"id": "primary", "name": "Primary"}]):
                result = await integration.import_calendar_events(
                    access_token="test_token",
                    start_date=datetime(2024, 1, 15),
                    end_date=datetime(2024, 1, 16)
                )
        
        assert result.success
        assert result.items_processed == 3
        assert result.items_created == 3
        
        # Verify ADHD-specific processing
        events = result.metadata["events"]
        
        # Deep focus session should be high energy
        focus_event = next(e for e in events if "focus" in e.title.lower())
        assert focus_event.energy_level == "high"
        assert focus_event.is_focus_time == True
        assert focus_event.buffer_before == 5
        assert focus_event.buffer_after == 5
        
        # Quick task should be low energy
        quick_event = next(e for e in events if "quick" in e.title.lower())
        assert quick_event.energy_level == "low"
        assert quick_event.buffer_before == 2
        
        # Meeting should have appropriate energy and buffers
        meeting_event = next(e for e in events if "meeting" in e.title.lower())
        assert meeting_event.energy_level == "medium"
        assert meeting_event.buffer_before >= 5
    
    async def test_calendar_export_with_adhd_metadata(self):
        """Test exporting events with ADHD metadata preserved."""
        integration = GoogleCalendarIntegration()
        
        # Create ADHD-optimized events
        events = [
            CalendarEventSync(
                external_id="",
                title="Deep Work Session",
                description="Focus time for complex project",
                start_time=datetime(2024, 1, 15, 9, 0),
                end_time=datetime(2024, 1, 15, 11, 0),
                energy_level="high",
                is_focus_time=True,
                buffer_before=10,
                buffer_after=5
            ),
            CalendarEventSync(
                external_id="",
                title="Admin Tasks",
                description="Quick administrative work",
                start_time=datetime(2024, 1, 15, 14, 0),
                end_time=datetime(2024, 1, 15, 14, 30),
                energy_level="low",
                buffer_before=5,
                buffer_after=5
            )
        ]
        
        # Mock successful creation responses
        mock_responses = [
            {"id": "created_event_1"},
            {"id": "created_event_2"}
        ]
        
        with patch.object(integration, '_make_request', side_effect=mock_responses):
            with patch.object(integration, 'get_calendars', return_value=[{"id": "primary", "name": "Primary"}]):
                result = await integration.export_calendar_events(
                    access_token="test_token",
                    events=events
                )
        
        assert result.success
        assert result.items_created == 2
        
        # Verify events got external IDs
        assert events[0].external_id == "created_event_1"
        assert events[1].external_id == "created_event_2"


@pytest.mark.integration
class TestTodoistIntegration:
    """Test Todoist integration with ADHD task management."""
    
    async def test_task_import_with_energy_detection(self):
        """Test importing tasks with automatic energy level detection."""
        integration = TodoistIntegration()
        
        # Mock Todoist API response
        mock_tasks = [
            {
                "id": "task_1",
                "content": "Deep analysis of quarterly performance data",
                "description": "Complex data analysis requiring focus",
                "priority": 4,
                "labels": ["work", "analysis"],
                "due": {"datetime": "2024-01-20T17:00:00Z"},
                "is_completed": False,
                "project_id": "project_1"
            },
            {
                "id": "task_2",
                "content": "Quick email to client - 5min",
                "description": "Simple follow-up email",
                "priority": 2,
                "labels": ["admin", "5min"],
                "due": None,
                "is_completed": False,
                "project_id": "project_1"
            },
            {
                "id": "task_3",
                "content": "Creative design mockups for new feature",
                "description": "Design work requiring creativity",
                "priority": 3,
                "labels": ["design", "creative", "30min"],
                "due": {"datetime": "2024-01-18T12:00:00Z"},
                "is_completed": False,
                "project_id": "project_2"
            }
        ]
        
        with patch.object(integration, '_make_request', return_value=mock_tasks):
            result = await integration.import_tasks(
                access_token="test_token",
                project_ids=["project_1", "project_2"]
            )
        
        assert result.success
        assert result.items_processed == 3
        
        # Verify ADHD-specific categorization
        tasks = result.metadata["tasks"]
        
        # Complex analysis should be high energy
        analysis_task = next(t for t in tasks if "analysis" in t.title.lower())
        assert analysis_task.energy_level == "high"
        assert analysis_task.complexity == "high"
        assert analysis_task.estimated_duration is None or analysis_task.estimated_duration > 60
        
        # Quick email should be low energy with duration from label
        email_task = next(t for t in tasks if "email" in t.title.lower())
        assert email_task.energy_level == "low"
        assert email_task.complexity == "low"
        assert email_task.estimated_duration == 5
        
        # Creative work should be medium energy with duration from label
        design_task = next(t for t in tasks if "design" in t.title.lower())
        assert design_task.energy_level == "medium"
        assert design_task.estimated_duration == 30
    
    async def test_task_export_with_adhd_metadata(self):
        """Test exporting tasks with ADHD metadata preserved."""
        integration = TodoistIntegration()
        
        # Create ADHD-optimized tasks
        tasks = [
            TaskSync(
                external_id="",
                title="Complex project analysis",
                description="Deep dive into project metrics",
                energy_level="high",
                complexity="high",
                estimated_duration=90,
                chunk_size=30,
                priority="high",
                labels=["work", "analysis"]
            ),
            TaskSync(
                external_id="",
                title="Quick admin update",
                description="Update contact information",
                energy_level="low",
                complexity="low",
                estimated_duration=10,
                priority="medium",
                labels=["admin", "quick"]
            )
        ]
        
        # Mock project and task creation
        with patch.object(integration, 'get_projects', return_value=[{"id": "inbox", "name": "Inbox"}]):
            with patch.object(integration, '_make_request', side_effect=[
                {"id": "created_task_1"},
                {"id": "created_task_2"}
            ]):
                result = await integration.export_tasks(
                    access_token="test_token",
                    tasks=tasks,
                    project_id="inbox"
                )
        
        assert result.success
        assert result.items_created == 2
        
        # Verify tasks got external IDs
        assert tasks[0].external_id == "created_task_1"
        assert tasks[1].external_id == "created_task_2"


@pytest.mark.integration
class TestSlackIntegration:
    """Test Slack integration for ADHD-friendly notifications."""
    
    async def test_adhd_friendly_notification_formatting(self):
        """Test that notifications use ADHD-friendly formatting."""
        integration = SlackIntegration()
        
        # Mock successful message sending
        mock_response = {
            "ok": True,
            "ts": "1234567890.123456",
            "channel": "C1234567890",
            "message": {"text": "Test message"}
        }
        
        with patch.object(integration, '_make_request', return_value=mock_response):
            result = await integration.send_notification(
                access_token="test_token",
                channel="#focus-sessions",
                notification_type="task_reminder",
                title="Task Reminder",
                message="Your important task is due soon!",
                metadata={
                    "energy_level": "high",
                    "priority": "urgent",
                    "due_date": "2024-01-15T17:00:00Z"
                }
            )
        
        assert result.success
        assert result.items_created == 1
        assert "message_ts" in result.metadata
    
    async def test_body_doubling_invitation(self):
        """Test sending body doubling session invitations."""
        integration = SlackIntegration()
        
        mock_response = {
            "ok": True,
            "ts": "1234567890.123456",
            "channel": "C1234567890"
        }
        
        with patch.object(integration, '_make_request', return_value=mock_response):
            result = await integration.send_body_doubling_invitation(
                access_token="test_token",
                channel="#body-doubling",
                session_title="Morning Focus Session",
                start_time=datetime(2024, 1, 15, 9, 0),
                duration_minutes=90,
                join_url="https://app.chronos.com/body-doubling/session-123"
            )
        
        assert result.success
        assert result.items_created == 1
        assert "session_title" in result.metadata


@pytest.mark.integration
class TestNotionIntegration:
    """Test Notion integration for database synchronization."""
    
    async def test_database_task_import_with_adhd_properties(self):
        """Test importing tasks from Notion with ADHD properties."""
        integration = NotionIntegration()
        
        # Mock Notion database query response
        mock_pages = {
            "results": [
                {
                    "id": "page_1",
                    "properties": {
                        "Name": {
                            "type": "title",
                            "title": [{"text": {"content": "Complex research project"}}]
                        },
                        "Description": {
                            "type": "rich_text",
                            "rich_text": [{"text": {"content": "Deep research requiring focus"}}]
                        },
                        "Due Date": {
                            "type": "date",
                            "date": {"start": "2024-01-20"}
                        },
                        "Done": {
                            "type": "checkbox",
                            "checkbox": False
                        },
                        "Priority": {
                            "type": "select",
                            "select": {"name": "High"}
                        },
                        "Energy Level": {
                            "type": "select",
                            "select": {"name": "High"}
                        },
                        "Complexity": {
                            "type": "select",
                            "select": {"name": "High"}
                        },
                        "Duration (min)": {
                            "type": "number",
                            "number": 120
                        }
                    }
                }
            ]
        }
        
        with patch.object(integration, '_make_request', return_value=mock_pages):
            result = await integration.import_tasks(
                access_token="test_token",
                database_ids=["database_1"]
            )
        
        assert result.success
        assert result.items_processed == 1
        
        # Verify ADHD properties are preserved
        tasks = result.metadata["tasks"]
        task = tasks[0]
        
        assert task.title == "Complex research project"
        assert task.energy_level == "high"
        assert task.complexity == "high"
        assert task.estimated_duration == 120
        assert task.priority == "high"


@pytest.mark.integration
class TestIntegrationService:
    """Test the integration service orchestration."""
    
    async def test_create_integration_with_adhd_defaults(self, db_session):
        """Test creating integration with ADHD-optimized defaults."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        service = IntegrationService(db_session)
        
        integration_data = IntegrationCreate(
            integration_type=IntegrationType.GOOGLE_CALENDAR,
            name="My Calendar",
            description="Primary calendar integration",
            config={
                "calendar_ids": ["primary"],
                "sync_frequency": "real_time"
            },
            sync_settings={
                "import_events": True,
                "export_events": True
            }
        )
        
        integration = await service.create_integration(
            user_id=user.id,
            integration_data=integration_data
        )
        
        assert integration.integration_type == IntegrationType.GOOGLE_CALENDAR
        assert integration.status == IntegrationStatus.ACTIVE
        assert integration.user_id == user.id
        
        # Verify ADHD-friendly defaults are applied
        assert "buffer_time" in integration.config or "buffer_time" in integration.sync_settings
        assert integration.sync_settings.get("respect_focus_mode", False)
    
    async def test_sync_operation_with_progress_tracking(self, db_session):
        """Test sync operation with ADHD-friendly progress tracking."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        integration = await IntegrationFactory.create(
            db_session,
            user_id=user.id,
            integration_type=IntegrationType.TODOIST,
            status=IntegrationStatus.ACTIVE
        )
        
        service = IntegrationService(db_session)
        
        sync_request = SyncRequest(
            operation_type="import_tasks",
            force=False,
            dry_run=False,
            filters={
                "include_adhd_categorization": True,
                "energy_mapping": True
            }
        )
        
        sync_response = await service.start_sync(
            user_id=user.id,
            integration_id=integration.id,
            sync_request=sync_request
        )
        
        assert sync_response.status.value in ["pending", "in_progress"]
        assert sync_response.estimated_duration is not None
        assert "sync_id" in sync_response.model_dump()
    
    async def test_integration_health_monitoring(self, db_session):
        """Test integration health monitoring with ADHD considerations."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        integration = await IntegrationFactory.create(
            db_session,
            user_id=user.id,
            integration_type=IntegrationType.SLACK,
            status=IntegrationStatus.ACTIVE,
            error_count=0,
            total_syncs=50,
            successful_syncs=48
        )
        
        service = IntegrationService(db_session)
        
        health = await service.check_integration_health(
            user_id=user.id,
            integration_id=integration.id
        )
        
        assert health.integration_id == integration.id
        assert health.is_healthy == True
        assert health.token_status in ["valid", "expiring_soon", "expired", "missing"]
        assert isinstance(health.recommendations, list)
        
        # ADHD-friendly recommendations should be supportive
        for recommendation in health.recommendations:
            assert not any(word in recommendation.lower() 
                         for word in ["failed", "broken", "error"]), \
                   "Recommendations should use supportive language"
