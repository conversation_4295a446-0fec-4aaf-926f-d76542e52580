"""
Integration tests for time blindness support features.

This module tests features specifically designed to combat time blindness
in ADHD users, including visual time interfaces, buffer time insertion,
and time estimation tracking.
"""

import pytest
from datetime import datetime, timedelta
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from tests.factories import UserFactory, TaskFactory
from tests.utils import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ADHDTestAssertions


class TestTimeBlindnessSupport:
    """Test features specifically designed to combat time blindness."""
    
    @pytest.mark.integration
    @pytest.mark.adhd
    async def test_visual_time_interface_generation(
        self, 
        client: AsyncClient, 
        authenticated_user: dict
    ):
        """Test circular clock and timeline view generation."""
        
        # Create time blocks for testing
        user_id = authenticated_user["user"].id
        token = authenticated_user["token"]
        
        # Create sample tasks with different durations
        tasks_data = [
            {
                "title": "Morning Task",
                "estimated_duration": 60,
                "energy_level": "high",
                "context_tags": ["morning", "computer"]
            },
            {
                "title": "Afternoon Task", 
                "estimated_duration": 45,
                "energy_level": "medium",
                "context_tags": ["afternoon", "phone"]
            },
            {
                "title": "Evening Task",
                "estimated_duration": 30,
                "energy_level": "low",
                "context_tags": ["evening", "home"]
            }
        ]
        
        # Create tasks via API
        created_tasks = []
        for task_data in tasks_data:
            response = await client.post(
                "/api/v1/tasks/",
                json=task_data,
                headers={"Authorization": f"Bearer {token}"}
            )
            assert response.status_code == 201
            created_tasks.append(response.json())
        
        # Test circular view generation
        today = datetime.now().date()
        response = await client.get(
            f"/api/v1/timeblocks/daily/{today}?view_type=circular",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify circular view data structure
        assert "circular_segments" in data
        assert "total_hours" in data
        assert data["total_hours"] == 24
        
        # Verify proportional time representation
        segments = data["circular_segments"]
        total_degrees = sum(segment["degrees"] for segment in segments)
        assert abs(total_degrees - 360) < 1  # Allow for rounding
        
        # Verify visual cues for ADHD users
        assert "visual_cues" in data
        assert data["visual_cues"]["color_coding"] is True
        assert data["visual_cues"]["time_remaining_indicators"] is True
    
    @pytest.mark.integration
    @pytest.mark.adhd
    async def test_buffer_time_automatic_insertion(
        self, 
        client: AsyncClient, 
        authenticated_user: dict
    ):
        """Test automatic buffer time insertion for appointments."""
        
        token = authenticated_user["token"]
        
        # Create appointment without explicit buffer
        appointment_data = {
            "title": "Doctor Appointment",
            "start_time": (datetime.now() + timedelta(days=1)).isoformat(),
            "duration": 60,
            "block_type": "appointment",
            "auto_buffer": True  # Enable automatic buffer insertion
        }
        
        response = await client.post(
            "/api/v1/timeblocks/",
            json=appointment_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 201
        created_block = response.json()
        
        # Verify buffer times were automatically applied
        assert "buffer_before" in created_block
        assert "buffer_after" in created_block
        assert created_block["buffer_before"] >= 15  # ADHD-friendly buffer
        assert created_block["buffer_after"] >= 10
        
        # Check that schedule validation accounts for buffers
        validation_date = (datetime.now() + timedelta(days=1)).date()
        validation_response = await client.post(
            f"/api/v1/timeblocks/validate/{validation_date}",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert validation_response.status_code == 200
        validation_data = validation_response.json()
        
        # Verify buffer conflict detection
        assert "buffer_conflicts" in validation_data
        assert "total_scheduled_time" in validation_data
        
        # Total time should include buffers
        expected_total = appointment_data["duration"] + created_block["buffer_before"] + created_block["buffer_after"]
        assert validation_data["total_scheduled_time"] >= expected_total
    
    @pytest.mark.integration
    @pytest.mark.adhd
    async def test_time_estimation_accuracy_tracking(
        self, 
        client: AsyncClient, 
        authenticated_user: dict,
        db_session: AsyncSession
    ):
        """Test time estimation accuracy tracking for learning."""
        
        token = authenticated_user["token"]
        user_id = authenticated_user["user"].id
        
        # Create task with initial time estimate
        task_data = {
            "title": "Time Estimation Test Task",
            "description": "Task to test time estimation accuracy",
            "estimated_duration": 45,  # 45 minutes estimated
            "energy_level": "medium",
            "context_tags": ["computer", "test"]
        }
        
        # Create task
        response = await client.post(
            "/api/v1/tasks/",
            json=task_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 201
        task = response.json()
        task_id = task["id"]
        
        # Start the task
        start_response = await client.patch(
            f"/api/v1/tasks/{task_id}",
            json={"status": "in_progress"},
            headers={"Authorization": f"Bearer {token}"}
        )
        assert start_response.status_code == 200
        
        # Simulate task completion with actual duration
        completion_data = {
            "status": "completed",
            "actual_duration": 75  # 75 minutes actual (underestimated)
        }
        
        complete_response = await client.patch(
            f"/api/v1/tasks/{task_id}",
            json=completion_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert complete_response.status_code == 200
        completed_task = complete_response.json()
        
        # Verify time estimation tracking
        assert completed_task["actual_duration"] == 75
        assert completed_task["estimated_duration"] == 45
        
        # Calculate and verify accuracy
        expected_accuracy = 45 / 75  # 0.6 (60% accuracy)
        assert abs(completed_task["duration_accuracy"] - expected_accuracy) < 0.01
        
        # Check that learning data is stored for future estimates
        stats_response = await client.get(
            f"/api/v1/users/{user_id}/time-estimation-stats",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert stats_response.status_code == 200
        stats = stats_response.json()
        
        assert "average_accuracy" in stats
        assert "underestimation_tendency" in stats
        assert stats["underestimation_tendency"] > 0  # User tends to underestimate
    
    @pytest.mark.integration
    @pytest.mark.adhd
    async def test_time_awareness_notifications(
        self, 
        client: AsyncClient, 
        authenticated_user: dict
    ):
        """Test time awareness notifications for ADHD users."""
        
        token = authenticated_user["token"]
        
        # Create focus session with time awareness features
        session_data = {
            "session_type": "pomodoro",
            "planned_duration": 25,
            "time_awareness_notifications": True,
            "gentle_reminders": True,
            "visual_time_cues": True
        }
        
        response = await client.post(
            "/api/v1/focus-sessions/",
            json=session_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 201
        session = response.json()
        session_id = session["id"]
        
        # Start the session
        start_response = await client.patch(
            f"/api/v1/focus-sessions/{session_id}/start",
            headers={"Authorization": f"Bearer {token}"}
        )
        assert start_response.status_code == 200
        
        # Wait for time awareness notification (simulated)
        await AsyncTestHelper.simulate_user_delay("adhd_typical", 0.1)
        
        # Check for time awareness notifications
        notifications_response = await client.get(
            f"/api/v1/focus-sessions/{session_id}/notifications",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert notifications_response.status_code == 200
        notifications = notifications_response.json()
        
        # Verify ADHD-specific notification features
        assert len(notifications) > 0
        
        time_notification = next(
            (n for n in notifications if n["type"] == "time_awareness"), 
            None
        )
        assert time_notification is not None
        assert time_notification["gentle"] is True
        assert time_notification["visual_cues"] is True
    
    @pytest.mark.integration
    @pytest.mark.adhd
    async def test_transition_time_calculation(
        self, 
        client: AsyncClient, 
        authenticated_user: dict
    ):
        """Test automatic transition time calculation between tasks."""
        
        token = authenticated_user["token"]
        
        # Create two tasks with different contexts
        task1_data = {
            "title": "Home Computer Task",
            "estimated_duration": 60,
            "context_tags": ["home", "computer"],
            "location": "home_office"
        }
        
        task2_data = {
            "title": "Errands Task",
            "estimated_duration": 45,
            "context_tags": ["errands", "car"],
            "location": "grocery_store"
        }
        
        # Create both tasks
        response1 = await client.post(
            "/api/v1/tasks/",
            json=task1_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        assert response1.status_code == 201
        task1 = response1.json()
        
        response2 = await client.post(
            "/api/v1/tasks/",
            json=task2_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        assert response2.status_code == 201
        task2 = response2.json()
        
        # Request transition time calculation
        transition_response = await client.post(
            "/api/v1/scheduling/calculate-transition",
            json={
                "from_task_id": task1["id"],
                "to_task_id": task2["id"],
                "include_adhd_buffer": True
            },
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert transition_response.status_code == 200
        transition_data = transition_response.json()
        
        # Verify transition time calculation
        assert "transition_time" in transition_data
        assert "context_switch_penalty" in transition_data
        assert "travel_time" in transition_data
        assert "adhd_buffer" in transition_data
        
        # ADHD users need extra time for context switching
        assert transition_data["context_switch_penalty"] >= 10  # At least 10 minutes
        assert transition_data["adhd_buffer"] >= 5  # Additional buffer
        
        # Total transition time should account for ADHD needs
        total_transition = (
            transition_data["travel_time"] + 
            transition_data["context_switch_penalty"] + 
            transition_data["adhd_buffer"]
        )
        assert transition_data["transition_time"] == total_transition
    
    @pytest.mark.integration
    @pytest.mark.adhd
    async def test_hyperfocus_time_protection(
        self, 
        client: AsyncClient, 
        authenticated_user: dict
    ):
        """Test hyperfocus detection and time protection features."""
        
        token = authenticated_user["token"]
        
        # Create extended focus session (simulating hyperfocus)
        session_data = {
            "session_type": "deep_work",
            "planned_duration": 60,
            "hyperfocus_protection": True,
            "gentle_break_reminders": True
        }
        
        response = await client.post(
            "/api/v1/focus-sessions/",
            json=session_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 201
        session = response.json()
        session_id = session["id"]
        
        # Start session
        start_response = await client.patch(
            f"/api/v1/focus-sessions/{session_id}/start",
            headers={"Authorization": f"Bearer {token}"}
        )
        assert start_response.status_code == 200
        
        # Simulate extended session (hyperfocus detected)
        update_response = await client.patch(
            f"/api/v1/focus-sessions/{session_id}",
            json={
                "actual_duration": 120,  # 2 hours - hyperfocus detected
                "hyperfocus_detected": True
            },
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert update_response.status_code == 200
        updated_session = update_response.json()
        
        # Verify hyperfocus detection
        assert updated_session["hyperfocus_detected"] is True
        assert updated_session["actual_duration"] > updated_session["planned_duration"]
        
        # Check for gentle break reminders
        reminders_response = await client.get(
            f"/api/v1/focus-sessions/{session_id}/break-reminders",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert reminders_response.status_code == 200
        reminders = reminders_response.json()
        
        # Verify gentle reminders were sent
        assert len(reminders) > 0
        assert all(reminder["gentle"] is True for reminder in reminders)
        assert any(reminder["type"] == "hyperfocus_break" for reminder in reminders)
