"""
Performance tests for ADHD-specific features.

This module tests performance characteristics that are critical for ADHD users,
including response times, memory usage, and system responsiveness.
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta
from unittest.mock import patch
import psutil
import gc

from app.services.task_service import TaskService
from app.services.ai_service import AIService
from app.services.time_block_service import TimeBlockService
from app.services.integration_service import IntegrationService
from tests.factories import UserFactory, TaskFactory, TimeBlockFactory


@pytest.mark.performance
class TestResponseTimeRequirements:
    """Test response time requirements for ADHD users."""
    
    async def test_task_creation_response_time(self, client, authenticated_user):
        """Test task creation responds within ADHD-friendly timeframe."""
        start_time = time.time()
        
        response = await client.post(
            "/api/v1/tasks",
            headers=authenticated_user["headers"],
            json={
                "title": "Test Task for Performance",
                "description": "Testing response time",
                "estimated_duration": 60,
                "energy_level": "medium"
            }
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        assert response.status_code == 201
        assert response_time < 0.5, f"Task creation took {response_time:.3f}s, should be < 0.5s"
    
    async def test_ai_chunking_performance(self, db_session):
        """Test AI chunking completes within acceptable timeframe."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        task = await TaskFactory.create(
            db_session,
            user_id=user.id,
            title="Large complex project requiring chunking",
            description="Multi-phase project with various components and dependencies",
            estimated_duration=240,
            complexity="high"
        )
        
        task_service = TaskService(db_session)
        
        # Mock AI service to simulate processing time
        mock_chunks = [
            {"title": f"Chunk {i}", "description": f"Part {i}", "estimated_duration": 45, "energy_level": "medium", "order": i}
            for i in range(1, 6)
        ]
        
        start_time = time.time()
        
        with patch.object(AIService, 'chunk_task', return_value=mock_chunks):
            chunks = await task_service.chunk_task(
                user_id=user.id,
                task_id=task.id,
                chunk_request={"max_chunk_duration": 60}
            )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        assert len(chunks) == 5
        assert processing_time < 2.0, f"AI chunking took {processing_time:.3f}s, should be < 2.0s"
    
    async def test_schedule_visualization_performance(self, db_session):
        """Test schedule visualization renders quickly for ADHD users."""
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "visual_preferences": {
                    "time_display": "circular",
                    "show_progress_bars": True
                }
            }
        )
        
        # Create a full day of time blocks
        today = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        time_blocks = []
        
        for hour in range(8, 18):  # 8 AM to 6 PM
            for slot in [0, 30]:  # Every 30 minutes
                time_block = await TimeBlockFactory.create(
                    db_session,
                    user_id=user.id,
                    start_time=today.replace(hour=hour, minute=slot),
                    end_time=today.replace(hour=hour, minute=slot + 30),
                    energy_level="medium"
                )
                time_blocks.append(time_block)
        
        time_block_service = TimeBlockService(db_session)
        
        start_time = time.time()
        
        visual_data = await time_block_service.get_visual_schedule(
            user_id=user.id,
            date=today.date(),
            view_type="circular"
        )
        
        end_time = time.time()
        render_time = end_time - start_time
        
        assert "time_segments" in visual_data
        assert len(visual_data["time_segments"]) == len(time_blocks)
        assert render_time < 1.0, f"Schedule visualization took {render_time:.3f}s, should be < 1.0s"
    
    async def test_real_time_updates_latency(self, client, authenticated_user):
        """Test real-time updates have low latency for ADHD attention spans."""
        # This would test WebSocket performance in a real implementation
        # For now, test API update speed
        
        # Create a task first
        create_response = await client.post(
            "/api/v1/tasks",
            headers=authenticated_user["headers"],
            json={
                "title": "Task for Update Test",
                "estimated_duration": 60
            }
        )
        
        task_id = create_response.json()["data"]["id"]
        
        start_time = time.time()
        
        # Update the task
        update_response = await client.put(
            f"/api/v1/tasks/{task_id}",
            headers=authenticated_user["headers"],
            json={
                "title": "Updated Task Title",
                "status": "in_progress"
            }
        )
        
        end_time = time.time()
        update_time = end_time - start_time
        
        assert update_response.status_code == 200
        assert update_time < 0.3, f"Task update took {update_time:.3f}s, should be < 0.3s"


@pytest.mark.performance
class TestMemoryUsageOptimization:
    """Test memory usage optimization for sustained ADHD user sessions."""
    
    async def test_task_list_memory_efficiency(self, db_session):
        """Test memory efficiency when loading large task lists."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        
        # Create many tasks to test memory usage
        tasks = []
        for i in range(100):
            task = await TaskFactory.create(
                db_session,
                user_id=user.id,
                title=f"Task {i}",
                estimated_duration=30
            )
            tasks.append(task)
        
        task_service = TaskService(db_session)
        
        # Measure memory before loading
        gc.collect()
        process = psutil.Process()
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        
        # Load all tasks
        loaded_tasks = await task_service.get_user_tasks(
            user_id=user.id,
            limit=100
        )
        
        # Measure memory after loading
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = memory_after - memory_before
        
        assert len(loaded_tasks) == 100
        assert memory_increase < 50, f"Memory increased by {memory_increase:.1f}MB, should be < 50MB"
    
    async def test_integration_sync_memory_stability(self, db_session):
        """Test memory stability during integration synchronization."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        
        # Simulate multiple integration syncs
        integration_service = IntegrationService(db_session)
        
        gc.collect()
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Simulate 10 sync operations
        for i in range(10):
            # This would normally involve actual API calls
            # For testing, we simulate the memory impact
            await asyncio.sleep(0.1)  # Simulate processing time
            
            # Force garbage collection to test for memory leaks
            gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_growth = final_memory - initial_memory
        
        assert memory_growth < 20, f"Memory grew by {memory_growth:.1f}MB, should be < 20MB"


@pytest.mark.performance
class TestConcurrentUserHandling:
    """Test system performance under concurrent ADHD user load."""
    
    async def test_concurrent_task_operations(self, db_session):
        """Test handling multiple concurrent task operations."""
        # Create multiple users
        users = []
        for i in range(10):
            user = await UserFactory.create(
                db_session,
                adhd_diagnosed=True,
                email=f"user{i}@test.com"
            )
            users.append(user)
        
        task_service = TaskService(db_session)
        
        async def create_tasks_for_user(user):
            """Create tasks for a single user."""
            tasks = []
            for j in range(5):
                task = await TaskFactory.create(
                    db_session,
                    user_id=user.id,
                    title=f"Task {j} for {user.email}",
                    estimated_duration=30
                )
                tasks.append(task)
            return tasks
        
        start_time = time.time()
        
        # Create tasks concurrently for all users
        task_creation_coroutines = [
            create_tasks_for_user(user) for user in users
        ]
        
        results = await asyncio.gather(*task_creation_coroutines)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Verify all tasks were created
        total_tasks = sum(len(user_tasks) for user_tasks in results)
        assert total_tasks == 50  # 10 users × 5 tasks each
        
        # Should complete within reasonable time even with concurrency
        assert total_time < 5.0, f"Concurrent task creation took {total_time:.3f}s, should be < 5.0s"
    
    async def test_concurrent_schedule_access(self, db_session):
        """Test concurrent access to schedule data."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        
        # Create time blocks
        today = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        for hour in range(9, 17):
            await TimeBlockFactory.create(
                db_session,
                user_id=user.id,
                start_time=today.replace(hour=hour),
                end_time=today.replace(hour=hour + 1)
            )
        
        time_block_service = TimeBlockService(db_session)
        
        async def get_schedule():
            """Get schedule for the user."""
            return await time_block_service.get_user_schedule(
                user_id=user.id,
                start_date=today.date(),
                end_date=today.date()
            )
        
        start_time = time.time()
        
        # Simulate 20 concurrent schedule requests
        schedule_requests = [get_schedule() for _ in range(20)]
        schedules = await asyncio.gather(*schedule_requests)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Verify all requests succeeded
        assert len(schedules) == 20
        assert all(len(schedule) == 8 for schedule in schedules)  # 8 time blocks
        
        # Should handle concurrent access efficiently
        assert total_time < 2.0, f"Concurrent schedule access took {total_time:.3f}s, should be < 2.0s"


@pytest.mark.performance
class TestADHDSpecificOptimizations:
    """Test performance optimizations specific to ADHD user needs."""
    
    async def test_energy_pattern_analysis_speed(self, db_session):
        """Test energy pattern analysis completes quickly."""
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "energy_patterns": {
                    "morning": "high",
                    "afternoon": "medium",
                    "evening": "low"
                }
            }
        )
        
        # Create historical task completion data
        for i in range(50):
            await TaskFactory.create(
                db_session,
                user_id=user.id,
                status="completed",
                completed_at=datetime.utcnow() - timedelta(days=i),
                energy_level=["low", "medium", "high"][i % 3]
            )
        
        task_service = TaskService(db_session)
        
        start_time = time.time()
        
        # Analyze energy patterns
        energy_analysis = await task_service.analyze_energy_patterns(
            user_id=user.id,
            days_back=30
        )
        
        end_time = time.time()
        analysis_time = end_time - start_time
        
        assert "patterns" in energy_analysis
        assert "recommendations" in energy_analysis
        assert analysis_time < 1.0, f"Energy analysis took {analysis_time:.3f}s, should be < 1.0s"
    
    async def test_focus_session_startup_speed(self, db_session):
        """Test focus session starts quickly to maintain ADHD momentum."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        
        from app.services.focus_session_service import FocusSessionService
        focus_service = FocusSessionService(db_session)
        
        start_time = time.time()
        
        session = await focus_service.start_focus_session(
            user_id=user.id,
            session_data={
                "session_type": "pomodoro",
                "title": "Quick Focus Session",
                "planned_duration": 25
            }
        )
        
        end_time = time.time()
        startup_time = end_time - start_time
        
        assert session.status == "active"
        assert startup_time < 0.2, f"Focus session startup took {startup_time:.3f}s, should be < 0.2s"
    
    async def test_notification_delivery_speed(self, db_session):
        """Test notifications are delivered quickly for ADHD attention spans."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        
        from app.services.notification_service import NotificationService
        notification_service = NotificationService(db_session)
        
        start_time = time.time()
        
        notification = await notification_service.create_notification(
            user_id=user.id,
            notification_data={
                "type": "task_reminder",
                "title": "Task Due Soon",
                "message": "Your important task is due in 15 minutes",
                "priority": "high"
            }
        )
        
        end_time = time.time()
        delivery_time = end_time - start_time
        
        assert notification.status == "sent"
        assert delivery_time < 0.1, f"Notification delivery took {delivery_time:.3f}s, should be < 0.1s"


@pytest.mark.performance
@pytest.mark.slow
class TestLongRunningOperations:
    """Test performance of long-running operations that ADHD users might abandon."""

    async def test_bulk_task_import_with_progress(self, db_session):
        """Test bulk import provides progress updates for ADHD users."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)

        # Simulate importing 100 tasks
        task_data = [
            {
                "title": f"Imported Task {i}",
                "description": f"Task imported from external service {i}",
                "estimated_duration": 30,
                "energy_level": ["low", "medium", "high"][i % 3]
            }
            for i in range(100)
        ]

        task_service = TaskService(db_session)

        start_time = time.time()
        progress_updates = []

        # Mock progress callback
        async def progress_callback(current, total, message):
            progress_updates.append({
                "current": current,
                "total": total,
                "message": message,
                "timestamp": time.time()
            })

        # Simulate bulk import with progress tracking
        imported_tasks = []
        for i, task_data_item in enumerate(task_data):
            task = await TaskFactory.create(
                db_session,
                user_id=user.id,
                **task_data_item
            )
            imported_tasks.append(task)

            # Simulate progress updates every 10 tasks
            if (i + 1) % 10 == 0:
                await progress_callback(i + 1, len(task_data), f"Imported {i + 1} tasks")

        end_time = time.time()
        total_time = end_time - start_time

        assert len(imported_tasks) == 100
        assert len(progress_updates) == 10  # Progress updates every 10 tasks
        assert total_time < 10.0, f"Bulk import took {total_time:.3f}s, should be < 10.0s"

        # Verify progress updates were timely
        for i, update in enumerate(progress_updates):
            if i > 0:
                time_between_updates = update["timestamp"] - progress_updates[i-1]["timestamp"]
                assert time_between_updates < 2.0, "Progress updates should be frequent for ADHD users"


@pytest.mark.performance
@pytest.mark.adhd_critical
class TestADHDCriticalResponseTimes:
    """Test response times for ADHD-critical features that must be instant."""

    async def test_quick_capture_instant_response(self, client, authenticated_user):
        """Test quick task capture has instant response for ADHD thought capture."""
        # ADHD users lose thoughts quickly - capture must be under 1 second
        start_time = time.time()

        response = await client.post(
            "/api/v1/tasks/quick-capture",
            headers=authenticated_user["headers"],
            json={
                "title": "Random thought that just occurred",
                "capture_method": "voice",
                "auto_process": True
            }
        )

        end_time = time.time()
        capture_time = end_time - start_time

        assert response.status_code == 201
        assert capture_time < 1.0, f"Quick capture took {capture_time:.3f}s, must be < 1.0s for ADHD"

        # Verify AI processing happened in background
        task_data = response.json()
        assert "ai_suggestions" in task_data
        assert task_data["estimated_duration"] > 0

    async def test_dopamine_menu_instant_load(self, client, authenticated_user):
        """Test dopamine menu loads instantly when motivation is low."""
        # When ADHD users need motivation boost, delay kills the effect
        start_time = time.time()

        response = await client.get(
            "/api/v1/motivation/dopamine-menu",
            headers=authenticated_user["headers"],
            params={
                "energy_level": "very_low",
                "available_time": 5,
                "urgency": "high"
            }
        )

        end_time = time.time()
        load_time = end_time - start_time

        assert response.status_code == 200
        assert load_time < 0.8, f"Dopamine menu took {load_time:.3f}s, must be < 0.8s"

        menu_data = response.json()
        assert len(menu_data["activities"]) >= 3
        assert all(activity["duration"] <= 5 for activity in menu_data["activities"])

    async def test_hyperfocus_break_immediate_response(self, client, authenticated_user):
        """Test hyperfocus break suggestions respond immediately."""
        # Create active focus session
        session_response = await client.post(
            "/api/v1/focus/sessions",
            headers=authenticated_user["headers"],
            json={
                "session_type": "deep_work",
                "planned_duration": 90,
                "enable_hyperfocus_protection": True
            }
        )

        session_id = session_response.json()["data"]["id"]

        # Start session
        await client.post(
            f"/api/v1/focus/sessions/{session_id}/start",
            headers=authenticated_user["headers"]
        )

        # Test break suggestion speed
        start_time = time.time()

        response = await client.get(
            f"/api/v1/focus/sessions/{session_id}/break-suggestions",
            headers=authenticated_user["headers"]
        )

        end_time = time.time()
        suggestion_time = end_time - start_time

        assert response.status_code == 200
        assert suggestion_time < 0.5, f"Break suggestions took {suggestion_time:.3f}s, must be < 0.5s"

    async def test_energy_update_instant_feedback(self, client, authenticated_user):
        """Test energy level updates provide instant feedback."""
        start_time = time.time()

        response = await client.post(
            "/api/v1/user/energy-update",
            headers=authenticated_user["headers"],
            json={
                "current_energy": "low",
                "energy_trend": "declining",
                "context": "post_lunch_crash",
                "request_suggestions": True
            }
        )

        end_time = time.time()
        update_time = end_time - start_time

        assert response.status_code == 200
        assert update_time < 0.3, f"Energy update took {update_time:.3f}s, must be < 0.3s"

        # Should include immediate suggestions
        update_data = response.json()
        assert "immediate_suggestions" in update_data
        assert "energy_boost_activities" in update_data


@pytest.mark.performance
class TestADHDWorkflowPerformance:
    """Test performance of complete ADHD workflows."""

    async def test_overwhelming_task_to_chunks_workflow(self, db_session):
        """Test complete workflow from overwhelming task to manageable chunks."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)

        # Create overwhelming task
        overwhelming_task = await TaskFactory.create(
            db_session,
            user_id=user.id,
            title="Reorganize entire home office and filing system",
            description="Complete overhaul of workspace including digital and physical organization",
            estimated_duration=480,  # 8 hours - very overwhelming
            complexity="very_high"
        )

        task_service = TaskService(db_session)

        # Test complete workflow timing
        workflow_start = time.time()

        # Step 1: AI analysis and chunking
        chunk_start = time.time()

        with patch.object(AIService, 'chunk_task') as mock_chunk:
            mock_chunk.return_value = [
                {
                    "title": "Clear desk surface completely",
                    "description": "Remove all items, clean surface, only return essentials",
                    "estimated_duration": 30,
                    "energy_level": "low",
                    "order": 1
                },
                {
                    "title": "Sort papers into keep/file/shred piles",
                    "description": "Go through all papers and documents systematically",
                    "estimated_duration": 45,
                    "energy_level": "medium",
                    "order": 2
                },
                {
                    "title": "Set up digital filing system",
                    "description": "Create folder structure and organize computer files",
                    "estimated_duration": 60,
                    "energy_level": "high",
                    "order": 3
                }
            ]

            chunks = await task_service.chunk_task(
                user_id=user.id,
                task_id=overwhelming_task.id,
                chunk_request={"max_chunk_duration": 60}
            )

        chunk_time = time.time() - chunk_start

        # Step 2: Schedule first chunk
        schedule_start = time.time()

        time_block_service = TimeBlockService(db_session)
        first_chunk_block = await time_block_service.create_time_block(
            user_id=user.id,
            time_block_data={
                "title": chunks[0]["title"],
                "start_time": datetime.utcnow() + timedelta(hours=1),
                "duration_minutes": chunks[0]["estimated_duration"],
                "energy_level": chunks[0]["energy_level"],
                "task_id": overwhelming_task.id
            }
        )

        schedule_time = time.time() - schedule_start

        workflow_total = time.time() - workflow_start

        # Verify workflow completed successfully
        assert len(chunks) == 3
        assert all(chunk["estimated_duration"] <= 60 for chunk in chunks)
        assert first_chunk_block.duration_minutes == 30

        # Verify timing requirements for ADHD users
        assert chunk_time < 3.0, f"AI chunking took {chunk_time:.3f}s, should be < 3.0s"
        assert schedule_time < 1.0, f"Scheduling took {schedule_time:.3f}s, should be < 1.0s"
        assert workflow_total < 5.0, f"Complete workflow took {workflow_total:.3f}s, should be < 5.0s"

    async def test_focus_session_complete_cycle_performance(self, db_session):
        """Test complete focus session cycle performance."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)
        task = await TaskFactory.create(db_session, user_id=user.id)

        from app.services.focus_session_service import FocusSessionService
        focus_service = FocusSessionService(db_session)

        cycle_start = time.time()

        # Create session
        create_start = time.time()
        session = await focus_service.create_focus_session(
            user_id=user.id,
            session_data={
                "task_id": task.id,
                "session_type": "pomodoro",
                "planned_duration": 25,
                "enable_hyperfocus_protection": True
            }
        )
        create_time = time.time() - create_start

        # Start session
        start_session_time = time.time()
        await focus_service.start_session(session.id)
        start_time = time.time() - start_session_time

        # Simulate session completion
        complete_start = time.time()
        await focus_service.complete_session(
            session_id=session.id,
            completion_data={
                "actual_duration": 23,
                "completion_notes": "Completed successfully",
                "energy_after": "medium"
            }
        )
        complete_time = time.time() - complete_start

        cycle_total = time.time() - cycle_start

        # Verify timing for ADHD attention spans
        assert create_time < 0.5, f"Session creation took {create_time:.3f}s, should be < 0.5s"
        assert start_time < 0.2, f"Session start took {start_time:.3f}s, should be < 0.2s"
        assert complete_time < 1.0, f"Session completion took {complete_time:.3f}s, should be < 1.0s"
        assert cycle_total < 2.0, f"Complete cycle took {cycle_total:.3f}s, should be < 2.0s"


@pytest.mark.performance
class TestADHDStressConditions:
    """Test performance under conditions that stress ADHD users."""

    async def test_high_cognitive_load_response_times(self, db_session):
        """Test response times remain good under high cognitive load simulation."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)

        # Create high cognitive load scenario: many overdue tasks
        overdue_tasks = []
        for i in range(20):
            task = await TaskFactory.create(
                db_session,
                user_id=user.id,
                title=f"Overdue Task {i}",
                due_date=datetime.utcnow() - timedelta(days=i+1),
                priority="high",
                status="pending"
            )
            overdue_tasks.append(task)

        task_service = TaskService(db_session)

        # Test task list loading under stress
        stress_start = time.time()

        task_list = await task_service.get_user_tasks(
            user_id=user.id,
            include_overdue=True,
            sort_by="urgency"
        )

        stress_time = time.time() - stress_start

        assert len(task_list) == 20
        assert stress_time < 1.5, f"Task loading under stress took {stress_time:.3f}s, should be < 1.5s"

        # Test priority calculation under stress
        priority_start = time.time()

        priority_analysis = await task_service.analyze_task_priorities(
            user_id=user.id,
            context="high_stress"
        )

        priority_time = time.time() - priority_start

        assert "urgent_tasks" in priority_analysis
        assert priority_time < 2.0, f"Priority analysis took {priority_time:.3f}s, should be < 2.0s"

    async def test_interruption_recovery_performance(self, db_session):
        """Test system performance when recovering from interruptions."""
        user = await UserFactory.create(db_session, adhd_diagnosed=True)

        from app.services.focus_session_service import FocusSessionService
        focus_service = FocusSessionService(db_session)

        # Create and start focus session
        session = await focus_service.create_focus_session(
            user_id=user.id,
            session_data={
                "session_type": "deep_work",
                "planned_duration": 60
            }
        )

        await focus_service.start_session(session.id)

        # Simulate interruption and recovery
        interruption_start = time.time()

        # Pause for interruption
        await focus_service.pause_session(
            session_id=session.id,
            reason="urgent_interruption"
        )

        # Resume after interruption
        resume_response = await focus_service.resume_session(
            session_id=session.id,
            resume_data={
                "interruption_duration": 10,
                "context_recovery_needed": True
            }
        )

        recovery_time = time.time() - interruption_start

        assert resume_response["status"] == "resumed"
        assert recovery_time < 1.0, f"Interruption recovery took {recovery_time:.3f}s, should be < 1.0s"

        # Verify context recovery suggestions are provided
        assert "context_recovery_suggestions" in resume_response
