"""
Tests for the motivation service.

This module tests ADHD-optimized motivation features including
dopamine menu, activity tracking, and personalization.
"""

import pytest
from uuid import uuid4

from app.services.motivation_service import MotivationService
from app.models.motivation import DopamineActivity, UserDopaminePreference, DopamineActivityCompletion
from app.models.user import User
from app.schemas.motivation import (
    DopamineMenuRequest,
    DopamineActivityCompletionCreate,
    CustomActivityRequest
)


class TestMotivationService:
    """Test suite for MotivationService."""
    
    @pytest.fixture
    async def mock_user(self, db_session):
        """Create a mock user for testing."""
        user = User(
            email="<EMAIL>",
            hashed_password="test_hash",
            first_name="Test",
            last_name="User",
            is_active=True,
            adhd_diagnosis=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user
    
    @pytest.fixture
    async def sample_activities(self, db_session):
        """Create sample dopamine activities."""
        activities = [
            DopamineActivity(
                name="Quick Walk",
                description="Take a 5-minute walk",
                category="movement",
                duration_min=3,
                duration_max=10,
                energy_requirement="low",
                energy_boost="medium",
                requires_equipment=False,
                requires_space=False,
                can_do_anywhere=True,
                tags=["outdoor", "exercise"]
            ),
            DopamineActivity(
                name="Deep Breathing",
                description="Practice breathing exercises",
                category="mental",
                duration_min=2,
                duration_max=5,
                energy_requirement="low",
                energy_boost="low",
                requires_equipment=False,
                requires_space=False,
                can_do_anywhere=True,
                tags=["relaxation", "mindfulness"]
            ),
            DopamineActivity(
                name="Dance Break",
                description="Dance to your favorite song",
                category="movement",
                duration_min=3,
                duration_max=5,
                energy_requirement="medium",
                energy_boost="high",
                requires_equipment=True,
                requires_space=True,
                can_do_anywhere=False,
                tags=["music", "exercise", "fun"]
            )
        ]
        
        for activity in activities:
            db_session.add(activity)
        
        await db_session.commit()
        
        for activity in activities:
            await db_session.refresh(activity)
        
        return activities
    
    @pytest.fixture
    def motivation_service(self, db_session):
        """Create a motivation service instance."""
        return MotivationService(db_session)
    
    async def test_initialize_default_activities(
        self, motivation_service, db_session
    ):
        """Test initializing default dopamine activities."""
        await motivation_service.initialize_default_activities()
        
        # Check that activities were created
        from sqlalchemy import select, func
        result = await db_session.execute(
            select(func.count(DopamineActivity.id))
        )
        count = result.scalar()
        
        assert count > 0
    
    async def test_get_dopamine_menu_basic(
        self, motivation_service, mock_user, sample_activities, db_session
    ):
        """Test getting basic dopamine menu."""
        request = DopamineMenuRequest(
            energy_level="medium",
            available_time=10,
            context="pre_task"
        )
        
        menu = await motivation_service.get_dopamine_menu(mock_user.id, request)
        
        assert menu is not None
        assert len(menu.activities) > 0
        assert menu.energy_level == "medium"
        assert menu.available_time == 10
        assert menu.context == "pre_task"
        assert 0.0 <= menu.personalization_score <= 1.0
    
    async def test_get_dopamine_menu_filtered(
        self, motivation_service, mock_user, sample_activities, db_session
    ):
        """Test getting filtered dopamine menu."""
        request = DopamineMenuRequest(
            energy_level="low",
            available_time=5,
            context="break",
            exclude_categories=["movement"],
            preferred_categories=["mental"]
        )
        
        menu = await motivation_service.get_dopamine_menu(mock_user.id, request)
        
        # Should only include mental activities
        for activity in menu.activities:
            assert activity.category == "mental"
            assert activity.duration_min <= 5
    
    async def test_complete_dopamine_activity(
        self, motivation_service, mock_user, sample_activities, db_session
    ):
        """Test completing a dopamine activity."""
        activity = sample_activities[0]  # Quick Walk
        
        completion_data = DopamineActivityCompletionCreate(
            user_id=mock_user.id,
            activity_id=activity.id,
            actual_duration=5,
            energy_before="low",
            energy_after="medium",
            mood_before=3,
            mood_after=6,
            satisfaction_rating=4,
            would_do_again=True,
            context="pre_task",
            notes="Felt refreshing"
        )
        
        completion = await motivation_service.complete_dopamine_activity(completion_data)
        
        assert completion is not None
        assert completion.user_id == mock_user.id
        assert completion.activity_id == activity.id
        assert completion.actual_duration == 5
        assert completion.energy_before == "low"
        assert completion.energy_after == "medium"
        assert completion.satisfaction_rating == 4
    
    async def test_create_custom_activity(
        self, motivation_service, mock_user, db_session
    ):
        """Test creating a custom dopamine activity."""
        activity_request = CustomActivityRequest(
            name="My Custom Activity",
            description="A personalized activity",
            category="creative",
            duration=10,
            energy_requirement="medium",
            energy_boost="high",
            tags=["personal", "creative"]
        )
        
        preference = await motivation_service.create_custom_activity(
            user_id=mock_user.id,
            activity_request=activity_request
        )
        
        assert preference is not None
        assert preference.user_id == mock_user.id
        assert preference.activity_id is None  # Custom activity
        assert preference.preference_type == "custom"
        assert preference.custom_name == "My Custom Activity"
        assert preference.custom_category == "creative"
        assert preference.rating == 5  # Default high rating
    
    async def test_get_motivation_analytics(
        self, motivation_service, mock_user, sample_activities, db_session
    ):
        """Test getting motivation analytics."""
        # Create some completion data first
        activity = sample_activities[0]
        
        completion_data = DopamineActivityCompletionCreate(
            user_id=mock_user.id,
            activity_id=activity.id,
            actual_duration=5,
            energy_before="low",
            energy_after="medium",
            satisfaction_rating=4,
            context="pre_task"
        )
        
        await motivation_service.complete_dopamine_activity(completion_data)
        
        # Get analytics
        analytics = await motivation_service.get_motivation_analytics(mock_user.id)
        
        assert analytics is not None
        assert analytics.total_activities_completed >= 1
        assert isinstance(analytics.favorite_categories, list)
        assert isinstance(analytics.recommendations, list)
    
    async def test_personalization_with_preferences(
        self, motivation_service, mock_user, sample_activities, db_session
    ):
        """Test activity personalization based on user preferences."""
        activity = sample_activities[0]  # Quick Walk
        
        # Create a preference for this activity
        preference = UserDopaminePreference(
            user_id=mock_user.id,
            activity_id=activity.id,
            preference_type="favorite",
            rating=5,
            times_completed=3
        )
        db_session.add(preference)
        await db_session.commit()
        
        # Get menu - should prioritize the favorite activity
        request = DopamineMenuRequest(
            energy_level="low",
            available_time=10,
            context="pre_task"
        )
        
        menu = await motivation_service.get_dopamine_menu(mock_user.id, request)
        
        # The favorite activity should be included and likely first
        activity_ids = [a.id for a in menu.activities]
        assert activity.id in activity_ids
        assert menu.personalization_score > 0.0
    
    async def test_activity_filtering_by_requirements(
        self, motivation_service, mock_user, sample_activities, db_session
    ):
        """Test filtering activities by equipment and space requirements."""
        request = DopamineMenuRequest(
            energy_level="medium",
            available_time=10,
            context="office"  # Assume limited space/equipment
        )
        
        menu = await motivation_service.get_dopamine_menu(mock_user.id, request)
        
        # Should prefer activities that can be done anywhere
        for activity in menu.activities:
            # Most activities should be doable in office setting
            assert activity.can_do_anywhere or not activity.requires_space
    
    async def test_energy_level_matching(
        self, motivation_service, mock_user, sample_activities, db_session
    ):
        """Test that activities match user's energy level."""
        # Test low energy request
        low_energy_request = DopamineMenuRequest(
            energy_level="low",
            available_time=10,
            context="tired"
        )
        
        menu = await motivation_service.get_dopamine_menu(mock_user.id, low_energy_request)
        
        # Should only include low energy activities
        for activity in menu.activities:
            assert activity.energy_requirement in ["low"]
    
    async def test_completion_updates_preferences(
        self, motivation_service, mock_user, sample_activities, db_session
    ):
        """Test that completing activities updates user preferences."""
        activity = sample_activities[0]
        
        # Complete activity with high satisfaction
        completion_data = DopamineActivityCompletionCreate(
            user_id=mock_user.id,
            activity_id=activity.id,
            actual_duration=5,
            energy_before="low",
            energy_after="medium",
            satisfaction_rating=5,
            would_do_again=True,
            context="pre_task"
        )
        
        await motivation_service.complete_dopamine_activity(completion_data)
        
        # Check that preference was created/updated
        from sqlalchemy import select
        result = await db_session.execute(
            select(UserDopaminePreference).where(
                UserDopaminePreference.user_id == mock_user.id,
                UserDopaminePreference.activity_id == activity.id
            )
        )
        preference = result.scalar_one_or_none()
        
        assert preference is not None
        assert preference.times_completed == 1
        assert preference.preference_type == "liked"  # High satisfaction
        assert preference.rating == 5
