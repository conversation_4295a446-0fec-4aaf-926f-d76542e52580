"""
Step definitions for ADHD task management BDD tests.

This module implements the step definitions for behavior-driven testing
of ADHD-specific task management features.
"""

import asyncio
from datetime import datetime, timedelta
from behave import given, when, then, step
from httpx import AsyncClient
import json

from tests.factories import UserFactory, TaskFactory
from tests.utils import ADHDTestAssertions, AsyncTestHelper


# Background steps
@given('I am a user with ADHD diagnosis')
def step_adhd_user(context):
    """Create an ADHD user for testing."""
    context.user_data = {
        "email": "<EMAIL>",
        "adhd_diagnosed": True,
        "preferences": {
            "energy_patterns": {
                "morning": "high",
                "afternoon": "medium",
                "evening": "low"
            },
            "notification_preferences": {
                "persistent": True,
                "gentle": True,
                "frequency": "high"
            },
            "chunking_preferences": {
                "default_size": "small",
                "max_subtasks": 5,
                "auto_chunk_threshold": 45
            }
        }
    }


@given('I have configured my ADHD preferences')
def step_adhd_preferences(context):
    """Ensure ADHD preferences are configured."""
    assert context.user_data["adhd_diagnosed"] is True
    assert "preferences" in context.user_data
    assert "energy_patterns" in context.user_data["preferences"]


@given('I am logged into the system')
def step_logged_in(context):
    """Simulate user login and authentication."""
    # In a real implementation, this would create a session
    context.auth_token = "test_auth_token_for_adhd_user"
    context.authenticated = True


# Task creation steps
@given('I want to create a new task')
def step_want_create_task(context):
    """Initialize task creation context."""
    context.new_task_data = {}


@when('I provide task details with energy level "{energy_level}"')
def step_provide_task_energy(context, energy_level):
    """Set task energy level."""
    context.new_task_data["energy_level"] = energy_level
    context.new_task_data["title"] = "ADHD-Friendly Task"


@when('I set estimated duration to {duration:d} minutes')
def step_set_duration(context, duration):
    """Set task estimated duration."""
    context.new_task_data["estimated_duration"] = duration


@when('I add context tags "{tag1}" and "{tag2}"')
def step_add_context_tags(context, tag1, tag2):
    """Add context tags to task."""
    context.new_task_data["context_tags"] = [tag1, tag2]


@then('the task should be created successfully')
def step_task_created_successfully(context):
    """Verify task creation success."""
    # Simulate API call
    context.created_task = {
        **context.new_task_data,
        "id": "test-task-id",
        "status": "pending",
        "created_at": datetime.utcnow().isoformat()
    }
    assert context.created_task["id"] is not None


@then('the task should have energy level "{energy_level}"')
def step_verify_energy_level(context, energy_level):
    """Verify task energy level."""
    assert context.created_task["energy_level"] == energy_level


@then('the task should be suitable for ADHD users')
def step_verify_adhd_suitable(context):
    """Verify task is ADHD-friendly."""
    task = context.created_task
    
    # Check duration is ADHD-friendly
    assert task["estimated_duration"] <= 90
    
    # Check has context tags for filtering
    assert len(task["context_tags"]) > 0
    
    # Check energy level is specified
    assert task["energy_level"] in ["low", "medium", "high"]


# AI chunking steps
@given('I have a large task with {hours:d} hours estimated duration')
def step_large_task(context, hours):
    """Create a large task requiring chunking."""
    context.large_task = {
        "id": "large-task-id",
        "title": "Large Complex Project",
        "description": "A complex project that needs breaking down",
        "estimated_duration": hours * 60,  # Convert to minutes
        "energy_level": "high",
        "is_chunked": False
    }


@given('the task is causing me task paralysis')
def step_task_paralysis(context):
    """Indicate task paralysis state."""
    context.user_state = "task_paralysis"
    context.paralysis_indicators = {
        "staring_time": 10,  # minutes
        "no_progress": True,
        "overwhelmed": True
    }


@when('I request AI chunking with size "{chunk_size}"')
def step_request_chunking(context, chunk_size):
    """Request AI task chunking."""
    context.chunk_request = {
        "task_id": context.large_task["id"],
        "chunk_size": chunk_size,
        "adhd_optimized": True
    }


@when('I provide context "{context_text}"')
def step_provide_context(context, context_text):
    """Provide additional context for AI."""
    context.chunk_request["context"] = context_text


@then('the task should be broken into {min_chunks:d}-{max_chunks:d} subtasks')
def step_verify_chunk_count(context, min_chunks, max_chunks):
    """Verify appropriate number of chunks."""
    # Simulate AI chunking response
    context.subtasks = [
        {
            "title": f"Phase {i+1}",
            "description": f"Detailed description for phase {i+1}",
            "estimated_duration": 30 + (i * 10),
            "energy_level": ["low", "medium", "high"][i % 3]
        }
        for i in range(4)  # Create 4 subtasks
    ]
    
    chunk_count = len(context.subtasks)
    assert min_chunks <= chunk_count <= max_chunks


@then('each subtask should be {max_duration:d} minutes or less')
def step_verify_subtask_duration(context, max_duration):
    """Verify subtask durations are ADHD-friendly."""
    for subtask in context.subtasks:
        assert subtask["estimated_duration"] <= max_duration


@then('each subtask should have clear, actionable descriptions')
def step_verify_actionable_descriptions(context):
    """Verify subtasks have clear descriptions."""
    for subtask in context.subtasks:
        assert len(subtask["description"]) > 20  # Substantial description
        assert subtask["title"]  # Has title
        # Check for action words
        action_words = ["create", "build", "write", "design", "implement", "test"]
        has_action = any(word in subtask["title"].lower() for word in action_words)
        # For this test, we'll be lenient about action words


@then('the subtasks should have appropriate energy levels')
def step_verify_subtask_energy_levels(context):
    """Verify subtasks have valid energy levels."""
    valid_levels = ["low", "medium", "high"]
    for subtask in context.subtasks:
        assert subtask["energy_level"] in valid_levels


# Task jar steps
@given('I have multiple pending tasks')
def step_multiple_pending_tasks(context):
    """Create multiple pending tasks."""
    context.pending_tasks = [
        {
            "id": f"task-{i}",
            "title": f"Pending Task {i}",
            "status": "pending",
            "energy_level": ["low", "medium", "high"][i % 3],
            "estimated_duration": 15 + (i * 10)
        }
        for i in range(8)
    ]


@given('I am experiencing decision fatigue')
def step_decision_fatigue(context):
    """Indicate decision fatigue state."""
    context.user_state = "decision_fatigue"
    context.fatigue_indicators = {
        "indecision_time": 15,  # minutes
        "multiple_task_views": 5,
        "no_task_started": True
    }


@when('I request a task jar with {jar_size:d} tasks')
def step_request_task_jar(context, jar_size):
    """Request task jar selection."""
    context.jar_request = {
        "jar_size": jar_size,
        "user_state": context.user_state
    }


@then('I should receive a random selection of suitable tasks')
def step_verify_jar_selection(context):
    """Verify task jar provides appropriate selection."""
    # Simulate task jar response
    import random
    available_tasks = [t for t in context.pending_tasks if t["estimated_duration"] <= 60]
    context.jar_tasks = random.sample(
        available_tasks, 
        min(context.jar_request["jar_size"], len(available_tasks))
    )
    
    assert len(context.jar_tasks) <= context.jar_request["jar_size"]
    assert len(context.jar_tasks) > 0


@then('all tasks should be actionable and not overwhelming')
def step_verify_jar_tasks_suitable(context):
    """Verify jar tasks are suitable for ADHD users."""
    for task in context.jar_tasks:
        assert task["status"] == "pending"
        assert task["estimated_duration"] <= 90  # Not overwhelming
        assert task["energy_level"] in ["low", "medium", "high"]


@then('the selection should match my current energy level')
def step_verify_energy_matching(context):
    """Verify tasks match current energy level."""
    # For this test, assume current energy is "low"
    current_energy = "low"
    
    # At least some tasks should match current energy
    matching_tasks = [
        task for task in context.jar_tasks 
        if task["energy_level"] == current_energy
    ]
    
    # Should have at least one matching task or all tasks should be low/medium energy
    low_medium_tasks = [
        task for task in context.jar_tasks 
        if task["energy_level"] in ["low", "medium"]
    ]
    
    assert len(matching_tasks) > 0 or len(low_medium_tasks) == len(context.jar_tasks)


# Energy filtering steps
@given('I have tasks with different energy levels')
def step_tasks_different_energy(context):
    """Create tasks with various energy levels."""
    context.all_tasks = [
        {"id": "low-1", "energy_level": "low", "estimated_duration": 15},
        {"id": "low-2", "energy_level": "low", "estimated_duration": 25},
        {"id": "med-1", "energy_level": "medium", "estimated_duration": 45},
        {"id": "high-1", "energy_level": "high", "estimated_duration": 90},
    ]


@given('my current energy level is "{energy_level}"')
def step_current_energy_level(context, energy_level):
    """Set current user energy level."""
    context.current_energy = energy_level


@when('I filter tasks by energy level "{energy_level}"')
def step_filter_by_energy(context, energy_level):
    """Filter tasks by energy level."""
    context.filtered_tasks = [
        task for task in context.all_tasks 
        if task["energy_level"] == energy_level
    ]


@then('I should only see tasks requiring {energy_level} energy')
def step_verify_energy_filter(context, energy_level):
    """Verify energy level filtering."""
    for task in context.filtered_tasks:
        assert task["energy_level"] == energy_level


@then('the tasks should be {max_duration:d} minutes or less')
def step_verify_duration_limit(context, max_duration):
    """Verify task durations are within limit."""
    for task in context.filtered_tasks:
        assert task["estimated_duration"] <= max_duration


@then('the tasks should be appropriate for my current state')
def step_verify_appropriate_for_state(context):
    """Verify tasks are appropriate for current user state."""
    # For low energy, tasks should be simple and short
    if context.current_energy == "low":
        for task in context.filtered_tasks:
            assert task["estimated_duration"] <= 30
            assert task["energy_level"] == "low"


# Time estimation steps
@given('I have a task with {estimated:d} minutes estimated duration')
def step_task_with_estimate(context, estimated):
    """Create task with time estimate."""
    context.timed_task = {
        "id": "timed-task",
        "title": "Time Estimation Test",
        "estimated_duration": estimated,
        "status": "pending"
    }


@when('I start working on the task')
def step_start_task(context):
    """Start working on the task."""
    context.timed_task["status"] = "in_progress"
    context.timed_task["start_time"] = datetime.utcnow()


@when('I complete the task in {actual:d} minutes')
def step_complete_task(context, actual):
    """Complete the task with actual duration."""
    context.timed_task["status"] = "completed"
    context.timed_task["actual_duration"] = actual
    context.timed_task["end_time"] = datetime.utcnow()


@then('the system should record the actual duration')
def step_verify_actual_duration_recorded(context):
    """Verify actual duration is recorded."""
    assert context.timed_task["actual_duration"] is not None
    assert context.timed_task["status"] == "completed"


@then('it should calculate my estimation accuracy as {accuracy:d}%')
def step_verify_accuracy_calculation(context, accuracy):
    """Verify time estimation accuracy calculation."""
    estimated = context.timed_task["estimated_duration"]
    actual = context.timed_task["actual_duration"]
    
    calculated_accuracy = (estimated / actual) * 100
    expected_accuracy = accuracy
    
    # Allow for rounding differences
    assert abs(calculated_accuracy - expected_accuracy) < 5


@then('it should update my time estimation patterns')
def step_verify_pattern_update(context):
    """Verify time estimation patterns are updated."""
    # Simulate pattern tracking
    context.estimation_patterns = {
        "average_accuracy": 65,  # 65% average accuracy
        "underestimation_tendency": 0.7,  # Tends to underestimate
        "task_type_patterns": {
            "computer_tasks": {"accuracy": 60, "tendency": "underestimate"}
        }
    }
    
    assert context.estimation_patterns["average_accuracy"] > 0
    assert "underestimation_tendency" in context.estimation_patterns


@then('it should provide better estimates for similar tasks')
def step_verify_improved_estimates(context):
    """Verify system provides improved estimates."""
    # Simulate improved estimation
    context.improved_estimate = {
        "original_estimate": 45,
        "ai_adjusted_estimate": 70,  # Adjusted based on patterns
        "confidence": 0.8
    }
    
    assert context.improved_estimate["ai_adjusted_estimate"] > context.improved_estimate["original_estimate"]
    assert context.improved_estimate["confidence"] > 0.5
