Feature: ADHD Focus Management
  As an ADHD user
  I want intelligent focus session management
  So that I can maintain productivity while protecting my mental health

  Background:
    Given I am an authenticated ADHD user
    And I have hyperfocus protection enabled
    And I have energy tracking enabled

  Sc<PERSON>rio: Starting a focus session with hyperfocus protection
    Given I have a task "Design new feature mockups"
    And the task requires "high" energy level
    When I start a deep work focus session
    And I set the planned duration to 90 minutes
    And I enable hyperfocus protection with threshold 75 minutes
    Then the session should start successfully
    And hyperfocus monitoring should be active
    And I should see a gentle reminder about the protection

  Scenario: Hyperfocus protection triggers gentle interruption
    Given I am in an active focus session
    And the session has been running for 80 minutes
    And my hyperfocus threshold is 75 minutes
    When the hyperfocus protection system checks my status
    Then I should receive a gentle interruption notification
    And the notification should suggest taking a break
    And the notification should not be jarring or stressful
    And I should have options to extend or take a break

  Sc<PERSON><PERSON>: Choosing to extend focus session mindfully
    Given I receive a hyperfocus protection notification
    And I feel I'm still productive and not tired
    When I choose to extend the session for 30 minutes
    Then the system should acknowledge my choice
    And set a new protection checkpoint
    And remind me to check in with my physical needs
    And track this as a mindful extension

  Scenario: Taking a restorative break
    Given I receive a hyperfocus protection notification
    And I decide to take a break
    When I pause the focus session
    Then the system should suggest break activities
    And the suggestions should match my current energy level
    And I should see options for 5, 10, and 15 minute breaks
    And the break timer should start automatically

  Scenario: Resuming after a restorative break
    Given I am on a break from a focus session
    And I have completed a 10-minute walk
    When I indicate I'm ready to resume
    Then the system should check my energy level
    And suggest whether to continue or switch tasks
    And resume the session with updated context
    And track the break effectiveness

  Scenario: Energy crash during focus session
    Given I am in an active focus session
    And I report my energy as "very low"
    When the system detects an energy crash
    Then it should suggest ending the session early
    And offer energy-boosting activities
    And reschedule remaining work for later
    And not penalize me for the early end

  Scenario: Successful focus session completion
    Given I complete a 45-minute focus session
    And I accomplished my planned work
    When I mark the session as complete
    Then I should receive encouraging feedback
    And earn appropriate gamification points
    And see my progress toward goals
    And get suggestions for next steps

  Scenario: Focus session with interruptions
    Given I am in an active focus session
    And I get interrupted by an urgent call
    When I pause the session to handle the interruption
    And resume 15 minutes later
    Then the system should acknowledge the interruption
    And adjust the session plan accordingly
    And not count the interruption against my focus metrics
    And offer to extend the session if needed

  Scenario: Multiple short focus sessions (sprint mode)
    Given I have low energy but want to be productive
    When I choose sprint mode with 15-minute sessions
    And complete three consecutive sprints
    Then each sprint should have a 5-minute break
    And I should earn momentum bonuses
    And the system should track my sprint effectiveness
    And suggest whether to continue or switch modes

  Scenario: Focus session analytics and insights
    Given I have completed several focus sessions this week
    When I view my focus analytics
    Then I should see my optimal session lengths
    And my most productive times of day
    And patterns in my focus quality
    And personalized recommendations for improvement
    And encouragement about my progress

  Scenario: Collaborative focus session (body doubling)
    Given I join a body doubling session
    And there are 3 other participants
    When I start my individual focus work
    Then I should see gentle presence indicators of others
    And be able to share progress updates
    And receive encouragement from the group
    And maintain my individual focus goals

  Scenario: Focus session with accessibility needs
    Given I have visual processing sensitivities
    And I enable high contrast mode
    When I start a focus session
    Then all visual elements should use high contrast colors
    And animations should be minimal or disabled
    And text should be clearly readable
    And focus indicators should be prominent

  Scenario: Emergency focus session end
    Given I am in a focus session
    And I experience ADHD overwhelm
    When I trigger the emergency stop
    Then the session should end immediately
    And I should see calming, supportive messaging
    And be offered grounding techniques
    And have the option to schedule recovery time
    And not lose any progress or points

  Scenario: Focus session with medication timing
    Given I track my ADHD medication timing
    And my medication is wearing off
    When I start a focus session
    Then the system should warn about medication timing
    And suggest a shorter session duration
    And offer to reschedule for optimal medication window
    And track correlation between medication and focus quality
