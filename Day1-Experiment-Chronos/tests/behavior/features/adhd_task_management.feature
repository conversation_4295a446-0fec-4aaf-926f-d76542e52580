Feature: ADHD-Optimized Task Management
  As a user with ADHD
  I want task management features that understand my neurodivergent needs
  So that I can overcome task paralysis and manage my workload effectively

  Background:
    Given I am a user with ADHD diagnosis
    And I have configured my ADHD preferences
    And I am logged into the system

  Scenario: Creating ADHD-friendly tasks with energy levels
    Given I want to create a new task
    When I provide task details with energy level "low"
    And I set estimated duration to 30 minutes
    And I add context tags "home" and "computer"
    Then the task should be created successfully
    And the task should have energy level "low"
    And the task should be suitable for ADHD users

  Scenario: AI task chunking for overwhelming projects
    Given I have a large task with 4 hours estimated duration
    And the task is causing me task paralysis
    When I request AI chunking with size "small"
    And I provide context "User has ADHD and needs specific steps"
    Then the task should be broken into 3-7 subtasks
    And each subtask should be 45 minutes or less
    And each subtask should have clear, actionable descriptions
    And the subtasks should have appropriate energy levels

  Scenario: Task jar for decision fatigue reduction
    Given I have multiple pending tasks
    And I am experiencing decision fatigue
    When I request a task jar with 5 tasks
    Then I should receive a random selection of suitable tasks
    And all tasks should be actionable and not overwhelming
    And the selection should match my current energy level

  Scenario: Energy-based task filtering
    Given I have tasks with different energy levels
    And my current energy level is "low"
    When I filter tasks by energy level "low"
    Then I should only see tasks requiring low energy
    And the tasks should be 30 minutes or less
    And the tasks should be appropriate for my current state

  Scenario: Time estimation accuracy tracking
    Given I have a task with 45 minutes estimated duration
    When I start working on the task
    And I complete the task in 75 minutes
    Then the system should record the actual duration
    And it should calculate my estimation accuracy as 60%
    And it should update my time estimation patterns
    And it should provide better estimates for similar tasks

  Scenario: Context-aware task organization
    Given I have tasks with different context tags
    When I filter by context "home"
    Then I should only see tasks tagged with "home"
    And the tasks should be grouped to minimize context switching
    And I should see suggestions for batch processing similar contexts

  Scenario: Gentle task reminders for ADHD
    Given I have a task due in 2 hours
    And I have enabled gentle notifications
    When the reminder time arrives
    Then I should receive a gentle, non-intrusive notification
    And the notification should include visual cues
    And it should provide options to snooze or reschedule
    And it should not cause anxiety or overwhelm

  Scenario: Hyperfocus session protection
    Given I am in a focus session
    And I have been working for 90 minutes straight
    When the system detects potential hyperfocus
    Then it should send gentle break reminders
    And it should protect my session from interruptions
    And it should track hyperfocus patterns for future planning
    And it should suggest appropriate break activities

  Scenario: Task paralysis recovery assistance
    Given I have been staring at my task list for 10 minutes
    And I haven't started any tasks
    When the system detects potential task paralysis
    Then it should suggest the easiest available task
    And it should offer to break down overwhelming tasks
    And it should provide momentum-building activities
    And it should reduce the cognitive load of decision making

  Scenario: Progressive task disclosure
    Given I have a complex multi-phase project
    And seeing all details at once is overwhelming
    When I request progressive disclosure
    Then I should see a high-level overview first
    And I can drill down into specific phases when ready
    And each level should show appropriate detail for ADHD users
    And the interface should prevent information overload

  Scenario: Time blindness support features
    Given I struggle with time awareness
    When I view my daily schedule
    Then I should see visual time representations
    And I should see buffer time automatically added
    And I should get time awareness notifications
    And I should see realistic time estimates based on my patterns

  Scenario: Momentum building through quick wins
    Given I am having trouble getting started
    When I request momentum-building suggestions
    Then I should see very quick tasks (2-5 minutes)
    And I should see easy wins that build confidence
    And completing these should increase my momentum score
    And the system should suggest the next appropriate task

  Scenario: Context switching minimization
    Given I have tasks requiring different contexts
    When I plan my work session
    Then the system should group similar context tasks together
    And it should minimize the number of context switches
    And it should add buffer time between context changes
    And it should warn me about potentially disruptive switches

  Scenario: Soft delete for ADHD recovery
    Given I accidentally delete an important task
    When I realize my mistake
    Then I should be able to recover the deleted task
    And the task should be in a "deleted items" area
    And I should be able to restore it with one click
    And all task relationships should be preserved

  Scenario: Energy pattern learning and adaptation
    Given I have been using the system for several weeks
    And the system has tracked my energy patterns
    When I view task recommendations
    Then the suggestions should match my historical energy patterns
    And the system should predict my energy levels by time of day
    And it should adapt recommendations based on my actual performance
    And it should learn from my task completion patterns

  Scenario: Overwhelm prevention and management
    Given my task list is getting overwhelming
    When the system detects high task load
    Then it should suggest task prioritization strategies
    And it should offer to hide non-essential tasks temporarily
    And it should recommend breaking down large tasks
    And it should provide calming interface elements
    And it should suggest stress-reduction techniques
