"""
Tests for the gamification service.

This module tests ADHD-optimized gamification features including
points, levels, achievements, and streak management.
"""

import pytest
from datetime import date, timedelta
from uuid import uuid4

from app.services.gamification_service import GamificationService
from app.models.gamification import UserGamification, PointsAward, UserStreak
from app.models.user import User


class TestGamificationService:
    """Test suite for GamificationService."""
    
    @pytest.fixture
    async def mock_user(self, db_session):
        """Create a mock user for testing."""
        user = User(
            email="<EMAIL>",
            hashed_password="test_hash",
            first_name="Test",
            last_name="User",
            is_active=True,
            adhd_diagnosis=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user
    
    @pytest.fixture
    def gamification_service(self, db_session):
        """Create a gamification service instance."""
        return GamificationService(db_session)
    
    async def test_get_or_create_user_gamification_new_user(
        self, gamification_service, mock_user, db_session
    ):
        """Test creating gamification profile for new user."""
        profile = await gamification_service.get_or_create_user_gamification(mock_user.id)
        
        assert profile is not None
        assert profile.user_id == mock_user.id
        assert profile.total_points == 0
        assert profile.level == 1
        assert profile.points_to_next_level == 100
        assert profile.gamification_enabled is True
        assert profile.celebration_style == "moderate"
    
    async def test_get_or_create_user_gamification_existing_user(
        self, gamification_service, mock_user, db_session
    ):
        """Test getting existing gamification profile."""
        # Create initial profile
        initial_profile = await gamification_service.get_or_create_user_gamification(mock_user.id)
        initial_profile.total_points = 150
        await db_session.commit()
        
        # Get profile again
        profile = await gamification_service.get_or_create_user_gamification(mock_user.id)
        
        assert profile.id == initial_profile.id
        assert profile.total_points == 150
    
    async def test_award_points_basic(
        self, gamification_service, mock_user, db_session
    ):
        """Test basic point awarding."""
        award, level_up = await gamification_service.award_points(
            user_id=mock_user.id,
            points=50,
            reason="Test task completion"
        )
        
        assert award is not None
        assert award.points_awarded == 50
        assert award.reason == "Test task completion"
        assert award.multiplier == 1.0
        assert award.total_points_after == 50
        assert level_up is None  # No level up at 50 points
    
    async def test_award_points_with_multiplier(
        self, gamification_service, mock_user, db_session
    ):
        """Test point awarding with multiplier."""
        award, level_up = await gamification_service.award_points(
            user_id=mock_user.id,
            points=30,
            reason="Difficult task",
            multiplier=1.5
        )
        
        assert award.points_awarded == 45  # 30 * 1.5
        assert award.multiplier == 1.5
        assert award.total_points_after == 45
    
    async def test_award_points_level_up(
        self, gamification_service, mock_user, db_session
    ):
        """Test level up when awarding points."""
        # First, give user 90 points
        await gamification_service.award_points(
            user_id=mock_user.id,
            points=90,
            reason="Setup"
        )
        
        # Award 20 more points to trigger level up
        award, level_up = await gamification_service.award_points(
            user_id=mock_user.id,
            points=20,
            reason="Level up trigger"
        )
        
        assert award.total_points_after == 110
        assert level_up is not None
        assert level_up.old_level == 1
        assert level_up.new_level == 2
        assert level_up.points_earned == 20
    
    async def test_calculate_points_multiplier_difficulty(
        self, gamification_service
    ):
        """Test points multiplier calculation with difficulty."""
        multiplier, breakdown = await gamification_service.calculate_points_multiplier(
            base_points=100,
            task_difficulty="hard"
        )
        
        assert multiplier == 1.5
        assert breakdown["base"] == 1.0
        assert breakdown["difficulty"] == 1.5
    
    async def test_calculate_points_multiplier_energy(
        self, gamification_service
    ):
        """Test points multiplier calculation with energy level."""
        multiplier, breakdown = await gamification_service.calculate_points_multiplier(
            base_points=100,
            energy_level="low"
        )
        
        assert multiplier == 1.3  # Reward for low energy effort
        assert breakdown["energy"] == 1.3
    
    async def test_calculate_points_multiplier_combined(
        self, gamification_service
    ):
        """Test points multiplier calculation with multiple factors."""
        multiplier, breakdown = await gamification_service.calculate_points_multiplier(
            base_points=100,
            task_difficulty="hard",
            energy_level="low",
            time_of_day="evening",
            context={"first_task_of_day": True}
        )
        
        # 1.0 * 1.5 (hard) * 1.3 (low energy) * 1.2 (evening) * 1.2 (first task)
        expected = 1.0 * 1.5 * 1.3 * 1.2 * 1.2
        assert abs(multiplier - expected) < 0.01
    
    async def test_update_streak_new_streak(
        self, gamification_service, mock_user, db_session
    ):
        """Test creating a new streak."""
        streak, changed = await gamification_service.update_streak(
            user_id=mock_user.id,
            streak_type="daily_tasks",
            action_completed=True
        )
        
        assert streak is not None
        assert streak.streak_type == "daily_tasks"
        assert streak.current_streak == 1
        assert streak.longest_streak == 1
        assert streak.last_activity_date == date.today()
        assert changed is True
    
    async def test_update_streak_continue(
        self, gamification_service, mock_user, db_session
    ):
        """Test continuing an existing streak."""
        # Create initial streak
        await gamification_service.update_streak(
            user_id=mock_user.id,
            streak_type="daily_tasks",
            action_completed=True
        )
        
        # Simulate next day
        from unittest.mock import patch
        tomorrow = date.today() + timedelta(days=1)
        
        with patch('app.services.gamification_service.date') as mock_date:
            mock_date.today.return_value = tomorrow
            
            streak, changed = await gamification_service.update_streak(
                user_id=mock_user.id,
                streak_type="daily_tasks",
                action_completed=True
            )
        
        assert streak.current_streak == 2
        assert streak.longest_streak == 2
        assert changed is True
    
    async def test_update_streak_same_day(
        self, gamification_service, mock_user, db_session
    ):
        """Test updating streak on same day (no change)."""
        # Create initial streak
        await gamification_service.update_streak(
            user_id=mock_user.id,
            streak_type="daily_tasks",
            action_completed=True
        )
        
        # Update again same day
        streak, changed = await gamification_service.update_streak(
            user_id=mock_user.id,
            streak_type="daily_tasks",
            action_completed=True
        )
        
        assert streak.current_streak == 1
        assert changed is False
    
    async def test_level_calculation(self, gamification_service):
        """Test level calculation logic."""
        assert gamification_service._calculate_level(0) == 1
        assert gamification_service._calculate_level(50) == 1
        assert gamification_service._calculate_level(100) == 2
        assert gamification_service._calculate_level(300) == 3
        assert gamification_service._calculate_level(600) == 4
        assert gamification_service._calculate_level(1000) > 4
    
    async def test_points_to_next_level_calculation(self, gamification_service):
        """Test points to next level calculation."""
        # Level 1 to 2
        points_needed = gamification_service._calculate_points_to_next_level(50, 1)
        assert points_needed == 50  # 100 - 50
        
        # Level 2 to 3
        points_needed = gamification_service._calculate_points_to_next_level(200, 2)
        assert points_needed == 100  # 300 - 200
    
    async def test_get_gamification_stats(
        self, gamification_service, mock_user, db_session
    ):
        """Test getting gamification statistics."""
        # Award some points first
        await gamification_service.award_points(
            user_id=mock_user.id,
            points=150,
            reason="Test"
        )
        
        stats = await gamification_service.get_gamification_stats(mock_user.id)
        
        assert stats.total_points == 150
        assert stats.current_level == 2
        assert stats.points_to_next_level == 150  # 300 - 150
        assert stats.level_progress_percentage > 0
