"""
End-to-end tests for ADHD user workflows.

This module tests complete user workflows that ADHD users would experience,
ensuring the entire system works together to support neurodivergent needs.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import patch

from tests.factories import UserFactory


@pytest.mark.e2e
class TestMorningRoutineWorkflow:
    """Test the complete morning routine workflow for ADHD users."""
    
    async def test_complete_morning_startup_routine(self, client, db_session):
        """Test the full morning routine from login to task selection."""
        
        # 1. Create ADHD user with morning preferences
        user = await UserFactory.create(
            db_session,
            adhd_diagnosed=True,
            preferences={
                "energy_patterns": {"morning": "high", "afternoon": "medium", "evening": "low"},
                "morning_routine": {
                    "show_energy_check": True,
                    "suggest_high_energy_tasks": True,
                    "display_daily_overview": True,
                    "motivational_message": True
                }
            }
        )
        
        # 2. Login
        login_response = await client.post("/api/v1/auth/login", json={
            "email": user.email,
            "password": "testpassword123"
        })
        
        assert login_response.status_code == 200
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # 3. Get morning dashboard
        dashboard_response = await client.get(
            "/api/v1/dashboard/morning",
            headers=headers
        )
        
        assert dashboard_response.status_code == 200
        dashboard = dashboard_response.json()["data"]
        
        # Verify ADHD-friendly morning dashboard
        assert "energy_check" in dashboard
        assert "daily_overview" in dashboard
        assert "suggested_tasks" in dashboard
        assert "motivational_message" in dashboard
        
        # 4. Complete energy check-in
        energy_checkin_response = await client.post(
            "/api/v1/user/energy-checkin",
            headers=headers,
            json={
                "current_energy": "high",
                "mood": "good",
                "focus_level": "ready",
                "notes": "Feeling great this morning!"
            }
        )
        
        assert energy_checkin_response.status_code == 200
        
        # 5. Get personalized task suggestions based on energy
        suggestions_response = await client.get(
            "/api/v1/tasks/suggestions",
            headers=headers,
            params={"energy_level": "high", "time_of_day": "morning"}
        )
        
        assert suggestions_response.status_code == 200
        suggestions = suggestions_response.json()["data"]
        
        # Should suggest high-energy tasks for morning
        assert len(suggestions["high_energy_tasks"]) > 0
        assert all(task["energy_level"] == "high" for task in suggestions["high_energy_tasks"])
        
        # 6. Create a new task for the day
        task_response = await client.post(
            "/api/v1/tasks",
            headers=headers,
            json={
                "title": "Morning deep work session",
                "description": "Focus on complex analysis project",
                "estimated_duration": 90,
                "energy_level": "high",
                "priority": "high"
            }
        )
        
        assert task_response.status_code == 201
        task = task_response.json()["data"]
        
        # 7. Schedule the task in time blocking
        schedule_response = await client.post(
            "/api/v1/time-blocks",
            headers=headers,
            json={
                "title": "Deep Work - Analysis Project",
                "start_time": (datetime.utcnow().replace(hour=9, minute=0)).isoformat(),
                "end_time": (datetime.utcnow().replace(hour=10, minute=30)).isoformat(),
                "block_type": "focus",
                "task_id": task["id"],
                "energy_level": "high"
            }
        )
        
        assert schedule_response.status_code == 201
        
        # 8. Start focus session
        focus_response = await client.post(
            "/api/v1/focus-sessions",
            headers=headers,
            json={
                "session_type": "deep_work",
                "title": "Morning Analysis Session",
                "planned_duration": 90,
                "task_id": task["id"],
                "enable_hyperfocus_protection": True
            }
        )
        
        assert focus_response.status_code == 201
        focus_session = focus_response.json()["data"]
        
        # Verify complete workflow success
        assert focus_session["status"] == "active"
        assert focus_session["hyperfocus_protection_enabled"] == True


@pytest.mark.e2e
class TestTaskManagementWorkflow:
    """Test complete task management workflow for ADHD users."""
    
    async def test_overwhelming_task_breakdown_workflow(self, client, adhd_user_with_tasks):
        """Test the workflow for breaking down overwhelming tasks."""
        
        headers = adhd_user_with_tasks["headers"]
        
        # 1. Create a large, overwhelming task
        large_task_response = await client.post(
            "/api/v1/tasks",
            headers=headers,
            json={
                "title": "Complete comprehensive quarterly business review presentation",
                "description": "Research, analyze, create slides, practice presentation for board meeting",
                "estimated_duration": 300,  # 5 hours - overwhelming!
                "energy_level": "high",
                "complexity": "high",
                "priority": "high",
                "due_date": (datetime.utcnow() + timedelta(days=7)).isoformat()
            }
        )
        
        assert large_task_response.status_code == 201
        large_task = large_task_response.json()["data"]
        
        # 2. Request AI chunking for the overwhelming task
        chunking_response = await client.post(
            f"/api/v1/tasks/{large_task['id']}/chunk",
            headers=headers,
            json={
                "max_chunk_duration": 45,  # ADHD-friendly chunk size
                "energy_preference": "mixed",
                "include_breaks": True,
                "respect_energy_patterns": True
            }
        )
        
        assert chunking_response.status_code == 200
        chunks = chunking_response.json()["data"]["chunks"]
        
        # Verify ADHD-friendly chunking
        assert 4 <= len(chunks) <= 8  # Reasonable number of chunks
        assert all(chunk["estimated_duration"] <= 60 for chunk in chunks)
        
        # 3. Accept the chunking and create subtasks
        accept_response = await client.post(
            f"/api/v1/tasks/{large_task['id']}/accept-chunks",
            headers=headers,
            json={"chunk_ids": [chunk["id"] for chunk in chunks]}
        )
        
        assert accept_response.status_code == 200
        
        # 4. Get updated task with subtasks
        updated_task_response = await client.get(
            f"/api/v1/tasks/{large_task['id']}",
            headers=headers,
            params={"include_subtasks": "true"}
        )
        
        assert updated_task_response.status_code == 200
        updated_task = updated_task_response.json()["data"]
        
        assert len(updated_task["subtasks"]) == len(chunks)
        
        # 5. Start working on first chunk
        first_chunk = updated_task["subtasks"][0]
        start_response = await client.post(
            f"/api/v1/tasks/{first_chunk['id']}/start",
            headers=headers
        )
        
        assert start_response.status_code == 200
        
        # 6. Complete first chunk
        complete_response = await client.post(
            f"/api/v1/tasks/{first_chunk['id']}/complete",
            headers=headers,
            json={
                "actual_duration": 40,
                "difficulty_rating": 3,
                "energy_used": "medium",
                "notes": "Went well, good focus"
            }
        )
        
        assert complete_response.status_code == 200
        
        # 7. Get progress update
        progress_response = await client.get(
            f"/api/v1/tasks/{large_task['id']}/progress",
            headers=headers
        )
        
        assert progress_response.status_code == 200
        progress = progress_response.json()["data"]
        
        # Should show encouraging progress
        assert progress["completion_percentage"] > 0
        assert "encouragement_message" in progress
        assert "next_suggested_chunk" in progress


@pytest.mark.e2e
class TestFocusSessionWorkflow:
    """Test complete focus session workflow."""
    
    async def test_pomodoro_with_hyperfocus_protection(self, client, focus_session_user):
        """Test Pomodoro session with ADHD hyperfocus protection."""
        
        headers = focus_session_user["headers"]
        
        # 1. Create a task for the focus session
        task_response = await client.post(
            "/api/v1/tasks",
            headers=headers,
            json={
                "title": "Code review and refactoring",
                "estimated_duration": 60,
                "energy_level": "high"
            }
        )
        
        task = task_response.json()["data"]
        
        # 2. Start Pomodoro session
        session_response = await client.post(
            "/api/v1/focus-sessions",
            headers=headers,
            json={
                "session_type": "pomodoro",
                "title": "Code Review Session",
                "planned_duration": 25,
                "break_duration": 5,
                "task_id": task["id"],
                "enable_hyperfocus_protection": True,
                "hyperfocus_threshold": 90  # Alert after 90 minutes
            }
        )
        
        assert session_response.status_code == 201
        session = session_response.json()["data"]
        
        # 3. Simulate session progress
        await asyncio.sleep(0.1)  # Brief pause
        
        # 4. Check session status
        status_response = await client.get(
            f"/api/v1/focus-sessions/{session['id']}/status",
            headers=headers
        )
        
        assert status_response.status_code == 200
        status = status_response.json()["data"]
        
        assert status["status"] == "active"
        assert status["hyperfocus_protection_active"] == True
        
        # 5. Simulate break reminder
        break_response = await client.post(
            f"/api/v1/focus-sessions/{session['id']}/break",
            headers=headers
        )
        
        assert break_response.status_code == 200
        
        # 6. Resume after break
        resume_response = await client.post(
            f"/api/v1/focus-sessions/{session['id']}/resume",
            headers=headers
        )
        
        assert resume_response.status_code == 200
        
        # 7. Complete session
        complete_response = await client.post(
            f"/api/v1/focus-sessions/{session['id']}/complete",
            headers=headers,
            json={
                "actual_duration": 25,
                "productivity_rating": 4,
                "focus_quality": "good",
                "notes": "Good focus, minimal distractions"
            }
        )
        
        assert complete_response.status_code == 200
        completed_session = complete_response.json()["data"]
        
        # Verify session completion tracking
        assert completed_session["status"] == "completed"
        assert completed_session["productivity_rating"] == 4


@pytest.mark.e2e
class TestIntegrationWorkflow:
    """Test external service integration workflow."""
    
    async def test_google_calendar_sync_workflow(self, client, authenticated_user):
        """Test complete Google Calendar integration workflow."""
        
        headers = authenticated_user["headers"]
        
        # 1. Initiate OAuth flow
        oauth_response = await client.post(
            "/api/v1/integrations/oauth/initiate",
            headers=headers,
            json={
                "integration_type": "google_calendar",
                "redirect_uri": "https://app.chronos.com/oauth/callback",
                "scopes": ["https://www.googleapis.com/auth/calendar"]
            }
        )
        
        assert oauth_response.status_code == 200
        oauth_data = oauth_response.json()["data"]
        
        assert "authorization_url" in oauth_data
        assert "state" in oauth_data
        
        # 2. Simulate OAuth callback (normally from Google)
        callback_response = await client.post(
            "/api/v1/integrations/oauth/callback/google_calendar",
            headers=headers,
            json={
                "code": "mock_authorization_code",
                "state": oauth_data["state"]
            }
        )
        
        assert callback_response.status_code == 200
        
        # 3. Create integration
        integration_response = await client.post(
            "/api/v1/integrations",
            headers=headers,
            json={
                "integration_type": "google_calendar",
                "name": "My Work Calendar",
                "description": "Primary calendar for work events",
                "config": {
                    "calendar_ids": ["primary"],
                    "buffer_time": 10,
                    "energy_detection": True
                },
                "sync_settings": {
                    "import_events": True,
                    "export_events": True,
                    "respect_focus_mode": True
                }
            }
        )
        
        assert integration_response.status_code == 201
        integration = integration_response.json()["data"]
        
        # 4. Start initial sync
        sync_response = await client.post(
            f"/api/v1/integrations/{integration['id']}/sync",
            headers=headers,
            json={
                "operation_type": "import_calendar_events",
                "filters": {
                    "start_date": datetime.utcnow().date().isoformat(),
                    "end_date": (datetime.utcnow().date() + timedelta(days=7)).isoformat(),
                    "add_buffer_time": True,
                    "energy_categorization": True
                }
            }
        )
        
        assert sync_response.status_code == 200
        sync_data = sync_response.json()["data"]
        
        assert sync_data["status"] in ["pending", "in_progress"]
        
        # 5. Check sync progress
        await asyncio.sleep(0.1)  # Brief pause for background processing
        
        logs_response = await client.get(
            f"/api/v1/integrations/{integration['id']}/sync-logs",
            headers=headers,
            params={"limit": 1}
        )
        
        assert logs_response.status_code == 200
        logs = logs_response.json()["data"]
        
        assert len(logs) > 0
        latest_log = logs[0]
        assert latest_log["operation_type"] == "import_calendar_events"
        
        # 6. Check integration health
        health_response = await client.get(
            f"/api/v1/integrations/{integration['id']}/health",
            headers=headers
        )
        
        assert health_response.status_code == 200
        health = health_response.json()["data"]
        
        assert health["integration_type"] == "google_calendar"
        assert "is_healthy" in health
        assert "recommendations" in health


@pytest.mark.e2e
class TestDailyWorkflowComplete:
    """Test a complete daily workflow for ADHD users."""
    
    async def test_full_day_adhd_workflow(self, client, adhd_user_with_schedule):
        """Test a complete day workflow from morning to evening."""
        
        headers = adhd_user_with_schedule["headers"]
        user = adhd_user_with_schedule["user"]
        
        # Morning: Energy check-in and task planning
        morning_checkin = await client.post(
            "/api/v1/user/energy-checkin",
            headers=headers,
            json={
                "current_energy": "high",
                "mood": "motivated",
                "focus_level": "ready"
            }
        )
        assert morning_checkin.status_code == 200
        
        # Get morning task suggestions
        morning_suggestions = await client.get(
            "/api/v1/tasks/suggestions",
            headers=headers,
            params={"energy_level": "high", "time_of_day": "morning"}
        )
        assert morning_suggestions.status_code == 200
        
        # Midday: Energy dip check and task adjustment
        midday_checkin = await client.post(
            "/api/v1/user/energy-checkin",
            headers=headers,
            json={
                "current_energy": "medium",
                "mood": "steady",
                "focus_level": "moderate"
            }
        )
        assert midday_checkin.status_code == 200
        
        # Get adjusted suggestions for medium energy
        midday_suggestions = await client.get(
            "/api/v1/tasks/suggestions",
            headers=headers,
            params={"energy_level": "medium", "time_of_day": "afternoon"}
        )
        assert midday_suggestions.status_code == 200
        
        # Evening: Reflection and next day preparation
        evening_reflection = await client.post(
            "/api/v1/user/daily-reflection",
            headers=headers,
            json={
                "completed_tasks": 3,
                "energy_throughout_day": "good",
                "focus_quality": "variable",
                "challenges": ["afternoon energy dip"],
                "wins": ["completed morning deep work"],
                "tomorrow_energy_prediction": "high"
            }
        )
        assert evening_reflection.status_code == 200
        
        # Get daily summary
        summary_response = await client.get(
            "/api/v1/dashboard/daily-summary",
            headers=headers,
            params={"date": datetime.utcnow().date().isoformat()}
        )
        assert summary_response.status_code == 200
        
        summary = summary_response.json()["data"]
        assert "tasks_completed" in summary
        assert "energy_patterns" in summary
        assert "focus_sessions" in summary
        assert "achievements" in summary
        assert "tomorrow_suggestions" in summary
