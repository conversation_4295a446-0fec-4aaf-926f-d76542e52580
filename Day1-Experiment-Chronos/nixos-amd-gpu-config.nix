# NixOS Configuration for AMD Radeon RX 7900 XT + Project Chronos
# Add this to your /etc/nixos/configuration.nix or import as a module

{ config, pkgs, lib, ... }:

{
  # AMD GPU Support
  hardware.opengl = {
    enable = true;
    driSupport = true;
    driSupport32Bit = true;
    
    # AMD-specific drivers
    extraPackages = with pkgs; [
      rocm-opencl-icd
      rocm-opencl-runtime
      amdvlk
    ];
    
    # 32-bit support for compatibility
    extraPackages32 = with pkgs; [
      driversi686Linux.amdvlk
    ];
  };

  # ROCm Support
  systemd.tmpfiles.rules = [
    "L+    /opt/rocm/hip   -    -    -     -    ${pkgs.hip}"
  ];

  # Hardware acceleration
  hardware.opengl.extraPackages = with pkgs; [
    vaapiVdpau
    libvdpau-va-gl
  ];

  # Docker Configuration with GPU support
  virtualisation.docker = {
    enable = true;
    enableOnBoot = true;
    
    # Docker daemon configuration for AMD GPU
    daemon.settings = {
      runtimes = {
        rocm = {
          path = "${pkgs.runc}/bin/runc";
          runtimeArgs = [];
        };
      };
      log-driver = "json-file";
      log-opts = {
        max-size = "10m";
        max-file = "3";
      };
    };
  };

  # User groups for GPU access
  users.users.cvr = {  # Replace 'cvr' with your username
    isNormalUser = true;
    extraGroups = [ 
      "wheel" 
      "docker" 
      "video" 
      "render" 
      "audio"
    ];
  };

  # System packages for development
  environment.systemPackages = with pkgs; [
    # ROCm and AMD GPU tools
    rocm-opencl-icd
    rocm-opencl-runtime
    rocminfo
    rocm-smi
    hip
    
    # Docker and containerization
    docker
    docker-compose
    
    # Development tools
    git
    curl
    wget
    htop
    
    # Audio support
    pulseaudio
    alsa-utils
    
    # Python development (optional)
    python310
    python310Packages.pip
    python310Packages.virtualenv
  ];

  # Services
  services = {
    # Audio support
    pulseaudio.enable = true;
    
    # GPU monitoring
    hardware.openrgb.enable = true;
  };

  # Kernel modules for AMD GPU
  boot.kernelModules = [ "amdgpu" ];
  
  # Kernel parameters for AMD GPU
  boot.kernelParams = [
    "amdgpu.si_support=1"
    "amdgpu.cik_support=1"
    "radeon.si_support=0"
    "radeon.cik_support=0"
  ];

  # Environment variables
  environment.variables = {
    ROC_ENABLE_PRE_VEGA = "1";
    HSA_OVERRIDE_GFX_VERSION = "11.0.0";  # For RX 7900 XT/XTX
    PYTORCH_ROCM_ARCH = "gfx1100;gfx1101;gfx1102";
  };

  # Udev rules for GPU access
  services.udev.extraRules = ''
    # AMD GPU access rules
    SUBSYSTEM=="drm", KERNEL=="card*", GROUP="video", MODE="0664"
    SUBSYSTEM=="drm", KERNEL=="renderD*", GROUP="render", MODE="0664"
    SUBSYSTEM=="drm", KERNEL=="controlD*", GROUP="video", MODE="0664"
    
    # ROCm device access
    SUBSYSTEM=="kfd", KERNEL=="kfd", TAG+="uaccess", GROUP="render", MODE="0666"
  '';

  # Networking (if needed for development)
  networking.firewall = {
    enable = true;
    allowedTCPPorts = [ 8090 8001 8002 8003 3000 9090 9000 ];  # Project Chronos ports
  };

  # Nix configuration for development
  nix = {
    settings = {
      experimental-features = [ "nix-command" "flakes" ];
      trusted-users = [ "root" "cvr" ];  # Replace 'cvr' with your username
    };
    
    # Garbage collection
    gc = {
      automatic = true;
      dates = "weekly";
      options = "--delete-older-than 30d";
    };
  };

  # System state version
  system.stateVersion = "23.11"; # Replace with your NixOS version
}
