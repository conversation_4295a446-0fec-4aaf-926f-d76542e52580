version: '3.8'

services:
  # PostgreSQL database for development
  postgres:
    image: postgres:15-alpine
    container_name: chronos_postgres_dev
    environment:
      POSTGRES_DB: chronos
      POSTGRES_USER: chronos
      POSTGRES_PASSWORD: chronos_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chronos -d chronos"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - chronos_network

  # PostgreSQL test database
  postgres_test:
    image: postgres:15-alpine
    container_name: chronos_postgres_test
    environment:
      POSTGRES_DB: chronos_test
      POSTGRES_USER: chronos
      POSTGRES_PASSWORD: chronos_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chronos -d chronos_test"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - chronos_network

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: chronos_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - chronos_network

  # Adminer for database management (development only)
  adminer:
    image: adminer:4.8.1
    container_name: chronos_adminer_dev
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
      ADMINER_DESIGN: pepa-linha
    depends_on:
      - postgres
    networks:
      - chronos_network

  # Redis Commander for Redis management (development only)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: chronos_redis_commander_dev
    ports:
      - "8081:8081"
    environment:
      REDIS_HOSTS: local:redis:6379
    depends_on:
      - redis
    networks:
      - chronos_network

volumes:
  postgres_data:
    driver: local
  postgres_test_data:
    driver: local
  redis_data:
    driver: local

networks:
  chronos_network:
    driver: bridge
