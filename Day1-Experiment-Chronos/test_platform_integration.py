#!/usr/bin/env python3
"""
Platform Integration Test
Tests the complete integration between Chronos platform and Speechbot
"""

import asyncio
import logging
import requests
import time
import json
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PlatformIntegrationTester:
    """Test suite for platform integration."""
    
    def __init__(self):
        self.chronos_api_url = "http://api.autism.localhost:8090"
        self.speechbot_url = "http://speechbot.autism.localhost:8090"
        self.ui_url = "http://chronos.autism.localhost:8090"
        self.test_user_token = None
        
    def test_service_availability(self):
        """Test that all services are available."""
        logger.info("🔍 Testing service availability...")
        
        services = [
            ("Chronos API", f"{self.chronos_api_url}/health"),
            ("Speechbot", f"{self.speechbot_url}/health"),
            ("Chronos UI", self.ui_url),
        ]
        
        results = {}
        
        for service_name, url in services:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    logger.info(f"✅ {service_name}: Available")
                    results[service_name] = True
                else:
                    logger.error(f"❌ {service_name}: HTTP {response.status_code}")
                    results[service_name] = False
            except Exception as e:
                logger.error(f"❌ {service_name}: {e}")
                results[service_name] = False
        
        return all(results.values())
    
    def test_user_authentication(self):
        """Test user authentication flow."""
        logger.info("🔐 Testing user authentication...")
        
        try:
            # Test user registration/login
            auth_data = {
                "email": "<EMAIL>",
                "password": "testpassword123"
            }
            
            # Try login first
            response = requests.post(
                f"{self.chronos_api_url}/api/v1/auth/login",
                json=auth_data,
                timeout=10
            )
            
            if response.status_code == 200:
                token_data = response.json()
                self.test_user_token = token_data.get("access_token")
                logger.info("✅ User authentication successful")
                return True
            else:
                logger.error(f"❌ Authentication failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Authentication error: {e}")
            return False
    
    def test_speechbot_proxy(self):
        """Test Speechbot API proxy through Chronos."""
        logger.info("🎭 Testing Speechbot API proxy...")
        
        if not self.test_user_token:
            logger.error("❌ No authentication token available")
            return False
        
        headers = {
            "Authorization": f"Bearer {self.test_user_token}",
            "Content-Type": "application/json"
        }
        
        try:
            # Test health check through proxy
            response = requests.get(
                f"{self.chronos_api_url}/api/v1/speechbot/health",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info("✅ Speechbot proxy health check successful")
                
                # Test capabilities
                response = requests.get(
                    f"{self.chronos_api_url}/api/v1/speechbot/capabilities",
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    capabilities = response.json()
                    logger.info(f"✅ Speechbot capabilities: {capabilities.get('engine', 'Unknown')}")
                    return True
                else:
                    logger.error(f"❌ Capabilities check failed: {response.status_code}")
                    return False
            else:
                logger.error(f"❌ Speechbot proxy failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Speechbot proxy error: {e}")
            return False
    
    def test_voice_synthesis(self):
        """Test voice synthesis through the platform."""
        logger.info("🎤 Testing voice synthesis...")
        
        if not self.test_user_token:
            logger.error("❌ No authentication token available")
            return False
        
        headers = {
            "Authorization": f"Bearer {self.test_user_token}",
            "Content-Type": "application/json"
        }
        
        try:
            # Test quick synthesis
            synthesis_data = {
                "text": "Hello! This is a test of the integrated Speechbot platform.",
                "voice": "default",
                "mode": "calm"
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.chronos_api_url}/api/v1/speechbot/tts/quick",
                headers=headers,
                json=synthesis_data,
                timeout=30
            )
            generation_time = time.time() - start_time
            
            if response.status_code == 200:
                audio_size = len(response.content)
                logger.info(f"✅ Voice synthesis successful: {audio_size} bytes in {generation_time:.2f}s")
                
                # Save test audio
                with open("test_synthesis.wav", "wb") as f:
                    f.write(response.content)
                
                return True
            else:
                logger.error(f"❌ Voice synthesis failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Voice synthesis error: {e}")
            return False
    
    def test_user_preferences(self):
        """Test user preferences integration."""
        logger.info("⚙️ Testing user preferences...")
        
        if not self.test_user_token:
            logger.error("❌ No authentication token available")
            return False
        
        headers = {
            "Authorization": f"Bearer {self.test_user_token}",
            "Content-Type": "application/json"
        }
        
        try:
            # Get user preferences
            response = requests.get(
                f"{self.chronos_api_url}/api/v1/speechbot/preferences",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                preferences = response.json()
                logger.info(f"✅ Got user preferences: {preferences.get('default_adhd_mode', 'Unknown')}")
                
                # Update preferences
                update_data = {
                    "default_adhd_mode": "focused",
                    "enable_nonverbals": True,
                    "nonverbal_frequency": 0.15
                }
                
                response = requests.put(
                    f"{self.chronos_api_url}/api/v1/speechbot/preferences",
                    headers=headers,
                    json=update_data,
                    timeout=10
                )
                
                if response.status_code == 200:
                    logger.info("✅ User preferences updated successfully")
                    return True
                else:
                    logger.error(f"❌ Preferences update failed: {response.status_code}")
                    return False
            else:
                logger.error(f"❌ Get preferences failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ User preferences error: {e}")
            return False
    
    def test_voice_profiles(self):
        """Test voice profile management."""
        logger.info("🎭 Testing voice profile management...")
        
        if not self.test_user_token:
            logger.error("❌ No authentication token available")
            return False
        
        headers = {
            "Authorization": f"Bearer {self.test_user_token}"
        }
        
        try:
            # List existing voice profiles
            response = requests.get(
                f"{self.chronos_api_url}/api/v1/speechbot/voice-profiles/list",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                profiles = response.json()
                logger.info(f"✅ Listed {len(profiles)} voice profiles")
                return True
            else:
                logger.error(f"❌ Voice profile listing failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Voice profile error: {e}")
            return False
    
    def test_ui_accessibility(self):
        """Test UI accessibility and navigation."""
        logger.info("🖥️ Testing UI accessibility...")
        
        try:
            # Test main UI
            response = requests.get(self.ui_url, timeout=10)
            
            if response.status_code == 200:
                logger.info("✅ Main UI accessible")
                
                # Test Speechbot page
                response = requests.get(f"{self.ui_url}/speechbot", timeout=10)
                
                if response.status_code == 200:
                    logger.info("✅ Speechbot page accessible")
                    return True
                else:
                    logger.error(f"❌ Speechbot page failed: {response.status_code}")
                    return False
            else:
                logger.error(f"❌ Main UI failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ UI accessibility error: {e}")
            return False
    
    def test_end_to_end_workflow(self):
        """Test complete end-to-end workflow."""
        logger.info("🔄 Testing end-to-end workflow...")
        
        if not self.test_user_token:
            logger.error("❌ No authentication token available")
            return False
        
        headers = {
            "Authorization": f"Bearer {self.test_user_token}",
            "Content-Type": "application/json"
        }
        
        try:
            # 1. Get user preferences
            prefs_response = requests.get(
                f"{self.chronos_api_url}/api/v1/speechbot/preferences",
                headers=headers,
                timeout=10
            )
            
            if prefs_response.status_code != 200:
                logger.error("❌ Failed to get preferences in workflow")
                return False
            
            # 2. Generate speech with user's preferred mode
            preferences = prefs_response.json()
            preferred_mode = preferences.get("default_adhd_mode", "calm")
            
            synthesis_data = {
                "text": f"This is a test using your preferred {preferred_mode} mode. You're doing great work today!",
                "voice": "default",
                "mode": preferred_mode
            }
            
            synthesis_response = requests.post(
                f"{self.chronos_api_url}/api/v1/speechbot/tts/synthesize",
                headers=headers,
                json=synthesis_data,
                timeout=30
            )
            
            if synthesis_response.status_code == 200:
                logger.info("✅ End-to-end workflow successful")
                
                # Save workflow test audio
                with open("test_workflow.wav", "wb") as f:
                    f.write(synthesis_response.content)
                
                return True
            else:
                logger.error(f"❌ Synthesis in workflow failed: {synthesis_response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ End-to-end workflow error: {e}")
            return False
    
    def run_all_tests(self):
        """Run all integration tests."""
        logger.info("🚀 Starting Platform Integration Tests")
        logger.info("=" * 50)
        
        tests = [
            ("Service Availability", self.test_service_availability),
            ("User Authentication", self.test_user_authentication),
            ("Speechbot Proxy", self.test_speechbot_proxy),
            ("Voice Synthesis", self.test_voice_synthesis),
            ("User Preferences", self.test_user_preferences),
            ("Voice Profiles", self.test_voice_profiles),
            ("UI Accessibility", self.test_ui_accessibility),
            ("End-to-End Workflow", self.test_end_to_end_workflow),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            logger.info(f"\n🧪 Running: {test_name}")
            try:
                result = test_func()
                results[test_name] = result
                if result:
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
            except Exception as e:
                logger.error(f"❌ {test_name}: ERROR - {e}")
                results[test_name] = False
        
        # Summary
        logger.info("\n" + "=" * 50)
        logger.info("📊 Test Results Summary")
        logger.info("=" * 50)
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{status} {test_name}")
        
        logger.info(f"\nOverall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
        
        if passed == total:
            logger.info("🎉 All integration tests passed! Platform is ready for use.")
        else:
            logger.error("⚠️ Some tests failed. Please review and fix issues.")
        
        return passed == total


def main():
    """Main test function."""
    tester = PlatformIntegrationTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 Platform integration is working perfectly!")
        print("🚀 Ready for production deployment!")
    else:
        print("\n⚠️ Platform integration has issues that need attention.")
        print("🔧 Please review the test results and fix any problems.")
    
    return success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
