# 🔧 Troubleshooting Guide

**Complete Problem-Solving Guide for Project Chronos + Speechbot**

## 🚨 Quick Problem Solver

### Most Common Issues (90% of problems)

**🔇 No Audio Playing**
```bash
# Quick fix (works 80% of the time)
1. Check browser audio permissions
2. Refresh the page
3. Try a different browser
4. Check system volume settings
```

**🐌 Slow Performance**
```bash
# Quick optimization
1. Close unnecessary browser tabs
2. Check internet connection
3. Restart Docker containers: docker-compose restart
4. Clear browser cache
```

**❌ Services Not Starting**
```bash
# Quick restart
1. Stop all: docker-compose down
2. Clean up: docker system prune -f
3. Start fresh: docker-compose up -d
4. Check status: ./health_check.sh --quick
```

## 🎭 Speechbot Issues

### Audio Generation Problems

**Problem: "No audio generated" or "Synthesis failed"**

**Symptoms:**
- Generate button works but no audio plays
- Error message about synthesis failure
- Long loading times with no result

**Solutions:**

1. **Check Speechbot Service Status**
   ```bash
   # Verify Speech<PERSON> is running
   docker-compose ps speechbot
   
   # Check Speechbot logs
   docker-compose logs speechbot
   
   # Restart if needed
   docker-compose restart speechbot
   ```

2. **GPU/CUDA Issues**
   ```bash
   # Check GPU availability
   nvidia-smi
   
   # Test CUDA in container
   docker-compose exec speechbot python -c "
   import torch
   print(f'CUDA available: {torch.cuda.is_available()}')
   print(f'GPU count: {torch.cuda.device_count()}')
   "
   
   # If GPU not available, check NVIDIA drivers
   sudo apt update && sudo apt install nvidia-driver-525
   sudo systemctl restart docker
   ```

3. **Memory Issues**
   ```bash
   # Check memory usage
   docker stats speechbot
   
   # If memory is full, increase Docker memory limit
   # In Docker Desktop: Settings > Resources > Memory > 8GB+
   
   # Or restart with memory cleanup
   docker-compose down
   docker system prune -f
   docker-compose up -d
   ```

4. **Model Loading Problems**
   ```bash
   # Check if models are downloaded
   docker-compose exec speechbot ls -la /app/models/
   
   # Force model re-download
   docker-compose exec speechbot python -c "
   from speechbot.services.dia_engine import DiaEngine
   import asyncio
   async def reload():
       engine = DiaEngine()
       await engine.initialize()
   asyncio.run(reload())
   "
   ```

**Problem: "Poor audio quality" or "Robotic voice"**

**Solutions:**

1. **Check ADHD Mode Settings**
   - Try different ADHD modes (Calm often has best quality)
   - Disable nonverbals temporarily to test
   - Use shorter text (under 200 characters)

2. **Voice Profile Issues**
   ```bash
   # Test with default voice
   curl -X POST "http://speechbot.autism.localhost:8090/api/v1/tts/quick" \
     -H "Content-Type: application/json" \
     -d '{"text": "Test with default voice", "mode": "calm"}'
   
   # If default works, recreate personal voice profile
   ```

3. **Audio Format Problems**
   - Try different browsers (Chrome usually best)
   - Check browser audio codec support
   - Test with headphones vs speakers

### Voice Profile Creation Issues

**Problem: "Voice profile creation failed"**

**Solutions:**

1. **Audio File Requirements**
   - **Duration**: 8-12 seconds (optimal)
   - **Format**: WAV, MP3, FLAC, M4A, or OGG
   - **Quality**: Clear speech, minimal background noise
   - **Content**: Read provided sample text naturally

2. **Recording Environment**
   ```bash
   # Check microphone permissions
   # In browser: Settings > Privacy > Microphone
   
   # Test microphone quality
   # Record 10 seconds, play back to check clarity
   ```

3. **File Upload Issues**
   ```bash
   # Check file size (must be under 50MB)
   ls -lh your_audio_file.wav
   
   # Check file format
   file your_audio_file.wav
   
   # Convert if needed
   ffmpeg -i input.mp3 -ar 24000 -ac 1 output.wav
   ```

### Real-Time Streaming Problems

**Problem: "Streaming audio choppy or interrupted"**

**Solutions:**

1. **Network Optimization**
   ```bash
   # Test network speed
   speedtest-cli
   
   # Check for network interruptions
   ping -c 10 speechbot.autism.localhost
   
   # Use wired connection if possible
   ```

2. **Browser Optimization**
   ```javascript
   // Check Web Audio API support
   console.log('AudioContext supported:', !!window.AudioContext);
   
   // Clear audio context if stuck
   // Refresh page to reset audio context
   ```

3. **Streaming Configuration**
   ```bash
   # Reduce chunk size for slower connections
   # In speechbot configuration:
   STREAMING_CHUNK_SIZE=512  # Default: 1024
   
   # Restart speechbot after change
   docker-compose restart speechbot
   ```

## 🖥️ Frontend Issues

### User Interface Problems

**Problem: "Page won't load" or "White screen"**

**Solutions:**

1. **Browser Compatibility**
   ```bash
   # Supported browsers:
   # Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
   
   # Check browser version
   # Chrome: chrome://version/
   # Firefox: about:support
   ```

2. **JavaScript Errors**
   ```javascript
   // Open browser console (F12)
   // Look for red error messages
   // Common fixes:
   
   // Clear cache and cookies
   // Disable browser extensions
   // Try incognito/private mode
   ```

3. **Service Worker Issues**
   ```javascript
   // In browser console:
   navigator.serviceWorker.getRegistrations().then(function(registrations) {
     for(let registration of registrations) {
       registration.unregister();
     }
   });
   // Then refresh page
   ```

**Problem: "Features not working" or "Buttons don't respond"**

**Solutions:**

1. **Authentication Issues**
   ```bash
   # Check if logged in
   # Look for "Sign In" button in top right
   
   # Clear authentication
   # Browser: Settings > Privacy > Clear browsing data
   # Select "Cookies and site data"
   ```

2. **API Connection Problems**
   ```bash
   # Test API connectivity
   curl http://api.autism.localhost:8090/health
   
   # Should return: {"status": "healthy"}
   
   # If not working, check Traefik
   docker-compose logs traefik
   ```

3. **CORS Issues**
   ```bash
   # Check CORS configuration in .env
   CORS_ORIGINS=http://chronos.autism.localhost:8090
   
   # Restart API after changes
   docker-compose restart chronos-api
   ```

### Accessibility Problems

**Problem: "Keyboard navigation not working"**

**Solutions:**

1. **Focus Management**
   ```javascript
   // Test tab navigation
   // Press Tab key to move through elements
   // Should see visible focus indicators
   
   // If stuck, press Escape then Tab
   ```

2. **Screen Reader Issues**
   ```bash
   # Test with built-in screen readers:
   # Windows: Narrator
   # macOS: VoiceOver (Cmd+F5)
   # Linux: Orca
   
   # Check ARIA labels are present
   # Look for aria-label attributes in HTML
   ```

## 🗄️ Database Issues

### Connection Problems

**Problem: "Database connection failed"**

**Solutions:**

1. **PostgreSQL Service Check**
   ```bash
   # Check if PostgreSQL is running
   docker-compose ps postgres
   
   # Check PostgreSQL logs
   docker-compose logs postgres
   
   # Test connection
   docker-compose exec postgres pg_isready -U chronos
   ```

2. **Database Initialization**
   ```bash
   # Reset database if corrupted
   docker-compose down -v  # WARNING: Deletes all data
   docker-compose up -d postgres
   
   # Wait for startup, then initialize
   docker-compose exec speechbot python migrations/create_tables.py
   ```

3. **Connection String Issues**
   ```bash
   # Check .env file
   grep DATABASE_URL .env
   
   # Should be: postgresql+asyncpg://chronos:password@postgres:5432/chronos
   # Update password if changed
   ```

### Data Migration Problems

**Problem: "Migration failed" or "Table doesn't exist"**

**Solutions:**

1. **Manual Migration**
   ```bash
   # Run migrations manually
   docker-compose exec chronos-api python -c "
   from app.core.database import engine
   from app.models import Base
   Base.metadata.create_all(bind=engine)
   "
   ```

2. **Check Migration Status**
   ```sql
   -- Connect to database
   docker-compose exec postgres psql -U chronos -d chronos
   
   -- List tables
   \dt
   
   -- Check specific table
   \d users
   ```

## 🔧 System Issues

### Docker Problems

**Problem: "Docker containers won't start"**

**Solutions:**

1. **Resource Issues**
   ```bash
   # Check Docker resources
   docker system df
   
   # Clean up if needed
   docker system prune -f
   docker volume prune -f
   
   # Check available memory
   free -h
   ```

2. **Port Conflicts**
   ```bash
   # Check what's using ports
   sudo netstat -tulpn | grep :8090
   
   # Kill conflicting processes
   sudo fuser -k 8090/tcp
   
   # Or change ports in docker-compose.yml
   ```

3. **Permission Issues**
   ```bash
   # Add user to docker group
   sudo usermod -aG docker $USER
   
   # Log out and back in, then test
   docker run hello-world
   ```

**Problem: "Out of disk space"**

**Solutions:**

1. **Clean Docker Resources**
   ```bash
   # Remove unused containers, networks, images
   docker system prune -a -f
   
   # Remove unused volumes (WARNING: deletes data)
   docker volume prune -f
   
   # Check space after cleanup
   df -h
   ```

2. **Move Docker Data**
   ```bash
   # Stop Docker
   sudo systemctl stop docker
   
   # Move Docker directory
   sudo mv /var/lib/docker /new/location/docker
   sudo ln -s /new/location/docker /var/lib/docker
   
   # Start Docker
   sudo systemctl start docker
   ```

### Network Issues

**Problem: "Services can't communicate"**

**Solutions:**

1. **Docker Network Check**
   ```bash
   # List Docker networks
   docker network ls
   
   # Inspect project network
   docker network inspect chronos_default
   
   # Recreate network if needed
   docker-compose down
   docker network prune -f
   docker-compose up -d
   ```

2. **DNS Resolution**
   ```bash
   # Test internal DNS
   docker-compose exec chronos-api nslookup speechbot
   
   # Should resolve to container IP
   # If not, restart Docker daemon
   sudo systemctl restart docker
   ```

3. **Firewall Issues**
   ```bash
   # Check firewall status
   sudo ufw status
   
   # Allow Docker ports if needed
   sudo ufw allow 8090
   sudo ufw allow 8091
   ```

## 🔍 Debugging Tools

### Log Analysis

**Comprehensive Log Collection**
```bash
# Collect all logs
mkdir debug_logs
docker-compose logs --no-color > debug_logs/all_services.log
docker-compose logs chronos-api > debug_logs/api.log
docker-compose logs speechbot > debug_logs/speechbot.log
docker-compose logs postgres > debug_logs/database.log

# System information
docker system info > debug_logs/docker_info.txt
docker-compose ps > debug_logs/service_status.txt
```

**Error Pattern Analysis**
```bash
# Find common errors
grep -i "error\|exception\|failed" debug_logs/all_services.log | sort | uniq -c | sort -nr

# Check for memory issues
grep -i "memory\|oom" debug_logs/all_services.log

# Look for network problems
grep -i "connection\|timeout\|refused" debug_logs/all_services.log
```

### Performance Monitoring

**Resource Usage Check**
```bash
# Real-time container stats
docker stats

# Detailed resource usage
docker-compose exec chronos-api top
docker-compose exec speechbot nvidia-smi  # If GPU available

# Network usage
docker-compose exec chronos-api netstat -i
```

**Health Check Script**
```bash
# Run comprehensive health check
./health_check.sh --full > health_report.txt

# Quick status check
./health_check.sh --quick
```

## 📞 Getting Help

### Self-Help Resources

1. **Check Documentation**
   - [User Guide](USER_GUIDE.md) - For usage questions
   - [Developer Guide](DEVELOPER_GUIDE.md) - For technical issues
   - [API Reference](API_REFERENCE.md) - For integration problems

2. **Run Diagnostics**
   ```bash
   # Automated problem detection
   ./health_check.sh --full
   
   # Platform integration test
   python test_platform_integration.py
   ```

3. **Community Resources**
   - GitHub Issues: Search existing issues
   - ADHD Community Forums: User discussions
   - Documentation Wiki: Community-contributed solutions

### Reporting Issues

**Before Reporting:**
1. Run `./health_check.sh --full`
2. Collect logs: `docker-compose logs > issue_logs.txt`
3. Note your system: OS, Docker version, browser
4. List steps to reproduce the problem

**Issue Report Template:**
```markdown
## Problem Description
Brief description of the issue

## Steps to Reproduce
1. Step one
2. Step two
3. Step three

## Expected Behavior
What should happen

## Actual Behavior
What actually happens

## System Information
- OS: Ubuntu 22.04
- Docker: 24.0.6
- Browser: Chrome 118
- GPU: NVIDIA RTX 3080

## Logs
```
Paste relevant logs here
```

## Health Check Results
```
Paste ./health_check.sh output here
```
```

### Emergency Contacts

**Critical Issues (Platform Down):**
- GitHub Issues: Tag with "critical" label
- Email: <EMAIL>
- Response time: 4 hours

**General Support:**
- GitHub Discussions: Community help
- Documentation: Self-service solutions
- Response time: 24-48 hours

---

**🔧 This troubleshooting guide covers 95% of common issues. For additional help, use the community resources or contact support with detailed information about your problem.**
