# 🛠️ Project Chronos + Speechbot Developer Guide

**Technical Documentation for ADHD Voice Assistant Platform**

## 🏗️ Architecture Overview

### System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[Next.js UI]
        RT[Real-time Components]
    end

    subgraph "API Gateway"
        TF[Traefik Proxy]
        LB[Load Balancer]
    end

    subgraph "Backend Services"
        API[Chronos API]
        SB[Speechbot Service]
        WS[WebSocket Handler]
    end

    subgraph "AI/ML Layer"
        DIA[Dia TTS Engine]
        EMO[Emotion Detection]
        STR[Streaming Engine]
    end

    subgraph "Data Layer"
        PG[PostgreSQL]
        RD[Redis Cache]
        MN[MinIO Storage]
    end

    subgraph "Monitoring"
        PR[Prometheus]
        GR[Grafana]
        LG[Logging]
    end

    UI --> TF
    RT --> WS
    TF --> API
    TF --> SB
    API --> PG
    API --> RD
    SB --> DIA
    SB --> EMO
    SB --> STR
    SB --> MN
    API --> PR
    SB --> PR
    PR --> GR
```

### Technology Stack

**Frontend:**
- **Next.js 14**: React framework with SSR/SSG
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling
- **Shadcn/ui**: Accessible component library
- **Web Audio API**: Real-time audio processing

**Backend:**
- **FastAPI**: High-performance Python API framework
- **SQLAlchemy**: ORM with async support
- **Pydantic**: Data validation and serialization
- **Celery**: Distributed task queue
- **WebSockets**: Real-time communication

**AI/ML:**
- **Dia TTS**: 1.6B parameter voice synthesis model
- **PyTorch**: Deep learning framework
- **Transformers**: Hugging Face model library
- **CUDA**: GPU acceleration
- **NumPy**: Numerical computing

**Infrastructure:**
- **Docker**: Containerization
- **Traefik**: Reverse proxy and load balancer
- **PostgreSQL**: Primary database
- **Redis**: Caching and session storage
- **MinIO**: Object storage for audio files

## 🚀 Development Setup

### Prerequisites

```bash
# Required software
- Docker Desktop 4.0+
- Node.js 18+
- Python 3.11+
- Git 2.30+

# Optional but recommended
- NVIDIA GPU with CUDA 11.8+
- 16GB+ RAM
- SSD storage
```

### Local Development Environment

```bash
# Clone repository
git clone https://github.com/forkrul/day1-idea.git
cd day1-idea

# Set up development environment
cp .env.example .env.dev
# Edit .env.dev with development settings

# Start development services
docker-compose -f docker-compose.dev.yml up -d

# Install frontend dependencies
cd chronos-ui
npm install
npm run dev

# Install backend dependencies
cd ../chronos
pip install -r requirements-dev.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Install Speechbot dependencies
cd ../speechbot
pip install -r requirements-dev.txt
uvicorn main:app --reload --host 0.0.0.0 --port 8001
```

### Development Workflow

```bash
# Create feature branch
git checkout -b feature/new-adhd-feature

# Make changes and test
npm run test          # Frontend tests
pytest               # Backend tests
python -m pytest speechbot/tests/  # Speechbot tests

# Run linting and formatting
npm run lint         # Frontend linting
black .              # Python formatting
flake8 .             # Python linting

# Commit changes
git add .
git commit -m "feat: add new ADHD feature"

# Push and create PR
git push origin feature/new-adhd-feature
```

## 📡 API Documentation

### Core API Endpoints

**Authentication:**
```http
POST /api/v1/auth/register
POST /api/v1/auth/login
POST /api/v1/auth/refresh
DELETE /api/v1/auth/logout
```

**User Management:**
```http
GET /api/v1/users/me
PUT /api/v1/users/me
GET /api/v1/users/{user_id}/preferences
PUT /api/v1/users/{user_id}/preferences
```

**Speechbot Integration:**
```http
GET /api/v1/speechbot/health
GET /api/v1/speechbot/capabilities
POST /api/v1/speechbot/tts/synthesize
POST /api/v1/speechbot/tts/quick
POST /api/v1/speechbot/streaming/synthesize
GET /api/v1/speechbot/streaming/events/{stream_id}
```

**Voice Profiles:**
```http
GET /api/v1/speechbot/voice-profiles/list
POST /api/v1/speechbot/voice-profiles/create
DELETE /api/v1/speechbot/voice-profiles/{profile_id}
POST /api/v1/speechbot/voice-profiles/{profile_id}/test
```

### Request/Response Examples

**Voice Synthesis:**
```json
// POST /api/v1/speechbot/tts/synthesize
{
  "text": "Hello, this is a test message for ADHD users!",
  "voice_profile": "default",
  "adhd_mode": "calm",
  "include_nonverbals": true,
  "format": "wav"
}

// Response: Audio stream with headers
// X-Audio-Duration: 3.2
// X-Sample-Rate: 24000
// X-ADHD-Mode: calm
// Content-Type: audio/wav
```

**Emotion Analysis:**
```json
// POST /api/v1/speechbot/streaming/emotion/analyze
{
  "text": "I'm feeling overwhelmed with all these tasks",
  "user_id": "user-123"
}

// Response
{
  "emotion_scores": {
    "overwhelmed": 0.8,
    "calm": 0.1,
    "excited": 0.0,
    "focused": 0.1,
    "motivated": 0.0
  },
  "suggested_adhd_mode": "overwhelmed",
  "confidence": 0.8,
  "insights": {
    "primary_emotion": "overwhelmed",
    "recommendations": [
      "Consider using 'calm' mode for soothing speech",
      "Take breaks between tasks"
    ]
  }
}
```

## 🎭 Speechbot Service Architecture

### Dia TTS Engine Integration

```python
# speechbot/services/dia_engine.py
class DiaEngine:
    """
    Dia TTS engine wrapper for ADHD-optimized voice synthesis.
    """

    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.adhd_mode_configs = {
            "calm": {"speed": 0.9, "pitch": -0.1, "energy": 0.7},
            "excited": {"speed": 1.2, "pitch": 0.2, "energy": 1.3},
            "focused": {"speed": 1.0, "pitch": 0.0, "energy": 1.0},
            "overwhelmed": {"speed": 0.8, "pitch": -0.2, "energy": 0.6},
            "motivated": {"speed": 1.1, "pitch": 0.1, "energy": 1.2}
        }

    async def initialize(self):
        """Initialize Dia TTS model and tokenizer."""
        self.model = await self._load_model()
        self.tokenizer = await self._load_tokenizer()

    async def synthesize_speech(
        self,
        text: str,
        voice_profile: str = "default",
        adhd_mode: str = "calm",
        include_nonverbals: bool = True
    ) -> Tuple[np.ndarray, int]:
        """
        Synthesize speech with ADHD-specific optimizations.
        """
        # Apply ADHD mode configuration
        config = self.adhd_mode_configs[adhd_mode]

        # Process text for ADHD optimization
        processed_text = self._process_text_for_adhd(text, adhd_mode)

        # Add nonverbals if requested
        if include_nonverbals:
            processed_text = self._add_nonverbals(processed_text, adhd_mode)

        # Generate audio
        audio_data = await self._generate_audio(
            processed_text, voice_profile, config
        )

        return audio_data, 24000  # Sample rate
```

### Real-Time Streaming Implementation

```python
# speechbot/services/streaming_engine.py
class StreamingAudioEngine:
    """
    Real-time audio streaming for immediate ADHD feedback.
    """

    async def stream_synthesis(
        self,
        text: str,
        voice_profile: str = "default",
        adhd_mode: str = "calm",
        user_id: str = None
    ) -> AsyncGenerator[StreamingChunk, None]:
        """
        Generate streaming audio chunks for real-time playback.
        """
        # Split text into streamable chunks
        text_chunks = self._prepare_text_for_streaming(text)

        chunk_index = 0
        for text_chunk in text_chunks:
            # Generate audio for chunk
            audio_data, sample_rate = await self._generate_chunk_audio(
                text_chunk, voice_profile, adhd_mode
            )

            # Split audio into smaller streaming chunks
            audio_chunks = self._split_audio_for_streaming(audio_data)

            for audio_chunk in audio_chunks:
                yield StreamingChunk(
                    audio_data=audio_chunk,
                    sample_rate=sample_rate,
                    chunk_index=chunk_index,
                    metadata={
                        "adhd_mode": adhd_mode,
                        "text_chunk": text_chunk,
                        "chunk_duration": len(audio_chunk) / sample_rate
                    }
                )
                chunk_index += 1

                # Small delay to prevent overwhelming client
                await asyncio.sleep(0.01)
```

### Emotion Detection System

```python
# speechbot/services/streaming_engine.py
class EmotionDetectionEngine:
    """
    ADHD-specific emotion detection from text content.
    """

    def __init__(self):
        self.emotion_keywords = {
            "overwhelmed": [
                "too much", "can't handle", "overwhelmed", "stressed",
                "anxious", "panic", "chaos", "scattered", "frazzled"
            ],
            "focused": [
                "concentrate", "focus", "attention", "clear", "organized",
                "systematic", "methodical", "structured", "planned"
            ],
            "excited": [
                "excited", "energetic", "motivated", "enthusiastic",
                "pumped", "ready", "eager", "passionate", "inspired"
            ],
            "calm": [
                "calm", "peaceful", "relaxed", "steady", "balanced",
                "centered", "composed", "tranquil", "serene"
            ],
            "motivated": [
                "motivated", "determined", "confident", "capable",
                "strong", "accomplished", "successful", "proud"
            ]
        }

    def detect_emotion_from_text(self, text: str) -> Dict[str, float]:
        """
        Analyze text for ADHD-relevant emotional content.
        """
        text_lower = text.lower()
        emotion_scores = {}

        for emotion, keywords in self.emotion_keywords.items():
            score = 0.0
            for keyword in keywords:
                if keyword in text_lower:
                    score += 1.0

            # Normalize by keyword count
            emotion_scores[emotion] = min(score / len(keywords), 1.0)

        return emotion_scores

    def suggest_adhd_mode(self, emotion_scores: Dict[str, float]) -> str:
        """
        Suggest optimal ADHD mode based on detected emotions.
        """
        if not emotion_scores:
            return "calm"

        max_emotion = max(emotion_scores.items(), key=lambda x: x[1])

        if max_emotion[1] > 0.3:  # Confidence threshold
            return max_emotion[0]

        return "calm"  # Safe default
```

## 🎨 Frontend Development

### Component Architecture

```typescript
// frontend/src/components/speechbot/SpeechbotDashboard.tsx
interface SpeechbotDashboardProps {
  userId: string;
}

export const SpeechbotDashboard: React.FC<SpeechbotDashboardProps> = ({
  userId
}) => {
  const [activeTab, setActiveTab] = useState('synthesize');
  const [userPreferences, setUserPreferences] = useState<any>(null);
  const [capabilities, setCapabilities] = useState<any>(null);

  // Load user preferences and system capabilities
  useEffect(() => {
    loadCapabilities();
    loadUserPreferences();
  }, [userId]);

  const loadCapabilities = async () => {
    try {
      const response = await fetch('/api/v1/speechbot/capabilities');
      const data = await response.json();
      setCapabilities(data);
    } catch (error) {
      console.error('Failed to load capabilities:', error);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* ADHD Mode Quick Selector */}
      <ADHDModeSelector
        selectedMode={userPreferences?.default_adhd_mode}
        onModeChange={(mode) => updateUserPreference('default_adhd_mode', mode)}
      />

      {/* Main Feature Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="synthesize">Synthesize</TabsTrigger>
          <TabsTrigger value="streaming">Real-time</TabsTrigger>
          <TabsTrigger value="voices">Voice Profiles</TabsTrigger>
          <TabsTrigger value="body-doubling">Body Doubling</TabsTrigger>
        </TabsList>

        <TabsContent value="synthesize">
          <SpeechSynthesizer userId={userId} />
        </TabsContent>

        <TabsContent value="streaming">
          <RealTimeStreaming userId={userId} />
        </TabsContent>
      </Tabs>
    </div>
  );
};
```

### Real-Time Audio Processing

```typescript
// frontend/src/components/speechbot/RealTimeStreaming.tsx
export const RealTimeStreaming: React.FC<Props> = ({ userId }) => {
  const audioContextRef = useRef<AudioContext | null>(null);
  const [isStreaming, setIsStreaming] = useState(false);

  const startStreaming = async () => {
    // Initialize Web Audio API
    if (!audioContextRef.current) {
      audioContextRef.current = new AudioContext();
    }

    // Resume audio context if suspended
    if (audioContextRef.current.state === 'suspended') {
      await audioContextRef.current.resume();
    }

    // Start streaming synthesis
    const response = await fetch('/api/v1/speechbot/streaming/synthesize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        text,
        user_id: userId,
        adhd_mode: selectedMode,
        auto_emotion_detection: true
      })
    });

    // Process streaming response
    const reader = response.body?.getReader();
    if (reader) {
      await processStreamingChunks(reader);
    }
  };

  const processStreamingChunks = async (
    reader: ReadableStreamDefaultReader
  ) => {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      // Decode and play audio chunk
      const audioBuffer = await audioContextRef.current!.decodeAudioData(
        value.buffer
      );

      const source = audioContextRef.current!.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(audioContextRef.current!.destination);
      source.start();
    }
  };
};
```

### ADHD-Optimized UI Components

```typescript
// frontend/src/components/ui/adhd-optimized.tsx
export const ADHDButton: React.FC<ButtonProps> = ({
  children,
  variant = "default",
  size = "default",
  loading = false,
  ...props
}) => {
  return (
    <button
      className={cn(
        // Base styles with ADHD considerations
        "inline-flex items-center justify-center rounded-md text-sm font-medium",
        "transition-colors focus-visible:outline-none focus-visible:ring-2",
        "focus-visible:ring-ring focus-visible:ring-offset-2",
        "disabled:opacity-50 disabled:pointer-events-none",

        // Large click targets for motor accessibility
        "min-h-[44px] min-w-[44px] px-4 py-2",

        // High contrast for visual accessibility
        "border-2 border-solid",

        // Reduced motion respect
        "motion-reduce:transition-none",

        // Variant styles
        variant === "default" && "bg-primary text-primary-foreground border-primary hover:bg-primary/90",
        variant === "calm" && "bg-blue-500 text-white border-blue-600 hover:bg-blue-600",
        variant === "excited" && "bg-yellow-500 text-black border-yellow-600 hover:bg-yellow-600",

        // Size variants
        size === "sm" && "h-9 px-3",
        size === "lg" && "h-12 px-8 text-base"
      )}
      disabled={loading}
      {...props}
    >
      {loading && (
        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
      )}
      {children}
    </button>
  );
};

export const ADHDCard: React.FC<CardProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <div
      className={cn(
        // Base card styles
        "rounded-lg border bg-card text-card-foreground shadow-sm",

        // ADHD-specific enhancements
        "border-2",  // Stronger borders for clarity
        "p-6",       // Generous padding
        "space-y-4", // Consistent spacing

        // Focus and hover states
        "hover:shadow-md transition-shadow",
        "focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",

        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};
```

## 🗄️ Database Schema

### Core Tables

```sql
-- Users table with ADHD-specific fields
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    is_superuser BOOLEAN DEFAULT false,
    adhd_type VARCHAR(50), -- 'inattentive', 'hyperactive', 'combined'
    adhd_severity VARCHAR(50), -- 'mild', 'moderate', 'severe'
    preferred_modes TEXT[], -- Array of preferred ADHD modes
    accessibility_needs TEXT[], -- Array of accessibility requirements
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Voice profiles for personal voice cloning
CREATE TABLE voice_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    audio_file_path VARCHAR(500),
    quality_score FLOAT DEFAULT 0.0,
    duration FLOAT DEFAULT 0.0,
    sample_rate INTEGER DEFAULT 24000,
    status VARCHAR(50) DEFAULT 'processing', -- 'processing', 'ready', 'failed'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Synthesis sessions for analytics
CREATE TABLE synthesis_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_type VARCHAR(50), -- 'tts', 'streaming', 'body_doubling'
    text_input TEXT,
    adhd_mode VARCHAR(50),
    voice_profile_id UUID REFERENCES voice_profiles(id),
    audio_duration FLOAT,
    generation_time FLOAT,
    quality_rating INTEGER, -- 1-5 user rating
    metadata JSONB, -- Additional session data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User preferences and settings
CREATE TABLE user_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    default_adhd_mode VARCHAR(50) DEFAULT 'calm',
    enable_nonverbals BOOLEAN DEFAULT true,
    nonverbal_frequency FLOAT DEFAULT 0.1,
    default_voice_profile_id UUID REFERENCES voice_profiles(id),
    enable_emotion_detection BOOLEAN DEFAULT true,
    accessibility_settings JSONB,
    notification_settings JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Body doubling sessions
CREATE TABLE body_doubling_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    task_description TEXT,
    planned_duration INTEGER, -- minutes
    actual_duration INTEGER, -- minutes
    encouragement_frequency VARCHAR(50), -- 'low', 'medium', 'high'
    companion_voice_id UUID REFERENCES voice_profiles(id),
    completed BOOLEAN DEFAULT false,
    success_rating INTEGER, -- 1-5 user rating
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);
```

### Indexes for Performance

```sql
-- Performance indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_voice_profiles_user_id ON voice_profiles(user_id);
CREATE INDEX idx_synthesis_sessions_user_id ON synthesis_sessions(user_id);
CREATE INDEX idx_synthesis_sessions_created_at ON synthesis_sessions(created_at);
CREATE INDEX idx_body_doubling_sessions_user_id ON body_doubling_sessions(user_id);

-- ADHD-specific indexes
CREATE INDEX idx_users_adhd_type ON users(adhd_type);
CREATE INDEX idx_synthesis_sessions_adhd_mode ON synthesis_sessions(adhd_mode);
CREATE INDEX idx_synthesis_sessions_session_type ON synthesis_sessions(session_type);
```

## 🧪 Testing Framework

### Backend Testing

```python
# tests/test_speechbot_integration.py
import pytest
import asyncio
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
class TestSpeechbotIntegration:
    """Test Speechbot service integration."""

    async def test_voice_synthesis_basic(self, client: AsyncClient):
        """Test basic voice synthesis functionality."""
        response = await client.post(
            "/api/v1/speechbot/tts/quick",
            json={
                "text": "Hello, this is a test for ADHD users!",
                "mode": "calm"
            }
        )

        assert response.status_code == 200
        assert response.headers["content-type"] == "audio/wav"
        assert int(response.headers["x-audio-duration"]) > 0

    async def test_adhd_modes(self, client: AsyncClient):
        """Test all ADHD emotional modes."""
        modes = ["calm", "excited", "focused", "overwhelmed", "motivated"]

        for mode in modes:
            response = await client.post(
                "/api/v1/speechbot/tts/quick",
                json={
                    "text": f"Testing {mode} mode for ADHD optimization",
                    "mode": mode
                }
            )

            assert response.status_code == 200
            assert response.headers["x-adhd-mode"] == mode

    async def test_emotion_detection(self, client: AsyncClient):
        """Test emotion detection accuracy."""
        test_cases = [
            ("I'm feeling overwhelmed with all these tasks", "overwhelmed"),
            ("I'm so excited to start this new project!", "excited"),
            ("I need to focus and concentrate on this work", "focused"),
            ("I feel calm and peaceful today", "calm"),
            ("I'm motivated to achieve my goals!", "motivated")
        ]

        for text, expected_mode in test_cases:
            response = await client.post(
                "/api/v1/speechbot/streaming/emotion/analyze",
                json={"text": text, "user_id": "test-user"}
            )

            assert response.status_code == 200
            data = response.json()
            assert data["suggested_adhd_mode"] == expected_mode
            assert data["confidence"] > 0.3

@pytest.mark.asyncio
class TestRealTimeStreaming:
    """Test real-time streaming functionality."""

    async def test_streaming_synthesis(self, client: AsyncClient):
        """Test streaming audio generation."""
        response = await client.post(
            "/api/v1/speechbot/streaming/synthesize",
            json={
                "text": "This is a longer text for streaming synthesis testing. " * 5,
                "user_id": "test-user",
                "adhd_mode": "calm"
            }
        )

        assert response.status_code == 200
        assert "multipart/x-mixed-replace" in response.headers["content-type"]

        # Verify streaming chunks
        chunks_received = 0
        async for chunk in response.aiter_bytes():
            if chunk:
                chunks_received += 1

        assert chunks_received > 1  # Should receive multiple chunks
```

### Frontend Testing

```typescript
// frontend/src/components/__tests__/SpeechSynthesizer.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SpeechSynthesizer } from '../speechbot/SpeechSynthesizer';

// Mock fetch for API calls
global.fetch = jest.fn();

describe('SpeechSynthesizer', () => {
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  test('renders ADHD mode selector', () => {
    render(<SpeechSynthesizer userId="test-user" />);

    expect(screen.getByText('Calm')).toBeInTheDocument();
    expect(screen.getByText('Excited')).toBeInTheDocument();
    expect(screen.getByText('Focused')).toBeInTheDocument();
    expect(screen.getByText('Overwhelmed')).toBeInTheDocument();
    expect(screen.getByText('Motivated')).toBeInTheDocument();
  });

  test('generates speech with selected mode', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      blob: () => Promise.resolve(new Blob(['audio data'])),
      headers: new Headers({
        'x-adhd-mode': 'calm',
        'x-audio-duration': '3.2'
      })
    });

    render(<SpeechSynthesizer userId="test-user" />);

    // Enter text
    const textInput = screen.getByPlaceholderText(/enter text/i);
    fireEvent.change(textInput, {
      target: { value: 'Hello ADHD community!' }
    });

    // Select calm mode
    fireEvent.click(screen.getByText('Calm'));

    // Generate speech
    fireEvent.click(screen.getByText(/generate speech/i));

    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        '/api/v1/speechbot/tts/synthesize',
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('"adhd_mode":"calm"')
        })
      );
    });
  });

  test('shows emotion detection results', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({
        emotion_scores: { overwhelmed: 0.8, calm: 0.2 },
        suggested_adhd_mode: 'overwhelmed',
        confidence: 0.8
      })
    });

    render(<SpeechSynthesizer userId="test-user" />);

    // Enable emotion detection
    const emotionToggle = screen.getByLabelText(/auto-detect emotion/i);
    fireEvent.click(emotionToggle);

    // Enter emotional text
    const textInput = screen.getByPlaceholderText(/enter text/i);
    fireEvent.change(textInput, {
      target: { value: 'I feel overwhelmed with everything' }
    });

    await waitFor(() => {
      expect(screen.getByText(/overwhelmed.*80%/i)).toBeInTheDocument();
    });
  });
});

// Accessibility testing
describe('SpeechSynthesizer Accessibility', () => {
  test('supports keyboard navigation', () => {
    render(<SpeechSynthesizer userId="test-user" />);

    const textInput = screen.getByPlaceholderText(/enter text/i);
    textInput.focus();

    // Tab through ADHD mode buttons
    fireEvent.keyDown(textInput, { key: 'Tab' });
    expect(screen.getByText('Calm')).toHaveFocus();

    fireEvent.keyDown(document.activeElement!, { key: 'Tab' });
    expect(screen.getByText('Excited')).toHaveFocus();
  });

  test('provides proper ARIA labels', () => {
    render(<SpeechSynthesizer userId="test-user" />);

    expect(screen.getByLabelText(/text to synthesize/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/adhd emotional mode/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /generate speech/i })).toBeInTheDocument();
  });

  test('announces status changes to screen readers', async () => {
    render(<SpeechSynthesizer userId="test-user" />);

    const statusRegion = screen.getByRole('status');
    expect(statusRegion).toBeInTheDocument();

    // Status should update during synthesis
    fireEvent.click(screen.getByText(/generate speech/i));

    await waitFor(() => {
      expect(statusRegion).toHaveTextContent(/generating/i);
    });
  });
});
```

### Integration Testing

```python
# tests/test_platform_integration.py
import pytest
import asyncio
from httpx import AsyncClient
from app.main import app

class TestPlatformIntegration:
    """End-to-end platform integration tests."""

    async def test_complete_user_workflow(self, client: AsyncClient):
        """Test complete user workflow from registration to voice synthesis."""

        # 1. User registration
        registration_data = {
            "email": "<EMAIL>",
            "password": "securepass123",
            "full_name": "Test ADHD User",
            "adhd_type": "combined",
            "adhd_severity": "moderate"
        }

        response = await client.post("/api/v1/auth/register", json=registration_data)
        assert response.status_code == 201
        user_data = response.json()

        # 2. User login
        login_data = {
            "username": "<EMAIL>",
            "password": "securepass123"
        }

        response = await client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 200
        token_data = response.json()

        headers = {"Authorization": f"Bearer {token_data['access_token']}"}

        # 3. Set user preferences
        preferences = {
            "default_adhd_mode": "calm",
            "enable_emotion_detection": True,
            "enable_nonverbals": True
        }

        response = await client.put(
            f"/api/v1/users/{user_data['id']}/preferences",
            json=preferences,
            headers=headers
        )
        assert response.status_code == 200

        # 4. Test voice synthesis
        synthesis_data = {
            "text": "Hello, this is my first ADHD-optimized voice message!",
            "adhd_mode": "calm",
            "include_nonverbals": True
        }

        response = await client.post(
            "/api/v1/speechbot/tts/synthesize",
            json=synthesis_data,
            headers=headers
        )
        assert response.status_code == 200
        assert response.headers["content-type"] == "audio/wav"

        # 5. Test emotion detection
        emotion_data = {
            "text": "I'm feeling overwhelmed with all these new features",
            "user_id": user_data['id']
        }

        response = await client.post(
            "/api/v1/speechbot/streaming/emotion/analyze",
            json=emotion_data,
            headers=headers
        )
        assert response.status_code == 200
        emotion_result = response.json()
        assert emotion_result["suggested_adhd_mode"] == "overwhelmed"

        # 6. Test body doubling session
        session_data = {
            "task_description": "Complete integration testing",
            "planned_duration": 25,
            "encouragement_frequency": "medium"
        }

        response = await client.post(
            "/api/v1/body-doubling/sessions",
            json=session_data,
            headers=headers
        )
        assert response.status_code == 201
```

## 🔧 Configuration Management

### Environment Configuration

```python
# app/core/config.py
from pydantic import BaseSettings, validator
from typing import List, Optional

class Settings(BaseSettings):
    """Application settings with ADHD-specific configurations."""

    # Basic app settings
    PROJECT_NAME: str = "Project Chronos"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"

    # Security
    SECRET_KEY: str
    JWT_SECRET_KEY: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Database
    DATABASE_URL: str
    DB_POOL_SIZE: int = 20
    DB_MAX_OVERFLOW: int = 30

    # Redis
    REDIS_URL: str
    REDIS_PASSWORD: Optional[str] = None

    # Speechbot configuration
    SPEECHBOT_URL: str = "http://speechbot:8001"
    DEFAULT_ADHD_MODE: str = "calm"
    ENABLE_EMOTION_DETECTION: bool = True

    # ADHD-specific settings
    ADHD_MODES: List[str] = ["calm", "excited", "focused", "overwhelmed", "motivated"]
    MAX_VOICE_PROFILES_PER_USER: int = 10
    VOICE_SAMPLE_MIN_DURATION: int = 5
    VOICE_SAMPLE_MAX_DURATION: int = 30

    # AI/ML settings
    HF_TOKEN: Optional[str] = None
    MODEL_CACHE_DIR: str = "/app/models"
    CUDA_VISIBLE_DEVICES: str = "0"

    # File upload limits
    MAX_UPLOAD_SIZE_MB: int = 50
    ALLOWED_AUDIO_FORMATS: List[str] = ["wav", "mp3", "flac", "m4a", "ogg"]

    # Monitoring
    ENABLE_METRICS: bool = True
    LOG_LEVEL: str = "INFO"

    @validator("ADHD_MODES")
    def validate_adhd_modes(cls, v):
        required_modes = {"calm", "excited", "focused", "overwhelmed", "motivated"}
        if not required_modes.issubset(set(v)):
            raise ValueError(f"ADHD_MODES must include: {required_modes}")
        return v

    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

### Docker Configuration

```yaml
# docker-compose.production.yml
version: '3.8'

services:
  chronos-api:
    build:
      context: ./chronos
      dockerfile: Dockerfile.production
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - WORKERS=4
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2'
        reservations:
          memory: 1G
          cpus: '1'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  speechbot:
    build:
      context: ./speechbot
      dockerfile: Dockerfile.production
    environment:
      - ENVIRONMENT=production
      - DIA_PRECISION=fp16
      - DIA_BATCH_SIZE=4
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4'
        reservations:
          memory: 4G
          cpus: '2'
    runtime: nvidia
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 120s

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
    driver: local
  voice_profiles:
    driver: local
  generated_audio:
    driver: local
```

## 📊 Monitoring and Observability

### Prometheus Metrics

```python
# app/core/metrics.py
from prometheus_client import Counter, Histogram, Gauge, generate_latest
import time
from functools import wraps

# ADHD-specific metrics
synthesis_requests_total = Counter(
    'speechbot_synthesis_requests_total',
    'Total number of speech synthesis requests',
    ['adhd_mode', 'voice_profile', 'user_type']
)

synthesis_duration_seconds = Histogram(
    'speechbot_synthesis_duration_seconds',
    'Time spent generating speech',
    ['adhd_mode'],
    buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0]
)

active_body_doubling_sessions = Gauge(
    'speechbot_active_body_doubling_sessions',
    'Number of active body doubling sessions'
)

emotion_detection_accuracy = Histogram(
    'speechbot_emotion_detection_confidence',
    'Confidence scores for emotion detection',
    buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
)

def track_synthesis_metrics(func):
    """Decorator to track synthesis performance metrics."""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()

        # Extract ADHD mode from kwargs
        adhd_mode = kwargs.get('adhd_mode', 'unknown')
        voice_profile = kwargs.get('voice_profile', 'default')

        try:
            result = await func(*args, **kwargs)

            # Record successful synthesis
            synthesis_requests_total.labels(
                adhd_mode=adhd_mode,
                voice_profile=voice_profile,
                user_type='adhd'
            ).inc()

            # Record duration
            duration = time.time() - start_time
            synthesis_duration_seconds.labels(adhd_mode=adhd_mode).observe(duration)

            return result

        except Exception as e:
            # Record failed synthesis
            synthesis_requests_total.labels(
                adhd_mode=adhd_mode,
                voice_profile=voice_profile,
                user_type='error'
            ).inc()
            raise

    return wrapper
```

### Grafana Dashboard Configuration

```json
{
  "dashboard": {
    "title": "Project Chronos + Speechbot ADHD Metrics",
    "panels": [
      {
        "title": "ADHD Mode Usage",
        "type": "piechart",
        "targets": [
          {
            "expr": "sum by (adhd_mode) (rate(speechbot_synthesis_requests_total[5m]))",
            "legendFormat": "{{adhd_mode}}"
          }
        ]
      },
      {
        "title": "Synthesis Performance by ADHD Mode",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(speechbot_synthesis_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(speechbot_synthesis_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "title": "Active Body Doubling Sessions",
        "type": "stat",
        "targets": [
          {
            "expr": "speechbot_active_body_doubling_sessions",
            "legendFormat": "Active Sessions"
          }
        ]
      },
      {
        "title": "Emotion Detection Confidence",
        "type": "heatmap",
        "targets": [
          {
            "expr": "rate(speechbot_emotion_detection_confidence_bucket[5m])",
            "legendFormat": "{{le}}"
          }
        ]
      }
    ]
  }
}
```

---

**🎯 This developer guide provides comprehensive technical documentation for building, extending, and maintaining the world's most advanced ADHD voice assistant platform!**