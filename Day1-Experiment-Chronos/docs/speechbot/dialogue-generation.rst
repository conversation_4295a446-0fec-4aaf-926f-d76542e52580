Dialogue Generation Guide
=========================

<PERSON><PERSON>'s dialogue generation creates realistic conversations between multiple speakers, providing powerful tools for ADHD support including body doubling, social practice, and therapeutic conversations. This guide covers creating, customizing, and using multi-speaker dialogue effectively.

.. note::
   Dialogue generation is one of Speechbot's most innovative features, specifically designed to address ADHD challenges like isolation, social anxiety, and executive function difficulties.

Understanding Dialogue Generation
----------------------------------

**What is Multi-Speaker Dialogue?**

Dialogue generation creates realistic conversations between 2-4 different speakers, each with unique voices and personalities. Unlike simple text-to-speech, this creates natural conversational flow with:

- Realistic turn-taking and pauses
- Speaker-specific voice characteristics
- Natural conversation rhythm
- Context-appropriate responses
- Emotional variation between speakers

**ADHD Benefits of Dialogue**

🤝 **Social Support**
   - Virtual companions for body doubling
   - Safe practice for social interactions
   - Reduced isolation during solo work
   - Accountability without judgment

🧠 **Cognitive Benefits**
   - Multiple perspectives on problems
   - Structured thinking through conversation
   - Executive function support via dialogue
   - Memory reinforcement through repetition

💝 **Emotional Benefits**
   - Supportive voices during difficult moments
   - Celebration of achievements
   - Gentle guidance and encouragement
   - Reduced anxiety through familiar interactions

Dialogue Components
-------------------

**Speakers and Voice Profiles**

Each dialogue can include up to 4 speakers:

.. list-table:: Speaker Configuration
   :header-rows: 1
   :widths: 15 25 60

   * - Speaker ID
     - Typical Role
     - Voice Profile Options
   * - [S1]
     - User/Primary
     - Your personal voice profile
   * - [S2]
     - Companion/Coach
     - Supportive, encouraging voice
   * - [S3]
     - Expert/Guide
     - Professional, knowledgeable voice
   * - [S4]
     - Friend/Peer
     - Casual, relatable voice

**Conversation Elements**

**Turn Structure**::

    [S1] "I'm feeling overwhelmed with this project."
    [S2] "That's completely understandable. Let's break it down together."
    [S1] "Where should I start?"
    [S2] "What's the most important part that needs to be done first?"

**Nonverbal Communication**::

    [S2] "You're doing great! (chuckles) I can see the progress you're making."
    [S1] "Really? (sighs) It doesn't feel like much."
    [S2] "Trust me, every step forward counts. (gentle breath)"

**Emotional Variation**::

    [S2] "Let's celebrate this win! You finished the first section!"
    [S1] "I did! (laughs) That actually felt good."
    [S2] "See? You're more capable than you give yourself credit for."

Creating Your First Dialogue
-----------------------------

**Step 1: Plan Your Scenario**

Choose a scenario that addresses your ADHD needs:

**Body Doubling Session**
   - Purpose: Accountability and presence during work
   - Speakers: You + Virtual companion
   - Duration: 25-90 minutes
   - Tone: Supportive, encouraging

**Task Coaching**
   - Purpose: Breaking down complex tasks
   - Speakers: You + Coach/Expert
   - Duration: 10-30 minutes
   - Tone: Structured, helpful

**Social Practice**
   - Purpose: Practicing conversations safely
   - Speakers: Multiple people in scenario
   - Duration: 5-15 minutes
   - Tone: Realistic, varied

**Motivation Session**
   - Purpose: Encouragement during difficult times
   - Speakers: You + Supportive friend
   - Duration: 3-10 minutes
   - Tone: Warm, understanding

**Step 2: Write Your Script**

**Basic Script Structure**::

    # Body Doubling Work Session
    [S2] "Hi! Ready to work together on your project?"
    [S1] "Yes, I'm working on my presentation today."
    [S2] "Great! I'll be here working alongside you. What's your first step?"
    [S1] "I need to outline the main sections."
    [S2] "Perfect. I'll start on my tasks too. Let's get focused!"

**Advanced Script with Nonverbals**::

    # Motivation and Support
    [S1] "I'm really struggling with this task. (sighs)"
    [S2] "I hear you. (gentle breath) What's making it feel difficult?"
    [S1] "It just seems so overwhelming."
    [S2] "Let's take it one piece at a time. (encouraging tone) You don't have to do it all at once."

**Step 3: Configure Speakers**

1. **Assign Voice Profiles**
   - [S1]: Your personal voice profile
   - [S2]: Supportive companion voice (default or custom)
   - [S3]: Professional/expert voice (if needed)
   - [S4]: Casual friend voice (if needed)

2. **Set ADHD Mode**
   - **Conversational**: Balanced, natural dialogue
   - **Supportive**: Extra encouragement and patience
   - **Focused**: Clear, structured communication
   - **Energetic**: Upbeat, motivating tone
   - **Calm**: Gentle, soothing interaction

**Step 4: Generate and Test**

1. **Generate Dialogue**
   - Upload your script to the dialogue generator
   - Select voice profiles for each speaker
   - Choose appropriate ADHD mode
   - Generate audio

2. **Review and Adjust**
   - Listen to the full dialogue
   - Check for natural flow and timing
   - Adjust pauses and pacing if needed
   - Regenerate sections as necessary

Pre-Built Dialogue Templates
----------------------------

Speechbot includes templates for common ADHD scenarios:

**Template 1: Body Doubling Work Session**

*Duration: 25 minutes*
*Speakers: User + Companion*

.. code-block:: none

   [S2] "Hi! I see you're working on [TASK] today. I'll be here working alongside you."
   [S1] "Thanks, I really need the company for this."
   [S2] "Let's start our focus session together. (gentle breath)"
   
   [10 minutes of work time with occasional check-ins]
   
   [S2] "You're doing great! Keep up the good work."
   [S1] "This is actually going better than I expected."
   [S2] "See? You're more capable than you think."
   
   [Continue with encouragement and presence]

**Template 2: Task Breakdown Coaching**

*Duration: 15 minutes*
*Speakers: User + Coach*

.. code-block:: none

   [S2] "Let's break this overwhelming task down into smaller steps."
   [S1] "I don't even know where to start."
   [S2] "That's okay. What's the end goal you're trying to achieve?"
   [S1] "I need to [GOAL]."
   [S2] "Perfect. Now, what's the very first small step toward that goal?"

**Template 3: Motivation and Encouragement**

*Duration: 5 minutes*
*Speakers: User + Supportive Friend*

.. code-block:: none

   [S1] "I'm feeling really down about my progress today."
   [S2] "I understand that feeling. (gentle sigh) Can you tell me one thing you accomplished?"
   [S1] "Well, I did manage to [SMALL ACCOMPLISHMENT]."
   [S2] "That's actually really significant! (encouraging tone) You're being too hard on yourself."

**Template 4: Social Skills Practice**

*Duration: 10 minutes*
*Speakers: User + Practice Partner*

.. code-block:: none

   [S2] "Let's practice that conversation you're nervous about."
   [S1] "Okay, but I'm worried I'll say the wrong thing."
   [S2] "Remember, this is just practice. There's no pressure here."
   [S1] "Hi, I wanted to talk to you about [TOPIC]."
   [S2] "I'm glad you brought this up. What's on your mind?"

Advanced Dialogue Techniques
-----------------------------

**Dynamic Conversations**

Create conversations that adapt to different scenarios:

**Variable Insertion**::

    [S2] "How are you feeling about [CURRENT_TASK] today?"
    [S1] "I'm feeling [MOOD_LEVEL] about it."
    [S2] "That's [MOOD_RESPONSE]. Let's work with that energy."

**Conditional Responses**::

    # If user expresses frustration
    [S2] "I can hear the frustration in your voice. (understanding tone)"
    [S2] "What would help you feel more supported right now?"
    
    # If user expresses confidence
    [S2] "I love hearing that confidence! (excited) Let's channel that energy."

**Emotional Progression**

Design conversations that evolve emotionally:

**Beginning: Uncertainty**::

    [S1] "I'm not sure I can handle this today."
    [S2] "That's a completely valid feeling. Let's start small."

**Middle: Building Confidence**::

    [S1] "Actually, this isn't as bad as I thought."
    [S2] "See? You're finding your rhythm. (encouraging)"

**End: Achievement**::

    [S1] "I can't believe I actually did it!"
    [S2] "I'm so proud of you! (laughs) You should be proud too."

**Multi-Context Dialogues**

Create longer dialogues that cover multiple scenarios:

**Full Work Session Arc**::

    1. **Opening**: Greeting and goal setting
    2. **Work Period 1**: Initial focus and momentum
    3. **Check-in**: Progress assessment and encouragement
    4. **Work Period 2**: Continued effort with support
    5. **Challenge**: Addressing difficulties or distractions
    6. **Recovery**: Getting back on track
    7. **Final Push**: Completing the session
    8. **Celebration**: Acknowledging accomplishments

Body Doubling Dialogues
-----------------------

**Virtual Companion Features**

Body doubling dialogues provide:

🤝 **Presence Without Pressure**
   - Gentle awareness of shared work time
   - Minimal interruption to flow states
   - Supportive check-ins at appropriate intervals

⏰ **Time Structure**
   - Natural work rhythm cues
   - Break reminders without jarring alarms
   - Session transition support

💪 **Motivation and Accountability**
   - Encouragement during difficult moments
   - Celebration of progress and achievements
   - Gentle redirection when attention wanders

**Customizing Body Doubling Sessions**

**Encouragement Frequency**

.. list-table:: Encouragement Settings
   :header-rows: 1
   :widths: 20 30 50

   * - Setting
     - Frequency
     - Best For
   * - Low
     - Every 15-20 minutes
     - Deep focus work, hyperfocus sessions
   * - Medium
     - Every 8-12 minutes
     - Regular tasks, balanced support
   * - High
     - Every 5-8 minutes
     - Difficult tasks, low motivation days

**Companion Personalities**

**Supportive Coach**::

    [S2] "You're making steady progress. I can see your focus improving."
    [S2] "Remember, it's okay to take breaks when you need them."
    [S2] "Every step forward is worth celebrating."

**Enthusiastic Cheerleader**::

    [S2] "You're crushing this! (excited) Keep that momentum going!"
    [S2] "I'm so impressed by your dedication today!"
    [S2] "You've got this! I believe in you completely!"

**Calm Presence**::

    [S2] "I'm here with you. (gentle breath) Take your time."
    [S2] "You're doing exactly what you need to do."
    [S2] "Feel free to work at your own pace."

**Practical Accountability Partner**::

    [S2] "How's that first section coming along?"
    [S2] "Ready to move on to the next part?"
    [S2] "What's your next priority after this?"

Social Practice Dialogues
--------------------------

**Safe Conversation Practice**

Use dialogue generation to practice challenging conversations:

**Workplace Scenarios**::

    # Asking for help
    [S1] "Hi, I was wondering if you could help me with something."
    [S2] "Of course! What do you need assistance with?"
    [S1] "I'm struggling with [SPECIFIC ISSUE] and could use some guidance."

    # Requesting accommodations
    [S1] "I'd like to discuss some workplace accommodations that would help me."
    [S2] "I'm glad you brought this up. What accommodations would be helpful?"

**Social Situations**::

    # Making plans
    [S1] "Would you like to get coffee sometime this week?"
    [S2] "That sounds great! When works best for you?"
    [S1] "How about Thursday afternoon?"

    # Declining invitations
    [S1] "Thanks for the invitation, but I won't be able to make it."
    [S2] "No problem at all! Maybe next time."
    [S1] "Definitely, I'd love to join another time."

**Difficult Conversations**::

    # Setting boundaries
    [S1] "I need to talk to you about something that's been bothering me."
    [S2] "I appreciate you bringing this up. What's going on?"
    [S1] "I feel like [BOUNDARY ISSUE] and I'd like to find a solution."

Therapeutic Dialogue Applications
---------------------------------

**Self-Compassion Conversations**

Create dialogues that model self-compassionate thinking:

**Internal Dialogue Externalized**::

    [S1] "I messed up again. I'm so stupid."
    [S2] "Hey, that's not true. You made a mistake, but that doesn't define you."
    [S1] "But I should have known better."
    [S2] "Everyone makes mistakes. What matters is what you learn from this."

**Cognitive Reframing**::

    [S1] "I'll never be able to finish this project."
    [S2] "That sounds like all-or-nothing thinking. What if we broke it down?"
    [S1] "I guess I could focus on just one section at a time."
    [S2] "Exactly! You don't have to do everything at once."

**Emotional Validation**::

    [S1] "I'm feeling overwhelmed and I don't know why."
    [S2] "Feeling overwhelmed is completely valid. You don't need a reason."
    [S1] "Really? It's okay to feel this way?"
    [S2] "Absolutely. Your feelings are always valid, even when they're difficult."

**Problem-Solving Support**::

    [S1] "I have so many things to do and I don't know where to start."
    [S2] "Let's make a list together. What's the most urgent thing?"
    [S1] "Probably the work deadline tomorrow."
    [S2] "Great start. What's one small step you can take on that right now?"

Technical Implementation
------------------------

**API Usage for Dialogue Generation**

**Basic Dialogue Request**::

    curl -X POST "https://speechbot.autism.localhost:8090/api/v1/dialogue/generate" \
         -H "Content-Type: application/json" \
         -H "Authorization: Bearer YOUR_TOKEN" \
         -d '{
           "dialogue": [
             {"speaker": "[S1]", "text": "Hi, I need some help staying focused today."},
             {"speaker": "[S2]", "text": "I'm here to help! What are you working on?"},
             {"speaker": "[S1]", "text": "I have a presentation to prepare."},
             {"speaker": "[S2]", "text": "Great! Let's break that down into manageable steps."}
           ],
           "voice_profiles": {
             "[S1]": "user_voice_profile",
             "[S2]": "supportive_companion"
           },
           "adhd_mode": "supportive"
         }'

**Body Doubling Session Request**::

    curl -X POST "https://speechbot.autism.localhost:8090/api/v1/dialogue/body-doubling" \
         -H "Content-Type: application/json" \
         -H "Authorization: Bearer YOUR_TOKEN" \
         -d '{
           "user_task": "Writing project proposal",
           "session_duration": 25,
           "encouragement_frequency": "medium",
           "user_voice_profile": "my_voice",
           "companion_voice_profile": "supportive_coach"
         }'

**Advanced Features**

**Conversation Templates**::

    GET /api/v1/dialogue/templates
    # Returns pre-built conversation templates

**Custom Scenarios**::

    POST /api/v1/dialogue/scenarios/custom
    # Create and save custom dialogue scenarios

**Real-time Dialogue**::

    WebSocket: wss://speechbot.autism.localhost:8090/ws/dialogue
    # Interactive, real-time dialogue generation

Best Practices for ADHD Users
------------------------------

**Creating Effective Dialogues**

✅ **Do:**
   - Start with shorter dialogues (2-5 minutes)
   - Use familiar, comfortable language
   - Include encouraging and supportive messages
   - Test different voice combinations
   - Save successful dialogues for reuse

❌ **Avoid:**
   - Overly long or complex conversations
   - Negative or critical dialogue content
   - Too many speakers in one conversation
   - Unrealistic or judgmental scenarios

**Managing Dialogue Fatigue**

- Vary dialogue types and lengths
- Take breaks between dialogue sessions
- Use simpler dialogues when energy is low
- Combine with other ADHD support strategies

**Personalizing for Your Needs**

- Create dialogues for your specific challenges
- Use your own voice for self-compassion work
- Include family/friend voices (with permission)
- Adapt templates to your communication style

Troubleshooting Dialogue Issues
-------------------------------

**Common Problems and Solutions**

**Unnatural Conversation Flow**
   - Add appropriate pauses between speakers
   - Use more natural, conversational language
   - Include nonverbal cues and emotions

**Speaker Voices Too Similar**
   - Use distinctly different voice profiles
   - Adjust ADHD modes for each speaker
   - Consider gender and age variety in voices

**Dialogue Too Long or Short**
   - Adjust script length based on purpose
   - Use templates as starting points
   - Test different durations for effectiveness

**Technical Issues**
   - Check voice profile availability
   - Verify speaker tag formatting [S1], [S2], etc.
   - Ensure proper JSON formatting for API requests

Next Steps
----------

Master dialogue generation by:

1. **Experimenting with templates** to understand effective patterns
2. **Creating personal dialogue scenarios** for your specific ADHD challenges
3. **Building a library** of helpful conversations for different situations
4. **Integrating dialogues** with other Speechbot features like voice cloning
5. **Sharing successful dialogues** with the ADHD community

.. seealso::

   :doc:`voice-cloning`
      Create custom voice profiles for more personalized dialogues

   :doc:`adhd-features`
      Learn how dialogue generation integrates with other ADHD support features

   :doc:`api-reference`
      Technical documentation for developers building dialogue applications
