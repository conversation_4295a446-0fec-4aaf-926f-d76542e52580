Speechbot API Reference
=======================

This reference provides comprehensive documentation for Speechbot's REST API, designed for developers integrating ADHD-optimized voice synthesis into applications and workflows.

.. note::
   All API endpoints require authentication. Include your JWT token in the Authorization header: ``Authorization: Bearer YOUR_TOKEN``

Base URL and Authentication
---------------------------

**Base URL**::

    https://speechbot.autism.localhost:8090/api/v1

**Authentication**::

    curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
         -H "Content-Type: application/json" \
         https://speechbot.autism.localhost:8090/api/v1/tts/capabilities

**Rate Limits**

- **Text-to-Speech**: 100 requests per minute per user
- **Voice Profile Creation**: 10 requests per hour per user
- **Dialogue Generation**: 50 requests per minute per user

Text-to-Speech Endpoints
------------------------

POST /tts/synthesize
~~~~~~~~~~~~~~~~~~~~

Generate speech with full ADHD optimization features.

**Request Body**:

.. code-block:: json

    {
      "text": "Hello, this is a test of ADHD-optimized speech synthesis.",
      "voice_profile": "user_voice_profile",
      "adhd_mode": "calm",
      "include_nonverbals": true,
      "speaker_id": "[S1]",
      "format": "wav"
    }

**Parameters**:

.. list-table::
   :header-rows: 1
   :widths: 20 15 15 50

   * - Parameter
     - Type
     - Required
     - Description
   * - text
     - string
     - Yes
     - Text to synthesize (1-5000 characters)
   * - voice_profile
     - string
     - No
     - Voice profile name (default: "default")
   * - adhd_mode
     - string
     - No
     - ADHD emotional mode (default: "calm")
   * - include_nonverbals
     - boolean
     - No
     - Include nonverbal sounds (default: true)
   * - speaker_id
     - string
     - No
     - Speaker identifier (default: "[S1]")
   * - format
     - string
     - No
     - Audio format: wav, mp3, flac (default: "wav")

**ADHD Modes**:

- ``calm``: Slower tempo, gentle tone for focused work
- ``excited``: Faster pace, upbeat tone for motivation
- ``focused``: Clear articulation, structured delivery
- ``overwhelmed``: Softer volume, comforting nonverbals
- ``motivated``: Dynamic pace, confident tone

**Response**:

Returns audio file with headers:

.. code-block:: http

    HTTP/1.1 200 OK
    Content-Type: audio/wav
    X-Audio-Duration: 3.2
    X-Sample-Rate: 24000
    X-ADHD-Mode: calm
    X-Voice-Profile: user_voice_profile
    X-Nonverbals-Included: true

**Example**::

    curl -X POST "https://speechbot.autism.localhost:8090/api/v1/tts/synthesize" \
         -H "Authorization: Bearer YOUR_TOKEN" \
         -H "Content-Type: application/json" \
         -d '{
           "text": "You are doing great work today! Keep up the excellent progress.",
           "voice_profile": "my_voice",
           "adhd_mode": "motivated",
           "include_nonverbals": true
         }' \
         --output motivational_message.wav

POST /tts/quick
~~~~~~~~~~~~~~~

Simple text-to-speech for basic use cases.

**Request Body**:

.. code-block:: json

    {
      "text": "Quick message for immediate synthesis",
      "voice": "default",
      "mode": "calm"
    }

**Parameters**:

.. list-table::
   :header-rows: 1
   :widths: 20 15 15 50

   * - Parameter
     - Type
     - Required
     - Description
   * - text
     - string
     - Yes
     - Text to synthesize (max 500 characters)
   * - voice
     - string
     - No
     - Voice profile (default: "default")
   * - mode
     - string
     - No
     - ADHD mode (default: "calm")

**Response**: Audio file (WAV format)

**Example**::

    curl -X POST "https://speechbot.autism.localhost:8090/api/v1/tts/quick" \
         -H "Authorization: Bearer YOUR_TOKEN" \
         -H "Content-Type: application/json" \
         -d '{
           "text": "Time for a break! You have been working hard.",
           "voice": "default",
           "mode": "calm"
         }' \
         --output break_reminder.wav

GET /tts/voices
~~~~~~~~~~~~~~~

List available voice profiles.

**Response**:

.. code-block:: json

    {
      "voices": [
        {
          "id": "user_123_my_voice",
          "name": "My Voice",
          "user_id": "user_123",
          "quality_score": 0.85,
          "duration": 8.5,
          "created_at": **********
        }
      ],
      "total_count": 1,
      "default_voices": ["default", "system"],
      "adhd_optimized": true
    }

GET /tts/adhd-modes
~~~~~~~~~~~~~~~~~~~

Get available ADHD optimization modes.

**Response**:

.. code-block:: json

    {
      "modes": {
        "calm": {
          "description": "Relaxed, steady pace for focused work",
          "characteristics": ["slower tempo", "gentle tone", "minimal nonverbals"],
          "best_for": ["deep work", "reading", "concentration tasks"]
        },
        "excited": {
          "description": "Energetic, enthusiastic tone for motivation",
          "characteristics": ["faster tempo", "upbeat tone", "frequent nonverbals"],
          "best_for": ["celebrations", "motivation", "exercise"]
        }
      },
      "default_mode": "calm",
      "adhd_optimized": true
    }

GET /tts/capabilities
~~~~~~~~~~~~~~~~~~~~~

Get TTS engine capabilities and system information.

**Response**:

.. code-block:: json

    {
      "engine": "Dia TTS (Nari Labs)",
      "model_size": "1.6B parameters",
      "features": {
        "voice_cloning": {
          "enabled": true,
          "min_sample_duration": 5,
          "max_sample_duration": 15,
          "sample_rate": 24000
        },
        "dialogue_generation": {
          "enabled": true,
          "max_speakers": 4,
          "speaker_tags": ["[S1]", "[S2]", "[S3]", "[S4]"]
        },
        "adhd_optimization": {
          "enabled": true,
          "modes": ["calm", "excited", "focused", "overwhelmed", "motivated"]
        }
      },
      "audio_formats": ["wav", "mp3", "flac"],
      "max_text_length": 5000,
      "real_time_factor": 2.0
    }

Voice Profile Endpoints
-----------------------

POST /voice-profiles/create
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Create a new voice profile from audio sample.

**Request**: Multipart form data

**Form Fields**:

.. list-table::
   :header-rows: 1
   :widths: 20 15 15 50

   * - Field
     - Type
     - Required
     - Description
   * - name
     - string
     - Yes
     - Voice profile name (1-50 characters)
   * - user_id
     - string
     - Yes
     - User identifier
   * - description
     - string
     - No
     - Optional description (max 200 characters)
   * - audio_file
     - file
     - Yes
     - Audio sample (5-15 seconds, WAV/MP3/FLAC)

**Response**:

.. code-block:: json

    {
      "profile_id": "user_123_my_voice",
      "name": "My Voice",
      "user_id": "user_123",
      "duration": 8.5,
      "quality_score": 0.85,
      "status": "ready",
      "created_at": **********
    }

**Example**::

    curl -X POST "https://speechbot.autism.localhost:8090/api/v1/voice-profiles/create" \
         -H "Authorization: Bearer YOUR_TOKEN" \
         -F "name=My Calm Voice" \
         -F "user_id=user_123" \
         -F "description=Recorded when feeling relaxed" \
         -F "audio_file=@my_voice_sample.wav"

GET /voice-profiles/list/{user_id}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

List all voice profiles for a user.

**Response**:

.. code-block:: json

    [
      {
        "profile_id": "user_123_my_voice",
        "name": "My Voice",
        "user_id": "user_123",
        "duration": 8.5,
        "quality_score": 0.85,
        "status": "ready",
        "created_at": **********
      }
    ]

GET /voice-profiles/{profile_id}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Get details of a specific voice profile.

**Response**:

.. code-block:: json

    {
      "profile_id": "user_123_my_voice",
      "name": "My Voice",
      "user_id": "user_123",
      "duration": 8.5,
      "quality_score": 0.85,
      "status": "ready",
      "created_at": **********
    }

POST /voice-profiles/{profile_id}/test
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Generate test audio using a voice profile.

**Request**: Form data

**Form Fields**:

.. list-table::
   :header-rows: 1
   :widths: 20 15 15 50

   * - Field
     - Type
     - Required
     - Description
   * - test_text
     - string
     - No
     - Text to synthesize (default provided)
   * - adhd_mode
     - string
     - No
     - ADHD mode for testing (default: "calm")

**Response**: Audio file (WAV format)

DELETE /voice-profiles/{profile_id}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Delete a voice profile.

**Response**:

.. code-block:: json

    {
      "success": true,
      "message": "Voice profile user_123_my_voice deleted successfully"
    }

GET /voice-profiles/requirements/audio
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Get audio requirements for voice cloning.

**Response**:

.. code-block:: json

    {
      "duration": {
        "minimum": 5,
        "maximum": 15,
        "recommended": 10
      },
      "format": {
        "supported": ["wav", "mp3", "flac", "ogg"],
        "recommended": "wav"
      },
      "quality": {
        "sample_rate": {
          "minimum": 16000,
          "recommended": 24000,
          "maximum": 48000
        }
      },
      "tips": [
        "Record in a quiet environment",
        "Speak clearly and naturally",
        "Use your normal speaking voice"
      ]
    }

Dialogue Generation Endpoints
-----------------------------

POST /dialogue/generate
~~~~~~~~~~~~~~~~~~~~~~~~

Generate multi-speaker dialogue.

**Request Body**:

.. code-block:: json

    {
      "dialogue": [
        {
          "speaker": "[S1]",
          "text": "I'm feeling overwhelmed with this project.",
          "emotion": "stressed",
          "pause_before": 0.5
        },
        {
          "speaker": "[S2]",
          "text": "That's completely understandable. Let's break it down together.",
          "emotion": "supportive",
          "pause_before": 1.0
        }
      ],
      "voice_profiles": {
        "[S1]": "user_voice",
        "[S2]": "supportive_companion"
      },
      "adhd_mode": "supportive",
      "format": "wav"
    }

**Parameters**:

.. list-table::
   :header-rows: 1
   :widths: 20 15 15 50

   * - Parameter
     - Type
     - Required
     - Description
   * - dialogue
     - array
     - Yes
     - List of dialogue turns (1-20 turns)
   * - voice_profiles
     - object
     - No
     - Speaker ID to voice profile mapping
   * - adhd_mode
     - string
     - No
     - ADHD optimization mode
   * - scenario
     - string
     - No
     - Dialogue scenario type
   * - format
     - string
     - No
     - Audio format (default: "wav")

**Dialogue Turn Object**:

.. list-table::
   :header-rows: 1
   :widths: 20 15 15 50

   * - Field
     - Type
     - Required
     - Description
   * - speaker
     - string
     - Yes
     - Speaker ID ([S1], [S2], [S3], [S4])
   * - text
     - string
     - Yes
     - Text for this turn (1-1000 characters)
   * - emotion
     - string
     - No
     - Emotional tone for this turn
   * - pause_before
     - number
     - No
     - Pause before turn in seconds (0-5)

**Response**: Audio file with dialogue metadata headers

POST /dialogue/body-doubling
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Generate a body doubling session.

**Request Body**:

.. code-block:: json

    {
      "user_task": "Writing project proposal",
      "session_duration": 25,
      "encouragement_frequency": "medium",
      "user_voice_profile": "my_voice",
      "companion_voice_profile": "supportive_coach"
    }

**Parameters**:

.. list-table::
   :header-rows: 1
   :widths: 25 15 15 45

   * - Parameter
     - Type
     - Required
     - Description
   * - user_task
     - string
     - Yes
     - Task description (max 200 characters)
   * - session_duration
     - integer
     - No
     - Duration in minutes (5-120, default: 25)
   * - encouragement_frequency
     - string
     - No
     - low/medium/high (default: "medium")
   * - user_voice_profile
     - string
     - No
     - User's voice profile
   * - companion_voice_profile
     - string
     - No
     - Companion voice (default: "default")

**Response**: Audio file with body doubling session

GET /dialogue/templates
~~~~~~~~~~~~~~~~~~~~~~~

Get pre-built conversation templates.

**Response**:

.. code-block:: json

    [
      {
        "name": "Body Doubling - Work Session",
        "description": "Virtual companion for focused work sessions",
        "speakers": ["[S1]", "[S2]"],
        "turns": [
          {
            "speaker": "[S2]",
            "text": "Hi! Ready to work together on your task?"
          }
        ],
        "adhd_benefits": [
          "Provides accountability",
          "Reduces isolation"
        ]
      }
    ]

GET /dialogue/scenarios
~~~~~~~~~~~~~~~~~~~~~~~

Get available dialogue scenarios.

**Response**:

.. code-block:: json

    {
      "scenarios": {
        "body_doubling": {
          "name": "Body Doubling",
          "description": "Virtual companion for work sessions",
          "typical_duration": "25-90 minutes",
          "speakers": 2,
          "adhd_benefits": ["Accountability", "Reduced isolation"]
        }
      },
      "total_count": 4,
      "adhd_optimized": true
    }

Health and Status Endpoints
---------------------------

GET /health
~~~~~~~~~~~

Check TTS engine health.

**Response**:

.. code-block:: json

    {
      "status": "healthy",
      "engine": {
        "status": "healthy",
        "model_loaded": true,
        "device": "cuda",
        "voice_profiles_count": 15,
        "generation_count": 1247,
        "avg_generation_time": 2.3
      },
      "timestamp": **********.789
    }

Error Handling
--------------

**Error Response Format**:

.. code-block:: json

    {
      "error": "Validation Error",
      "detail": "Text length exceeds maximum of 5000 characters",
      "type": "ValidationError",
      "timestamp": **********.789
    }

**Common HTTP Status Codes**:

.. list-table::
   :header-rows: 1
   :widths: 15 25 60

   * - Code
     - Status
     - Description
   * - 200
     - OK
     - Request successful
   * - 400
     - Bad Request
     - Invalid request parameters
   * - 401
     - Unauthorized
     - Missing or invalid authentication
   * - 404
     - Not Found
     - Resource not found
   * - 413
     - Payload Too Large
     - File or text too large
   * - 429
     - Too Many Requests
     - Rate limit exceeded
   * - 500
     - Internal Server Error
     - Server processing error
   * - 503
     - Service Unavailable
     - TTS engine not available

**ADHD-Specific Error Messages**:

Speechbot provides ADHD-friendly error messages:

.. code-block:: json

    {
      "error": "Voice Profile Creation Failed",
      "detail": "Your audio sample is a bit too short. Try recording for 8-12 seconds - you've got this!",
      "helpful_tip": "Remember, it doesn't need to be perfect. Natural speech works best.",
      "retry_suggestion": "Take a deep breath and try again when you're ready."
    }

SDK and Integration Examples
----------------------------

**Python SDK Example**::

    import requests
    import json

    class SpeechbotClient:
        def __init__(self, token, base_url="https://speechbot.autism.localhost:8090/api/v1"):
            self.token = token
            self.base_url = base_url
            self.headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
        
        def synthesize_speech(self, text, voice_profile="default", adhd_mode="calm"):
            """Generate ADHD-optimized speech synthesis."""
            data = {
                "text": text,
                "voice_profile": voice_profile,
                "adhd_mode": adhd_mode,
                "include_nonverbals": True
            }
            
            response = requests.post(
                f"{self.base_url}/tts/synthesize",
                headers=self.headers,
                json=data
            )
            
            if response.status_code == 200:
                return response.content  # Audio data
            else:
                raise Exception(f"TTS failed: {response.json()}")
        
        def create_voice_profile(self, name, audio_file_path, user_id):
            """Create a voice profile from audio sample."""
            files = {"audio_file": open(audio_file_path, "rb")}
            data = {"name": name, "user_id": user_id}
            
            # Remove Content-Type for multipart
            headers = {"Authorization": f"Bearer {self.token}"}
            
            response = requests.post(
                f"{self.base_url}/voice-profiles/create",
                headers=headers,
                files=files,
                data=data
            )
            
            return response.json()

    # Usage
    client = SpeechbotClient("your_jwt_token")
    
    # Generate motivational message
    audio = client.synthesize_speech(
        "You're doing amazing work today! Keep up the great progress!",
        voice_profile="my_voice",
        adhd_mode="motivated"
    )
    
    with open("motivation.wav", "wb") as f:
        f.write(audio)

**JavaScript/Node.js Example**::

    const axios = require('axios');
    const FormData = require('form-data');
    const fs = require('fs');

    class SpeechbotClient {
        constructor(token, baseUrl = 'https://speechbot.autism.localhost:8090/api/v1') {
            this.token = token;
            this.baseUrl = baseUrl;
            this.headers = {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };
        }

        async synthesizeSpeech(text, voiceProfile = 'default', adhdMode = 'calm') {
            const data = {
                text: text,
                voice_profile: voiceProfile,
                adhd_mode: adhdMode,
                include_nonverbals: true
            };

            try {
                const response = await axios.post(
                    `${this.baseUrl}/tts/synthesize`,
                    data,
                    { 
                        headers: this.headers,
                        responseType: 'arraybuffer'
                    }
                );
                return response.data;
            } catch (error) {
                throw new Error(`TTS failed: ${error.response.data}`);
            }
        }

        async generateBodyDoublingSession(task, duration = 25) {
            const data = {
                user_task: task,
                session_duration: duration,
                encouragement_frequency: 'medium'
            };

            const response = await axios.post(
                `${this.baseUrl}/dialogue/body-doubling`,
                data,
                { 
                    headers: this.headers,
                    responseType: 'arraybuffer'
                }
            );

            return response.data;
        }
    }

    // Usage
    const client = new SpeechbotClient('your_jwt_token');

    // Generate body doubling session
    client.generateBodyDoublingSession('Writing project proposal', 30)
        .then(audio => {
            fs.writeFileSync('body_doubling_session.wav', audio);
            console.log('Body doubling session generated!');
        })
        .catch(error => {
            console.error('Error:', error.message);
        });

WebSocket API (Real-time)
-------------------------

For real-time dialogue and interactive sessions:

**Connection**::

    wss://speechbot.autism.localhost:8090/ws/dialogue?token=YOUR_JWT_TOKEN

**Message Format**:

.. code-block:: json

    {
      "type": "dialogue_turn",
      "speaker": "[S1]",
      "text": "I need some encouragement right now",
      "adhd_mode": "overwhelmed"
    }

**Response**:

.. code-block:: json

    {
      "type": "audio_response",
      "speaker": "[S2]",
      "audio_data": "base64_encoded_audio",
      "duration": 3.2,
      "text": "I understand you're having a tough moment. You're stronger than you know."
    }

This completes the comprehensive API reference for Speechbot's ADHD-optimized voice synthesis platform.

.. seealso::

   :doc:`getting-started`
      Learn how to use these APIs through the web interface

   :doc:`voice-cloning`
      Understand voice profile creation for API integration

   :doc:`dialogue-generation`
      Learn dialogue scripting for API-driven conversations
