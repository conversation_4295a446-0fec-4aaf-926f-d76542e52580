Deployment Guide
================

This section provides comprehensive deployment documentation for Project Chronos.

.. toctree::
   :maxdepth: 2
   :caption: Deployment:

   docker
   kubernetes
   monitoring
   security

Deployment Overview
------------------

Project Chronos supports multiple deployment strategies:

- **Development**: Docker Compose for local development
- **Staging**: Docker Compose with production-like configuration
- **Production**: Kubernetes with high availability and auto-scaling

Architecture Components
----------------------

.. mermaid::

   graph TB
       subgraph "Load Balancer"
           LB[Nginx Load Balancer]
           SSL[SSL Termination]
       end
       
       subgraph "Application Tier"
           API1[FastAPI Instance 1]
           API2[FastAPI Instance 2]
           API3[FastAPI Instance N]
           WS1[WebSocket Server 1]
           WS2[WebSocket Server 2]
       end
       
       subgraph "Background Services"
           CELERY[Celery Workers]
           SCHEDULER[Celery Beat]
           MONITOR[Health Monitors]
       end
       
       subgraph "Data Tier"
           PG_PRIMARY[(PostgreSQL Primary)]
           PG_REPLICA[(PostgreSQL Replica)]
           REDIS_CLUSTER[(Redis Cluster)]
       end
       
       subgraph "External Services"
           STORAGE[Object Storage]
           MONITORING[Monitoring Stack]
           LOGGING[Logging Stack]
       end
       
       LB --> SSL
       SSL --> API1
       SSL --> API2
       SSL --> API3
       SSL --> WS1
       SSL --> WS2
       
       API1 --> PG_PRIMARY
       API2 --> PG_REPLICA
       API3 --> PG_REPLICA
       WS1 --> REDIS_CLUSTER
       WS2 --> REDIS_CLUSTER
       
       CELERY --> PG_PRIMARY
       CELERY --> REDIS_CLUSTER
       SCHEDULER --> REDIS_CLUSTER
       
       API1 --> STORAGE
       API2 --> MONITORING
       API3 --> LOGGING

Docker Deployment
-----------------

Development Environment
~~~~~~~~~~~~~~~~~~~~~~

**docker-compose.yml**:

.. code-block:: yaml

   version: '3.8'
   
   services:
     api:
       build: .
       ports:
         - "8000:8000"
       environment:
         - DATABASE_URL=postgresql+asyncpg://chronos:chronos_password@postgres:5432/chronos
         - REDIS_URL=redis://redis:6379
         - SECRET_KEY=dev-secret-key
       depends_on:
         - postgres
         - redis
       volumes:
         - ./chronos:/app/chronos
       command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
   
     postgres:
       image: postgres:15
       environment:
         - POSTGRES_USER=chronos
         - POSTGRES_PASSWORD=chronos_password
         - POSTGRES_DB=chronos
       ports:
         - "5432:5432"
       volumes:
         - postgres_data:/var/lib/postgresql/data
   
     redis:
       image: redis:7
       ports:
         - "6379:6379"
       volumes:
         - redis_data:/data
   
     celery:
       build: .
       environment:
         - DATABASE_URL=postgresql+asyncpg://chronos:chronos_password@postgres:5432/chronos
         - REDIS_URL=redis://redis:6379
       depends_on:
         - postgres
         - redis
       command: celery -A app.core.celery worker --loglevel=info
   
   volumes:
     postgres_data:
     redis_data:

Production Environment
~~~~~~~~~~~~~~~~~~~~~

**docker-compose.prod.yml**:

.. code-block:: yaml

   version: '3.8'
   
   services:
     nginx:
       image: nginx:alpine
       ports:
         - "80:80"
         - "443:443"
       volumes:
         - ./nginx.conf:/etc/nginx/nginx.conf
         - ./ssl:/etc/nginx/ssl
       depends_on:
         - api
   
     api:
       build:
         context: .
         dockerfile: Dockerfile.prod
       environment:
         - DATABASE_URL=${DATABASE_URL}
         - REDIS_URL=${REDIS_URL}
         - SECRET_KEY=${SECRET_KEY}
       deploy:
         replicas: 3
         resources:
           limits:
             memory: 512M
           reservations:
             memory: 256M
   
     postgres:
       image: postgres:15
       environment:
         - POSTGRES_USER=${POSTGRES_USER}
         - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
         - POSTGRES_DB=${POSTGRES_DB}
       volumes:
         - postgres_data:/var/lib/postgresql/data
         - ./postgresql.conf:/etc/postgresql/postgresql.conf
       deploy:
         resources:
           limits:
             memory: 2G
           reservations:
             memory: 1G
   
     redis:
       image: redis:7
       command: redis-server --appendonly yes
       volumes:
         - redis_data:/data
       deploy:
         resources:
           limits:
             memory: 512M
           reservations:
             memory: 256M

Kubernetes Deployment
---------------------

Namespace Configuration
~~~~~~~~~~~~~~~~~~~~~~

**namespace.yaml**:

.. code-block:: yaml

   apiVersion: v1
   kind: Namespace
   metadata:
     name: chronos
     labels:
       name: chronos

ConfigMap and Secrets
~~~~~~~~~~~~~~~~~~~~

**configmap.yaml**:

.. code-block:: yaml

   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: chronos-config
     namespace: chronos
   data:
     POSTGRES_DB: "chronos"
     REDIS_URL: "redis://redis-service:6379"
     LOG_LEVEL: "INFO"
     ENVIRONMENT: "production"

**secrets.yaml**:

.. code-block:: yaml

   apiVersion: v1
   kind: Secret
   metadata:
     name: chronos-secrets
     namespace: chronos
   type: Opaque
   data:
     DATABASE_URL: <base64-encoded-database-url>
     SECRET_KEY: <base64-encoded-secret-key>
     POSTGRES_PASSWORD: <base64-encoded-password>

Application Deployment
~~~~~~~~~~~~~~~~~~~~~

**api-deployment.yaml**:

.. code-block:: yaml

   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: chronos-api
     namespace: chronos
   spec:
     replicas: 3
     selector:
       matchLabels:
         app: chronos-api
     template:
       metadata:
         labels:
           app: chronos-api
       spec:
         containers:
         - name: api
           image: chronos/api:latest
           ports:
           - containerPort: 8000
           env:
           - name: DATABASE_URL
             valueFrom:
               secretKeyRef:
                 name: chronos-secrets
                 key: DATABASE_URL
           - name: SECRET_KEY
             valueFrom:
               secretKeyRef:
                 name: chronos-secrets
                 key: SECRET_KEY
           envFrom:
           - configMapRef:
               name: chronos-config
           resources:
             requests:
               memory: "256Mi"
               cpu: "250m"
             limits:
               memory: "512Mi"
               cpu: "500m"
           livenessProbe:
             httpGet:
               path: /health
               port: 8000
             initialDelaySeconds: 30
             periodSeconds: 10
           readinessProbe:
             httpGet:
               path: /ready
               port: 8000
             initialDelaySeconds: 5
             periodSeconds: 5

**websocket-deployment.yaml**:

.. code-block:: yaml

   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: chronos-websocket
     namespace: chronos
   spec:
     replicas: 2
     selector:
       matchLabels:
         app: chronos-websocket
     template:
       metadata:
         labels:
           app: chronos-websocket
       spec:
         containers:
         - name: websocket
           image: chronos/websocket:latest
           ports:
           - containerPort: 8001
           env:
           - name: REDIS_URL
             valueFrom:
               configMapKeyRef:
                 name: chronos-config
                 key: REDIS_URL
           resources:
             requests:
               memory: "128Mi"
               cpu: "100m"
             limits:
               memory: "256Mi"
               cpu: "200m"

Services and Ingress
~~~~~~~~~~~~~~~~~~~

**services.yaml**:

.. code-block:: yaml

   apiVersion: v1
   kind: Service
   metadata:
     name: chronos-api-service
     namespace: chronos
   spec:
     selector:
       app: chronos-api
     ports:
     - port: 80
       targetPort: 8000
     type: ClusterIP
   
   ---
   apiVersion: v1
   kind: Service
   metadata:
     name: chronos-websocket-service
     namespace: chronos
   spec:
     selector:
       app: chronos-websocket
     ports:
     - port: 80
       targetPort: 8001
     type: ClusterIP

**ingress.yaml**:

.. code-block:: yaml

   apiVersion: networking.k8s.io/v1
   kind: Ingress
   metadata:
     name: chronos-ingress
     namespace: chronos
     annotations:
       kubernetes.io/ingress.class: nginx
       cert-manager.io/cluster-issuer: letsencrypt-prod
       nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
       nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
   spec:
     tls:
     - hosts:
       - api.chronos.dev
       secretName: chronos-tls
     rules:
     - host: api.chronos.dev
       http:
         paths:
         - path: /ws
           pathType: Prefix
           backend:
             service:
               name: chronos-websocket-service
               port:
                 number: 80
         - path: /
           pathType: Prefix
           backend:
             service:
               name: chronos-api-service
               port:
                 number: 80

Database Deployment
~~~~~~~~~~~~~~~~~~

**postgres-deployment.yaml**:

.. code-block:: yaml

   apiVersion: apps/v1
   kind: StatefulSet
   metadata:
     name: postgres
     namespace: chronos
   spec:
     serviceName: postgres-service
     replicas: 1
     selector:
       matchLabels:
         app: postgres
     template:
       metadata:
         labels:
           app: postgres
       spec:
         containers:
         - name: postgres
           image: postgres:15
           env:
           - name: POSTGRES_DB
             valueFrom:
               configMapKeyRef:
                 name: chronos-config
                 key: POSTGRES_DB
           - name: POSTGRES_PASSWORD
             valueFrom:
               secretKeyRef:
                 name: chronos-secrets
                 key: POSTGRES_PASSWORD
           ports:
           - containerPort: 5432
           volumeMounts:
           - name: postgres-storage
             mountPath: /var/lib/postgresql/data
           resources:
             requests:
               memory: "1Gi"
               cpu: "500m"
             limits:
               memory: "2Gi"
               cpu: "1000m"
     volumeClaimTemplates:
     - metadata:
         name: postgres-storage
       spec:
         accessModes: ["ReadWriteOnce"]
         resources:
           requests:
             storage: 20Gi

Monitoring and Logging
---------------------

Prometheus Configuration
~~~~~~~~~~~~~~~~~~~~~~~

**prometheus-config.yaml**:

.. code-block:: yaml

   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: prometheus-config
     namespace: chronos
   data:
     prometheus.yml: |
       global:
         scrape_interval: 15s
       scrape_configs:
       - job_name: 'chronos-api'
         static_configs:
         - targets: ['chronos-api-service:80']
         metrics_path: /metrics
       - job_name: 'chronos-websocket'
         static_configs:
         - targets: ['chronos-websocket-service:80']
         metrics_path: /metrics

Health Checks
~~~~~~~~~~~~

The application includes comprehensive health checks:

**Health Check Endpoints**:

- ``/health``: Basic health check
- ``/ready``: Readiness check (database connectivity)
- ``/metrics``: Prometheus metrics

**Health Check Implementation**:

.. code-block:: python

   @app.get("/health")
   async def health_check():
       return {"status": "healthy", "timestamp": datetime.utcnow()}
   
   @app.get("/ready")
   async def readiness_check(db: AsyncSession = Depends(get_db)):
       try:
           await db.execute(text("SELECT 1"))
           return {"status": "ready", "database": "connected"}
       except Exception as e:
           raise HTTPException(status_code=503, detail="Database not ready")

Security Configuration
---------------------

SSL/TLS Configuration
~~~~~~~~~~~~~~~~~~~

**nginx-ssl.conf**:

.. code-block:: nginx

   server {
       listen 443 ssl http2;
       server_name api.chronos.dev;
       
       ssl_certificate /etc/nginx/ssl/cert.pem;
       ssl_certificate_key /etc/nginx/ssl/key.pem;
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
       
       location / {
           proxy_pass http://chronos-api-service;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
       
       location /ws {
           proxy_pass http://chronos-websocket-service;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
           proxy_set_header Host $host;
           proxy_read_timeout 86400;
       }
   }

Network Policies
~~~~~~~~~~~~~~~

**network-policy.yaml**:

.. code-block:: yaml

   apiVersion: networking.k8s.io/v1
   kind: NetworkPolicy
   metadata:
     name: chronos-network-policy
     namespace: chronos
   spec:
     podSelector: {}
     policyTypes:
     - Ingress
     - Egress
     ingress:
     - from:
       - namespaceSelector:
           matchLabels:
             name: ingress-nginx
     egress:
     - to:
       - namespaceSelector:
           matchLabels:
             name: kube-system
     - to: []
       ports:
       - protocol: TCP
         port: 5432
       - protocol: TCP
         port: 6379

Backup and Recovery
------------------

Database Backup
~~~~~~~~~~~~~~

**backup-cronjob.yaml**:

.. code-block:: yaml

   apiVersion: batch/v1
   kind: CronJob
   metadata:
     name: postgres-backup
     namespace: chronos
   spec:
     schedule: "0 2 * * *"  # Daily at 2 AM
     jobTemplate:
       spec:
         template:
           spec:
             containers:
             - name: postgres-backup
               image: postgres:15
               env:
               - name: PGPASSWORD
                 valueFrom:
                   secretKeyRef:
                     name: chronos-secrets
                     key: POSTGRES_PASSWORD
               command:
               - /bin/bash
               - -c
               - |
                 pg_dump -h postgres-service -U chronos chronos | \
                 gzip > /backup/chronos-$(date +%Y%m%d-%H%M%S).sql.gz
               volumeMounts:
               - name: backup-storage
                 mountPath: /backup
             restartPolicy: OnFailure
             volumes:
             - name: backup-storage
               persistentVolumeClaim:
                 claimName: backup-pvc

This deployment guide provides comprehensive instructions for deploying Project Chronos in various environments, from development to production-ready Kubernetes clusters.
