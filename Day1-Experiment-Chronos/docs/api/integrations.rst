Integration API Reference
=========================

.. currentmodule:: app.api.v1.integrations

The Integration API provides comprehensive management of external service connections with ADHD-optimized features. All endpoints are designed with neurodivergent users in mind, offering gentle error handling, clear progress tracking, and intelligent automation.

.. note::
   All integration endpoints require authentication and automatically scope operations to the authenticated user for security and privacy.

Base URL
--------

All integration endpoints are available under::

    /api/v1/integrations

Authentication
--------------

All endpoints require a valid JWT token in the Authorization header::

    Authorization: Bearer <your_jwt_token>

Integration Management
---------------------

List User Integrations
~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/integrations

   Get all integrations configured by the authenticated user.

   **Query Parameters:**

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Parameter
        - Type
        - Description
      * - integration_type
        - string
        - Filter by integration type (google_calendar, todoist, slack, notion)
      * - status
        - string
        - Filter by status (active, inactive, error, expired, revoked)

   **Example Request:**

   .. code-block:: http

      GET /api/v1/integrations?integration_type=google_calendar&status=active HTTP/1.1
      Host: api.chronos.app
      Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "id": "550e8400-e29b-41d4-a716-************",
            "user_id": "123e4567-e89b-12d3-a456-426614174000",
            "integration_type": "google_calendar",
            "name": "My Work Calendar",
            "description": "Primary calendar for work events",
            "status": "active",
            "last_sync_at": "2024-01-15T10:30:00Z",
            "total_syncs": 45,
            "successful_syncs": 43,
            "success_rate": 0.956,
            "is_token_expired": false,
            "needs_refresh": false,
            "config": {
              "calendar_ids": ["primary"],
              "buffer_time": 5,
              "energy_detection": true
            },
            "sync_settings": {
              "import_events": true,
              "export_events": true,
              "respect_focus_mode": true,
              "quiet_hours": {
                "start": "22:00",
                "end": "07:00"
              }
            },
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-15T10:30:00Z"
          }
        ],
        "meta": {
          "timestamp": "2024-01-15T10:30:00Z",
          "request_id": "req_123456789"
        }
      }

Create Integration
~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/integrations

   Create a new external service integration.

   **Request Body:**

   .. code-block:: json

      {
        "integration_type": "google_calendar",
        "name": "My Personal Calendar",
        "description": "Personal events and appointments",
        "external_id": "<EMAIL>",
        "config": {
          "calendar_ids": ["primary"],
          "sync_frequency": "real_time",
          "buffer_time": 5,
          "energy_detection": true
        },
        "sync_settings": {
          "import_events": true,
          "export_events": false,
          "respect_focus_mode": true,
          "quiet_hours": {
            "start": "22:00",
            "end": "07:00"
          },
          "adhd_optimizations": {
            "add_buffer_time": true,
            "detect_focus_sessions": true,
            "energy_categorization": true
          }
        }
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "id": "550e8400-e29b-41d4-a716-446655440001",
          "integration_type": "google_calendar",
          "name": "My Personal Calendar",
          "status": "active",
          "message": "Integration created successfully. Complete OAuth authentication to begin syncing."
        }
      }

Get Integration Details
~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/integrations/{integration_id}

   Get detailed information about a specific integration.

   **Path Parameters:**

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Parameter
        - Type
        - Description
      * - integration_id
        - UUID
        - Unique identifier of the integration

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "id": "550e8400-e29b-41d4-a716-************",
          "integration_type": "google_calendar",
          "name": "My Work Calendar",
          "status": "active",
          "last_sync_at": "2024-01-15T10:30:00Z",
          "last_error": null,
          "error_count": 0,
          "total_syncs": 45,
          "successful_syncs": 43,
          "success_rate": 0.956,
          "token_expires_at": "2024-02-15T10:30:00Z",
          "is_token_expired": false,
          "needs_refresh": false,
          "config": {
            "calendar_ids": ["primary", "<EMAIL>"],
            "buffer_time": 5,
            "energy_detection": true
          }
        }
      }

Update Integration
~~~~~~~~~~~~~~~~~

.. http:put:: /api/v1/integrations/{integration_id}

   Update an existing integration's configuration.

   **Request Body:**

   .. code-block:: json

      {
        "name": "Updated Calendar Name",
        "description": "Updated description",
        "config": {
          "buffer_time": 10,
          "energy_detection": true,
          "focus_session_detection": true
        },
        "sync_settings": {
          "respect_focus_mode": true,
          "quiet_hours": {
            "start": "23:00",
            "end": "06:00"
          }
        }
      }

Delete Integration
~~~~~~~~~~~~~~~~~

.. http:delete:: /api/v1/integrations/{integration_id}

   Delete an integration and all associated data.

   **Example Response:**

   .. code-block:: json

      {
        "message": "Integration deleted successfully",
        "data": {
          "deleted_integration_id": "550e8400-e29b-41d4-a716-************",
          "cleanup_completed": true
        }
      }

Synchronization Operations
-------------------------

Start Sync Operation
~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/integrations/{integration_id}/sync

   Start a synchronization operation with ADHD-optimized processing.

   **Request Body:**

   .. code-block:: json

      {
        "operation_type": "import_calendar_events",
        "force": false,
        "dry_run": false,
        "filters": {
          "start_date": "2024-01-15",
          "end_date": "2024-02-15",
          "include_focus_time": true,
          "add_buffer_time": true,
          "energy_categorization": true
        }
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "sync_id": "sync_123456789",
          "status": "pending",
          "message": "✨ Sync operation started! We're importing your calendar events with ADHD optimizations.",
          "estimated_duration": 30,
          "features_enabled": [
            "Energy level detection",
            "Buffer time addition",
            "Focus session recognition",
            "Gentle conflict resolution"
          ]
        }
      }

Get Sync Logs
~~~~~~~~~~~~~

.. http:get:: /api/v1/integrations/{integration_id}/sync-logs

   Get synchronization history for an integration.

   **Query Parameters:**

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Parameter
        - Type
        - Description
      * - operation_type
        - string
        - Filter by operation type
      * - status
        - string
        - Filter by sync status
      * - limit
        - integer
        - Maximum logs to return (1-100, default: 50)

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "id": "sync_123456789",
            "integration_id": "550e8400-e29b-41d4-a716-************",
            "operation_type": "import_calendar_events",
            "status": "completed",
            "started_at": "2024-01-15T10:00:00Z",
            "completed_at": "2024-01-15T10:02:30Z",
            "duration_seconds": 150.5,
            "items_processed": 25,
            "items_created": 20,
            "items_updated": 5,
            "items_deleted": 0,
            "conflicts_detected": 2,
            "error_message": null,
            "adhd_features_applied": {
              "energy_categorization": 25,
              "buffer_times_added": 20,
              "focus_sessions_detected": 3,
              "conflicts_resolved": 2
            }
          }
        ]
      }

Health Monitoring
----------------

Check Integration Health
~~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/integrations/{integration_id}/health

   Get comprehensive health status of an integration.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "integration_id": "550e8400-e29b-41d4-a716-************",
          "integration_type": "google_calendar",
          "status": "active",
          "is_healthy": true,
          "last_successful_sync": "2024-01-15T10:30:00Z",
          "error_count": 0,
          "token_status": "valid",
          "webhook_status": "active",
          "recommendations": [],
          "health_score": 95,
          "performance_metrics": {
            "average_sync_duration": 45.2,
            "success_rate_7_days": 0.98,
            "last_24h_syncs": 12,
            "data_quality_score": 92
          }
        }
      }

Get Integration Statistics
~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/integrations/stats/summary

   Get overview statistics for all user integrations.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "total_integrations": 4,
          "active_integrations": 3,
          "failed_integrations": 0,
          "total_syncs_today": 15,
          "successful_syncs_today": 14,
          "average_sync_duration": 42.5,
          "most_active_integration": "google_calendar",
          "recent_errors": [],
          "adhd_features_usage": {
            "energy_categorization": 89,
            "buffer_time_addition": 76,
            "focus_session_detection": 23,
            "conflict_resolution": 8
          },
          "user_satisfaction_indicators": {
            "sync_reliability": 0.93,
            "error_recovery_rate": 1.0,
            "feature_adoption": 0.85
          }
        }
      }

OAuth Authentication
-------------------

Initiate OAuth Flow
~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/integrations/oauth/initiate

   Start OAuth authentication flow for an external service.

   **Request Body:**

   .. code-block:: json

      {
        "integration_type": "google_calendar",
        "redirect_uri": "https://app.chronos.com/oauth/callback",
        "scopes": [
          "https://www.googleapis.com/auth/calendar",
          "https://www.googleapis.com/auth/calendar.events"
        ],
        "state": "user_generated_state_parameter"
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "authorization_url": "https://accounts.google.com/o/oauth2/v2/auth?client_id=...&scope=...&response_type=code&state=...",
          "state": "user_generated_state_parameter",
          "expires_in": 600,
          "instructions": {
            "step_1": "Click the authorization URL to grant permissions",
            "step_2": "Complete the OAuth flow on the external service",
            "step_3": "You'll be redirected back to Chronos automatically",
            "adhd_tip": "💡 This process is secure and only takes a minute. You're doing great!"
          }
        }
      }

Handle OAuth Callback
~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/integrations/oauth/callback/{integration_type}

   Process OAuth callback and complete authentication.

   **Path Parameters:**

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Parameter
        - Type
        - Description
      * - integration_type
        - string
        - Type of integration (google_calendar, todoist, etc.)

   **Request Body:**

   .. code-block:: json

      {
        "code": "oauth_authorization_code_from_external_service",
        "state": "user_generated_state_parameter",
        "error": null,
        "error_description": null
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "message": "🎉 OAuth authentication completed successfully!",
          "integration_type": "google_calendar",
          "status": "connected",
          "next_steps": [
            "Your calendar integration is now active",
            "We'll start syncing your events with ADHD optimizations",
            "Check your integration dashboard for sync status"
          ],
          "initial_sync_scheduled": true,
          "estimated_sync_time": "2-3 minutes"
        }
      }

Webhook Management
-----------------

Handle Incoming Webhooks
~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/integrations/webhooks/{integration_type}

   Process incoming webhook events from external services.

   **Path Parameters:**

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Parameter
        - Type
        - Description
      * - integration_type
        - string
        - Type of integration sending the webhook

   **Request Body:**

   The request body varies by integration type but typically includes:

   .. code-block:: json

      {
        "event_type": "calendar_event_updated",
        "resource_id": "external_resource_id",
        "timestamp": "2024-01-15T10:30:00Z",
        "data": {
          "event_id": "external_event_id",
          "changes": ["title", "start_time", "end_time"]
        }
      }

   **Example Response:**

   .. code-block:: json

      {
        "message": "Webhook received and queued for processing",
        "data": {
          "webhook_id": "webhook_123456789",
          "processing_status": "queued",
          "estimated_processing_time": "30 seconds",
          "adhd_features": {
            "gentle_processing": true,
            "conflict_detection": true,
            "user_notification": "optional"
          }
        }
      }

Bulk Operations
--------------

Bulk Sync Multiple Integrations
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/integrations/bulk/sync

   Start synchronization for multiple integrations simultaneously.

   **Request Body:**

   .. code-block:: json

      {
        "integration_ids": [
          "550e8400-e29b-41d4-a716-************",
          "550e8400-e29b-41d4-a716-446655440001"
        ],
        "operation_type": "bidirectional_sync",
        "force": false,
        "parallel": true,
        "max_concurrent": 3,
        "adhd_optimizations": {
          "stagger_start_times": true,
          "gentle_progress_updates": true,
          "batch_notifications": true
        }
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "batch_id": "batch_123456789",
          "total_integrations": 2,
          "started_syncs": [
            "sync_123456790",
            "sync_123456791"
          ],
          "failed_starts": [],
          "estimated_completion": "2024-01-15T10:35:00Z",
          "progress_tracking_url": "/api/v1/integrations/bulk/sync/batch_123456789/status"
        }
      }

Error Handling
-------------

All API endpoints return ADHD-friendly error responses:

**Validation Error (400):**

.. code-block:: json

   {
     "error": {
       "code": "VALIDATION_ERROR",
       "message": "The integration name is required and should be descriptive.",
       "details": {
         "field": "name",
         "provided_value": "",
         "expected_format": "Non-empty string, 1-100 characters"
       },
       "suggestions": [
         "Try something like 'My Work Calendar' or 'Personal Tasks'",
         "Use a name that helps you identify this integration easily"
       ],
       "help_url": "https://docs.chronos.app/integrations/setup"
     }
   }

**Integration Error (400):**

.. code-block:: json

   {
     "error": {
       "code": "INTEGRATION_ERROR",
       "message": "Your Google Calendar integration needs attention.",
       "details": {
         "integration_id": "550e8400-e29b-41d4-a716-************",
         "issue": "token_expired",
         "last_successful_sync": "2024-01-10T10:30:00Z"
       },
       "suggestions": [
         "Click 'Reconnect' in your integration settings",
         "This is normal and happens periodically for security",
         "Your data is safe and sync will resume after reconnecting"
       ],
       "recovery_steps": [
         {
           "step": 1,
           "action": "Go to Integration Settings",
           "url": "/integrations/550e8400-e29b-41d4-a716-************"
         },
         {
           "step": 2,
           "action": "Click 'Reconnect Integration'",
           "description": "This will start a new OAuth flow"
         },
         {
           "step": 3,
           "action": "Grant permissions again",
           "description": "Same process as initial setup"
         }
       ]
     }
   }

**Rate Limit Error (429):**

.. code-block:: json

   {
     "error": {
       "code": "RATE_LIMIT_EXCEEDED",
       "message": "You're syncing a lot today! Let's take a quick break.",
       "details": {
         "limit": 100,
         "window": "60 seconds",
         "retry_after": 45,
         "current_usage": 105
       },
       "suggestions": [
         "This helps protect both you and the external service",
         "Your sync will automatically resume in 45 seconds",
         "Consider using scheduled syncs for large operations"
       ],
       "adhd_support": {
         "message": "Taking breaks is important! This pause helps prevent overwhelm.",
         "tip": "Use this time to check in with yourself - how are you feeling?",
         "encouragement": "You're managing your productivity tools really well! 🌟"
       }
     }
   }

This comprehensive API provides all the tools needed to build ADHD-friendly applications that seamlessly integrate with external productivity services while maintaining the specialized accommodations that neurodivergent users depend on.
