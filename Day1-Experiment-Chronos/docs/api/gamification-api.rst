Gamification API Reference
==========================

This section provides comprehensive API documentation for the gamification and motivation features.

.. currentmodule:: app.api.v1.gamification

Gamification Endpoints
----------------------

Profile Management
~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/gamification/profile

   Get user's complete gamification profile including points, level, and preferences.

   **Example Request**:

   .. code-block:: http

      GET /api/v1/gamification/profile HTTP/1.1
      Host: api.chronos.app
      Authorization: Bearer <token>

   **Example Response**:

   .. code-block:: json

      {
        "id": "550e8400-e29b-41d4-a716-************",
        "user_id": "550e8400-e29b-41d4-a716-446655440001",
        "total_points": 1250,
        "level": 5,
        "points_to_next_level": 150,
        "gamification_enabled": true,
        "celebration_style": "moderate",
        "preferred_rewards": {
          "types": ["badges", "themes"],
          "frequency": "immediate"
        },
        "created_at": "2024-01-01T08:00:00Z",
        "updated_at": "2024-01-01T12:00:00Z"
      }

   :statuscode 200: Profile retrieved successfully
   :statuscode 401: Authentication required
   :statuscode 404: User not found

.. http:patch:: /api/v1/gamification/profile

   Update user's gamification preferences and settings.

   **Example Request**:

   .. code-block:: http

      PATCH /api/v1/gamification/profile HTTP/1.1
      Host: api.chronos.app
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "celebration_style": "enthusiastic",
        "preferred_rewards": {
          "types": ["badges", "points", "themes"],
          "frequency": "immediate"
        }
      }

   **Example Response**:

   .. code-block:: json

      {
        "id": "550e8400-e29b-41d4-a716-************",
        "total_points": 1250,
        "level": 5,
        "celebration_style": "enthusiastic",
        "preferred_rewards": {
          "types": ["badges", "points", "themes"],
          "frequency": "immediate"
        }
      }

   :statuscode 200: Profile updated successfully
   :statuscode 400: Invalid request data
   :statuscode 401: Authentication required

Statistics & Analytics
~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/gamification/stats

   Get comprehensive gamification statistics for the user.

   **Example Response**:

   .. code-block:: json

      {
        "total_points": 1250,
        "current_level": 5,
        "points_to_next_level": 150,
        "achievements_unlocked": 12,
        "total_achievements": 25,
        "active_streaks": [
          {
            "id": "550e8400-e29b-41d4-a716-446655440002",
            "streak_type": "daily_tasks",
            "current_streak": 7,
            "longest_streak": 14,
            "last_activity_date": "2024-01-01"
          }
        ],
        "recent_awards": [
          {
            "id": "550e8400-e29b-41d4-a716-446655440003",
            "points_awarded": 45,
            "reason": "Completed difficult task during low energy",
            "multiplier": 1.5,
            "created_at": "2024-01-01T12:00:00Z"
          }
        ],
        "level_progress_percentage": 75.5
      }

   :statuscode 200: Statistics retrieved successfully
   :statuscode 401: Authentication required

.. http:get:: /api/v1/gamification/dashboard

   Get comprehensive dashboard data including profile, stats, achievements, and suggestions.

   **Example Response**:

   .. code-block:: json

      {
        "profile": {
          "total_points": 1250,
          "level": 5,
          "points_to_next_level": 150
        },
        "stats": {
          "achievements_unlocked": 12,
          "active_streaks": [...],
          "level_progress_percentage": 75.5
        },
        "recent_achievements": [
          {
            "achievement": {
              "name": "Week Warrior",
              "description": "Complete tasks for 7 days in a row",
              "badge_icon": "🔥"
            },
            "unlocked_at": "2024-01-01T10:00:00Z"
          }
        ],
        "trending_up": ["Task completion", "Consistency"],
        "suggestions": [
          "Try completing a task during your high-energy time for bonus points",
          "You're close to unlocking the 'Productivity Pro' achievement!"
        ],
        "next_milestones": [
          {
            "type": "level",
            "description": "Reach level 6",
            "progress": 0.75
          }
        ]
      }

Points & Rewards
~~~~~~~~~~~~~~~

.. http:post:: /api/v1/gamification/points/calculate

   Calculate points with multipliers for a given scenario (useful for previewing rewards).

   **Example Request**:

   .. code-block:: json

      {
        "base_points": 30,
        "task_difficulty": "hard",
        "energy_level": "low",
        "time_of_day": "morning",
        "context": {
          "first_task_of_day": true,
          "after_break": false
        }
      }

   **Example Response**:

   .. code-block:: json

      {
        "base_points": 30,
        "final_points": 70,
        "multiplier": 2.34,
        "breakdown": {
          "base": 1.0,
          "difficulty": 1.5,
          "energy": 1.3,
          "time_of_day": 1.1,
          "first_task": 1.2
        },
        "celebration_level": "enthusiastic"
      }

   :statuscode 200: Calculation completed successfully
   :statuscode 400: Invalid request data

Achievements
~~~~~~~~~~~

.. http:get:: /api/v1/gamification/achievements

   Get user achievements with progress information.

   **Query Parameters**:
   
   - ``unlocked_only`` (boolean): Return only unlocked achievements (default: false)

   **Example Response**:

   .. code-block:: json

      {
        "achievements": [
          {
            "id": "550e8400-e29b-41d4-a716-446655440004",
            "user_id": "550e8400-e29b-41d4-a716-446655440001",
            "achievement_id": "550e8400-e29b-41d4-a716-446655440005",
            "is_unlocked": true,
            "unlocked_at": "2024-01-01T10:00:00Z",
            "progress_data": {},
            "achievement": {
              "id": "550e8400-e29b-41d4-a716-446655440005",
              "achievement_key": "first_task",
              "name": "Getting Started",
              "description": "Complete your first task",
              "category": "task_completion",
              "reward_points": 50,
              "badge_icon": "🎯",
              "is_hidden": false
            }
          }
        ]
      }

   :statuscode 200: Achievements retrieved successfully
   :statuscode 401: Authentication required

.. http:get:: /api/v1/gamification/achievements/available

   Get all available achievements that can be unlocked.

   **Query Parameters**:
   
   - ``category`` (string): Filter by achievement category (optional)

   **Example Response**:

   .. code-block:: json

      {
        "achievements": [
          {
            "id": "550e8400-e29b-41d4-a716-446655440005",
            "achievement_key": "first_task",
            "name": "Getting Started",
            "description": "Complete your first task",
            "category": "task_completion",
            "reward_points": 50,
            "badge_icon": "🎯",
            "is_hidden": false
          }
        ]
      }

Streaks
~~~~~~

.. http:get:: /api/v1/gamification/streaks

   Get user's current streaks across all streak types.

   **Example Response**:

   .. code-block:: json

      {
        "streaks": [
          {
            "id": "550e8400-e29b-41d4-a716-446655440006",
            "user_id": "550e8400-e29b-41d4-a716-446655440001",
            "streak_type": "daily_tasks",
            "current_streak": 8,
            "longest_streak": 14,
            "last_activity_date": "2024-01-01",
            "freeze_count": 0,
            "max_freezes": 3
          }
        ]
      }

   :statuscode 200: Streaks retrieved successfully
   :statuscode 401: Authentication required

.. http:post:: /api/v1/gamification/streaks/update

   Update a user's streak (typically called automatically by the system).

   **Example Request**:

   .. code-block:: json

      {
        "streak_type": "daily_tasks",
        "action_completed": true
      }

   **Example Response**:

   .. code-block:: json

      {
        "id": "550e8400-e29b-41d4-a716-446655440006",
        "streak_type": "daily_tasks",
        "current_streak": 9,
        "longest_streak": 14,
        "last_activity_date": "2024-01-01",
        "freeze_count": 0,
        "max_freezes": 3
      }

   :statuscode 200: Streak updated successfully
   :statuscode 400: Invalid request data
   :statuscode 401: Authentication required

Motivation Endpoints
--------------------

Dopamine Menu
~~~~~~~~~~~~

.. http:get:: /api/v1/motivation/dopamine-menu

   Get personalized dopamine menu activities based on current context.

   **Query Parameters**:
   
   - ``energy_level`` (string): Current energy level (low/medium/high, default: medium)
   - ``available_time`` (integer): Available time in minutes (default: 5)
   - ``context`` (string): Context for the activity (default: pre_task)
   - ``exclude_categories`` (array): Categories to exclude
   - ``preferred_categories`` (array): Preferred categories

   **Example Request**:

   .. code-block:: http

      GET /api/v1/motivation/dopamine-menu?energy_level=low&available_time=5&context=pre_task HTTP/1.1
      Host: api.chronos.app
      Authorization: Bearer <token>

   **Example Response**:

   .. code-block:: json

      {
        "activities": [
          {
            "id": "550e8400-e29b-41d4-a716-446655440007",
            "name": "5-Minute Walk",
            "description": "Take a quick walk around the block or office",
            "category": "movement",
            "duration_min": 3,
            "duration_max": 10,
            "energy_requirement": "low",
            "energy_boost": "medium",
            "requires_equipment": false,
            "requires_space": false,
            "can_do_anywhere": true,
            "tags": ["outdoor", "exercise"],
            "instructions": "Step outside and walk at a comfortable pace for 5 minutes"
          }
        ],
        "personalization_score": 0.8,
        "context": "pre_task",
        "energy_level": "low",
        "available_time": 5
      }

   :statuscode 200: Menu retrieved successfully
   :statuscode 401: Authentication required

Activity Management
~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/motivation/dopamine-menu/complete

   Record completion of a dopamine activity and track its effectiveness.

   **Example Request**:

   .. code-block:: json

      {
        "activity_id": "550e8400-e29b-41d4-a716-446655440007",
        "actual_duration": 5,
        "energy_before": "low",
        "energy_after": "medium",
        "mood_before": 3,
        "mood_after": 6,
        "satisfaction_rating": 4,
        "would_do_again": true,
        "context": "pre_task",
        "notes": "Felt refreshing and helped me focus"
      }

   **Example Response**:

   .. code-block:: json

      {
        "id": "550e8400-e29b-41d4-a716-446655440008",
        "user_id": "550e8400-e29b-41d4-a716-446655440001",
        "activity_id": "550e8400-e29b-41d4-a716-446655440007",
        "actual_duration": 5,
        "energy_before": "low",
        "energy_after": "medium",
        "satisfaction_rating": 4,
        "context": "pre_task",
        "created_at": "2024-01-01T12:00:00Z"
      }

   :statuscode 200: Activity completion recorded successfully
   :statuscode 400: Invalid request data
   :statuscode 401: Authentication required

.. http:post:: /api/v1/motivation/activities/custom

   Create a custom dopamine activity personalized for the user.

   **Example Request**:

   .. code-block:: json

      {
        "name": "Pet My Cat",
        "description": "Spend time with my cat for instant mood boost",
        "category": "social",
        "duration": 3,
        "energy_requirement": "low",
        "energy_boost": "high",
        "tags": ["pets", "comfort", "quick"]
      }

   **Example Response**:

   .. code-block:: json

      {
        "id": "550e8400-e29b-41d4-a716-446655440009",
        "user_id": "550e8400-e29b-41d4-a716-446655440001",
        "activity_id": null,
        "preference_type": "custom",
        "custom_name": "Pet My Cat",
        "custom_description": "Spend time with my cat for instant mood boost",
        "custom_category": "social",
        "rating": 5,
        "created_at": "2024-01-01T12:00:00Z"
      }

   :statuscode 200: Custom activity created successfully
   :statuscode 400: Invalid request data
   :statuscode 401: Authentication required

Analytics & Insights
~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/motivation/analytics

   Get comprehensive motivation analytics including patterns and effectiveness data.

   **Example Response**:

   .. code-block:: json

      {
        "total_activities_completed": 45,
        "favorite_categories": ["movement", "creative"],
        "most_effective_activities": [
          {
            "id": "550e8400-e29b-41d4-a716-446655440007",
            "name": "5-Minute Walk",
            "effectiveness_score": 0.9,
            "completion_count": 12
          }
        ],
        "energy_patterns": {
          "morning": "high",
          "afternoon": "medium",
          "evening": "low"
        },
        "mood_improvements": {
          "average_improvement": 2.5,
          "best_category": "movement"
        },
        "completion_rate_by_context": {
          "pre_task": 0.8,
          "break": 0.9,
          "reward": 0.95
        },
        "recommendations": [
          "Try movement activities when energy is low",
          "Creative activities work well for you in the evening"
        ]
      }

   :statuscode 200: Analytics retrieved successfully
   :statuscode 401: Authentication required

.. http:get:: /api/v1/motivation/dashboard

   Get comprehensive motivation dashboard with recent activities, suggestions, and insights.

   **Example Response**:

   .. code-block:: json

      {
        "recent_completions": [...],
        "suggested_activities": [...],
        "active_insights": [
          {
            "id": "550e8400-e29b-41d4-a716-446655440010",
            "insight_type": "pattern",
            "title": "Morning Movement Works Best",
            "description": "You're 40% more likely to complete movement activities in the morning",
            "confidence_score": 0.85
          }
        ],
        "analytics": {...},
        "personalization_tips": [
          "Try tracking your energy levels to get better activity suggestions",
          "Rate completed activities to improve recommendations"
        ]
      }

   :statuscode 200: Dashboard data retrieved successfully
   :statuscode 401: Authentication required

Error Handling
--------------

All endpoints use consistent error response format:

.. code-block:: json

   {
     "detail": "Error description",
     "error_code": "SPECIFIC_ERROR_CODE",
     "context": {
       "additional": "error context"
     }
   }

Common HTTP status codes:

- ``200 OK``: Request successful
- ``400 Bad Request``: Invalid request data
- ``401 Unauthorized``: Authentication required
- ``403 Forbidden``: Insufficient permissions
- ``404 Not Found``: Resource not found
- ``422 Unprocessable Entity``: Validation error
- ``500 Internal Server Error``: Server error

Rate Limiting
------------

API endpoints are rate limited to ensure fair usage:

- **Gamification endpoints**: 100 requests per minute per user
- **Motivation endpoints**: 50 requests per minute per user
- **Analytics endpoints**: 20 requests per minute per user

Rate limit headers are included in responses:

.. code-block:: http

   X-RateLimit-Limit: 100
   X-RateLimit-Remaining: 95
   X-RateLimit-Reset: 1640995200

Body Doubling Endpoints
----------------------

Session Management
~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/body-doubling/sessions

   Create a new virtual body doubling session.

   **Example Request**:

   .. code-block:: json

      {
        "title": "Morning Focus Session",
        "description": "Working on important projects together",
        "session_type": "work",
        "max_participants": 4,
        "is_public": true,
        "scheduled_start": "2024-01-01T09:00:00Z",
        "scheduled_duration": 120,
        "settings": {
          "allow_chat": true,
          "progress_sharing": true,
          "break_reminders": true
        }
      }

   **Example Response**:

   .. code-block:: json

      {
        "id": "550e8400-e29b-41d4-a716-************",
        "title": "Morning Focus Session",
        "host_user_id": "550e8400-e29b-41d4-a716-446655440001",
        "session_type": "work",
        "max_participants": 4,
        "current_participant_count": 1,
        "status": "waiting",
        "is_public": true,
        "created_at": "2024-01-01T08:00:00Z"
      }

   :statuscode 200: Session created successfully
   :statuscode 400: Invalid session data
   :statuscode 401: Authentication required

.. http:get:: /api/v1/body-doubling/sessions/discover

   Discover available body doubling sessions.

   **Query Parameters**:

   - ``session_type`` (string): Filter by session type (work, study, creative)
   - ``max_participants`` (integer): Maximum participants filter
   - ``starting_soon`` (boolean): Sessions starting within 30 minutes
   - ``has_space`` (boolean): Only sessions with available space
   - ``public_only`` (boolean): Only public sessions

   **Example Response**:

   .. code-block:: json

      {
        "sessions": [
          {
            "id": "550e8400-e29b-41d4-a716-************",
            "title": "Study Group",
            "session_type": "study",
            "current_participant_count": 2,
            "max_participants": 4,
            "scheduled_start": "2024-01-01T10:00:00Z",
            "status": "waiting"
          }
        ],
        "total_count": 1,
        "filters_applied": {
          "session_type": "study",
          "has_space": true
        }
      }

   :statuscode 200: Sessions retrieved successfully
   :statuscode 401: Authentication required

.. http:post:: /api/v1/body-doubling/sessions/{session_id}/join

   Join an existing body doubling session.

   **Example Request**:

   .. code-block:: json

      {
        "display_name": "Focus Buddy",
        "is_anonymous": false,
        "share_progress": true,
        "receive_encouragement": true
      }

   **Example Response**:

   .. code-block:: json

      {
        "session": {
          "id": "550e8400-e29b-41d4-a716-************",
          "title": "Study Group",
          "current_participant_count": 3
        },
        "participant": {
          "id": "550e8400-e29b-41d4-a716-446655440002",
          "display_name": "Focus Buddy",
          "joined_at": "2024-01-01T09:30:00Z",
          "status": "active"
        },
        "websocket_token": "ws_token_user123_session456",
        "connection_info": {
          "websocket_url": "/ws/body-doubling/550e8400-e29b-41d4-a716-************",
          "token_param": "token"
        }
      }

   :statuscode 200: Successfully joined session
   :statuscode 404: Session not found
   :statuscode 409: Session is full
   :statuscode 400: Invalid join data

.. http:post:: /api/v1/body-doubling/sessions/{session_id}/leave

   Leave a body doubling session.

   **Example Request**:

   .. code-block:: json

      {
        "reason": "Need to take a break"
      }

   **Example Response**:

   .. code-block:: json

      {
        "message": "Successfully left session",
        "session_id": "550e8400-e29b-41d4-a716-************",
        "left_at": "2024-01-01T10:30:00Z"
      }

   :statuscode 200: Successfully left session
   :statuscode 400: User not in session

Focus Session Management
~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/body-doubling/sessions/{session_id}/focus

   Start a synchronized group focus session.

   **Example Request**:

   .. code-block:: json

      {
        "focus_type": "pomodoro",
        "focus_duration": 25,
        "break_duration": 5,
        "total_cycles": 4,
        "scheduled_start": "2024-01-01T10:00:00Z"
      }

   **Example Response**:

   .. code-block:: json

      {
        "id": "550e8400-e29b-41d4-a716-446655440003",
        "body_doubling_session_id": "550e8400-e29b-41d4-a716-************",
        "focus_type": "pomodoro",
        "focus_duration": 25,
        "break_duration": 5,
        "total_cycles": 4,
        "status": "scheduled",
        "scheduled_start": "2024-01-01T10:00:00Z"
      }

   :statuscode 200: Focus session created successfully
   :statuscode 403: Permission denied
   :statuscode 404: Session not found

Communication Features
~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/body-doubling/sessions/{session_id}/encouragement

   Send encouragement to session participants.

   **Example Request**:

   .. code-block:: json

      {
        "recipient_id": "550e8400-e29b-41d4-a716-446655440004",
        "message": "You're doing great! Keep it up!",
        "message_type": "encouragement",
        "is_anonymous": false
      }

   **Example Response**:

   .. code-block:: json

      {
        "message": "Encouragement sent successfully",
        "recipients": 1,
        "timestamp": "2024-01-01T10:15:00Z"
      }

   :statuscode 200: Encouragement sent successfully
   :statuscode 400: User not in session

.. http:post:: /api/v1/body-doubling/sessions/{session_id}/progress

   Share progress update with session participants.

   **Example Request**:

   .. code-block:: json

      {
        "progress_type": "task_completion",
        "description": "Finished the first chapter",
        "completion_percentage": 0.25,
        "celebration_level": "moderate",
        "share_with_session": true
      }

   **Example Response**:

   .. code-block:: json

      {
        "message": "Progress shared successfully",
        "progress_type": "task_completion",
        "timestamp": "2024-01-01T10:20:00Z"
      }

   :statuscode 200: Progress shared successfully
   :statuscode 403: Progress sharing disabled

WebSocket Communication
~~~~~~~~~~~~~~~~~~~~~~

.. websocket:: /ws/body-doubling/{session_id}

   Real-time communication for body doubling sessions.

   **Connection Parameters**:

   - ``session_id`` (UUID): Body doubling session ID
   - ``token`` (string): Authentication token

   **Message Types**:

   **Encouragement Message**:

   .. code-block:: json

      {
        "message_type": "encouragement",
        "data": {
          "message": "You're doing amazing!",
          "message_type": "encouragement"
        }
      }

   **Progress Share**:

   .. code-block:: json

      {
        "message_type": "progress_share",
        "data": {
          "progress_type": "task_completion",
          "description": "Completed important task",
          "completion_percentage": 1.0
        }
      }

   **Focus Session Start**:

   .. code-block:: json

      {
        "message_type": "focus_start",
        "data": {
          "focus_duration": 25,
          "break_duration": 5
        }
      }

   **Incoming Message Format**:

   .. code-block:: json

      {
        "message_type": "encouragement",
        "success": true,
        "data": {
          "sender_id": "550e8400-e29b-41d4-a716-446655440001",
          "message": "Great work!",
          "timestamp": "2024-01-01T10:25:00Z"
        }
      }

Authentication Endpoints
-----------------------

User Registration
~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/auth/register

   Register a new user with ADHD-friendly process.

   **Example Request**:

   .. code-block:: json

      {
        "email": "<EMAIL>",
        "password": "securepassword123",
        "confirm_password": "securepassword123",
        "first_name": "Alex",
        "last_name": "Smith",
        "adhd_diagnosis": true,
        "preferred_chunk_size": "medium",
        "default_energy_level": "medium"
      }

   **Example Response**:

   .. code-block:: json

      {
        "message": "Registration successful. Please check your email for verification.",
        "user": {
          "id": "550e8400-e29b-41d4-a716-************",
          "email": "<EMAIL>",
          "first_name": "Alex",
          "is_active": true,
          "is_verified": false,
          "created_at": "2024-01-01T08:00:00Z"
        },
        "verification_required": true
      }

   :statuscode 200: Registration successful
   :statuscode 400: Invalid registration data
   :statuscode 409: Email already registered

.. http:post:: /api/v1/auth/login

   Authenticate user and return JWT tokens.

   **Example Request**:

   .. code-block:: json

      {
        "email": "<EMAIL>",
        "password": "securepassword123",
        "remember_me": true
      }

   **Example Response**:

   .. code-block:: json

      {
        "message": "Login successful",
        "tokens": {
          "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
          "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
          "token_type": "bearer",
          "expires_in": 900
        },
        "user": {
          "id": "550e8400-e29b-41d4-a716-************",
          "email": "<EMAIL>",
          "first_name": "Alex",
          "is_verified": true
        }
      }

   :statuscode 200: Login successful
   :statuscode 401: Invalid credentials
   :statuscode 400: Account disabled

Token Management
~~~~~~~~~~~~~~~

.. http:post:: /api/v1/auth/refresh

   Refresh access token using refresh token.

   **Example Request**:

   .. code-block:: json

      {
        "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
      }

   **Example Response**:

   .. code-block:: json

      {
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_type": "bearer",
        "expires_in": 900,
        "user": {
          "id": "550e8400-e29b-41d4-a716-************",
          "email": "<EMAIL>"
        }
      }

   :statuscode 200: Token refreshed successfully
   :statuscode 401: Invalid refresh token

.. http:post:: /api/v1/auth/logout

   Logout user and invalidate tokens.

   **Example Response**:

   .. code-block:: json

      {
        "message": "Logout successful"
      }

   :statuscode 200: Logout successful
   :statuscode 401: Authentication required

Password Management
~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/auth/forgot-password

   Initiate password reset process.

   **Example Request**:

   .. code-block:: json

      {
        "email": "<EMAIL>"
      }

   **Example Response**:

   .. code-block:: json

      {
        "message": "If an account with that email exists, password reset instructions have been sent."
      }

   :statuscode 200: Reset instructions sent (always returns success)

.. http:post:: /api/v1/auth/reset-password

   Complete password reset using token.

   **Example Request**:

   .. code-block:: json

      {
        "token": "reset_token_from_email",
        "new_password": "newsecurepassword123",
        "confirm_password": "newsecurepassword123"
      }

   **Example Response**:

   .. code-block:: json

      {
        "message": "Password reset successfully. You can now log in with your new password."
      }

   :statuscode 200: Password reset successful
   :statuscode 400: Invalid token or password

.. http:post:: /api/v1/auth/change-password

   Change password for authenticated user.

   **Example Request**:

   .. code-block:: json

      {
        "current_password": "currentpassword123",
        "new_password": "newsecurepassword123",
        "confirm_password": "newsecurepassword123"
      }

   **Example Response**:

   .. code-block:: json

      {
        "message": "Password changed successfully"
      }

   :statuscode 200: Password changed successfully
   :statuscode 400: Invalid current password
   :statuscode 401: Authentication required

User Information
~~~~~~~~~~~~~~~

.. http:get:: /api/v1/auth/me

   Get current user information.

   **Example Response**:

   .. code-block:: json

      {
        "id": "550e8400-e29b-41d4-a716-************",
        "email": "<EMAIL>",
        "first_name": "Alex",
        "last_name": "Smith",
        "is_active": true,
        "is_verified": true,
        "adhd_diagnosis": true,
        "preferred_chunk_size": "medium",
        "default_energy_level": "medium",
        "created_at": "2024-01-01T08:00:00Z",
        "updated_at": "2024-01-01T12:00:00Z"
      }

   :statuscode 200: User information retrieved
   :statuscode 401: Authentication required

.. http:get:: /api/v1/auth/status

   Get authentication status.

   **Example Response**:

   .. code-block:: json

      {
        "authenticated": true,
        "user": {
          "id": "550e8400-e29b-41d4-a716-************",
          "email": "<EMAIL>",
          "first_name": "Alex"
        },
        "token_valid": true,
        "expires_at": "2024-01-01T12:15:00Z"
      }

   :statuscode 200: Status retrieved successfully

This API reference provides complete documentation for integrating with Project Chronos's authentication, gamification, motivation, and body doubling features.
