API Documentation
=================

This section provides comprehensive documentation for all Project Chronos API endpoints.=

This section provides comprehensive API documentation for Project Chronos.

.. toctree::
   :maxdepth: 2
   :caption: API Documentation:

   rest-api
   gamification-api
   websocket-api
   schemas
   authentication
   rate-limiting
   error-handling

API Overview
------------

Project Chronos provides both REST and WebSocket APIs to support different interaction patterns:

- **REST API**: Traditional HTTP endpoints for CRUD operations
- **WebSocket API**: Real-time communication for body doubling and live updates
- **Authentication**: JWT-based authentication for both API types
- **Rate Limiting**: Comprehensive rate limiting to prevent abuse

API Base URLs
-------------

**Development**:
- REST API: ``http://localhost:8000/api/v1``
- WebSocket: ``ws://localhost:8000/ws``

**Production**:
- REST API: ``https://api.chronos.dev/api/v1``
- WebSocket: ``wss://api.chronos.dev/ws``

Authentication
--------------

All API endpoints require authentication using JWT tokens:

**Header Format**:
```http
Authorization: Bearer <jwt_token>
```

**WebSocket Authentication**:
```javascript
// Query parameter
ws://localhost:8000/ws/body-doubling/session-id?token=jwt_token

// Or in headers during handshake
{
  "Authorization": "Bearer jwt_token"
}
```

REST API Endpoints
------------------

Authentication Endpoints
~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /auth/login
   
   Authenticate user and receive JWT tokens.
   
   **Request Body**:
   
   .. code-block:: json
   
      {
        "email": "<EMAIL>",
        "password": "secure_password"
      }
   
   **Response**:
   
   .. code-block:: json
   
      {
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_type": "bearer",
        "expires_in": 900,
        "user": {
          "id": "uuid",
          "email": "<EMAIL>",
          "full_name": "John Doe"
        }
      }

.. http:post:: /auth/refresh
   
   Refresh access token using refresh token.

.. http:post:: /auth/logout
   
   Logout and invalidate tokens.

Body Doubling Endpoints
~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /body-doubling/sessions
   
   List available body doubling sessions.
   
   **Query Parameters**:
   
   - ``page`` (int): Page number (default: 1)
   - ``page_size`` (int): Items per page (default: 10, max: 50)
   - ``session_type`` (str): Filter by session type
   - ``is_public`` (bool): Filter by public/private sessions
   
   **Response**:
   
   .. code-block:: json
   
      {
        "sessions": [
          {
            "id": "uuid",
            "title": "Morning Focus Session",
            "description": "Let's get our morning tasks done together!",
            "host_user_id": "uuid",
            "max_participants": 4,
            "participant_count": 2,
            "session_type": "focus_group",
            "is_public": true,
            "status": "active",
            "scheduled_start": "2024-01-01T09:00:00Z",
            "created_at": "2024-01-01T08:45:00Z"
          }
        ],
        "total": 25,
        "page": 1,
        "page_size": 10,
        "has_next": true,
        "has_prev": false
      }

.. http:post:: /body-doubling/sessions
   
   Create a new body doubling session.
   
   **Request Body**:
   
   .. code-block:: json
   
      {
        "title": "Afternoon Study Session",
        "description": "Working on project documentation",
        "max_participants": 6,
        "session_type": "study_group",
        "is_public": true,
        "requires_approval": false,
        "password_protected": false,
        "scheduled_start": "2024-01-01T14:00:00Z",
        "scheduled_end": "2024-01-01T16:00:00Z",
        "session_settings": {
          "allow_chat": true,
          "allow_progress_sharing": true,
          "focus_mode": "pomodoro"
        }
      }

.. http:get:: /body-doubling/sessions/{session_id}
   
   Get details of a specific session.

.. http:put:: /body-doubling/sessions/{session_id}
   
   Update session details (host only).

.. http:delete:: /body-doubling/sessions/{session_id}
   
   Delete session (host only).

.. http:post:: /body-doubling/sessions/{session_id}/join
   
   Join a body doubling session.
   
   **Request Body**:
   
   .. code-block:: json
   
      {
        "share_progress": true,
        "anonymous_mode": false,
        "session_password": "optional_password",
        "participant_settings": {
          "notifications_enabled": true,
          "sound_enabled": false
        }
      }

.. http:post:: /body-doubling/sessions/{session_id}/leave
   
   Leave a body doubling session.

.. http:get:: /body-doubling/sessions/{session_id}/participants
   
   List session participants.

.. http:post:: /body-doubling/sessions/{session_id}/focus

   Start a group focus session.

   **Request Body**:

   .. code-block:: json

      {
        "focus_duration": 25,
        "break_duration": 5,
        "session_type": "pomodoro",
        "focus_settings": {
          "synchronized_breaks": true,
          "break_reminders": true
        }
      }

Gamification Endpoints
~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /gamification/profile

   Get user's complete gamification profile.

   **Response**:

   .. code-block:: json

      {
        "id": "uuid",
        "user_id": "uuid",
        "total_points": 1250,
        "level": 5,
        "points_to_next_level": 150,
        "gamification_enabled": true,
        "celebration_style": "moderate",
        "preferred_rewards": {
          "types": ["badges", "themes"],
          "frequency": "immediate"
        },
        "created_at": "2024-01-01T08:00:00Z",
        "updated_at": "2024-01-01T12:00:00Z"
      }

.. http:patch:: /gamification/profile

   Update user's gamification preferences.

   **Request Body**:

   .. code-block:: json

      {
        "celebration_style": "enthusiastic",
        "preferred_rewards": {
          "types": ["badges", "points", "themes"]
        }
      }

.. http:get:: /gamification/stats

   Get comprehensive gamification statistics.

   **Response**:

   .. code-block:: json

      {
        "total_points": 1250,
        "current_level": 5,
        "points_to_next_level": 150,
        "achievements_unlocked": 12,
        "total_achievements": 25,
        "active_streaks": [
          {
            "id": "uuid",
            "streak_type": "daily_tasks",
            "current_streak": 7,
            "longest_streak": 14,
            "last_activity_date": "2024-01-01"
          }
        ],
        "recent_awards": [
          {
            "id": "uuid",
            "points_awarded": 45,
            "reason": "Completed difficult task",
            "multiplier": 1.5,
            "created_at": "2024-01-01T12:00:00Z"
          }
        ],
        "level_progress_percentage": 75.5
      }

.. http:get:: /gamification/achievements

   Get user achievements with progress.

   **Query Parameters**:

   - ``unlocked_only`` (bool): Return only unlocked achievements (default: false)

   **Response**:

   .. code-block:: json

      {
        "achievements": [
          {
            "id": "uuid",
            "user_id": "uuid",
            "achievement_id": "uuid",
            "is_unlocked": true,
            "unlocked_at": "2024-01-01T10:00:00Z",
            "progress_data": {},
            "achievement": {
              "id": "uuid",
              "achievement_key": "first_task",
              "name": "Getting Started",
              "description": "Complete your first task",
              "category": "task_completion",
              "reward_points": 50,
              "badge_icon": "🎯",
              "is_hidden": false
            }
          }
        ]
      }

.. http:post:: /gamification/streaks/update

   Update a user's streak.

   **Request Body**:

   .. code-block:: json

      {
        "streak_type": "daily_tasks",
        "action_completed": true
      }

   **Response**:

   .. code-block:: json

      {
        "id": "uuid",
        "user_id": "uuid",
        "streak_type": "daily_tasks",
        "current_streak": 8,
        "longest_streak": 14,
        "last_activity_date": "2024-01-01",
        "freeze_count": 0,
        "max_freezes": 3
      }

.. http:get:: /gamification/dashboard

   Get comprehensive gamification dashboard data.

   **Response**:

   .. code-block:: json

      {
        "profile": {
          "total_points": 1250,
          "level": 5,
          "points_to_next_level": 150
        },
        "stats": {
          "achievements_unlocked": 12,
          "active_streaks": [...],
          "level_progress_percentage": 75.5
        },
        "recent_achievements": [...],
        "trending_up": ["Task completion", "Consistency"],
        "suggestions": [
          "Try completing a task during your high-energy time for bonus points",
          "You're close to unlocking the 'Week Warrior' achievement!"
        ],
        "next_milestones": [
          {
            "type": "level",
            "description": "Reach level 6",
            "progress": 0.75
          }
        ]
      }

Motivation Endpoints
~~~~~~~~~~~~~~~~~~~

.. http:get:: /motivation/dopamine-menu

   Get personalized dopamine menu activities.

   **Query Parameters**:

   - ``energy_level`` (str): Current energy level (low/medium/high, default: medium)
   - ``available_time`` (int): Available time in minutes (default: 5)
   - ``context`` (str): Context for the activity (default: pre_task)
   - ``exclude_categories`` (array): Categories to exclude
   - ``preferred_categories`` (array): Preferred categories

   **Response**:

   .. code-block:: json

      {
        "activities": [
          {
            "id": "uuid",
            "name": "5-Minute Walk",
            "description": "Take a quick walk around the block",
            "category": "movement",
            "duration_min": 3,
            "duration_max": 10,
            "energy_requirement": "low",
            "energy_boost": "medium",
            "requires_equipment": false,
            "requires_space": false,
            "can_do_anywhere": true,
            "tags": ["outdoor", "exercise"],
            "instructions": "Step outside and walk at a comfortable pace"
          }
        ],
        "personalization_score": 0.8,
        "context": "pre_task",
        "energy_level": "medium",
        "available_time": 5
      }

.. http:post:: /motivation/dopamine-menu/complete

   Record completion of a dopamine activity.

   **Request Body**:

   .. code-block:: json

      {
        "activity_id": "uuid",
        "actual_duration": 5,
        "energy_before": "low",
        "energy_after": "medium",
        "mood_before": 3,
        "mood_after": 6,
        "satisfaction_rating": 4,
        "would_do_again": true,
        "context": "pre_task",
        "notes": "Felt refreshing and energizing"
      }

   **Response**:

   .. code-block:: json

      {
        "id": "uuid",
        "user_id": "uuid",
        "activity_id": "uuid",
        "actual_duration": 5,
        "energy_before": "low",
        "energy_after": "medium",
        "satisfaction_rating": 4,
        "context": "pre_task",
        "created_at": "2024-01-01T12:00:00Z"
      }

.. http:post:: /motivation/activities/custom

   Create a custom dopamine activity.

   **Request Body**:

   .. code-block:: json

      {
        "name": "Pet My Cat",
        "description": "Spend time with my cat for instant mood boost",
        "category": "social",
        "duration": 3,
        "energy_requirement": "low",
        "energy_boost": "high",
        "tags": ["pets", "comfort", "quick"]
      }

   **Response**:

   .. code-block:: json

      {
        "id": "uuid",
        "user_id": "uuid",
        "activity_id": null,
        "preference_type": "custom",
        "custom_name": "Pet My Cat",
        "custom_description": "Spend time with my cat for instant mood boost",
        "custom_category": "social",
        "rating": 5,
        "created_at": "2024-01-01T12:00:00Z"
      }

.. http:get:: /motivation/analytics

   Get motivation analytics for the user.

   **Response**:

   .. code-block:: json

      {
        "total_activities_completed": 45,
        "favorite_categories": ["movement", "creative"],
        "most_effective_activities": [
          {
            "id": "uuid",
            "name": "5-Minute Walk",
            "effectiveness_score": 0.9,
            "completion_count": 12
          }
        ],
        "energy_patterns": {
          "morning": "high",
          "afternoon": "medium",
          "evening": "low"
        },
        "mood_improvements": {
          "average_improvement": 2.5,
          "best_category": "movement"
        },
        "completion_rate_by_context": {
          "pre_task": 0.8,
          "break": 0.9,
          "reward": 0.95
        },
        "recommendations": [
          "Try movement activities when energy is low",
          "Creative activities work well for you in the evening"
        ]
      }

.. http:get:: /motivation/dashboard

   Get comprehensive motivation dashboard data.

   **Response**:

   .. code-block:: json

      {
        "recent_completions": [...],
        "suggested_activities": [...],
        "active_insights": [
          {
            "id": "uuid",
            "insight_type": "pattern",
            "title": "Morning Movement Works Best",
            "description": "You're 40% more likely to complete movement activities in the morning",
            "confidence_score": 0.85
          }
        ],
        "analytics": {...},
        "personalization_tips": [
          "Try tracking your energy levels to get better activity suggestions",
          "Rate completed activities to improve recommendations"
        ]
      }

WebSocket API
-------------

Connection Endpoints
~~~~~~~~~~~~~~~~~~~

.. websocket:: /ws/body-doubling/{session_id}
   
   Connect to a body doubling session for real-time communication.
   
   **Authentication**: JWT token required via query parameter or header
   
   **Connection Flow**:
   
   1. Client connects with valid JWT token
   2. Server validates token and user permissions
   3. User is added to session participant list
   4. Welcome message sent to user
   5. Other participants notified of new user
   
   **Message Types**:
   
   - ``participant_joined``: New user joins session
   - ``participant_left``: User leaves session
   - ``participant_status_changed``: User status update
   - ``chat_message``: Text communication
   - ``task_progress``: Task completion updates
   - ``encouragement``: Reactions and support
   - ``focus_session_started``: Group focus begins
   - ``focus_session_ended``: Group focus ends
   - ``heartbeat``: Connection health check

Message Formats
~~~~~~~~~~~~~~

**Outgoing Messages (Client to Server)**:

.. code-block:: json

   {
     "type": "chat_message",
     "data": {
       "content": "Great job everyone! 🎉",
       "message_type": "chat"
     },
     "timestamp": "2024-01-01T12:00:00Z"
   }

.. code-block:: json

   {
     "type": "task_progress",
     "data": {
       "task_id": "uuid",
       "progress_type": "completed",
       "message": "Just finished my first task!",
       "progress_data": {
         "completion_percentage": 100,
         "time_spent": 45
       }
     },
     "timestamp": "2024-01-01T12:00:00Z"
   }

.. code-block:: json

   {
     "type": "encouragement",
     "data": {
       "target_user_id": "uuid",
       "encouragement_type": "thumbs_up",
       "message": "Keep it up!"
     },
     "timestamp": "2024-01-01T12:00:00Z"
   }

**Incoming Messages (Server to Client)**:

.. code-block:: json

   {
     "type": "participant_joined",
     "data": {
       "user_id": "uuid",
       "user_name": "Jane Doe",
       "anonymous_mode": false,
       "participant_count": 3
     },
     "timestamp": "2024-01-01T12:00:00Z"
   }

.. code-block:: json

   {
     "type": "focus_session_started",
     "data": {
       "focus_session_id": "uuid",
       "duration": 25,
       "break_duration": 5,
       "started_by": "uuid",
       "participants": ["uuid1", "uuid2", "uuid3"]
     },
     "timestamp": "2024-01-01T12:00:00Z"
   }

Error Handling
--------------

HTTP Error Responses
~~~~~~~~~~~~~~~~~~~

All REST API errors follow a consistent format:

.. code-block:: json

   {
     "error": {
       "code": 400,
       "message": "Validation error",
       "friendly_message": "Please check your input and try again",
       "details": {
         "field": "email",
         "issue": "Invalid email format"
       },
       "timestamp": "2024-01-01T12:00:00Z",
       "path": "/api/v1/auth/login"
     }
   }

**Common HTTP Status Codes**:

- ``200 OK``: Successful request
- ``201 Created``: Resource created successfully
- ``400 Bad Request``: Invalid request data
- ``401 Unauthorized``: Authentication required
- ``403 Forbidden``: Insufficient permissions
- ``404 Not Found``: Resource not found
- ``422 Unprocessable Entity``: Validation error
- ``429 Too Many Requests``: Rate limit exceeded
- ``500 Internal Server Error``: Server error

WebSocket Error Messages
~~~~~~~~~~~~~~~~~~~~~~~

WebSocket errors are sent as special message types:

.. code-block:: json

   {
     "type": "error",
     "data": {
       "error_code": "INVALID_MESSAGE_TYPE",
       "message": "Unknown message type: invalid_type",
       "friendly_message": "That action isn't supported. Please try again.",
       "recoverable": true
     },
     "timestamp": "2024-01-01T12:00:00Z"
   }

**WebSocket Error Codes**:

- ``AUTHENTICATION_FAILED``: Invalid or expired token
- ``SESSION_NOT_FOUND``: Session doesn't exist
- ``SESSION_FULL``: Maximum participants reached
- ``PERMISSION_DENIED``: Insufficient permissions
- ``INVALID_MESSAGE_TYPE``: Unknown message type
- ``RATE_LIMIT_EXCEEDED``: Too many messages
- ``VALIDATION_ERROR``: Invalid message data

Rate Limiting
-------------

REST API Rate Limits
~~~~~~~~~~~~~~~~~~~

- **Authentication endpoints**: 5 requests per minute per IP
- **General API endpoints**: 100 requests per minute per user
- **Session creation**: 10 sessions per hour per user
- **Message sending**: 60 messages per minute per user

WebSocket Rate Limits
~~~~~~~~~~~~~~~~~~~~

- **Connection attempts**: 10 per minute per IP
- **Message sending**: 30 messages per minute per connection
- **Session joins**: 20 per hour per user

Rate limit headers are included in all responses:

.. code-block:: http

   X-RateLimit-Limit: 100
   X-RateLimit-Remaining: 95
   X-RateLimit-Reset: 1640995200

When rate limits are exceeded, a ``429 Too Many Requests`` response is returned with a ``Retry-After`` header indicating when the client can retry.

API Versioning
--------------

The API uses URL-based versioning:

- **Current version**: ``/api/v1/``
- **Future versions**: ``/api/v2/``, ``/api/v3/``, etc.

Version compatibility is maintained for at least 12 months after a new version is released. Deprecated endpoints will include a ``Deprecation`` header with the sunset date.

SDK and Client Libraries
------------------------

Official client libraries are available for:

- **JavaScript/TypeScript**: ``@chronos/api-client``
- **Python**: ``chronos-api-client``
- **React**: ``@chronos/react-hooks``

**Example Usage**:

.. code-block:: javascript

   import { ChronosClient } from '@chronos/api-client';
   
   const client = new ChronosClient({
     baseURL: 'https://api.chronos.dev',
     token: 'your-jwt-token'
   });
   
   // Create a body doubling session
   const session = await client.bodyDoubling.createSession({
     title: 'Morning Focus',
     maxParticipants: 4,
     isPublic: true
   });
   
   // Connect to WebSocket
   const ws = client.websocket.connect(session.id);
   ws.on('participant_joined', (data) => {
     console.log('New participant:', data.user_name);
   });

This API documentation provides a comprehensive guide for integrating with Project Chronos's real-time body doubling and productivity features.
