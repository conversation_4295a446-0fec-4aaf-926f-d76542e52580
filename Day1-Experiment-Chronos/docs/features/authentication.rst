Authentication & Security Features
===================================

.. currentmodule:: app.services.auth_service

Project Chronos provides ADHD-optimized authentication and security features designed to minimize cognitive load while maintaining robust security standards.

Core Functionality
------------------

ADHD-Friendly Authentication
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Simplified Registration Process**
   The registration process is designed to minimize cognitive load and decision fatigue:
   
   - **Minimal Required Fields**: Only email, password, and first name required
   - **Progressive Profile Completion**: ADHD-specific fields are optional during registration
   - **Clear Validation**: Simple, non-technical error messages
   - **Password Confirmation**: Immediate validation to prevent typos
   - **Smart Defaults**: Sensible defaults for ADHD-specific preferences

   .. code-block:: python
   
      registration_data = UserRegister(
          email="<EMAIL>",
          password="securepassword123",
          confirm_password="securepassword123",
          first_name="Alex",
          # Optional ADHD-specific fields
          adhd_diagnosis=True,
          preferred_chunk_size="medium",
          default_energy_level="medium"
      )

**Streamlined Login Experience**
   Login process accommodates ADHD challenges:
   
   - **Remember Me Option**: Extended sessions for users who forget to stay logged in
   - **Clear Error Messages**: Non-judgmental feedback for authentication failures
   - **Password Recovery**: Simple, step-by-step password reset process
   - **Email Verification**: Optional but recommended for account security

   .. code-block:: python
   
      credentials = UserLogin(
          email="<EMAIL>",
          password="securepassword123",
          remember_me=True  # Extends refresh token expiry
      )

JWT Token Management
~~~~~~~~~~~~~~~~~~~

**Dual Token System**
   Secure and user-friendly token management:
   
   - **Access Tokens**: Short-lived (15 minutes) for API access
   - **Refresh Tokens**: Longer-lived (7 days, 30 days with "remember me")
   - **Automatic Refresh**: Seamless token renewal without re-authentication
   - **Secure Storage**: Tokens include user context and validation metadata

**Token Security Features**
   - **Cryptographic Signing**: HMAC-SHA256 algorithm with secret key
   - **Expiration Validation**: Automatic token expiry checking
   - **Type Validation**: Separate access and refresh token validation
   - **User Context**: Tokens include user ID, email, and verification status

   .. code-block:: python
   
      # Token creation with user context
      token_data = {
          "sub": str(user.id),
          "email": user.email,
          "is_verified": user.is_verified
      }
      
      tokens = security_manager.create_token_pair(
          user_id=str(user.id),
          additional_claims=token_data
      )

Password Security
~~~~~~~~~~~~~~~~

**Robust Password Handling**
   Industry-standard password security with ADHD considerations:
   
   - **Bcrypt Hashing**: Secure password hashing with automatic salt generation
   - **Minimum Length**: 8-character minimum to balance security and usability
   - **Password Confirmation**: Prevents typos during registration and changes
   - **Secure Reset Process**: Time-limited reset tokens with single-use validation

**Password Management Features**
   - **Change Password**: Authenticated users can update passwords
   - **Forgot Password**: Email-based reset process with clear instructions
   - **Reset Tokens**: Secure, time-limited tokens for password recovery
   - **Password Validation**: Client and server-side validation

   .. code-block:: python
   
      # Secure password hashing
      hashed_password = security_manager.hash_password("user_password")
      
      # Password verification
      is_valid = security_manager.verify_password(
          plain_password="user_input",
          hashed_password=stored_hash
      )

ADHD-Specific Optimizations
--------------------------

Cognitive Load Reduction
~~~~~~~~~~~~~~~~~~~~~~~

**Simplified Decision Making**
   Authentication flows designed to minimize decision fatigue:
   
   - **Default Options**: Smart defaults for all optional fields
   - **Progressive Disclosure**: Advanced options hidden until needed
   - **Clear Labels**: Simple, descriptive field labels and instructions
   - **Minimal Steps**: Streamlined processes with clear progress indicators

**Memory Support**
   Features that accommodate ADHD memory challenges:
   
   - **Remember Me**: Extended sessions reduce re-authentication frequency
   - **Email Reminders**: Optional reminders for account verification
   - **Clear Recovery**: Simple password reset with email instructions
   - **Session Persistence**: Longer token expiry for authenticated sessions

**Error Handling**
   ADHD-friendly error messages and recovery:
   
   - **Non-Technical Language**: Clear, simple error descriptions
   - **Actionable Guidance**: Specific steps to resolve issues
   - **No Blame Language**: Supportive tone in error messages
   - **Recovery Options**: Clear paths to resolve authentication problems

Executive Function Support
~~~~~~~~~~~~~~~~~~~~~~~~~

**Reduced Complexity**
   Authentication system designed for executive dysfunction:
   
   - **Single Sign-On Ready**: Prepared for future SSO integration
   - **Automatic Validation**: Real-time field validation during input
   - **Clear Requirements**: Upfront password and email requirements
   - **Progress Indicators**: Clear status of registration and verification

**Task Completion Support**
   Features that help users complete authentication tasks:
   
   - **Email Verification**: Optional but encouraged for security
   - **Account Activation**: Immediate access with optional verification
   - **Profile Completion**: Gradual profile building over time
   - **Success Feedback**: Clear confirmation of completed actions

Security Features
----------------

Industry-Standard Security
~~~~~~~~~~~~~~~~~~~~~~~~~

**Encryption and Hashing**
   Robust cryptographic protection:
   
   - **JWT Signing**: HMAC-SHA256 with configurable secret keys
   - **Password Hashing**: Bcrypt with automatic salt generation
   - **Token Validation**: Comprehensive token verification and expiry checking
   - **Secure Transmission**: HTTPS-only token transmission (production)

**Authentication Flow Security**
   - **Rate Limiting**: Protection against brute force attacks (configurable)
   - **Token Blacklisting**: Refresh token invalidation on logout
   - **Session Management**: Secure session handling with proper expiry
   - **Input Validation**: Comprehensive validation of all authentication inputs

**Privacy Protection**
   - **Email Enumeration Prevention**: Consistent responses for password reset
   - **Secure Token Storage**: Tokens stored securely with proper expiry
   - **User Data Protection**: Minimal data collection with clear purposes
   - **Audit Logging**: Security event logging for monitoring

Data Protection
~~~~~~~~~~~~~~

**User Information Security**
   - **Minimal Data Collection**: Only necessary information collected
   - **Secure Storage**: Encrypted storage of sensitive information
   - **Access Controls**: Proper authorization for user data access
   - **Data Retention**: Clear policies for data retention and deletion

**ADHD-Specific Privacy**
   - **Optional Disclosure**: ADHD diagnosis and preferences are optional
   - **Profile Privacy**: Users control what information is shared
   - **Gradual Disclosure**: Progressive profile completion over time
   - **Data Portability**: Users can export their data

Technical Implementation
-----------------------

Service Architecture
~~~~~~~~~~~~~~~~~~~

**AuthService**
   Core authentication business logic:
   
   .. autoclass:: app.services.auth_service.AuthService
      :members:
      :show-inheritance:

**SecurityManager**
   Centralized security operations:
   
   .. autoclass:: app.core.security.SecurityManager
      :members:
      :show-inheritance:

Database Models
~~~~~~~~~~~~~~

**User Authentication Fields**
   Extended user model with authentication support:
   
   .. code-block:: python
   
      class User(BaseModel):
          # Core authentication
          email: str
          hashed_password: str
          is_active: bool = True
          is_verified: bool = False
          
          # Token management
          reset_token: Optional[str] = None
          reset_token_expires: Optional[datetime] = None
          verification_token: Optional[str] = None
          verified_at: Optional[datetime] = None
          last_login: Optional[datetime] = None

API Endpoints
~~~~~~~~~~~~

**Authentication Endpoints**
   Complete REST API for authentication:
   
   - ``POST /auth/register`` - User registration with ADHD-friendly process
   - ``POST /auth/login`` - User authentication with remember me option
   - ``POST /auth/refresh`` - Token refresh for seamless sessions
   - ``POST /auth/logout`` - Secure logout with token invalidation
   - ``POST /auth/forgot-password`` - Password reset initiation
   - ``POST /auth/reset-password`` - Password reset completion
   - ``POST /auth/change-password`` - Password change for authenticated users
   - ``POST /auth/verify-email`` - Email verification completion
   - ``GET /auth/status`` - Authentication status check
   - ``GET /auth/me`` - Current user information

**Request/Response Examples**
   
   **Registration Request**:
   
   .. code-block:: json
   
      {
        "email": "<EMAIL>",
        "password": "securepassword123",
        "confirm_password": "securepassword123",
        "first_name": "Alex",
        "adhd_diagnosis": true,
        "preferred_chunk_size": "medium"
      }
   
   **Login Response**:
   
   .. code-block:: json
   
      {
        "message": "Login successful",
        "tokens": {
          "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
          "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
          "token_type": "bearer",
          "expires_in": 900
        },
        "user": {
          "id": "550e8400-e29b-41d4-a716-446655440000",
          "email": "<EMAIL>",
          "first_name": "Alex",
          "is_verified": true
        }
      }

Integration Features
-------------------

WebSocket Authentication
~~~~~~~~~~~~~~~~~~~~~~~

**Real-time Authentication**
   Secure WebSocket connections for body doubling and real-time features:
   
   - **Token-Based Auth**: JWT tokens for WebSocket authentication
   - **Connection Validation**: User validation on WebSocket connection
   - **Session Management**: Proper session handling for real-time features
   - **Automatic Reconnection**: Seamless reconnection with valid tokens

**Usage Example**:

.. code-block:: javascript

   // WebSocket connection with authentication
   const ws = new WebSocket(
       `ws://localhost:8000/ws/body-doubling/${sessionId}?token=${accessToken}`
   );

API Security Integration
~~~~~~~~~~~~~~~~~~~~~~~

**Protected Endpoints**
   All API endpoints properly secured with authentication:
   
   - **Bearer Token Authentication**: Standard JWT bearer token validation
   - **User Context**: Authenticated user available in all protected endpoints
   - **Permission Checking**: User-specific data access controls
   - **Optional Authentication**: Some endpoints support optional authentication

**Usage Example**:

.. code-block:: python

   @router.get("/protected-endpoint")
   async def protected_endpoint(
       current_user: User = Depends(get_current_user)
   ):
       # Endpoint automatically requires authentication
       return {"user_id": current_user.id}

Error Handling
-------------

ADHD-Friendly Error Messages
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Clear Communication**
   Error messages designed for ADHD users:
   
   - **Simple Language**: Non-technical, easy-to-understand messages
   - **Actionable Steps**: Clear instructions for resolving issues
   - **Supportive Tone**: Encouraging rather than critical language
   - **Context Awareness**: Relevant information for the specific situation

**Example Error Responses**:

.. code-block:: json

   {
     "detail": "Email address is already registered",
     "suggestion": "Try logging in instead, or use the 'Forgot Password' option if you can't remember your password."
   }

Security Error Handling
~~~~~~~~~~~~~~~~~~~~~~

**Secure Error Responses**
   - **Information Disclosure Prevention**: Consistent responses to prevent enumeration
   - **Rate Limiting**: Protection against brute force attacks
   - **Audit Logging**: Security events logged for monitoring
   - **Graceful Degradation**: System continues to function during security events

This comprehensive authentication system provides ADHD users with secure, user-friendly access to Project Chronos while maintaining industry-standard security practices and accommodating the unique challenges of ADHD.
