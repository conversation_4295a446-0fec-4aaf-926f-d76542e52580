Body Doubling & Real-time Collaboration
=======================================

.. currentmodule:: app.services.body_doubling_service

Project Chronos provides comprehensive virtual body doubling features designed specifically for ADHD users who benefit from working alongside others for accountability and shared focus.

Core Functionality
------------------

Virtual Body Doubling Sessions
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Purpose**
   Virtual body doubling addresses one of the most significant ADHD challenges: task initiation and sustained focus. Many ADHD users find it easier to work when others are present, even if they're working on different tasks.

**Session Types**
   - **General**: Open-ended co-working sessions
   - **Study**: Academic work and learning sessions
   - **Work**: Professional tasks and projects
   - **Creative**: Artistic and creative endeavors
   - **Cleaning**: Household tasks and organization

**ADHD-Optimized Features**
   - Flexible session timing that accommodates ADHD schedules
   - Anonymous participation options for social anxiety
   - Gentle accountability without pressure or judgment
   - Energy-aware session matching and recommendations
   - Break-friendly environments that understand ADHD needs

Session Management
~~~~~~~~~~~~~~~~~

**Creating Sessions**
   Users can create body doubling sessions with customizable settings:

   .. code-block:: python

      session_data = BodyDoublingSessionCreate(
          title="Morning Focus Session",
          description="Working on important projects together",
          session_type="work",
          max_participants=4,
          is_public=True,
          scheduled_start=datetime.utcnow() + timedelta(hours=1),
          scheduled_duration=120,  # 2 hours
          settings={
              "allow_chat": True,
              "progress_sharing": True,
              "break_reminders": True
          }
      )

**Session Discovery**
   ADHD-friendly session discovery with filters:

   - Session type matching (work, study, creative)
   - Available space filtering
   - Starting time preferences
   - Energy level compatibility
   - Social comfort level matching

**Joining Sessions**
   Simple joining process with privacy controls:

   .. code-block:: python

      join_data = BodyDoublingParticipantJoin(
          display_name="Focus Buddy",  # Optional custom name
          is_anonymous=False,          # Privacy option
          share_progress=True,         # Share task updates
          receive_encouragement=True   # Accept support messages
      )

Real-time Communication
----------------------

WebSocket Integration
~~~~~~~~~~~~~~~~~~~~

**Live Connection Management**
   Real-time WebSocket connections provide immediate feedback and social presence:

   - Instant participant join/leave notifications
   - Live presence indicators showing who's actively working
   - Real-time message delivery for encouragement and chat
   - Connection health monitoring and automatic reconnection

**Message Types**
   The system supports various real-time message types:

   .. code-block:: python

      # Encouragement messages
      {
          "message_type": "encouragement",
          "sender_id": "user_123",
          "message": "You're doing great! Keep it up!",
          "timestamp": "2024-01-01T12:00:00Z"
      }

      # Progress sharing
      {
          "message_type": "progress_share",
          "user_id": "user_456",
          "progress_type": "task_completion",
          "description": "Finished the first chapter",
          "completion_percentage": 0.25
      }

      # Focus session synchronization
      {
          "message_type": "focus_start",
          "focus_duration": 25,
          "break_duration": 5,
          "started_by": "user_789"
      }

**Connection Authentication**
   Secure WebSocket connections with token-based authentication:

   .. code-block:: javascript

      const ws = new WebSocket(
          `ws://localhost:8000/ws/body-doubling/${sessionId}?token=${authToken}`
      );

Progress Sharing & Encouragement
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Progress Updates**
   Participants can share their progress in real-time:

   - Task completion milestones
   - Energy level changes
   - Break announcements
   - Achievement celebrations
   - Struggle acknowledgments (for support)

**Encouragement System**
   ADHD-friendly support features:

   - Quick encouragement buttons (👍, 🎉, 💪, ❤️)
   - Custom encouragement messages
   - Anonymous support options
   - Celebration of small wins
   - Gentle check-ins during difficult periods

**Social Accountability**
   Gentle accountability features that work with ADHD:

   - Presence indicators without pressure
   - Optional progress sharing
   - Supportive rather than competitive environment
   - Flexible participation levels
   - Understanding of ADHD challenges

Synchronized Focus Sessions
--------------------------

Group Focus Management
~~~~~~~~~~~~~~~~~~~~~

**Pomodoro Synchronization**
   Synchronized focus sessions help groups work together:

   .. code-block:: python

      focus_session = GroupFocusSessionCreate(
          focus_type="pomodoro",
          focus_duration=25,        # 25-minute focus period
          break_duration=5,         # 5-minute break
          total_cycles=4,           # 4 complete cycles
          scheduled_start=datetime.utcnow() + timedelta(minutes=5)
      )

**Flexible Focus Modes**
   Different focus session types to accommodate ADHD needs:

   - **Standard Pomodoro**: 25/5 minute cycles
   - **Extended Focus**: 45/15 minute cycles for hyperfocus
   - **Short Burst**: 15/3 minute cycles for low attention days
   - **Custom Timing**: User-defined focus and break periods
   - **Flow Protection**: Longer periods with optional breaks

**Break Coordination**
   Synchronized breaks that maintain group cohesion:

   - Group break announcements
   - Break activity suggestions
   - Flexible break timing for individual needs
   - Social break activities (optional chat)
   - Gentle return-to-focus reminders

ADHD-Specific Features
---------------------

Executive Function Support
~~~~~~~~~~~~~~~~~~~~~~~~~

**Task Initiation Assistance**
   Body doubling specifically helps with ADHD task initiation challenges:

   - Social presence reduces task avoidance
   - Gentle accountability encourages starting
   - Shared energy helps overcome inertia
   - Positive peer pressure without judgment
   - Celebration of getting started

**Attention Regulation**
   Features that help maintain and regulate attention:

   - Presence indicators provide gentle focus cues
   - Break reminders prevent hyperfocus burnout
   - Social energy helps sustain attention
   - Flexible participation accommodates attention fluctuations
   - Understanding environment for ADHD challenges

**Decision Fatigue Reduction**
   Simplified choices and default options:

   - Quick session joining with minimal setup
   - Default settings that work for most ADHD users
   - Pre-configured session types
   - Simple encouragement options
   - Streamlined interface design

Social Anxiety Accommodation
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Anonymous Participation**
   Options for users with social anxiety:

   - Anonymous usernames in sessions
   - Private progress sharing settings
   - Optional voice/video participation
   - Text-only communication options
   - Ability to observe without active participation

**Gentle Social Interaction**
   ADHD-friendly social features:

   - No pressure to constantly interact
   - Understanding of social energy fluctuations
   - Supportive rather than competitive environment
   - Respect for different communication styles
   - Safe space for ADHD struggles

**Privacy Controls**
   Comprehensive privacy settings:

   - Control over what information is shared
   - Anonymous encouragement options
   - Private session creation
   - Selective progress sharing
   - Comfortable participation levels

Energy Management
~~~~~~~~~~~~~~~~

**Energy-Aware Matching**
   Session recommendations based on energy levels:

   - High-energy sessions for productive periods
   - Low-energy sessions for difficult days
   - Mixed-energy sessions for balanced support
   - Energy level indicators for participants
   - Adaptive session suggestions

**Flexible Participation**
   Accommodating ADHD energy fluctuations:

   - Easy session leaving without guilt
   - Flexible focus session participation
   - Understanding of energy crashes
   - Support during low-energy periods
   - Celebration of any participation level

**Break Management**
   ADHD-friendly break handling:

   - Flexible break timing
   - Movement break suggestions
   - Sensory break activities
   - Social break options
   - Individual break needs accommodation

Technical Implementation
-----------------------

Database Models
~~~~~~~~~~~~~~

**BodyDoublingSession**
   Core session management with ADHD considerations:

   .. autoclass:: app.models.body_doubling.BodyDoublingSession
      :members:
      :show-inheritance:

**BodyDoublingParticipant**
   Participant management with privacy and preference controls:

   .. autoclass:: app.models.body_doubling.BodyDoublingParticipant
      :members:
      :show-inheritance:

**GroupFocusSession**
   Synchronized focus session management:

   .. autoclass:: app.models.body_doubling.GroupFocusSession
      :members:
      :show-inheritance:

Service Architecture
~~~~~~~~~~~~~~~~~~~

**BodyDoublingService**
   Core business logic for session management:

   .. autoclass:: app.services.body_doubling_service.BodyDoublingService
      :members:
      :show-inheritance:

**WebSocketService**
   Real-time communication and connection management:

   .. autoclass:: app.services.websocket_service.WebSocketService
      :members:
      :show-inheritance:

**ConnectionManager**
   WebSocket connection lifecycle management:

   .. autoclass:: app.services.websocket_service.ConnectionManager
      :members:
      :show-inheritance:

API Integration
~~~~~~~~~~~~~~

**REST Endpoints**
   Standard HTTP endpoints for session management:

   - ``POST /body-doubling/sessions`` - Create new session
   - ``GET /body-doubling/sessions/discover`` - Find available sessions
   - ``POST /body-doubling/sessions/{id}/join`` - Join session
   - ``POST /body-doubling/sessions/{id}/leave`` - Leave session
   - ``POST /body-doubling/sessions/{id}/focus`` - Start group focus
   - ``POST /body-doubling/sessions/{id}/encouragement`` - Send support
   - ``POST /body-doubling/sessions/{id}/progress`` - Share progress

**WebSocket Endpoints**
   Real-time communication endpoints:

   - ``/ws/body-doubling/{session_id}`` - Session-specific WebSocket
   - ``/ws/general`` - General updates and notifications

**Authentication**
   Secure access to body doubling features:

   - JWT token authentication for REST APIs
   - Token-based WebSocket authentication
   - Session-based permission checking
   - Privacy-aware data access controls

Usage Examples
-------------

Basic Session Flow
~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Create a body doubling session
   session_data = BodyDoublingSessionCreate(
       title="Morning Productivity",
       session_type="work",
       max_participants=3,
       scheduled_duration=90
   )

   session = await body_doubling_service.create_session(
       host_user_id=user.id,
       session_data=session_data
   )

   # Discover and join sessions
   discovery = SessionDiscoveryRequest(
       session_type="work",
       has_space=True,
       starting_soon=True
   )

   sessions, count = await body_doubling_service.discover_sessions(
       user_id=user.id,
       discovery_request=discovery
   )

   # Join a session
   join_data = BodyDoublingParticipantJoin(
       share_progress=True,
       receive_encouragement=True
   )

   session, participant = await body_doubling_service.join_session(
       session_id=sessions[0].id,
       user_id=user.id,
       participant_data=join_data
   )

Real-time Communication
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   // Connect to WebSocket
   const ws = new WebSocket(
       `ws://localhost:8000/ws/body-doubling/${sessionId}?token=${token}`
   );

   // Send encouragement
   ws.send(JSON.stringify({
       message_type: "encouragement",
       data: {
           message: "You're doing amazing!",
           message_type: "encouragement"
       }
   }));

   // Share progress
   ws.send(JSON.stringify({
       message_type: "progress_share",
       data: {
           progress_type: "task_completion",
           description: "Finished the report",
           completion_percentage: 1.0
       }
   }));

   // Handle incoming messages
   ws.onmessage = (event) => {
       const message = JSON.parse(event.data);

       switch (message.message_type) {
           case "encouragement":
               showEncouragement(message.data);
               break;
           case "progress_share":
               updateProgressFeed(message.data);
               break;
           case "focus_start":
               startSynchronizedFocus(message.data);
               break;
       }
   };

This comprehensive body doubling system provides ADHD users with the social accountability and collaborative support they need to overcome task initiation barriers and maintain productive focus, all while accommodating the unique challenges and strengths of the ADHD brain.
