Gamification & Motivation Features
===================================

.. currentmodule:: app.services

Project Chronos includes a comprehensive gamification and motivation system specifically designed for ADHD users. This system addresses the dopamine deficit that makes task initiation difficult by providing intrinsic motivation through points, achievements, streaks, and personalized motivation activities.

Core Functionality
------------------

Points & Rewards System
~~~~~~~~~~~~~~~~~~~~~~~

The points system provides immediate positive reinforcement for any progress, with ADHD-optimized multipliers:

**Base Point Awards**
   - Task initiation: 5 points (encouraging the hardest step)
   - Task completion: 20-50+ points (based on difficulty and energy)
   - Bonus multipliers for challenging circumstances

**ADHD-Friendly Multipliers**
   - **Low Energy Bonus**: 1.3x multiplier when completing tasks during low energy periods
   - **Time-Based Bonuses**: Extra points for morning (1.1x) and evening (1.2x) productivity
   - **Context Bonuses**: First task of day (1.2x), after break (1.1x), hyperfocus session (1.3x)
   - **Difficulty Scaling**: Easy (1.0x), Medium (1.2x), Hard (1.5x), Overwhelming (2.0x)

**Example Point Calculation**:

.. code-block:: python

   # Base task: 30 points
   # Hard difficulty: 1.5x multiplier
   # Low energy: 1.3x multiplier  
   # Evening time: 1.2x multiplier
   # Final points: 30 × 1.5 × 1.3 × 1.2 = 70 points

Level Progression System
~~~~~~~~~~~~~~~~~~~~~~~

**Logarithmic Scaling**
   Prevents level stagnation while maintaining achievability:
   
   - Level 1-3: Quick early wins (100, 300, 600 points)
   - Level 4+: Logarithmic progression to prevent grinding
   - Maximum level 50 to maintain meaningful progression

**Level Benefits**
   - Unlock new dopamine activities
   - Access to advanced features
   - Celebration animations and rewards
   - Social recognition (when enabled)

Achievement System
~~~~~~~~~~~~~~~~~

**Achievement Categories**
   - **Task Completion**: First task, 10 tasks, 50 tasks, 100 tasks
   - **Consistency**: 3-day streak, 7-day streak, 30-day streak
   - **Focus**: First focus session, 25 focus sessions completed
   - **Motivation**: Try 5 different dopamine activities
   - **Milestones**: Level achievements, special accomplishments

**ADHD-Friendly Design**
   - Achievable milestones that don't require perfection
   - Multiple paths to unlock achievements
   - Hidden achievements for surprise rewards
   - Recovery options for missed opportunities

Flexible Streak System
~~~~~~~~~~~~~~~~~~~~~~

**Streak Types**
   - Daily task completion
   - Focus session participation
   - App engagement
   - Custom user-defined streaks

**ADHD Accommodations**
   - **Streak Freezes**: 3 freeze days per month for difficult periods
   - **Recovery Options**: Ways to rebuild broken streaks
   - **Flexible Definitions**: Streaks based on effort, not just completion
   - **Multiple Streak Types**: Different ways to maintain consistency

Dopamine Menu System
-------------------

Pre-Task Motivation Activities
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Purpose**
   Provides quick dopamine boosts to overcome task initiation barriers - the biggest challenge for ADHD users.

**Activity Categories**
   - **Movement**: 5-minute walk, stretching, dance break
   - **Sensory**: Listen to favorite song, look out window, fidget toys
   - **Mental**: Deep breathing, meditation, positive affirmations
   - **Creative**: Quick doodle, journal entry, photo taking
   - **Social**: Text a friend, pet an animal, quick call

**Personalization Features**
   - Energy level matching (low/medium/high)
   - Time constraint filtering (1-20 minutes)
   - Location awareness (home, office, public)
   - Equipment requirements (none, minimal, specific)
   - Effectiveness learning from user feedback

Energy-Aware Filtering
~~~~~~~~~~~~~~~~~~~~~

**Low Energy Activities**
   When user reports low energy, system suggests:
   
   - Gentle movement (slow walk, light stretching)
   - Calming activities (breathing, meditation)
   - Minimal effort options (listen to music, look outside)
   - Comfort activities (pet therapy, warm drink)

**Medium Energy Activities**
   For moderate energy levels:
   
   - Light exercise (quick walk, yoga)
   - Creative tasks (drawing, writing)
   - Social interaction (quick chat, text friends)
   - Organizing activities (tidy desk, sort items)

**High Energy Activities**
   When energy is abundant:
   
   - Vigorous movement (dance, jumping jacks)
   - Challenging tasks (puzzles, brain games)
   - Social activities (video calls, group activities)
   - High-intensity focus preparation

Custom Activity Creation
~~~~~~~~~~~~~~~~~~~~~~~

**User-Defined Activities**
   Users can create personalized dopamine activities:
   
   - Custom names and descriptions
   - Personal duration preferences
   - Individual effectiveness ratings
   - Private notes and instructions

**Learning System**
   The system learns from user behavior:
   
   - Tracks completion rates by activity
   - Monitors mood/energy improvements
   - Adjusts recommendations based on effectiveness
   - Suggests optimal timing for activities

Motivation Analytics
-------------------

Pattern Recognition
~~~~~~~~~~~~~~~~~~

**Energy Patterns**
   - Identifies user's natural energy rhythms
   - Suggests optimal times for different activities
   - Predicts low-energy periods for proactive support
   - Tracks energy improvement from activities

**Activity Effectiveness**
   - Measures mood improvements from each activity
   - Calculates completion rates by context
   - Identifies most successful motivation strategies
   - Provides personalized recommendations

**Productivity Insights**
   - Correlates dopamine activities with task completion
   - Identifies most effective pre-task routines
   - Tracks consistency patterns and improvements
   - Suggests optimization strategies

Personalized Recommendations
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**AI-Driven Suggestions**
   - "Movement activities work best for you in the morning"
   - "You're 40% more likely to complete tasks after a 5-minute walk"
   - "Creative activities help you most during afternoon slumps"
   - "Your longest streaks happen when you use the dopamine menu"

**Adaptive Learning**
   - Continuously improves recommendations
   - Adapts to changing user patterns
   - Suggests new activities to try
   - Identifies declining effectiveness and suggests alternatives

ADHD-Specific Optimizations
---------------------------

Dopamine-Driven Design
~~~~~~~~~~~~~~~~~~~~~

**Immediate Feedback**
   Every action provides instant positive reinforcement:
   
   - Points appear immediately upon task start/completion
   - Achievement unlocks trigger celebration animations
   - Progress bars update in real-time
   - Positive messages for any effort

**Variable Reward Schedules**
   Creates excitement through unpredictability:
   
   - Bonus point multipliers for different contexts
   - Surprise achievement unlocks
   - Random encouragement messages
   - Special event bonuses

**Choice Architecture**
   Provides multiple paths to success:
   
   - Different types of achievements to unlock
   - Various dopamine activities to choose from
   - Flexible streak definitions
   - Customizable reward preferences

Executive Function Support
~~~~~~~~~~~~~~~~~~~~~~~~~

**Decision Simplification**
   - Dopamine menu limited to 5 activities to prevent overwhelm
   - Clear, simple activity descriptions
   - Default options for quick selection
   - Minimal configuration required

**Task Initiation Assistance**
   - Dopamine menu specifically for pre-task motivation
   - Points awarded just for starting tasks
   - Breaking overwhelming tasks into smaller chunks
   - Gentle encouragement without pressure

**Working Memory Aids**
   - Visual progress indicators
   - Recent activity history
   - Achievement galleries for motivation
   - Clear next steps and suggestions

Emotional Regulation
~~~~~~~~~~~~~~~~~~~

**Positive Reinforcement**
   - Celebrates small wins and progress
   - Focuses on effort rather than just outcomes
   - Provides encouragement during difficult periods
   - Maintains hope through recovery options

**Stress Reduction**
   - Flexible systems that accommodate bad days
   - No punishment for missed streaks or goals
   - Gentle reminders rather than pressure
   - Options to pause or adjust expectations

**Mood Support**
   - Tracks mood improvements from activities
   - Suggests mood-boosting activities during low periods
   - Celebrates emotional regulation successes
   - Provides comfort activities for difficult times

Integration with Core Features
-----------------------------

Task Management Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Automatic Gamification**
   When users interact with tasks, the system automatically:
   
   - Awards points for task creation, start, and completion
   - Checks for achievement unlocks
   - Updates relevant streaks
   - Suggests dopamine activities for difficult tasks

**Context-Aware Rewards**
   - Higher points for completing overdue tasks
   - Bonuses for tackling high-energy tasks during low energy
   - Extra rewards for using AI chunking features
   - Special recognition for consistency

Focus Session Integration
~~~~~~~~~~~~~~~~~~~~~~~~

**Focus Rewards**
   - Points for starting and completing focus sessions
   - Achievements for focus milestones
   - Streak tracking for regular focus practice
   - Dopamine activities for focus breaks

**Energy Management**
   - Pre-focus dopamine activities for motivation
   - Post-focus celebration and rewards
   - Energy level tracking throughout sessions
   - Adaptive break suggestions

Real-time Collaboration
~~~~~~~~~~~~~~~~~~~~~~

**Social Gamification**
   - Shared achievements in body doubling sessions
   - Group challenges and milestones
   - Encouragement system integration
   - Social streak support

**Motivation Sharing**
   - Share effective dopamine activities with session partners
   - Group motivation challenges
   - Collective achievement celebrations
   - Peer support for difficult days

Technical Implementation
-----------------------

Service Architecture
~~~~~~~~~~~~~~~~~~~

**GamificationService**
   Handles points, levels, and streak management:
   
   .. code-block:: python
   
      # Award points with context-aware multipliers
      award, level_up = await gamification_service.award_points(
          user_id=user_id,
          points=30,
          reason="Completed difficult task",
          multiplier=1.5,
          metadata={"energy_level": "low", "time_of_day": "morning"}
      )

**MotivationService**
   Manages dopamine menu and activity tracking:
   
   .. code-block:: python
   
      # Get personalized dopamine menu
      menu = await motivation_service.get_dopamine_menu(
          user_id=user_id,
          request=DopamineMenuRequest(
              energy_level="low",
              available_time=5,
              context="pre_task"
          )
      )

**AchievementService**
   Handles achievement rules and unlocking:
   
   .. code-block:: python
   
      # Check for new achievements
      achievements = await achievement_service.check_achievements(
          user_id=user_id,
          trigger_event="task_completed",
          event_data={"task_count": 10}
      )

Database Design
~~~~~~~~~~~~~~

**Efficient Storage**
   - Indexed foreign keys for fast lookups
   - JSONB fields for flexible metadata
   - Optimized queries with proper joins
   - Audit trails for all point awards

**Scalable Architecture**
   - Async/await throughout for performance
   - Background processing for analytics
   - Caching for frequently accessed data
   - Connection pooling for database efficiency

API Design
~~~~~~~~~

**RESTful Endpoints**
   - ``/gamification/*`` - Profile, stats, achievements, streaks
   - ``/motivation/*`` - Dopamine menu, activities, analytics
   - Comprehensive request/response validation
   - Detailed error handling and user feedback

**Real-time Updates**
   - WebSocket integration for live updates
   - Immediate feedback on user actions
   - Real-time achievement notifications
   - Live progress tracking

This gamification and motivation system provides the dopamine-driven engagement that ADHD users need to overcome task initiation barriers and maintain consistent productivity habits, all while working with the ADHD brain rather than against it.
