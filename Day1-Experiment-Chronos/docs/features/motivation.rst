Motivation & Dopamine Management
=================================

.. currentmodule:: app.services.motivation_service

Project Chronos includes sophisticated motivation management features designed specifically for ADHD users. The system addresses the core challenge of task initiation through dopamine-driven motivation strategies and personalized activity recommendations.

Dopamine Menu System
--------------------

Overview
~~~~~~~~

The dopamine menu is a curated list of quick, enjoyable activities designed to provide the dopamine boost needed to overcome task initiation barriers. This addresses one of the most significant challenges for ADHD users: getting started.

**Core Principle**: Provide immediate, accessible dopamine rewards to prime the brain for productivity.

Activity Categories
~~~~~~~~~~~~~~~~~~

**Movement Activities**
   Physical activities that boost energy and mood:
   
   - **5-Minute Walk**: Quick outdoor movement for fresh air and energy
   - **Stretching Routine**: Gentle movement to release tension
   - **Dance Break**: High-energy movement to favorite music
   - **Jumping Jacks**: Quick cardio burst for immediate energy
   - **Yoga Poses**: Mindful movement for centering and focus

**Sensory Activities**
   Activities that engage the senses for immediate pleasure:
   
   - **Favorite Song**: 3-5 minute mood boost through music
   - **Nature Viewing**: Look outside or at nature photos
   - **Texture Play**: Fidget toys, stress balls, or tactile objects
   - **Aromatherapy**: Quick scent-based mood enhancement
   - **Visual Stimulation**: Colorful art, patterns, or videos

**Mental Activities**
   Cognitive activities that calm and center:
   
   - **Deep Breathing**: 2-5 minute breathing exercises
   - **Meditation**: Brief mindfulness or guided meditation
   - **Positive Affirmations**: Self-encouragement and motivation
   - **Gratitude Practice**: Quick appreciation exercise
   - **Visualization**: Imagine successful task completion

**Creative Activities**
   Expressive activities that engage creativity:
   
   - **Quick Doodle**: 5-minute artistic expression
   - **Journal Entry**: Brief writing or reflection
   - **Photo Taking**: Capture something beautiful or interesting
   - **Voice Recording**: Express thoughts or feelings
   - **Color Therapy**: Adult coloring or color selection

**Social Activities**
   Connection-based activities for social dopamine:
   
   - **Text a Friend**: Quick social connection
   - **Pet Interaction**: Spend time with animals
   - **Social Media Check**: Brief positive social engagement
   - **Video Call**: Quick face-to-face connection
   - **Compliment Someone**: Spread positivity

Personalization Engine
~~~~~~~~~~~~~~~~~~~~~

**Energy Level Matching**
   Activities are filtered based on current energy state:
   
   .. code-block:: python
   
      # Low energy suggestions
      if user_energy == "low":
          suggested_activities = [
              "deep_breathing",
              "gentle_music",
              "nature_viewing",
              "pet_interaction"
          ]
      
      # High energy suggestions  
      elif user_energy == "high":
          suggested_activities = [
              "dance_break",
              "jumping_jacks", 
              "vigorous_walk",
              "energetic_music"
          ]

**Time Constraint Filtering**
   Activities matched to available time:
   
   - **1-2 minutes**: Deep breathing, look outside, quick affirmation
   - **3-5 minutes**: Favorite song, brief walk, simple stretch
   - **5-10 minutes**: Longer walk, creative activity, social connection
   - **10+ minutes**: Extended creative work, longer meditation, exercise

**Context Awareness**
   Suggestions adapt to user's environment and situation:
   
   - **Home**: Full range of activities including equipment-based ones
   - **Office**: Quiet, professional-appropriate activities
   - **Public**: Discrete activities that don't require privacy
   - **Limited Space**: Activities that don't require movement
   - **No Equipment**: Activities using only what's immediately available

**Learning Algorithm**
   The system continuously learns from user behavior:
   
   .. code-block:: python
   
      # Track activity effectiveness
      effectiveness_score = calculate_effectiveness(
          mood_before=3,
          mood_after=6,
          energy_before="low", 
          energy_after="medium",
          task_completion_rate=0.8
      )
      
      # Adjust future recommendations
      update_activity_weight(activity_id, effectiveness_score)

Custom Activity Creation
~~~~~~~~~~~~~~~~~~~~~~~

**User-Defined Activities**
   Users can create personalized dopamine activities:
   
   .. code-block:: python
   
      custom_activity = {
          "name": "Pet My Cat",
          "description": "Spend 3 minutes with my cat for instant mood boost",
          "category": "social",
          "duration": 3,
          "energy_requirement": "low",
          "energy_boost": "high",
          "personal_notes": "Works best when I'm feeling overwhelmed",
          "effectiveness_rating": 5
      }

**Activity Customization**
   - Personal instructions and modifications
   - Individual timing preferences
   - Effectiveness ratings and notes
   - Trigger conditions and contexts
   - Equipment or setup requirements

Motivation Analytics
-------------------

Pattern Recognition
~~~~~~~~~~~~~~~~~~

**Energy Pattern Analysis**
   The system identifies user's natural rhythms:
   
   - **Morning Energy**: Typically high, good for challenging activities
   - **Afternoon Dip**: Common low period, needs gentle activities
   - **Evening Patterns**: Variable, tracked individually
   - **Weekly Cycles**: Monday blues, Friday energy, weekend patterns
   - **Seasonal Variations**: Seasonal affective patterns

**Activity Effectiveness Tracking**
   Measures which activities work best for each user:
   
   .. code-block:: python
   
      effectiveness_metrics = {
          "mood_improvement": 2.3,  # Average mood increase
          "energy_boost": "medium",  # Typical energy change
          "completion_rate": 0.85,   # How often user completes activity
          "task_success_rate": 0.72, # Task completion after activity
          "optimal_timing": "morning", # Best time for this activity
          "context_effectiveness": {
              "pre_task": 0.8,
              "break": 0.9,
              "reward": 0.95
          }
      }

**Productivity Correlation**
   Links motivation activities to task completion success:
   
   - Activities that most improve task initiation
   - Optimal pre-task routine identification
   - Break activities that maintain momentum
   - Reward activities that sustain motivation

Personalized Insights
~~~~~~~~~~~~~~~~~~~~~

**AI-Generated Recommendations**
   The system provides intelligent insights:
   
   - "Movement activities work 40% better for you in the morning"
   - "You're most likely to complete tasks after listening to music"
   - "Creative activities help you recover from afternoon energy dips"
   - "Your 7-day streaks always include at least 3 dopamine activities"

**Trend Analysis**
   - Identifies improving or declining patterns
   - Suggests new activities to try
   - Warns of potential motivation challenges
   - Celebrates positive changes and growth

**Optimization Suggestions**
   - Recommends optimal timing for different activities
   - Suggests activity combinations that work well together
   - Identifies activities that may be losing effectiveness
   - Proposes new strategies based on successful patterns

ADHD-Specific Features
---------------------

Executive Function Support
~~~~~~~~~~~~~~~~~~~~~~~~~

**Decision Simplification**
   Reduces cognitive load in activity selection:
   
   - Maximum 5 activities shown at once
   - Clear, simple descriptions
   - Visual icons for quick recognition
   - Default "quick pick" option
   - One-tap activity start

**Working Memory Assistance**
   Supports memory challenges:
   
   - Recent activity history
   - Favorite activities quick access
   - Visual reminders of effective activities
   - Simple instructions and guidance
   - Progress tracking without complexity

**Attention Management**
   Designed for ADHD attention patterns:
   
   - Short activity durations (1-10 minutes)
   - Clear start and end points
   - Optional timer integration
   - Gentle transition back to tasks
   - No overwhelming choices or complexity

Emotional Regulation
~~~~~~~~~~~~~~~~~~~

**Mood Support**
   Activities specifically chosen for emotional regulation:
   
   - **Anxiety Reduction**: Breathing exercises, calming music
   - **Depression Support**: Movement, social connection, creativity
   - **Overwhelm Management**: Simplification, grounding activities
   - **Frustration Release**: Physical movement, expression activities
   - **Energy Regulation**: Matching activities to current state

**Stress Response**
   Special support for high-stress periods:
   
   - Emergency calm-down activities
   - Quick stress relief options
   - Grounding and centering exercises
   - Comfort activities for difficult days
   - Recovery activities after setbacks

**Rejection Sensitivity Support**
   Activities that build resilience and self-compassion:
   
   - Self-affirmation exercises
   - Comfort and self-care activities
   - Social connection without vulnerability
   - Achievement celebration activities
   - Confidence-building exercises

Hyperfocus Management
~~~~~~~~~~~~~~~~~~~~

**Hyperfocus Preparation**
   Activities that help channel hyperfocus productively:
   
   - Energy assessment before deep work
   - Environment preparation activities
   - Intention setting exercises
   - Resource gathering activities
   - Distraction elimination routines

**Hyperfocus Breaks**
   Gentle activities that don't break flow but provide necessary breaks:
   
   - Micro-movements (30 seconds)
   - Hydration reminders with pleasant activities
   - Eye rest with visual activities
   - Posture adjustment with stretching
   - Brief mindfulness check-ins

**Hyperfocus Recovery**
   Activities for transitioning out of hyperfocus:
   
   - Gentle re-engagement with surroundings
   - Social reconnection activities
   - Physical needs assessment
   - Emotional state check-in
   - Planning next steps activities

Integration Features
-------------------

Task Management Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Pre-Task Motivation**
   Automatic suggestions before difficult tasks:
   
   .. code-block:: python
   
      if task.energy_level == "high" and user.current_energy == "low":
          suggested_activities = get_energy_boosting_activities(
              duration=5,
              context="pre_task",
              user_preferences=user.dopamine_preferences
          )

**Task Completion Rewards**
   Celebration activities after task completion:
   
   - Achievement celebration activities
   - Reward activities based on task difficulty
   - Social sharing and recognition options
   - Progress visualization activities
   - Momentum-building activities for next tasks

**Break Activities**
   Structured break activities that maintain productivity:
   
   - Pomodoro break activities (5 minutes)
   - Longer break activities (15-30 minutes)
   - Transition activities between different types of tasks
   - Re-energizing activities for continued work
   - Mindful break activities for mental reset

Focus Session Integration
~~~~~~~~~~~~~~~~~~~~~~~~

**Pre-Focus Preparation**
   Activities that prepare the mind for focused work:
   
   - Intention setting exercises
   - Distraction clearing activities
   - Energy assessment and optimization
   - Environment preparation routines
   - Mindfulness and centering exercises

**Focus Break Activities**
   Carefully chosen activities that don't break concentration:
   
   - Gentle movement without overstimulation
   - Hydration with mindful drinking
   - Brief nature viewing or visual rest
   - Posture adjustment and stretching
   - Breathing exercises for continued focus

**Post-Focus Recovery**
   Activities for healthy transition out of focus:
   
   - Accomplishment celebration
   - Social reconnection if needed
   - Physical needs assessment
   - Energy level evaluation
   - Planning and reflection activities

Real-time Collaboration
~~~~~~~~~~~~~~~~~~~~~~

**Social Motivation**
   Shared motivation activities in body doubling sessions:
   
   - Group energizing activities
   - Shared break activities
   - Mutual encouragement exercises
   - Collective achievement celebrations
   - Peer support activities

**Motivation Sharing**
   Users can share effective activities with others:
   
   - Activity recommendations to session partners
   - Success story sharing
   - Group challenges and activities
   - Peer support for difficult periods
   - Collective motivation building

This comprehensive motivation system provides ADHD users with the dopamine-driven support they need to overcome task initiation barriers and maintain consistent productivity, all while building healthy coping strategies and self-awareness.
