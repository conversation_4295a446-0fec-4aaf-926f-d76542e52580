System Architecture Overview
============================

Project Chronos is designed as a comprehensive ADHD-optimized productivity platform with a microservices-inspired architecture that prioritizes user experience, reliability, and scalability while maintaining simplicity for neurodivergent users.

.. currentmodule:: app

High-Level Architecture
----------------------

The system follows a layered architecture pattern optimized for ADHD user needs:

.. mermaid::

   graph TB
       subgraph "Frontend Layer"
           UI[Web Interface]
           Mobile[Mobile App]
           PWA[Progressive Web App]
       end
       
       subgraph "API Gateway"
           Gateway[FastAPI Gateway]
           Auth[Authentication]
           RateLimit[Rate Limiting]
       end
       
       subgraph "Core Services"
           TaskService[Task Management]
           TimeService[Time Blocking]
           FocusService[Focus Sessions]
           NotificationService[Notifications]
           GamificationService[Gamification]
           BodyDoublingService[Body Doubling]
       end
       
       subgraph "Background Processing"
           Celery[Celery Workers]
           Redis[Redis Queue]
           Scheduler[Task Scheduler]
       end
       
       subgraph "Data Layer"
           PostgreSQL[(PostgreSQL)]
           FileStorage[File Storage]
           Cache[Redis Cache]
       end
       
       subgraph "External Integrations"
           Calendar[Calendar APIs]
           Email[Email Service]
           SMS[SMS Service]
           AI[AI Services]
       end
       
       UI --> Gateway
       Mobile --> Gateway
       PWA --> Gateway
       
       Gateway --> Auth
       Gateway --> RateLimit
       Gateway --> TaskService
       Gateway --> TimeService
       Gateway --> FocusService
       Gateway --> NotificationService
       Gateway --> GamificationService
       Gateway --> BodyDoublingService
       
       TaskService --> PostgreSQL
       TimeService --> PostgreSQL
       FocusService --> PostgreSQL
       NotificationService --> Celery
       GamificationService --> PostgreSQL
       BodyDoublingService --> PostgreSQL
       
       Celery --> Redis
       Celery --> PostgreSQL
       Scheduler --> Redis
       
       NotificationService --> Email
       NotificationService --> SMS
       TaskService --> AI
       TimeService --> Calendar

Core Design Principles
---------------------

ADHD-First Design
~~~~~~~~~~~~~~~~

Every architectural decision prioritizes ADHD user needs:

- **Cognitive Load Reduction**: Simple, predictable interfaces
- **Executive Function Support**: Structured workflows and clear next steps
- **Attention Management**: Minimal distractions and context preservation
- **Memory Assistance**: Persistent state and gentle reminders
- **Flexibility**: Accommodating attention fluctuations and changing needs

Reliability and Resilience
~~~~~~~~~~~~~~~~~~~~~~~~~

The system is designed to be dependable for users who rely on it:

- **Fault Tolerance**: Graceful degradation when services are unavailable
- **Data Consistency**: ACID transactions for critical user data
- **Background Processing**: Reliable task execution with retry logic
- **Monitoring**: Comprehensive health checks and alerting
- **Backup and Recovery**: Automated backups and disaster recovery

Scalability and Performance
~~~~~~~~~~~~~~~~~~~~~~~~~~

Architecture supports growth while maintaining responsiveness:

- **Horizontal Scaling**: Services can scale independently
- **Caching Strategy**: Multi-layer caching for optimal performance
- **Database Optimization**: Efficient queries and indexing
- **Async Processing**: Non-blocking operations for better UX
- **CDN Integration**: Fast content delivery globally

Service Architecture
-------------------

Core Services Overview
~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph LR
       subgraph "User Management"
           Auth[Authentication Service]
           Profile[User Profile Service]
       end
       
       subgraph "Productivity Core"
           Tasks[Task Management]
           Time[Time Blocking]
           Focus[Focus Sessions]
       end
       
       subgraph "Social & Motivation"
           Gamification[Gamification Engine]
           BodyDoubling[Body Doubling]
           Achievements[Achievement System]
       end
       
       subgraph "Communication"
           Notifications[Notification Service]
           Reminders[Reminder Engine]
           Escalation[Escalation System]
       end
       
       subgraph "Intelligence"
           AI[AI Task Processing]
           Analytics[Usage Analytics]
           Recommendations[Recommendation Engine]
       end
       
       Auth --> Profile
       Tasks --> AI
       Tasks --> Time
       Time --> Focus
       Focus --> Notifications
       Gamification --> Achievements
       BodyDoubling --> Notifications
       Notifications --> Reminders
       Reminders --> Escalation
       Analytics --> Recommendations

Data Flow Architecture
~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant User
       participant API
       participant Service
       participant Database
       participant Background
       participant External
       
       User->>API: Request (with auth)
       API->>API: Validate & authorize
       API->>Service: Process request
       Service->>Database: Query/update data
       Database-->>Service: Return data
       Service->>Background: Queue async tasks
       Service-->>API: Return response
       API-->>User: JSON response
       
       Background->>External: Send notifications
       Background->>Database: Update status
       External-->>Background: Delivery confirmation

Technology Stack
---------------

Backend Technologies
~~~~~~~~~~~~~~~~~~~

**Core Framework**
   - **FastAPI**: Modern, fast web framework with automatic API documentation
   - **Python 3.11+**: Latest Python features for optimal performance
   - **Pydantic**: Data validation and serialization with type hints
   - **SQLAlchemy 2.0**: Modern ORM with async support

**Database Layer**
   - **PostgreSQL 15+**: Primary database with JSONB support for flexibility
   - **Alembic**: Database migrations with version control
   - **asyncpg**: High-performance async PostgreSQL driver

**Background Processing**
   - **Celery**: Distributed task queue for background processing
   - **Redis**: Message broker and caching layer
   - **Celery Beat**: Periodic task scheduling

**Authentication & Security**
   - **JWT**: Stateless authentication with refresh tokens
   - **bcrypt**: Secure password hashing
   - **CORS**: Cross-origin resource sharing configuration
   - **Rate Limiting**: API protection against abuse

Development & Deployment
~~~~~~~~~~~~~~~~~~~~~~~

**Development Tools**
   - **Poetry**: Dependency management and packaging
   - **Black**: Code formatting for consistency
   - **isort**: Import sorting and organization
   - **mypy**: Static type checking
   - **pytest**: Comprehensive testing framework

**Documentation**
   - **Sphinx**: Documentation generation
   - **Mermaid**: Diagram generation for visual documentation
   - **OpenAPI**: Automatic API documentation

**Deployment**
   - **Docker**: Containerization for consistent environments
   - **Docker Compose**: Local development orchestration
   - **GitHub Actions**: CI/CD pipeline automation

Database Design
--------------

Entity Relationship Overview
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   erDiagram
       User ||--o{ Task : creates
       User ||--o{ TimeBlock : schedules
       User ||--o{ FocusSession : conducts
       User ||--o{ Notification : receives
       User ||--o{ Achievement : earns
       User ||--|| UserProfile : has
       User ||--|| NotificationPreference : configures
       User ||--|| SchedulingPreference : sets
       
       Task ||--o{ TimeBlock : scheduled_in
       Task ||--o{ FocusSession : worked_on
       Task ||--o{ Notification : triggers
       Task ||--o{ TaskChunk : broken_into
       
       FocusSession ||--o{ FocusBreak : includes
       FocusSession ||--o{ Notification : generates
       FocusSession }|--|| FocusMode : uses
       
       TimeBlock ||--o{ Notification : reminds
       
       BodyDoublingSession ||--o{ BodyDoublingParticipant : includes
       User ||--o{ BodyDoublingParticipant : participates
       
       Achievement }|--|| AchievementType : categorized_by
       User ||--o{ UserAchievement : unlocks

Core Data Models
~~~~~~~~~~~~~~~

**User-Centric Design**
   All data models are designed around the user as the central entity:
   
   - **User**: Central identity with ADHD-specific preferences
   - **UserProfile**: Extended user information and settings
   - **Preferences**: Notification, scheduling, and UI preferences

**Productivity Models**
   Core productivity features with ADHD optimizations:
   
   - **Task**: Flexible task management with AI chunking
   - **TimeBlock**: Visual time management with buffer times
   - **FocusSession**: Attention-aware focus tracking

**Social and Motivation**
   Features that leverage social accountability and gamification:
   
   - **BodyDoublingSession**: Virtual co-working sessions
   - **Achievement**: Dopamine-driven progress recognition
   - **Gamification**: Points, levels, and motivation systems

Security Architecture
--------------------

Authentication Flow
~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Client
       participant API
       participant Auth
       participant Database
       participant Redis
       
       Client->>API: Login request
       API->>Auth: Validate credentials
       Auth->>Database: Check user credentials
       Database-->>Auth: User data
       Auth->>Auth: Generate JWT tokens
       Auth->>Redis: Store refresh token
       Auth-->>API: Access & refresh tokens
       API-->>Client: Authentication response
       
       Note over Client,Redis: Subsequent requests
       Client->>API: Request with access token
       API->>Auth: Validate token
       Auth-->>API: Token valid
       API->>API: Process request
       API-->>Client: Response

Security Measures
~~~~~~~~~~~~~~~~

**Data Protection**
   - **Encryption at Rest**: Database encryption for sensitive data
   - **Encryption in Transit**: TLS 1.3 for all communications
   - **Password Security**: bcrypt hashing with salt
   - **Token Security**: Short-lived access tokens with refresh rotation

**Access Control**
   - **Role-Based Access**: User roles and permissions
   - **Resource Isolation**: Users can only access their own data
   - **API Rate Limiting**: Protection against abuse
   - **CORS Configuration**: Controlled cross-origin access

**Privacy by Design**
   - **Data Minimization**: Collect only necessary information
   - **User Consent**: Clear consent for data usage
   - **Data Retention**: Automatic cleanup of old data
   - **Export/Delete**: User control over their data

Monitoring and Observability
---------------------------

Health Monitoring
~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Application Metrics"
           API[API Response Times]
           DB[Database Performance]
           Queue[Queue Lengths]
           Workers[Worker Health]
       end
       
       subgraph "Business Metrics"
           Users[Active Users]
           Tasks[Task Completion]
           Focus[Focus Sessions]
           Notifications[Notification Delivery]
       end
       
       subgraph "Infrastructure"
           CPU[CPU Usage]
           Memory[Memory Usage]
           Disk[Disk Space]
           Network[Network I/O]
       end
       
       subgraph "Alerting"
           Slack[Slack Notifications]
           Email[Email Alerts]
           PagerDuty[PagerDuty Integration]
       end
       
       API --> Alerting
       DB --> Alerting
       Queue --> Alerting
       Workers --> Alerting
       CPU --> Alerting
       Memory --> Alerting

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~

**Database Optimization**
   - **Query Optimization**: Efficient queries with proper indexing
   - **Connection Pooling**: Optimal database connection management
   - **Read Replicas**: Separate read/write workloads
   - **Caching Strategy**: Multi-layer caching for frequently accessed data

**API Performance**
   - **Async Processing**: Non-blocking request handling
   - **Response Compression**: Gzip compression for API responses
   - **Pagination**: Efficient data loading for large datasets
   - **Background Tasks**: Offload heavy processing to background workers

Deployment Architecture
----------------------

Production Environment
~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Load Balancer"
           LB[Load Balancer]
       end
       
       subgraph "Application Tier"
           API1[API Server 1]
           API2[API Server 2]
           API3[API Server 3]
       end
       
       subgraph "Background Processing"
           Worker1[Celery Worker 1]
           Worker2[Celery Worker 2]
           Beat[Celery Beat Scheduler]
       end
       
       subgraph "Data Tier"
           Primary[(PostgreSQL Primary)]
           Replica[(PostgreSQL Replica)]
           RedisCluster[Redis Cluster]
       end
       
       subgraph "External Services"
           Email[Email Service]
           SMS[SMS Service]
           Storage[File Storage]
       end
       
       LB --> API1
       LB --> API2
       LB --> API3
       
       API1 --> Primary
       API2 --> Primary
       API3 --> Primary
       
       API1 --> Replica
       API2 --> Replica
       API3 --> Replica
       
       Worker1 --> Primary
       Worker2 --> Primary
       Beat --> Primary
       
       API1 --> RedisCluster
       API2 --> RedisCluster
       API3 --> RedisCluster
       Worker1 --> RedisCluster
       Worker2 --> RedisCluster
       
       Worker1 --> Email
       Worker1 --> SMS
       Worker2 --> Email
       Worker2 --> SMS

Scalability Considerations
~~~~~~~~~~~~~~~~~~~~~~~~~

**Horizontal Scaling**
   - **Stateless Services**: All services designed to be stateless
   - **Load Balancing**: Distribute traffic across multiple instances
   - **Database Sharding**: Partition data for improved performance
   - **Microservices Ready**: Architecture supports service separation

**Caching Strategy**
   - **Application Cache**: In-memory caching for frequently accessed data
   - **Database Query Cache**: Cache expensive database queries
   - **CDN**: Content delivery network for static assets
   - **Session Storage**: Redis-based session management

This architecture provides a solid foundation for an ADHD-optimized productivity platform that can scale while maintaining the reliability and user experience that neurodivergent users depend on.
