WebSocket Architecture
======================

This document details the WebSocket architecture for Project Chronos, specifically focusing on Agent 6's real-time communication infrastructure.

Overview
--------

The WebSocket architecture enables real-time communication for virtual body doubling sessions, live progress sharing, and synchronized focus sessions. It's designed to support thousands of concurrent connections while maintaining low latency and high reliability.

Architecture Components
-----------------------

System Architecture
~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Client Layer"
           WEB[Web Clients]
           MOBILE[Mobile Clients]
           PWA[PWA Clients]
       end
       
       subgraph "Load Balancer"
           LB[Nginx Load Balancer]
           STICKY[Sticky Sessions]
       end
       
       subgraph "WebSocket Servers"
           WS1[WebSocket Server 1]
           WS2[WebSocket Server 2]
           WS3[WebSocket Server N]
       end
       
       subgraph "Connection Management"
           WSM[WebSocket Manager]
           AUTH[WebSocket Auth]
           POOL[Connection Pool]
       end
       
       subgraph "Message Processing"
           ROUTER[Message Router]
           HANDLER[Message Handlers]
           VALIDATOR[Message Validator]
       end
       
       subgraph "State Management"
           REDIS[(Redis Cluster)]
           PUBSUB[Redis Pub/Sub]
           SESSIONS[Session Store]
       end
       
       subgraph "Persistence"
           PG[(PostgreSQL)]
           QUEUE[Message Queue]
       end
       
       WEB --> LB
       MOBILE --> LB
       PWA --> LB
       LB --> STICKY
       STICKY --> WS1
       STICKY --> WS2
       STICKY --> WS3
       
       WS1 --> WSM
       WS2 --> WSM
       WS3 --> WSM
       
       WSM --> AUTH
       WSM --> POOL
       WSM --> ROUTER
       
       ROUTER --> HANDLER
       ROUTER --> VALIDATOR
       
       HANDLER --> REDIS
       HANDLER --> PUBSUB
       HANDLER --> SESSIONS
       HANDLER --> PG
       HANDLER --> QUEUE

Connection Management
--------------------

Connection Lifecycle
~~~~~~~~~~~~~~~~~~~

.. mermaid::

   stateDiagram-v2
       [*] --> Connecting
       Connecting --> Authenticating : WebSocket Handshake
       Authenticating --> Connected : Valid JWT Token
       Authenticating --> Rejected : Invalid Token
       Connected --> Active : Join Session
       Active --> Idle : No Activity
       Idle --> Active : Send/Receive Message
       Active --> Disconnecting : Client Disconnect
       Idle --> Disconnecting : Timeout
       Connected --> Disconnecting : Leave All Sessions
       Disconnecting --> [*] : Cleanup Complete
       Rejected --> [*] : Connection Closed

Connection Flow
~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Client
       participant LoadBalancer
       participant WSServer
       participant WSManager
       participant Auth
       participant Redis
       participant Database
       
       Client->>LoadBalancer: WebSocket Connect
       LoadBalancer->>WSServer: Route to Server
       WSServer->>WSManager: Handle Connection
       WSManager->>Auth: Validate JWT Token
       Auth->>Database: Verify User
       Database-->>Auth: User Valid
       Auth-->>WSManager: Authentication Success
       WSManager->>Redis: Store Connection Metadata
       WSManager->>WSServer: Connection Established
       WSServer-->>Client: WebSocket Connected
       
       Note over Client,Database: Connection Active
       
       Client->>WSServer: Heartbeat
       WSServer-->>Client: Heartbeat Response
       
       Client->>WSServer: Disconnect
       WSServer->>WSManager: Handle Disconnect
       WSManager->>Redis: Remove Connection
       WSManager->>Database: Update User Status
       WSManager->>WSServer: Cleanup Complete

Authentication & Authorization
-----------------------------

WebSocket Authentication
~~~~~~~~~~~~~~~~~~~~~~

WebSocket connections use JWT tokens for authentication, passed either as:

1. **Query Parameter**: ``wss://api.chronos.dev/ws?token=jwt_token``
2. **Header**: ``Authorization: Bearer jwt_token``
3. **Subprotocol**: ``Sec-WebSocket-Protocol: access_token, jwt_token``

Authentication Flow
~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Client
       participant WSEndpoint
       participant AuthMiddleware
       participant JWTService
       participant UserService
       participant Database
       
       Client->>WSEndpoint: Connect with JWT
       WSEndpoint->>AuthMiddleware: Validate Token
       AuthMiddleware->>JWTService: Decode JWT
       JWTService->>JWTService: Verify Signature
       JWTService->>JWTService: Check Expiration
       JWTService-->>AuthMiddleware: Token Valid
       AuthMiddleware->>UserService: Get User Info
       UserService->>Database: Query User
       Database-->>UserService: User Data
       UserService-->>AuthMiddleware: User Object
       AuthMiddleware-->>WSEndpoint: Authenticated User
       WSEndpoint-->>Client: Connection Accepted

Session-Based Authorization
~~~~~~~~~~~~~~~~~~~~~~~~~

Once authenticated, users are authorized to join specific body doubling sessions based on:

- **Public Sessions**: Open to all authenticated users
- **Private Sessions**: Invitation-only or password-protected
- **Host Permissions**: Session creators have administrative rights
- **Participant Limits**: Maximum capacity enforcement

Message Processing
-----------------

Message Types & Routing
~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Incoming Messages"
           JOIN[join_session]
           LEAVE[leave_session]
           CHAT[chat_message]
           PROGRESS[task_progress]
           ENCOURAGE[encouragement]
           STATUS[status_update]
           HEARTBEAT[heartbeat]
       end
       
       subgraph "Message Router"
           ROUTER[Message Router]
           VALIDATOR[Schema Validator]
           RATE_LIMITER[Rate Limiter]
       end
       
       subgraph "Message Handlers"
           SESSION_HANDLER[Session Handler]
           CHAT_HANDLER[Chat Handler]
           PROGRESS_HANDLER[Progress Handler]
           STATUS_HANDLER[Status Handler]
           SYSTEM_HANDLER[System Handler]
       end
       
       subgraph "Broadcasting"
           BROADCAST[Broadcast Service]
           REDIS_PUB[Redis Publisher]
           REDIS_SUB[Redis Subscriber]
       end
       
       JOIN --> ROUTER
       LEAVE --> ROUTER
       CHAT --> ROUTER
       PROGRESS --> ROUTER
       ENCOURAGE --> ROUTER
       STATUS --> ROUTER
       HEARTBEAT --> ROUTER
       
       ROUTER --> VALIDATOR
       VALIDATOR --> RATE_LIMITER
       RATE_LIMITER --> SESSION_HANDLER
       RATE_LIMITER --> CHAT_HANDLER
       RATE_LIMITER --> PROGRESS_HANDLER
       RATE_LIMITER --> STATUS_HANDLER
       RATE_LIMITER --> SYSTEM_HANDLER
       
       SESSION_HANDLER --> BROADCAST
       CHAT_HANDLER --> BROADCAST
       PROGRESS_HANDLER --> BROADCAST
       STATUS_HANDLER --> BROADCAST
       
       BROADCAST --> REDIS_PUB
       REDIS_PUB --> REDIS_SUB
       REDIS_SUB --> BROADCAST

Message Processing Flow
~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Client
       participant WSServer
       participant MessageRouter
       participant Handler
       participant Validator
       participant RateLimiter
       participant BroadcastService
       participant Redis
       participant Database
       
       Client->>WSServer: Send Message
       WSServer->>MessageRouter: Route Message
       MessageRouter->>Validator: Validate Schema
       Validator->>RateLimiter: Check Rate Limits
       RateLimiter->>Handler: Process Message
       Handler->>Database: Store Message (if needed)
       Handler->>BroadcastService: Broadcast to Session
       BroadcastService->>Redis: Publish Message
       Redis->>BroadcastService: Deliver to Subscribers
       BroadcastService->>WSServer: Send to Clients
       WSServer->>Client: Message Delivered

State Management
---------------

Redis-Based State Store
~~~~~~~~~~~~~~~~~~~~~~

The WebSocket system uses Redis for distributed state management:

**Connection Registry**:
```redis
connections:user:{user_id} = {
    "connection_id": "uuid",
    "server_id": "ws-server-1",
    "connected_at": "2024-01-01T12:00:00Z",
    "last_activity": "2024-01-01T12:05:00Z",
    "status": "active"
}
```

**Session Participants**:
```redis
session:{session_id}:participants = {
    "user_1": {"status": "active", "joined_at": "..."},
    "user_2": {"status": "focused", "joined_at": "..."}
}
```

**Active Sessions**:
```redis
sessions:active = {
    "session_1": {"host": "user_1", "participants": 3},
    "session_2": {"host": "user_2", "participants": 2}
}
```

State Synchronization
~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Server1 as WS Server 1
       participant Server2 as WS Server 2
       participant Redis
       participant Database
       
       Note over Server1,Database: User joins session on Server 1
       
       Server1->>Redis: Update session participants
       Server1->>Redis: Publish join event
       Redis->>Server2: Deliver join event
       Server2->>Server2: Update local state
       
       Note over Server1,Database: User updates status on Server 2
       
       Server2->>Redis: Update user status
       Server2->>Redis: Publish status event
       Redis->>Server1: Deliver status event
       Server1->>Server1: Update local state
       
       Note over Server1,Database: Periodic state sync
       
       Server1->>Database: Persist session state
       Server2->>Database: Persist user activity

Scalability & Performance
-------------------------

Horizontal Scaling
~~~~~~~~~~~~~~~~~

The WebSocket architecture supports horizontal scaling through:

**Load Balancing**:
- Sticky sessions to maintain connection affinity
- Health checks for automatic failover
- Dynamic server pool management

**State Distribution**:
- Redis cluster for distributed state
- Pub/Sub for cross-server communication
- Consistent hashing for session distribution

**Connection Distribution**:
- Round-robin with session affinity
- Server capacity-based routing
- Geographic distribution support

Performance Optimizations
~~~~~~~~~~~~~~~~~~~~~~~~

**Connection Pooling**:
- Efficient connection management
- Connection reuse and recycling
- Memory-efficient data structures

**Message Batching**:
- Batch multiple messages for efficiency
- Compression for large message payloads
- Priority queuing for critical messages

**Caching Strategy**:
- Connection metadata caching
- Session state caching
- User preference caching

Monitoring & Observability
--------------------------

Key Metrics
~~~~~~~~~~

**Connection Metrics**:
- Active connections per server
- Connection establishment rate
- Connection duration distribution
- Authentication success/failure rates

**Message Metrics**:
- Messages per second (send/receive)
- Message processing latency
- Message delivery success rate
- Queue depth and processing time

**Session Metrics**:
- Active sessions count
- Session duration distribution
- Participant count per session
- Session creation/completion rates

**Performance Metrics**:
- CPU and memory usage per server
- Redis operation latency
- Database query performance
- Network bandwidth utilization

Health Checks
~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Health Check Types"
           LIVENESS[Liveness Check]
           READINESS[Readiness Check]
           STARTUP[Startup Check]
       end
       
       subgraph "Check Components"
           WS_HEALTH[WebSocket Health]
           REDIS_HEALTH[Redis Health]
           DB_HEALTH[Database Health]
           AUTH_HEALTH[Auth Service Health]
       end
       
       subgraph "Actions"
           RESTART[Restart Container]
           REMOVE_LB[Remove from Load Balancer]
           ALERT[Send Alert]
       end
       
       LIVENESS --> WS_HEALTH
       READINESS --> REDIS_HEALTH
       READINESS --> DB_HEALTH
       STARTUP --> AUTH_HEALTH
       
       WS_HEALTH --> RESTART
       REDIS_HEALTH --> REMOVE_LB
       DB_HEALTH --> REMOVE_LB
       AUTH_HEALTH --> ALERT

Error Handling & Recovery
-------------------------

Connection Recovery
~~~~~~~~~~~~~~~~~

**Client-Side Recovery**:
- Automatic reconnection with exponential backoff
- Message queue for offline messages
- State synchronization on reconnection

**Server-Side Recovery**:
- Graceful connection cleanup
- Session state preservation
- Automatic failover to healthy servers

Error Types & Handling
~~~~~~~~~~~~~~~~~~~~~

**Authentication Errors**:
- Invalid or expired JWT tokens
- User account disabled/suspended
- Rate limiting violations

**Session Errors**:
- Session not found or expired
- Maximum participants reached
- Permission denied for private sessions

**Message Errors**:
- Invalid message format
- Message size limits exceeded
- Unsupported message types

**System Errors**:
- Redis connection failures
- Database connectivity issues
- Server overload conditions

Security Considerations
----------------------

Rate Limiting
~~~~~~~~~~~~

**Connection Rate Limiting**:
- Maximum connections per IP address
- Maximum connections per user
- Connection establishment rate limits

**Message Rate Limiting**:
- Messages per second per connection
- Message size limits
- Burst capacity with token bucket algorithm

**Session Rate Limiting**:
- Session creation rate per user
- Maximum sessions per user
- Session join rate limits

Input Validation
~~~~~~~~~~~~~~~

**Message Validation**:
- JSON schema validation
- Content length limits
- Sanitization of user input

**Session Validation**:
- Session ID format validation
- Permission checks for session access
- Participant limit enforcement

**Authentication Validation**:
- JWT signature verification
- Token expiration checks
- User status validation

This WebSocket architecture provides a robust, scalable foundation for real-time communication in Project Chronos, specifically designed to support the social accountability features that are crucial for ADHD users.
