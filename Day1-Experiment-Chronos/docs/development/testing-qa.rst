Testing & Quality Assurance
============================

Project Chronos implements comprehensive testing and quality assurance practices specifically designed to ensure ADHD accommodations work reliably and effectively for neurodivergent users.

.. currentmodule:: tests

Testing Philosophy
-----------------

ADHD-Centered Testing Approach
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Our testing strategy prioritizes the unique needs of ADHD users:

**Reliability First**
   ADHD users depend on consistent, predictable behavior from productivity tools. Our tests ensure that accommodations work reliably across all scenarios.

**User Experience Validation**
   Every ADHD-specific feature is tested from the user's perspective, ensuring that accommodations actually improve the experience rather than adding complexity.

**Performance Assurance**
   ADHD users have limited attention spans and low tolerance for slow interfaces. Performance tests ensure response times meet neurodivergent needs.

**Accessibility Compliance**
   Comprehensive accessibility testing ensures the platform works for users with various ADHD presentations and comorbid conditions.

Test Architecture
----------------

Test Categories
~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Test Pyramid"
           Unit[Unit Tests<br/>90%+ Coverage]
           Integration[Integration Tests<br/>Service Interactions]
           E2E[End-to-End Tests<br/>User Workflows]
       end
       
       subgraph "ADHD-Specific Tests"
           Features[ADHD Feature Tests<br/>Accommodation Validation]
           Performance[Performance Tests<br/>Response Time Validation]
           Accessibility[Accessibility Tests<br/>Neurodivergent UX]
       end
       
       subgraph "Quality Gates"
           Security[Security Scanning<br/>Vulnerability Assessment]
           Code[Code Quality<br/>Standards Compliance]
           Docs[Documentation<br/>Completeness Check]
       end
       
       Unit --> Integration
       Integration --> E2E
       Features --> Performance
       Performance --> Accessibility
       E2E --> Security
       Security --> Code
       Code --> Docs

Test Organization
~~~~~~~~~~~~~~~~

**Unit Tests** (``tests/unit/``)
   - Core business logic validation
   - ADHD-specific algorithms (chunking, energy detection)
   - Service layer functionality
   - Model behavior and validation

**Integration Tests** (``tests/integration/``)
   - External service integrations (Google Calendar, Todoist, etc.)
   - Database interactions
   - API endpoint functionality
   - Authentication and authorization

**ADHD Feature Tests** (``tests/unit/test_adhd_features.py``)
   - AI-powered task chunking validation
   - Energy-aware scheduling algorithms
   - Time blindness support features
   - Gentle error handling and recovery

**Performance Tests** (``tests/performance/``)
   - Response time validation for ADHD attention spans
   - Memory usage optimization
   - Concurrent user handling
   - Real-time update latency

**Accessibility Tests** (``tests/accessibility/``)
   - Visual accessibility for ADHD users
   - Cognitive load reduction validation
   - Sensory consideration compliance
   - Keyboard navigation support

**End-to-End Tests** (``tests/e2e/``)
   - Complete user workflow validation
   - Morning routine workflows
   - Task management scenarios
   - Focus session workflows
   - Integration workflows

Running Tests
------------

Quick Test Execution
~~~~~~~~~~~~~~~~~~~

**Run All Tests**::

   # Comprehensive test suite
   python scripts/test_runner.py
   
   # Quick test run (skip slow tests)
   python scripts/test_runner.py --skip-slow

**Run Specific Test Categories**::

   # Unit tests only
   python scripts/test_runner.py --suite unit
   
   # ADHD feature tests
   python scripts/test_runner.py --suite adhd_features
   
   # Performance tests
   python scripts/test_runner.py --suite performance

**Generate Test Report**::

   python scripts/test_runner.py --report test-results.json

Using Poetry Directly
~~~~~~~~~~~~~~~~~~~~~

**Unit Tests with Coverage**::

   poetry run pytest tests/unit/ -v \
     --cov=app \
     --cov-report=html \
     --cov-report=term-missing \
     --cov-fail-under=90

**ADHD-Specific Tests**::

   poetry run pytest tests/ -v \
     -m "adhd_feature" \
     --tb=short

**Integration Tests**::

   poetry run pytest tests/integration/ -v \
     -m "integration and not slow"

**Performance Tests**::

   poetry run pytest tests/performance/ -v \
     -m "performance" \
     --durations=10

**Accessibility Tests**::

   poetry run pytest tests/accessibility/ -v \
     -m "accessibility"

**End-to-End Tests**::

   poetry run pytest tests/e2e/ -v \
     -m "e2e" \
     --tb=short

ADHD-Specific Test Features
--------------------------

AI Chunking Validation
~~~~~~~~~~~~~~~~~~~~~

Tests ensure AI-powered task chunking produces ADHD-friendly results:

.. code-block:: python

   async def test_chunk_large_task_creates_manageable_pieces(self, db_session, adhd_test_utils):
       """Test that large tasks are broken into ADHD-friendly chunks."""
       # Create large complex task
       large_task = await TaskFactory.create(
           db_session,
           estimated_duration=240,  # 4 hours
           complexity="high"
       )
       
       # Test chunking
       chunks = await task_service.chunk_task(user_id, large_task.id)
       
       # Validate ADHD-friendly chunking
       adhd_test_utils.assert_chunking_quality(chunks, large_task)
       assert all(chunk["estimated_duration"] <= 75 for chunk in chunks)

Energy Pattern Testing
~~~~~~~~~~~~~~~~~~~~~

Validates energy-aware scheduling algorithms:

.. code-block:: python

   async def test_schedule_respects_energy_patterns(self, db_session, adhd_test_utils):
       """Test that scheduling respects user energy patterns."""
       user = await UserFactory.create(
           preferences={
               "energy_patterns": {
                   "morning": "high",
                   "afternoon": "medium", 
                   "evening": "low"
               }
           }
       )
       
       # Test high-energy task scheduling
       suggestions = await time_block_service.suggest_time_slots(
           user_id=user.id,
           task_energy="high"
       )
       
       # Should suggest morning slots for high-energy tasks
       morning_suggestions = [s for s in suggestions if 6 <= s["start_time"].hour <= 11]
       assert len(morning_suggestions) > 0

Performance Requirements
~~~~~~~~~~~~~~~~~~~~~~~

ADHD users have specific performance needs that are validated:

.. code-block:: python

   async def test_task_creation_response_time(self, client, authenticated_user):
       """Test task creation responds within ADHD-friendly timeframe."""
       start_time = time.time()
       
       response = await client.post("/api/v1/tasks", ...)
       
       end_time = time.time()
       response_time = end_time - start_time
       
       # ADHD users need quick responses to maintain momentum
       assert response_time < 0.5, f"Task creation took {response_time:.3f}s, should be < 0.5s"

Accessibility Validation
~~~~~~~~~~~~~~~~~~~~~~~

Ensures visual and cognitive accessibility for ADHD users:

.. code-block:: python

   async def test_color_coding_consistency(self, db_session):
       """Test consistent color coding across the application."""
       user = await UserFactory.create(
           preferences={
               "visual_preferences": {
                   "energy_colors": {
                       "low": "#10B981",    # Green
                       "medium": "#F59E0B", # Amber  
                       "high": "#EF4444"   # Red
                   }
               }
           }
       )
       
       tasks = await task_service.get_user_tasks(
           user_id=user.id,
           include_visual_metadata=True
       )
       
       # Verify consistent color coding
       for task in tasks:
           expected_color = user.preferences["visual_preferences"]["energy_colors"][task.energy_level]
           assert task.visual_metadata["color"] == expected_color
           assert task.visual_metadata["contrast_ratio"] >= 4.5  # WCAG AA compliance

Quality Assurance Tools
----------------------

Automated Quality Checks
~~~~~~~~~~~~~~~~~~~~~~~~

**Code Quality Script**::

   python scripts/quality_check.py
   
   # Quick check (essential only)
   python scripts/quality_check.py --quick
   
   # Verbose output with details
   python scripts/quality_check.py --verbose
   
   # Save detailed report
   python scripts/quality_check.py --report quality-report.json

**Deployment Readiness**::

   python scripts/deployment_readiness.py
   
   # Save readiness report
   python scripts/deployment_readiness.py --report deployment-report.json

CI/CD Pipeline
~~~~~~~~~~~~~

The automated CI/CD pipeline (`.github/workflows/comprehensive-ci.yml`) runs:

**Code Quality Checks**
   - Black formatting validation
   - Flake8 linting
   - MyPy type checking
   - Docstring validation
   - Security scanning (Bandit)
   - Dependency vulnerability checks (Safety)

**Test Execution**
   - Unit tests with 90%+ coverage requirement
   - Integration tests for external services
   - ADHD-specific feature validation
   - Performance benchmarking
   - Accessibility compliance testing
   - End-to-end workflow validation

**Quality Gates**
   - All tests must pass
   - Coverage threshold must be met
   - No high-severity security issues
   - ADHD feature compliance validated
   - Performance requirements met

Test Data and Fixtures
---------------------

ADHD-Focused Test Factories
~~~~~~~~~~~~~~~~~~~~~~~~~~

Specialized factories create realistic test data for ADHD scenarios:

.. code-block:: python

   # Create ADHD user with specific preferences
   user = await UserFactory.create(
       adhd_diagnosed=True,
       preferences={
           "energy_patterns": {"morning": "high", "afternoon": "medium", "evening": "low"},
           "chunking_preferences": {"default_size": "small", "max_subtasks": 5},
           "notification_preferences": {"gentle": True, "persistent": True}
       }
   )
   
   # Create complex task for chunking tests
   task = await TaskFactory.create(
       user_id=user.id,
       estimated_duration=240,  # 4 hours - needs chunking
       complexity="high",
       energy_level="high"
   )
   
   # Create time blocks with ADHD features
   time_block = await TimeBlockFactory.create(
       user_id=user.id,
       buffer_before=10,  # ADHD transition time
       buffer_after=5,
       energy_level="high"
   )

Test Utilities
~~~~~~~~~~~~~

ADHD-specific test utilities validate neurodivergent accommodations:

.. code-block:: python

   class ADHDTestUtils:
       @staticmethod
       def assert_adhd_friendly_response(response_data):
           """Assert response follows ADHD-friendly patterns."""
           if "error" in response_data:
               error = response_data["error"]
               assert "suggestions" in error
               assert not any(word in error["message"].lower() 
                            for word in ["failed", "invalid", "wrong"])
       
       @staticmethod
       def assert_chunking_quality(chunks, original_task):
           """Assert AI chunking produces quality results."""
           assert 3 <= len(chunks) <= 7  # Appropriate number of chunks
           
           total_estimated = sum(chunk.get("estimated_duration", 0) for chunk in chunks)
           original_duration = original_task.get("estimated_duration", 0)
           
           # Allow 20% variance in total time estimation
           assert abs(total_estimated - original_duration) <= original_duration * 0.2

Continuous Quality Improvement
-----------------------------

Test Metrics and Monitoring
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Coverage Tracking**
   - Minimum 90% code coverage required
   - ADHD feature coverage separately tracked
   - Integration test coverage monitored

**Performance Benchmarking**
   - Response time trends tracked
   - Memory usage patterns monitored
   - Concurrent user capacity validated

**User Experience Validation**
   - ADHD workflow completion rates
   - Error recovery success rates
   - Feature adoption metrics

**Quality Trends**
   - Test execution time optimization
   - Flaky test identification and resolution
   - Security vulnerability trend analysis

Best Practices
~~~~~~~~~~~~~

**Writing ADHD-Focused Tests**
   1. Test from the user's perspective
   2. Validate accommodations actually help
   3. Ensure consistent, predictable behavior
   4. Test error scenarios with gentle handling
   5. Validate performance meets ADHD needs

**Test Maintenance**
   1. Keep tests fast and reliable
   2. Use descriptive test names
   3. Maintain test data factories
   4. Regular test suite optimization
   5. Continuous integration monitoring

**Quality Assurance**
   1. Automated quality gates in CI/CD
   2. Regular security scanning
   3. Performance regression testing
   4. Accessibility compliance validation
   5. Documentation completeness checks

This comprehensive testing and quality assurance framework ensures that Project Chronos reliably delivers the ADHD accommodations that neurodivergent users depend on for productivity and well-being.
