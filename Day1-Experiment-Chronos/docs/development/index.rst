Development Guide
=================

This section provides comprehensive development documentation for Project Chronos.

.. toctree::
   :maxdepth: 2
   :caption: Development:

   setup
   agent6-development
   gamification-development
   integration-development
   testing
   testing-qa
   contributing
   code-style

Development Environment Setup
-----------------------------

Prerequisites
~~~~~~~~~~~~

- **Python**: 3.11 or higher
- **Node.js**: 18 or higher (for frontend development)
- **Docker**: Latest version with Docker Compose
- **PostgreSQL**: 15 or higher (or use Docker)
- **Redis**: 7 or higher (or use Docker)

Quick Start with Docker
~~~~~~~~~~~~~~~~~~~~~~

1. **Clone the repository**:

   .. code-block:: bash

      git clone https://github.com/forkrul/day1-idea.git
      cd day1-idea

2. **Copy environment file**:

   .. code-block:: bash

      cp .env.example .env

3. **Start services with Dock<PERSON> Compose**:

   .. code-block:: bash

      docker-compose up -d

4. **Run database migrations**:

   .. code-block:: bash

      docker-compose exec api alembic upgrade head

5. **Access the application**:

   - API: http://localhost:8000
   - API Docs: http://localhost:8000/docs
   - WebSocket: ws://localhost:8000/ws

Local Development Setup
~~~~~~~~~~~~~~~~~~~~~~

1. **Install Python dependencies**:

   .. code-block:: bash

      pip install -r requirements.txt
      # Or with Poetry
      poetry install

2. **Set up environment variables**:

   .. code-block:: bash

      export DATABASE_URL="postgresql+asyncpg://chronos:chronos_password@localhost:5432/chronos"
      export REDIS_URL="redis://localhost:6379"
      export SECRET_KEY="your-secret-key"

3. **Start PostgreSQL and Redis**:

   .. code-block:: bash

      # Using Docker
      docker run -d --name chronos-postgres -p 5432:5432 \
        -e POSTGRES_USER=chronos \
        -e POSTGRES_PASSWORD=chronos_password \
        -e POSTGRES_DB=chronos \
        postgres:15

      docker run -d --name chronos-redis -p 6379:6379 redis:7

4. **Run database migrations**:

   .. code-block:: bash

      alembic upgrade head

5. **Start the development server**:

   .. code-block:: bash

      uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

Agent 6 Development Guide
-------------------------

Current Implementation Status
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**✅ Phase 1 Complete: Foundation Models & Schemas**

- Database models for body doubling sessions
- Participant and message models  
- Comprehensive Pydantic schemas
- Model relationships and validation

**🔄 Phase 2 In Progress: WebSocket Infrastructure**

Next development tasks for Agent 6:

1. **WebSocket Connection Manager**
2. **Redis Integration**
3. **Authentication Middleware**
4. **Message Broadcasting**

Development Workflow
~~~~~~~~~~~~~~~~~~~

**1. Create Feature Branch**:

.. code-block:: bash

   git checkout -b feature/agent6-websocket-manager
   git push -u origin feature/agent6-websocket-manager

**2. Implement WebSocket Manager**:

Create ``chronos/app/api/websockets/manager.py``:

.. code-block:: python

   from typing import Dict, Set
   from uuid import UUID
   from fastapi import WebSocket
   import redis.asyncio as redis
   
   class WebSocketManager:
       def __init__(self):
           self.user_connections: Dict[UUID, WebSocket] = {}
           self.session_participants: Dict[UUID, Set[UUID]] = {}
           self.redis = redis.Redis.from_url("redis://localhost:6379")
       
       async def connect_user(self, websocket: WebSocket, user_id: UUID):
           await websocket.accept()
           self.user_connections[user_id] = websocket
           # Store connection in Redis for distributed state
           await self.redis.hset(
               f"connections:user:{user_id}",
               mapping={
                   "connected_at": datetime.utcnow().isoformat(),
                   "server_id": "ws-server-1"
               }
           )

**3. Add Tests**:

Create ``tests/unit/test_websocket_manager.py``:

.. code-block:: python

   import pytest
   from chronos.app.api.websockets.manager import WebSocketManager
   
   @pytest.mark.asyncio
   async def test_websocket_manager_connection():
       manager = WebSocketManager()
       # Test connection logic
       assert manager is not None

**4. Commit Changes**:

.. code-block:: bash

   git add .
   git commit -m "feat(agent6): Implement WebSocket connection manager
   
   - Add WebSocketManager class for connection handling
   - Integrate Redis for distributed state management
   - Add connection lifecycle management
   - Include unit tests for connection logic"

**5. Push and Create PR**:

.. code-block:: bash

   git push origin feature/agent6-websocket-manager
   # Create pull request on GitHub

Code Organization
~~~~~~~~~~~~~~~~

Agent 6 follows the established project structure:

.. code-block::

   chronos/app/
   ├── api/
   │   ├── v1/
   │   │   └── body_doubling.py          # REST endpoints
   │   └── websockets/
   │       ├── __init__.py
   │       ├── manager.py                # Connection management
   │       └── body_doubling.py          # WebSocket endpoints
   ├── core/
   │   └── redis.py                      # Redis connection
   ├── middleware/
   │   └── websocket_auth.py             # WebSocket authentication
   ├── models/
   │   └── body_doubling.py              # Database models ✅
   ├── schemas/
   │   └── body_doubling.py              # Pydantic schemas ✅
   ├── services/
   │   ├── body_doubling_service.py      # Business logic
   │   └── realtime_service.py           # Real-time coordination
   └── utils/
       ├── websocket_utils.py            # WebSocket utilities
       └── broadcast_utils.py            # Message broadcasting

Testing Strategy
---------------

Test Structure
~~~~~~~~~~~~~

.. code-block::

   tests/
   ├── unit/
   │   ├── test_models/
   │   │   └── test_body_doubling.py     # Model tests ✅
   │   ├── test_schemas/
   │   │   └── test_body_doubling.py     # Schema tests ✅
   │   ├── test_services/
   │   │   ├── test_websocket_manager.py
   │   │   └── test_body_doubling_service.py
   │   └── test_utils/
   │       └── test_websocket_utils.py
   ├── integration/
   │   ├── test_api/
   │   │   └── test_body_doubling_endpoints.py
   │   └── test_websockets/
   │       └── test_body_doubling_flow.py
   └── e2e/
       └── test_body_doubling_session.py

Running Tests
~~~~~~~~~~~~

**All Tests**:

.. code-block:: bash

   pytest

**Unit Tests Only**:

.. code-block:: bash

   pytest tests/unit/

**Agent 6 Tests**:

.. code-block:: bash

   pytest tests/ -k "body_doubling or websocket"

**With Coverage**:

.. code-block:: bash

   pytest --cov=chronos --cov-report=html

**Watch Mode** (for development):

.. code-block:: bash

   pytest-watch

Test Examples
~~~~~~~~~~~~

**Model Testing**:

.. code-block:: python

   import pytest
   from chronos.app.models.body_doubling import BodyDoublingSession
   
   @pytest.mark.asyncio
   async def test_create_body_doubling_session(db_session):
       session = BodyDoublingSession(
           host_user_id=user.id,
           title="Test Session",
           max_participants=4
       )
       db_session.add(session)
       await db_session.commit()
       
       assert session.id is not None
       assert session.status == "waiting"

**WebSocket Testing**:

.. code-block:: python

   import pytest
   from fastapi.testclient import TestClient
   from chronos.app.main import app
   
   def test_websocket_connection():
       client = TestClient(app)
       with client.websocket_connect("/ws/body-doubling/session-id?token=valid-jwt") as websocket:
           data = websocket.receive_json()
           assert data["type"] == "connection_established"

Code Style & Standards
---------------------

Python Code Style
~~~~~~~~~~~~~~~~~

Project Chronos follows PEP 8 with some modifications:

- **Line Length**: 88 characters (Black default)
- **Import Sorting**: isort with Black compatibility
- **Type Hints**: Required for all public functions
- **Docstrings**: Google-style docstrings

**Formatting Tools**:

.. code-block:: bash

   # Format code
   black chronos/
   
   # Sort imports
   isort chronos/
   
   # Type checking
   mypy chronos/
   
   # Linting
   flake8 chronos/

**Pre-commit Hooks**:

.. code-block:: bash

   # Install pre-commit
   pip install pre-commit
   pre-commit install
   
   # Run on all files
   pre-commit run --all-files

Documentation Standards
~~~~~~~~~~~~~~~~~~~~~~

- **API Documentation**: OpenAPI/Swagger with detailed examples
- **Code Documentation**: Comprehensive docstrings for all public APIs
- **Architecture Documentation**: Mermaid diagrams for complex flows
- **User Documentation**: Clear, ADHD-friendly language

**Docstring Example**:

.. code-block:: python

   async def create_body_doubling_session(
       self,
       host_user_id: UUID,
       session_data: BodyDoublingSessionCreate
   ) -> BodyDoublingSession:
       """
       Create a new body doubling session.
       
       This method creates a virtual co-working session where users can work
       together for accountability and shared focus, particularly helpful for
       ADHD users who benefit from body doubling.
       
       Args:
           host_user_id: UUID of the user creating the session
           session_data: Session configuration and preferences
           
       Returns:
           BodyDoublingSession: The created session with generated ID
           
       Raises:
           ValidationError: If session data is invalid
           PermissionError: If user cannot create sessions
           
       Example:
           >>> session = await service.create_body_doubling_session(
           ...     user_id=UUID("..."),
           ...     session_data=BodyDoublingSessionCreate(
           ...         title="Morning Focus",
           ...         max_participants=4
           ...     )
           ... )
           >>> print(session.id)
           UUID('...')
       """

Git Workflow
-----------

Branch Naming
~~~~~~~~~~~~

- **Feature branches**: ``feature/agent6-websocket-manager``
- **Bug fixes**: ``fix/agent6-connection-cleanup``
- **Documentation**: ``docs/agent6-api-documentation``
- **Refactoring**: ``refactor/agent6-message-handling``

Commit Messages
~~~~~~~~~~~~~~

Follow Conventional Commits format:

.. code-block::

   <type>(scope): <description>
   
   [optional body]
   
   [optional footer]

**Types**:
- ``feat``: New feature
- ``fix``: Bug fix
- ``docs``: Documentation changes
- ``style``: Code style changes
- ``refactor``: Code refactoring
- ``test``: Adding or updating tests
- ``chore``: Maintenance tasks

**Examples**:

.. code-block::

   feat(agent6): Add WebSocket connection manager
   
   - Implement WebSocketManager class for handling connections
   - Add Redis integration for distributed state management
   - Include connection lifecycle management
   - Add comprehensive error handling and cleanup
   
   Closes #123

   fix(agent6): Fix WebSocket authentication middleware
   
   - Resolve JWT token validation issue
   - Add proper error handling for expired tokens
   - Improve connection cleanup on auth failure
   
   Fixes #124

Pull Request Process
~~~~~~~~~~~~~~~~~~~

1. **Create feature branch** from ``master``
2. **Implement changes** with tests
3. **Update documentation** if needed
4. **Run full test suite** and ensure all pass
5. **Create pull request** with detailed description
6. **Request review** from team members
7. **Address feedback** and update PR
8. **Merge** after approval and CI passes

**PR Template**:

.. code-block:: markdown

   ## Description
   Brief description of changes and motivation.
   
   ## Type of Change
   - [ ] Bug fix
   - [ ] New feature
   - [ ] Breaking change
   - [ ] Documentation update
   
   ## Testing
   - [ ] Unit tests added/updated
   - [ ] Integration tests added/updated
   - [ ] Manual testing completed
   
   ## Checklist
   - [ ] Code follows style guidelines
   - [ ] Self-review completed
   - [ ] Documentation updated
   - [ ] Tests pass locally

This development guide provides the foundation for contributing to Agent 6 and the broader Project Chronos codebase.
