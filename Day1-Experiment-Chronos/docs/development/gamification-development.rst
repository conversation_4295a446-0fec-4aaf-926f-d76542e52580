Gamification Development Guide
==============================

This guide provides comprehensive information for developers working on Project Chronos's gamification and motivation systems.

Overview
--------

The gamification system is designed specifically for ADHD users, focusing on:

- **Dopamine-driven motivation**: Immediate rewards and variable reinforcement
- **Flexible systems**: Accommodating ADHD inconsistency patterns
- **Energy-aware features**: Adapting to user's current state
- **Overwhelming task support**: Breaking down barriers to task initiation

Architecture Principles
-----------------------

ADHD-First Design
~~~~~~~~~~~~~~~~

**Immediate Feedback**
   Every action should provide instant positive reinforcement:
   
   .. code-block:: python
   
      # Award points immediately on task start
      await gamification_service.award_points(
          user_id=user_id,
          points=5,
          reason="Started task",
          multiplier=1.0
      )

**Variable Rewards**
   Use multipliers and bonuses to create excitement:
   
   .. code-block:: python
   
      # Calculate context-aware multipliers
      multiplier, breakdown = await gamification_service.calculate_points_multiplier(
          base_points=30,
          task_difficulty="hard",
          energy_level="low",  # Extra reward for low energy
          time_of_day="morning",
          context={"first_task_of_day": True}
      )

**Flexible Expectations**
   Systems should accommodate ADHD inconsistency:
   
   .. code-block:: python
   
      # Streak system with freeze options
      streak = UserStreak(
          streak_type="daily_tasks",
          current_streak=7,
          freeze_count=1,  # Used one freeze day
          max_freezes=3    # Allow flexibility
      )

Service Layer Design
~~~~~~~~~~~~~~~~~~~

**Separation of Concerns**
   Each service has a specific responsibility:
   
   - ``GamificationService``: Points, levels, streaks
   - ``AchievementService``: Achievement rules and unlocking
   - ``MotivationService``: Dopamine menu and activity tracking
   - ``GamificationIntegration``: Cross-system integration

**Async/Await Throughout**
   All operations are non-blocking:
   
   .. code-block:: python
   
      async def handle_task_completion(self, task: Task, user_id: UUID) -> Dict:
          # All database operations are async
          profile = await self.get_or_create_user_gamification(user_id)
          award, level_up = await self.award_points(...)
          achievements = await self.check_achievements(...)
          return results

**Error Resilience**
   Gamification failures should never break core functionality:
   
   .. code-block:: python
   
      try:
          gamification_results = await gamification.handle_task_completion(task, user_id)
      except Exception as e:
          logger.error(f"Gamification error: {e}")
          # Task completion still succeeds
          gamification_results = {}

Database Design
~~~~~~~~~~~~~~~

**Efficient Queries**
   Use proper indexing and joins:
   
   .. code-block:: python
   
      # Indexed foreign keys for fast lookups
      user_id: Mapped[UUID] = mapped_column(
          PostgresUUID(as_uuid=True),
          ForeignKey("users.id", ondelete="CASCADE"),
          nullable=False,
          index=True  # Important for performance
      )

**Flexible Data Storage**
   Use JSONB for extensible metadata:
   
   .. code-block:: python
   
      # Store flexible achievement requirements
      requirement_data: Mapped[Dict[str, Any]] = mapped_column(
          JSONB,
          default=dict,
          nullable=False,
          doc="Data defining the achievement requirements"
      )

**Audit Trail**
   Track all point awards for transparency:
   
   .. code-block:: python
   
      class PointsAward(Base, TimestampMixin):
          points_awarded: Mapped[int]
          reason: Mapped[str]
          multiplier: Mapped[float]
          total_points_after: Mapped[int]
          award_metadata: Mapped[Dict[str, Any]]

Development Workflow
-------------------

Setting Up Development Environment
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Install Dependencies**:
   
   .. code-block:: bash
   
      pip install -r requirements.txt
      pip install -r requirements-dev.txt

2. **Initialize Database**:
   
   .. code-block:: bash
   
      alembic upgrade head
      python scripts/init_gamification.py

3. **Run Tests**:
   
   .. code-block:: bash
   
      pytest tests/test_gamification_service.py -v
      pytest tests/test_motivation_service.py -v
      pytest tests/test_gamification_integration.py -v

Adding New Features
~~~~~~~~~~~~~~~~~~

**New Achievement Types**
   
   1. Define achievement rule in ``AchievementService``:
   
   .. code-block:: python
   
      AchievementRule(
          achievement_key="new_achievement",
          name="New Achievement",
          description="Description of what triggers this",
          category="task_completion",
          trigger_event="task_completed",
          condition=lambda user_id, data: data.get("condition") >= threshold,
          reward_points=100,
          badge_icon="🏆"
      )
   
   2. Add trigger in appropriate service:
   
   .. code-block:: python
   
      await achievement_service.check_achievements(
          user_id=user_id,
          trigger_event="task_completed",
          event_data={"condition": value}
      )

**New Dopamine Activities**
   
   1. Add to default activities in ``MotivationService``:
   
   .. code-block:: python
   
      {
          "name": "New Activity",
          "description": "Description of the activity",
          "category": "movement",
          "duration_min": 3,
          "duration_max": 10,
          "energy_requirement": "low",
          "energy_boost": "medium",
          "tags": ["tag1", "tag2"]
      }
   
   2. Update filtering logic if needed:
   
   .. code-block:: python
   
      async def _filter_activities(self, criteria):
          # Add new filtering criteria
          pass

**New Point Multipliers**
   
   Add to ``calculate_points_multiplier`` method:
   
   .. code-block:: python
   
      # New context bonus
      if context.get("new_condition"):
          multiplier *= 1.2
          breakdown["new_condition"] = 1.2

Testing Guidelines
~~~~~~~~~~~~~~~~~

**Unit Test Structure**
   
   .. code-block:: python
   
      class TestGamificationService:
          @pytest.fixture
          async def mock_user(self, db_session):
              # Create test user
              pass
          
          @pytest.fixture
          def gamification_service(self, db_session):
              return GamificationService(db_session)
          
          async def test_specific_functionality(self, gamification_service, mock_user):
              # Test specific behavior
              result = await gamification_service.method(...)
              assert result.expected_property == expected_value

**Integration Test Patterns**
   
   .. code-block:: python
   
      async def test_task_completion_flow(self, gamification_integration, mock_user):
          # Test complete workflow
          task = create_test_task()
          results = await gamification_integration.handle_task_completion(task, user_id)
          
          # Verify all aspects
          assert results["points_awarded"] > 0
          assert "achievements_unlocked" in results
          assert "streak_updated" in results

**Mock External Dependencies**
   
   .. code-block:: python
   
      @patch('app.services.gamification_service.datetime')
      async def test_time_dependent_feature(self, mock_datetime, service):
          mock_datetime.now.return_value = specific_time
          # Test time-dependent behavior

Performance Considerations
-------------------------

Database Optimization
~~~~~~~~~~~~~~~~~~~~~

**Query Optimization**
   
   .. code-block:: python
   
      # Use selectinload for related data
      result = await db.execute(
          select(UserAchievement)
          .options(selectinload(UserAchievement.achievement))
          .where(UserAchievement.user_id == user_id)
      )

**Caching Strategy**
   
   .. code-block:: python
   
      # Cache frequently accessed data
      @lru_cache(maxsize=1000)
      def calculate_level(total_points: int) -> int:
          # Expensive calculation cached
          pass

**Batch Operations**
   
   .. code-block:: python
   
      # Process multiple achievements at once
      achievements = await achievement_service.check_achievements(
          user_id=user_id,
          trigger_event="batch_event",
          event_data=batch_data
      )

Real-time Performance
~~~~~~~~~~~~~~~~~~~~

**Async Operations**
   
   .. code-block:: python
   
      # Non-blocking gamification processing
      async def handle_task_completion(self, task, user_id):
          # All operations are async
          await asyncio.gather(
              self.award_points(...),
              self.update_streak(...),
              self.check_achievements(...)
          )

**Background Processing**
   
   .. code-block:: python
   
      # Heavy analytics in background
      async def generate_insights(user_id: UUID):
          # Process insights without blocking user actions
          pass

Memory Management
~~~~~~~~~~~~~~~~

**Efficient Data Structures**
   
   .. code-block:: python
   
      # Use generators for large datasets
      def get_user_activities(user_id: UUID):
          for activity in query_activities(user_id):
              yield activity

**Resource Cleanup**
   
   .. code-block:: python
   
      async def cleanup_old_data():
          # Regular cleanup of old records
          await db.execute(
              delete(PointsAward)
              .where(PointsAward.created_at < cutoff_date)
          )

ADHD-Specific Development Guidelines
-----------------------------------

User Experience Principles
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Immediate Feedback**
   Every user action should provide instant visual/auditory feedback:
   
   - Points awarded immediately
   - Achievement unlocks with celebration
   - Progress bars update in real-time
   - Positive reinforcement for any progress

**Reduce Cognitive Load**
   Simplify decision-making:
   
   - Limit dopamine menu to 5 activities
   - Use clear, simple language
   - Provide default options
   - Minimize configuration complexity

**Accommodate Inconsistency**
   Design for ADHD patterns:
   
   - Flexible streak systems
   - Multiple paths to success
   - Recovery options for setbacks
   - Gentle encouragement, not pressure

**Energy Awareness**
   Adapt to user's current state:
   
   - Energy-based activity filtering
   - Bonus points for low-energy efforts
   - Time-of-day considerations
   - Context-aware suggestions

Implementation Patterns
~~~~~~~~~~~~~~~~~~~~~~

**Motivation Before Obligation**
   
   .. code-block:: python
   
      # Suggest dopamine activity before difficult tasks
      if task.energy_level == "high" and user_energy == "low":
          menu = await motivation_service.get_dopamine_menu(
              user_id, 
              DopamineMenuRequest(energy_level="low", context="pre_task")
          )

**Celebrate Small Wins**
   
   .. code-block:: python
   
      # Award points for task initiation
      if task.status == "in_progress" and previous_status == "pending":
          await gamification_service.award_points(
              user_id=user_id,
              points=5,
              reason="Started task - great job taking the first step!"
          )

**Flexible Success Metrics**
   
   .. code-block:: python
   
      # Multiple ways to maintain streaks
      streak_maintained = (
          completed_task_today or 
          used_dopamine_activity or 
          logged_into_app or
          used_streak_freeze
      )

Common Pitfalls and Solutions
----------------------------

Pitfall: Over-Gamification
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Problem**: Too many points/achievements become meaningless
**Solution**: Carefully balance rewards and maintain scarcity

.. code-block:: python

   # Good: Meaningful point values
   base_points = 20  # Substantial but not excessive
   
   # Bad: Point inflation
   base_points = 1000  # Makes points meaningless

Pitfall: Rigid Systems
~~~~~~~~~~~~~~~~~~~~~

**Problem**: Inflexible rules that punish ADHD patterns
**Solution**: Build in flexibility and recovery options

.. code-block:: python

   # Good: Flexible streak with recovery
   if streak_broken and user.has_streak_freeze():
       streak.use_freeze()
       streak.maintain()
   
   # Bad: Harsh streak reset
   if missed_day:
       streak.reset_to_zero()

Pitfall: Overwhelming Choices
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Problem**: Too many options cause decision paralysis
**Solution**: Curate and limit choices

.. code-block:: python

   # Good: Limited, curated options
   activities = personalized_activities[:5]
   
   # Bad: Overwhelming choice
   activities = all_possible_activities  # 50+ options

Debugging and Troubleshooting
-----------------------------

Common Issues
~~~~~~~~~~~~

**Points Not Awarded**
   
   .. code-block:: python
   
      # Debug point calculation
      logger.info(f"Base points: {base_points}")
      logger.info(f"Multiplier: {multiplier}")
      logger.info(f"Final points: {final_points}")
      logger.info(f"User gamification enabled: {profile.gamification_enabled}")

**Achievements Not Unlocking**
   
   .. code-block:: python
   
      # Debug achievement conditions
      logger.info(f"Achievement condition data: {event_data}")
      logger.info(f"Condition result: {rule.condition(user_id, event_data)}")
      logger.info(f"Already unlocked: {achievement_id in unlocked_ids}")

**Performance Issues**
   
   .. code-block:: python
   
      # Profile database queries
      import time
      start = time.time()
      result = await db.execute(query)
      logger.info(f"Query took: {time.time() - start:.3f}s")

Monitoring and Metrics
~~~~~~~~~~~~~~~~~~~~~

**Key Metrics to Track**
   
   - Point award frequency and amounts
   - Achievement unlock rates
   - Dopamine activity completion rates
   - User engagement with gamification features
   - Performance of gamification operations

**Logging Strategy**
   
   .. code-block:: python
   
      # Structured logging for analytics
      logger.info(
          "gamification_event",
          extra={
              "event_type": "points_awarded",
              "user_id": str(user_id),
              "points": points_awarded,
              "reason": reason,
              "multiplier": multiplier
          }
      )

This development guide ensures that all gamification features maintain the ADHD-first design principles while providing robust, performant, and maintainable code.
