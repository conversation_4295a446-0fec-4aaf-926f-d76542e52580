# Testing Guide for Project Chronos ADHD Features

This comprehensive guide covers testing strategies, tools, and best practices for ensuring Project Chronos meets the highest quality standards for ADHD users.

## Table of Contents

1. [Testing Philosophy](#testing-philosophy)
2. [Test Types and Structure](#test-types-and-structure)
3. [ADHD-Specific Testing](#adhd-specific-testing)
4. [Running Tests](#running-tests)
5. [Quality Assurance](#quality-assurance)
6. [CI/CD Pipeline](#cicd-pipeline)
7. [Performance Testing](#performance-testing)
8. [Monitoring and Alerting](#monitoring-and-alerting)

## Testing Philosophy

Project Chronos follows a comprehensive testing approach with special emphasis on ADHD user needs:

### Core Principles

- **Accessibility First**: Every feature must work reliably for users with ADHD
- **Performance Matters**: ADHD users need fast, responsive interactions
- **Error Resilience**: Graceful handling of errors to prevent user frustration
- **Real-World Scenarios**: Tests reflect actual ADHD user behavior patterns

### Quality Standards

- **100% unit test coverage** for business logic
- **95% integration test coverage** for API endpoints
- **100% BDD scenario coverage** for user stories
- **WCAG 2.1 AA accessibility compliance**
- **Sub-second response times** for 95% of requests

## Test Types and Structure

### Directory Structure

```
tests/
├── unit/                    # Unit tests
│   ├── test_models/        # Model tests
│   ├── test_services/      # Service tests
│   └── test_api/           # API unit tests
├── integration/            # Integration tests
│   ├── test_adhd_features/ # ADHD-specific integration tests
│   └── test_api_endpoints.py
├── behavior/               # BDD tests
│   ├── features/           # Gherkin feature files
│   └── steps/              # Step definitions
├── performance/            # Performance tests
│   └── locustfile.py       # Load testing
├── fixtures/               # Test fixtures
├── conftest.py            # Global test configuration
├── factories.py           # Test data factories
└── utils.py               # Testing utilities
```

### Test Categories

#### Unit Tests
- **Model Tests**: Validate ADHD-specific fields and methods
- **Service Tests**: Test business logic and AI integration
- **API Tests**: Test endpoint behavior and validation

#### Integration Tests
- **ADHD Feature Tests**: End-to-end ADHD functionality
- **API Integration**: Full request/response cycles
- **Database Integration**: Data persistence and relationships

#### Behavior-Driven Development (BDD)
- **Feature Files**: User stories in Gherkin format
- **Step Definitions**: Implementation of BDD scenarios
- **ADHD Scenarios**: Specific ADHD user journeys

## ADHD-Specific Testing

### Key Testing Areas

#### 1. Time Blindness Support
```python
# Test visual time interfaces
def test_circular_time_display():
    # Verify proportional time representation
    # Check visual cues for ADHD users
    # Validate buffer time insertion

# Test time estimation accuracy
def test_time_estimation_tracking():
    # Track estimation vs actual duration
    # Learn from user patterns
    # Improve future estimates
```

#### 2. Task Paralysis Solutions
```python
# Test AI task chunking
def test_ai_task_chunking():
    # Verify appropriate chunk sizes
    # Check actionable descriptions
    # Validate energy level assignments

# Test task jar functionality
def test_task_jar_selection():
    # Random selection for decision fatigue
    # Appropriate task filtering
    # Energy level matching
```

#### 3. Energy Level Management
```python
# Test energy-based filtering
def test_energy_level_filtering():
    # Filter tasks by energy requirements
    # Match user's current energy state
    # Provide appropriate suggestions
```

### ADHD Test Markers

Use pytest markers to categorize ADHD-specific tests:

```python
@pytest.mark.adhd
@pytest.mark.time_blindness
def test_time_awareness_features():
    # Test implementation
    pass

@pytest.mark.adhd
@pytest.mark.task_paralysis
def test_chunking_features():
    # Test implementation
    pass
```

## Running Tests

### Quick Start

```bash
# Run all tests
poetry run python scripts/test_runner.py

# Run specific test types
poetry run python scripts/test_runner.py --unit-only
poetry run python scripts/test_runner.py --adhd-only
poetry run python scripts/test_runner.py --integration-only
```

### Detailed Commands

#### Unit Tests with Coverage
```bash
poetry run pytest tests/unit/ -v \
  --cov=app \
  --cov-report=html \
  --cov-fail-under=100
```

#### ADHD-Specific Tests
```bash
poetry run pytest tests/ -v \
  -m "adhd" \
  --cov=app \
  --cov-report=xml:adhd-coverage.xml
```

#### Integration Tests
```bash
poetry run pytest tests/integration/ -v \
  -m "integration"
```

#### BDD Tests
```bash
poetry run behave tests/behavior/ \
  --format=pretty \
  --format=json \
  --outfile=bdd-results.json
```

### Test Environment Setup

#### Database Setup
```bash
# Start test database
docker run -d \
  --name test-postgres \
  -e POSTGRES_PASSWORD=test \
  -e POSTGRES_USER=test \
  -e POSTGRES_DB=test_chronos \
  -p 5432:5432 \
  postgres:15-alpine

# Start Redis for caching tests
docker run -d \
  --name test-redis \
  -p 6379:6379 \
  redis:7-alpine
```

#### Environment Variables
```bash
export DATABASE_URL="postgresql+asyncpg://test:test@localhost:5432/test_chronos"
export REDIS_URL="redis://localhost:6379/1"
export ENVIRONMENT="testing"
```

## Quality Assurance

### Code Quality Checks

Run comprehensive quality checks:

```bash
poetry run python scripts/quality_check.py
```

This includes:
- **Black**: Code formatting
- **isort**: Import sorting
- **Flake8**: Linting
- **MyPy**: Type checking
- **pydocstyle**: Docstring quality
- **Bandit**: Security scanning
- **Safety**: Dependency vulnerabilities
- **ADHD Pattern Validation**: Custom checks for ADHD features

### Quality Standards

#### Code Coverage
- Minimum 90% overall coverage
- 100% coverage for ADHD-specific features
- HTML reports for detailed analysis

#### Performance Requirements
- API response times < 500ms (95th percentile)
- Task creation < 200ms average
- AI chunking < 5s timeout
- Database queries < 100ms

#### Security Standards
- Zero high-severity vulnerabilities
- No hardcoded secrets
- Input validation on all endpoints
- SQL injection prevention

## CI/CD Pipeline

### GitHub Actions Workflow

The CI/CD pipeline includes:

1. **Code Quality**: Formatting, linting, type checking
2. **Unit Tests**: 100% coverage requirement
3. **Integration Tests**: API and database testing
4. **ADHD Feature Tests**: Specialized ADHD functionality
5. **BDD Tests**: User story validation
6. **Security Scanning**: Vulnerability detection
7. **Performance Testing**: Load testing validation

### Quality Gates

All checks must pass before merge:
- ✅ Code quality checks
- ✅ Unit tests with coverage
- ✅ Integration tests
- ✅ ADHD feature validation
- ✅ Security scans
- ✅ Performance benchmarks

### Deployment Pipeline

```yaml
# Simplified workflow
on: [push, pull_request]
jobs:
  quality-checks:
    # Code quality validation
  unit-tests:
    # Unit testing with coverage
  integration-tests:
    # Integration testing
  adhd-feature-tests:
    # ADHD-specific validation
  security-scan:
    # Security vulnerability check
  deploy:
    needs: [quality-checks, unit-tests, integration-tests, adhd-feature-tests]
    if: github.ref == 'refs/heads/main'
    # Deployment steps
```

## Performance Testing

### Load Testing with Locust

Test ADHD user behavior patterns under load:

```bash
# Start performance testing
locust -f tests/performance/locustfile.py \
  --host=http://localhost:8000 \
  --users=50 \
  --spawn-rate=5 \
  --run-time=300s
```

### ADHD User Simulation

The performance tests simulate:
- **High-frequency users**: Hyperfocus periods
- **Low-energy users**: Slower interaction patterns
- **Typical ADHD behavior**: Task switching, filtering

### Performance Metrics

Key metrics for ADHD users:
- Task creation response time
- Task jar selection speed
- AI chunking performance
- Energy filtering responsiveness

## Monitoring and Alerting

### ADHD-Specific Alerts

Critical alerts for ADHD features:
- Task creation > 500ms (95th percentile)
- Task jar failure rate > 1%
- AI chunking service unavailable
- High error rates affecting UX

### Monitoring Setup

```bash
# Set up monitoring stack
poetry run python scripts/monitoring_setup.py

# Start monitoring
cd monitoring/
docker-compose up -d
```

### Dashboards

Grafana dashboards include:
- ADHD feature performance
- User interaction patterns
- Error rates and availability
- AI service health

## Best Practices

### Writing ADHD-Focused Tests

1. **Test Real Scenarios**: Use realistic ADHD user data
2. **Validate Accessibility**: Ensure features work for neurodivergent users
3. **Performance Matters**: Test response times under load
4. **Error Handling**: Verify graceful error recovery
5. **User Experience**: Test complete user journeys

### Test Data Management

```python
# Use factories for realistic test data
user = await UserFactory.create(
    adhd_diagnosed=True,
    preferences={
        "energy_patterns": {"morning": "high"},
        "chunking_preferences": {"default_size": "small"}
    }
)

# Create ADHD-appropriate tasks
task = await TaskFactory.create(
    user_id=user.id,
    energy_level="low",
    estimated_duration=30,  # ADHD-friendly duration
    context_tags=["home", "computer"]
)
```

### Continuous Improvement

- Monitor test results for patterns
- Update tests based on user feedback
- Refine ADHD-specific scenarios
- Maintain high coverage standards
- Regular performance benchmarking

## Troubleshooting

### Common Issues

#### Test Database Connection
```bash
# Check database status
docker ps | grep postgres
# Reset test database
docker restart test-postgres
```

#### Coverage Issues
```bash
# Generate detailed coverage report
poetry run pytest --cov=app --cov-report=html
# Open htmlcov/index.html for analysis
```

#### Performance Test Failures
```bash
# Check system resources
htop
# Reduce concurrent users
locust --users=10 --spawn-rate=1
```

### Getting Help

- Check test logs for detailed error messages
- Review CI/CD pipeline output
- Consult ADHD-specific test documentation
- Use debugging tools for complex issues

---

This testing guide ensures Project Chronos maintains the highest quality standards while providing reliable, accessible features for users with ADHD.
