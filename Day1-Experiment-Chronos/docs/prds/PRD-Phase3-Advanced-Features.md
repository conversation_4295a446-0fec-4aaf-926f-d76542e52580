# PRD: Phase 3 - Advanced Features
## Project Chronos Frontend Development - Weeks 9-12

### Document Information
- **Version**: 1.0
- **Date**: 2025-06-18
- **Author**: Project Chronos Advanced Features Team
- **Status**: Ready for Implementation
- **Priority**: High (P1)
- **Dependencies**: Phase 1 & 2 completion, WebSocket infrastructure

---

## 1. Executive Summary

### Problem Statement
ADHD users need advanced productivity features that go beyond basic task management: real-time collaboration for accountability, AI-powered assistance for overwhelming tasks, gamification for dopamine regulation, and comprehensive accessibility for diverse neurodivergent needs. These features must integrate seamlessly without adding cognitive overhead.

### Solution Overview
Implement four advanced feature sets: Body Doubling for social accountability, AI Integration for intelligent task assistance, Gamification for motivation management, and Enhanced Accessibility for inclusive design. Each feature will maintain ADHD-first principles while providing sophisticated functionality.

### Success Metrics
- **Body Doubling Engagement**: 70% of users try virtual co-working within first week
- **AI Feature Adoption**: 85% of users utilize AI chunking for complex tasks
- **Gamification Effectiveness**: 60% increase in task completion through rewards
- **Accessibility Compliance**: 100% WCAG AAA compliance across all features

---

## 2. Advanced Feature Architecture

### 2.1 Body Doubling - Real-Time Collaboration

#### Virtual Co-Working Sessions
```typescript
// components/body-doubling/BodyDoublingHub.tsx
interface BodyDoublingHubProps {
  userEnergy: number;
  cognitiveLoad: 'low' | 'medium' | 'high';
  currentTask?: Task;
}

interface BodyDoublingSession {
  id: string;
  name: string;
  description: string;
  hostId: string;
  participants: Participant[];
  maxParticipants: number;
  sessionType: 'silent' | 'ambient' | 'collaborative';
  duration: number;
  startTime: Date;
  isPrivate: boolean;
  tags: string[];
  adhdFriendly: boolean;
  currentActivity: string;
}

interface Participant {
  id: string;
  name: string;
  avatar: string;
  currentTask: string;
  energyLevel: number;
  joinedAt: Date;
  isActive: boolean;
  preferences: ParticipantPreferences;
}

interface ParticipantPreferences {
  showActivity: boolean;
  allowMessages: boolean;
  shareProgress: boolean;
  notificationLevel: 'none' | 'minimal' | 'normal';
}

export const BodyDoublingHub: React.FC<BodyDoublingHubProps> = ({
  userEnergy,
  cognitiveLoad,
  currentTask
}) => {
  const [activeView, setActiveView] = useState<'browse' | 'create' | 'session'>('browse');
  const [sessions, setSessions] = useState<BodyDoublingSession[]>([]);
  const [currentSession, setCurrentSession] = useState<BodyDoublingSession | null>(null);

  const { data: availableSessions } = useQuery(['body-doubling-sessions'],
    () => fetchAvailableSessions({ userEnergy, adhdFriendly: true })
  );

  // Real-time session updates
  useWebSocket('/ws/body-doubling', {
    onMessage: (message) => {
      handleSessionUpdate(message);
    },
    onConnect: () => {
      subscribeToSessionUpdates();
    }
  });

  const handleJoinSession = async (sessionId: string) => {
    try {
      const session = await joinBodyDoublingSession(sessionId, {
        currentTask: currentTask?.title,
        energyLevel: userEnergy,
        preferences: getUserPreferences()
      });

      setCurrentSession(session);
      setActiveView('session');

      // Show gentle join celebration
      showJoinCelebration();
    } catch (error) {
      showErrorToast('Unable to join session. Please try another one.');
    }
  };

  const handleCreateSession = async (config: CreateSessionConfig) => {
    try {
      const session = await createBodyDoublingSession({
        ...config,
        hostId: user.id,
        adhdFriendly: true,
        maxParticipants: cognitiveLoad === 'high' ? 3 : 8 // Smaller groups for high cognitive load
      });

      setCurrentSession(session);
      setActiveView('session');
    } catch (error) {
      showErrorToast('Unable to create session. Please try again.');
    }
  };

  if (activeView === 'session' && currentSession) {
    return (
      <ActiveBodyDoublingSession
        session={currentSession}
        userEnergy={userEnergy}
        cognitiveLoad={cognitiveLoad}
        onLeave={() => {
          setCurrentSession(null);
          setActiveView('browse');
        }}
      />
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header with Quick Actions */}
      <BodyDoublingHeader
        userEnergy={userEnergy}
        cognitiveLoad={cognitiveLoad}
        currentTask={currentTask}
        onQuickJoin={() => handleQuickJoin()}
        onCreateSession={() => setActiveView('create')}
      />

      {activeView === 'browse' && (
        <>
          {/* Recommended Sessions */}
          <RecommendedSessions
            sessions={availableSessions?.recommended || []}
            userEnergy={userEnergy}
            onJoin={handleJoinSession}
            cognitiveLoad={cognitiveLoad}
          />

          {/* Session Browser */}
          <SessionBrowser
            sessions={availableSessions?.all || []}
            filters={{
              energyLevel: userEnergy,
              adhdFriendly: true,
              maxParticipants: cognitiveLoad === 'high' ? 5 : 20
            }}
            onJoin={handleJoinSession}
            onFilterChange={handleFilterChange}
          />

          {/* Quick Start Options */}
          <QuickStartOptions
            userEnergy={userEnergy}
            cognitiveLoad={cognitiveLoad}
            onInstantSession={() => handleCreateInstantSession()}
            onScheduledSession={() => setActiveView('create')}
          />
        </>
      )}

      {activeView === 'create' && (
        <CreateSessionWizard
          userEnergy={userEnergy}
          cognitiveLoad={cognitiveLoad}
          currentTask={currentTask}
          onSessionCreate={handleCreateSession}
          onCancel={() => setActiveView('browse')}
        />
      )}
    </div>
  );
};
```

#### Active Session Interface
```typescript
// components/body-doubling/ActiveBodyDoublingSession.tsx
interface ActiveBodyDoublingSessionProps {
  session: BodyDoublingSession;
  userEnergy: number;
  cognitiveLoad: 'low' | 'medium' | 'high';
  onLeave: () => void;
}

export const ActiveBodyDoublingSession: React.FC<ActiveBodyDoublingSessionProps> = ({
  session,
  userEnergy,
  cognitiveLoad,
  onLeave
}) => {
  const [isMinimized, setIsMinimized] = useState(false);
  const [showParticipants, setShowParticipants] = useState(true);
  const [currentActivity, setCurrentActivity] = useState('');
  const [sessionTime, setSessionTime] = useState(0);

  // Adaptive interface based on cognitive load
  const interfaceMode = useMemo(() => {
    if (cognitiveLoad === 'high') return 'minimal';
    if (cognitiveLoad === 'medium') return 'focused';
    return 'full';
  }, [cognitiveLoad]);

  // Real-time session management
  useEffect(() => {
    const interval = setInterval(() => {
      setSessionTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Gentle presence indicators
  const handleActivityUpdate = (activity: string) => {
    setCurrentActivity(activity);
    updateParticipantActivity(session.id, activity);
  };

  if (isMinimized) {
    return (
      <MinimizedSessionWidget
        session={session}
        sessionTime={sessionTime}
        onExpand={() => setIsMinimized(false)}
        onLeave={onLeave}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      <div className="max-w-7xl mx-auto p-4">
        {/* Session Header */}
        <SessionHeader
          session={session}
          sessionTime={sessionTime}
          interfaceMode={interfaceMode}
          onMinimize={() => setIsMinimized(true)}
          onLeave={onLeave}
        />

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mt-6">
          {/* Main Work Area */}
          <div className="lg:col-span-3">
            <WorkArea
              currentActivity={currentActivity}
              onActivityUpdate={handleActivityUpdate}
              interfaceMode={interfaceMode}
              userEnergy={userEnergy}
            />
          </div>

          {/* Participants Panel */}
          {showParticipants && interfaceMode !== 'minimal' && (
            <div className="lg:col-span-1">
              <ParticipantsPanel
                participants={session.participants}
                sessionType={session.sessionType}
                onParticipantInteract={handleParticipantInteraction}
                interfaceMode={interfaceMode}
              />
            </div>
          )}
        </div>

        {/* Floating Controls */}
        <FloatingSessionControls
          session={session}
          interfaceMode={interfaceMode}
          onToggleParticipants={() => setShowParticipants(!showParticipants)}
          onToggleAudio={() => toggleAudioMode()}
          onSendEncouragement={() => sendEncouragement()}
        />

        {/* Gentle Notifications */}
        <SessionNotifications
          session={session}
          userPreferences={getUserNotificationPreferences()}
        />
      </div>
    </div>
  );
};

const ParticipantsPanel: React.FC<{
  participants: Participant[];
  sessionType: string;
  onParticipantInteract: (participantId: string, action: string) => void;
  interfaceMode: string;
}> = ({ participants, sessionType, onParticipantInteract, interfaceMode }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-medium text-gray-900">
          Co-workers ({participants.length})
        </h3>
        {interfaceMode === 'full' && (
          <SessionTypeIndicator type={sessionType} />
        )}
      </div>

      <div className="space-y-3">
        {participants.map(participant => (
          <ParticipantCard
            key={participant.id}
            participant={participant}
            interfaceMode={interfaceMode}
            onInteract={(action) => onParticipantInteract(participant.id, action)}
          />
        ))}
      </div>

      {interfaceMode === 'full' && (
        <div className="mt-4 pt-4 border-t">
          <EncouragementButtons
            onSendCheer={() => sendGroupEncouragement('cheer')}
            onSendFocus={() => sendGroupEncouragement('focus')}
            onSendBreak={() => sendGroupEncouragement('break')}
          />
        </div>
      )}
    </div>
  );
};
```

### 2.2 AI Integration - Intelligent Task Assistance

#### AI Task Chunking Interface
```typescript
// components/ai/AITaskChunking.tsx
interface AITaskChunkingProps {
  task: Task;
  userEnergy: number;
  cognitiveLoad: 'low' | 'medium' | 'high';
  onChunkingComplete: (chunks: TaskChunk[]) => void;
  onCancel: () => void;
}

interface TaskChunk {
  id: string;
  title: string;
  description: string;
  estimatedDuration: number;
  energyLevel: number;
  complexity: number;
  dependencies: string[];
  order: number;
  category: 'preparation' | 'execution' | 'review' | 'cleanup';
  tips: string[];
  resources: Resource[];
}

interface ChunkingOptions {
  chunkSize: 'micro' | 'small' | 'medium' | 'large';
  energyDistribution: 'even' | 'ascending' | 'descending' | 'mixed';
  includeBreaks: boolean;
  includePreparation: boolean;
  maxChunkDuration: number;
  userExperience: 'beginner' | 'intermediate' | 'expert';
}

export const AITaskChunking: React.FC<AITaskChunkingProps> = ({
  task,
  userEnergy,
  cognitiveLoad,
  onChunkingComplete,
  onCancel
}) => {
  const [chunkingStage, setChunkingStage] = useState<'options' | 'processing' | 'review'>('options');
  const [chunkingOptions, setChunkingOptions] = useState<ChunkingOptions>({
    chunkSize: cognitiveLoad === 'high' ? 'micro' : 'small',
    energyDistribution: 'even',
    includeBreaks: true,
    includePreparation: true,
    maxChunkDuration: cognitiveLoad === 'high' ? 15 : 30,
    userExperience: 'intermediate'
  });
  const [generatedChunks, setGeneratedChunks] = useState<TaskChunk[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleStartChunking = async () => {
    setChunkingStage('processing');
    setIsProcessing(true);

    try {
      const chunks = await generateTaskChunks({
        task,
        options: chunkingOptions,
        userContext: {
          energyLevel: userEnergy,
          cognitiveLoad,
          adhdProfile: getUserADHDProfile()
        }
      });

      setGeneratedChunks(chunks);
      setChunkingStage('review');
    } catch (error) {
      showErrorToast('Unable to chunk task. Please try again.');
      setChunkingStage('options');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleChunkEdit = (chunkId: string, updates: Partial<TaskChunk>) => {
    setGeneratedChunks(prev =>
      prev.map(chunk =>
        chunk.id === chunkId ? { ...chunk, ...updates } : chunk
      )
    );
  };

  const handleRegenerateChunk = async (chunkId: string) => {
    const chunk = generatedChunks.find(c => c.id === chunkId);
    if (!chunk) return;

    try {
      const newChunk = await regenerateTaskChunk(chunk, {
        userFeedback: 'regenerate',
        userContext: { energyLevel: userEnergy, cognitiveLoad }
      });

      handleChunkEdit(chunkId, newChunk);
    } catch (error) {
      showErrorToast('Unable to regenerate chunk.');
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Progress Indicator */}
      <ChunkingProgress
        stage={chunkingStage}
        cognitiveLoad={cognitiveLoad}
      />

      {chunkingStage === 'options' && (
        <ChunkingOptionsPanel
          task={task}
          options={chunkingOptions}
          onOptionsChange={setChunkingOptions}
          userEnergy={userEnergy}
          cognitiveLoad={cognitiveLoad}
          onStart={handleStartChunking}
          onCancel={onCancel}
        />
      )}

      {chunkingStage === 'processing' && (
        <ChunkingProcessingView
          task={task}
          options={chunkingOptions}
          isProcessing={isProcessing}
        />
      )}

      {chunkingStage === 'review' && (
        <ChunkingReviewPanel
          originalTask={task}
          chunks={generatedChunks}
          onChunkEdit={handleChunkEdit}
          onRegenerateChunk={handleRegenerateChunk}
          onAccept={() => onChunkingComplete(generatedChunks)}
          onBack={() => setChunkingStage('options')}
          cognitiveLoad={cognitiveLoad}
        />
      )}
    </div>
  );
};

const ChunkingOptionsPanel: React.FC<{
  task: Task;
  options: ChunkingOptions;
  onOptionsChange: (options: ChunkingOptions) => void;
  userEnergy: number;
  cognitiveLoad: string;
  onStart: () => void;
  onCancel: () => void;
}> = ({ task, options, onOptionsChange, userEnergy, cognitiveLoad, onStart, onCancel }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Break down: {task.title}
        </h2>
        <p className="text-gray-600">
          Let's make this task more manageable by breaking it into smaller, ADHD-friendly chunks.
        </p>
      </div>

      {/* Task Preview */}
      <TaskPreviewCard task={task} className="mb-6" />

      {/* Chunking Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Chunk Size */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Chunk Size
          </label>
          <ChunkSizeSelector
            value={options.chunkSize}
            onChange={(size) => onOptionsChange({ ...options, chunkSize: size })}
            userEnergy={userEnergy}
            cognitiveLoad={cognitiveLoad}
          />
        </div>

        {/* Energy Distribution */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Energy Distribution
          </label>
          <EnergyDistributionSelector
            value={options.energyDistribution}
            onChange={(dist) => onOptionsChange({ ...options, energyDistribution: dist })}
            userEnergy={userEnergy}
          />
        </div>

        {/* Additional Options */}
        <div className="md:col-span-2">
          <AdditionalChunkingOptions
            options={options}
            onOptionsChange={onOptionsChange}
            cognitiveLoad={cognitiveLoad}
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-6 border-t mt-6">
        <div className="text-sm text-gray-500">
          Estimated chunking time: ~30 seconds
        </div>
        <div className="flex space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
          <button
            onClick={onStart}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Start Chunking
          </button>
        </div>
      </div>
    </div>
  );
};
```

#### AI Smart Suggestions
```typescript
// components/ai/SmartSuggestions.tsx
interface SmartSuggestionsProps {
  context: UserContext;
  currentTask?: Task;
  recentActivity: UserActivity[];
  onSuggestionAccept: (suggestion: Suggestion) => void;
}

interface Suggestion {
  id: string;
  type: 'task' | 'break' | 'energy' | 'focus' | 'organization';
  title: string;
  description: string;
  reasoning: string;
  confidence: number;
  urgency: 'low' | 'medium' | 'high';
  estimatedImpact: number;
  action: SuggestionAction;
  dismissible: boolean;
}

interface SuggestionAction {
  type: 'navigate' | 'create' | 'update' | 'remind';
  payload: any;
}

export const SmartSuggestions: React.FC<SmartSuggestionsProps> = ({
  context,
  currentTask,
  recentActivity,
  onSuggestionAccept
}) => {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [dismissedSuggestions, setDismissedSuggestions] = useState<Set<string>>(new Set());

  // Generate contextual suggestions
  useEffect(() => {
    const generateSuggestions = async () => {
      const newSuggestions = await getAISuggestions({
        userContext: context,
        currentTask,
        recentActivity,
        adhdProfile: getUserADHDProfile(),
        timeOfDay: new Date().getHours()
      });

      // Filter out dismissed suggestions
      const filteredSuggestions = newSuggestions.filter(
        s => !dismissedSuggestions.has(s.id)
      );

      setSuggestions(filteredSuggestions);
    };

    generateSuggestions();
  }, [context, currentTask, recentActivity, dismissedSuggestions]);

  const handleSuggestionDismiss = (suggestionId: string) => {
    setDismissedSuggestions(prev => new Set([...prev, suggestionId]));
    setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
  };

  const handleSuggestionAccept = (suggestion: Suggestion) => {
    onSuggestionAccept(suggestion);
    handleSuggestionDismiss(suggestion.id);
  };

  // Don't show suggestions if cognitive load is high
  if (context.cognitiveLoad === 'high' && suggestions.length > 2) {
    return (
      <SimpleSuggestions
        suggestions={suggestions.slice(0, 2)}
        onAccept={handleSuggestionAccept}
        onDismiss={handleSuggestionDismiss}
      />
    );
  }

  if (suggestions.length === 0) return null;

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-900">
          Smart Suggestions
        </h3>
        <button
          onClick={() => setDismissedSuggestions(new Set())}
          className="text-xs text-blue-600 hover:underline"
        >
          Show all
        </button>
      </div>

      <div className="space-y-2">
        {suggestions.map(suggestion => (
          <SuggestionCard
            key={suggestion.id}
            suggestion={suggestion}
            onAccept={() => handleSuggestionAccept(suggestion)}
            onDismiss={() => handleSuggestionDismiss(suggestion.id)}
            cognitiveLoad={context.cognitiveLoad}
          />
        ))}
      </div>
    </div>
  );
};

const SuggestionCard: React.FC<{
  suggestion: Suggestion;
  onAccept: () => void;
  onDismiss: () => void;
  cognitiveLoad: string;
}> = ({ suggestion, onAccept, onDismiss, cognitiveLoad }) => {
  const getTypeIcon = (type: string) => {
    const icons = {
      task: TaskIcon,
      break: CoffeeIcon,
      energy: BatteryIcon,
      focus: FocusIcon,
      organization: FolderIcon
    };
    return icons[type] || TaskIcon;
  };

  const getTypeColor = (type: string) => {
    const colors = {
      task: 'blue',
      break: 'green',
      energy: 'yellow',
      focus: 'purple',
      organization: 'gray'
    };
    return colors[type] || 'blue';
  };

  const IconComponent = getTypeIcon(suggestion.type);
  const color = getTypeColor(suggestion.type);

  return (
    <div className={cn(
      'p-3 rounded-lg border bg-white hover:shadow-sm transition-shadow',
      suggestion.urgency === 'high' && 'border-red-200 bg-red-50',
      suggestion.urgency === 'medium' && 'border-yellow-200 bg-yellow-50'
    )}>
      <div className="flex items-start space-x-3">
        <div className={cn(
          'flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center',
          `bg-${color}-100 text-${color}-600`
        )}>
          <IconComponent className="w-4 h-4" />
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm font-medium text-gray-900">
                {suggestion.title}
              </p>
              <p className="text-xs text-gray-600 mt-1">
                {suggestion.description}
              </p>
              {cognitiveLoad !== 'high' && (
                <p className="text-xs text-gray-500 mt-1">
                  {suggestion.reasoning}
                </p>
              )}
            </div>

            <div className="flex items-center space-x-1 ml-2">
              <button
                onClick={onAccept}
                className={cn(
                  'px-2 py-1 text-xs rounded',
                  `bg-${color}-100 text-${color}-700 hover:bg-${color}-200`
                )}
              >
                Try it
              </button>
              {suggestion.dismissible && (
                <button
                  onClick={onDismiss}
                  className="p-1 text-gray-400 hover:text-gray-600"
                >
                  <XIcon className="w-3 h-3" />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
```

### 2.3 Gamification - Motivation Management

#### ADHD-Optimized Achievement System
```typescript
// components/gamification/AchievementSystem.tsx
interface AchievementSystemProps {
  userProgress: UserProgress;
  recentAchievements: Achievement[];
  onAchievementClaim: (achievementId: string) => void;
  cognitiveLoad: 'low' | 'medium' | 'high';
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  category: 'task' | 'focus' | 'streak' | 'social' | 'growth';
  difficulty: 'easy' | 'medium' | 'hard' | 'legendary';
  points: number;
  icon: string;
  unlockedAt?: Date;
  progress: number;
  maxProgress: number;
  isSecret: boolean;
  adhdFriendly: boolean;
  requirements: AchievementRequirement[];
}

interface AchievementRequirement {
  type: 'task_completion' | 'focus_time' | 'streak_days' | 'social_interaction';
  value: number;
  timeframe?: 'daily' | 'weekly' | 'monthly' | 'all_time';
}

export const AchievementSystem: React.FC<AchievementSystemProps> = ({
  userProgress,
  recentAchievements,
  onAchievementClaim,
  cognitiveLoad
}) => {
  const [activeTab, setActiveTab] = useState<'recent' | 'progress' | 'all'>('recent');
  const [showCelebration, setShowCelebration] = useState<Achievement | null>(null);

  // Simplified view for high cognitive load
  const viewMode = cognitiveLoad === 'high' ? 'minimal' : 'full';

  // Auto-show achievement celebrations
  useEffect(() => {
    const newAchievements = recentAchievements.filter(a =>
      a.unlockedAt && isWithinLast24Hours(a.unlockedAt)
    );

    if (newAchievements.length > 0 && !showCelebration) {
      setShowCelebration(newAchievements[0]);
    }
  }, [recentAchievements, showCelebration]);

  if (viewMode === 'minimal') {
    return (
      <MinimalAchievementView
        recentAchievements={recentAchievements.slice(0, 3)}
        userProgress={userProgress}
        onExpand={() => setViewMode('full')}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Achievement Celebration Modal */}
      {showCelebration && (
        <AchievementCelebration
          achievement={showCelebration}
          onClose={() => setShowCelebration(null)}
          onClaim={() => {
            onAchievementClaim(showCelebration.id);
            setShowCelebration(null);
          }}
        />
      )}

      {/* Progress Overview */}
      <AchievementProgressOverview
        userProgress={userProgress}
        cognitiveLoad={cognitiveLoad}
      />

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'recent', label: 'Recent', count: recentAchievements.length },
            { id: 'progress', label: 'In Progress', count: getInProgressCount() },
            { id: 'all', label: 'All Achievements', count: getAllAchievementsCount() }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={cn(
                'py-2 px-1 border-b-2 font-medium text-sm',
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              )}
            >
              {tab.label}
              {tab.count > 0 && (
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-4">
        {activeTab === 'recent' && (
          <RecentAchievements
            achievements={recentAchievements}
            onClaim={onAchievementClaim}
          />
        )}

        {activeTab === 'progress' && (
          <InProgressAchievements
            userProgress={userProgress}
            cognitiveLoad={cognitiveLoad}
          />
        )}

        {activeTab === 'all' && (
          <AllAchievements
            userProgress={userProgress}
            onClaim={onAchievementClaim}
            cognitiveLoad={cognitiveLoad}
          />
        )}
      </div>
    </div>
  );
};

const AchievementCelebration: React.FC<{
  achievement: Achievement;
  onClose: () => void;
  onClaim: () => void;
}> = ({ achievement, onClose, onClaim }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4 text-center">
        {/* Celebration Animation */}
        <div className="mb-6">
          <div className="w-20 h-20 mx-auto bg-yellow-100 rounded-full flex items-center justify-center mb-4">
            <span className="text-4xl">{achievement.icon}</span>
          </div>
          <div className="text-yellow-500 text-6xl mb-2">🎉</div>
        </div>

        {/* Achievement Details */}
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Achievement Unlocked!
        </h2>
        <h3 className="text-lg font-semibold text-blue-600 mb-2">
          {achievement.title}
        </h3>
        <p className="text-gray-600 mb-4">
          {achievement.description}
        </p>

        {/* Points Reward */}
        <div className="bg-blue-50 rounded-lg p-4 mb-6">
          <div className="text-2xl font-bold text-blue-600">
            +{achievement.points} points
          </div>
          <div className="text-sm text-blue-500">
            Added to your total score
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            View Later
          </button>
          <button
            onClick={onClaim}
            className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Claim Reward
          </button>
        </div>
      </div>
    </div>
  );
};
```

#### Dopamine-Optimized Reward System
```typescript
// components/gamification/RewardSystem.tsx
interface RewardSystemProps {
  userPoints: number;
  availableRewards: Reward[];
  claimedRewards: ClaimedReward[];
  onRewardClaim: (rewardId: string) => void;
  userEnergy: number;
}

interface Reward {
  id: string;
  title: string;
  description: string;
  cost: number;
  category: 'break' | 'treat' | 'activity' | 'social' | 'purchase';
  icon: string;
  estimatedTime: number;
  energyImpact: 'positive' | 'neutral' | 'negative';
  availability: 'always' | 'energy_dependent' | 'time_dependent';
  customizable: boolean;
  adhdBenefit: string;
}

export const RewardSystem: React.FC<RewardSystemProps> = ({
  userPoints,
  availableRewards,
  claimedRewards,
  onRewardClaim,
  userEnergy
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showCustomReward, setShowCustomReward] = useState(false);

  // Filter rewards based on energy and availability
  const filteredRewards = useMemo(() => {
    return availableRewards.filter(reward => {
      // Category filter
      if (selectedCategory !== 'all' && reward.category !== selectedCategory) {
        return false;
      }

      // Energy-dependent availability
      if (reward.availability === 'energy_dependent') {
        if (reward.energyImpact === 'negative' && userEnergy <= 2) {
          return false; // Don't show energy-draining rewards when energy is low
        }
      }

      // Affordability
      if (reward.cost > userPoints) {
        return false;
      }

      return true;
    });
  }, [availableRewards, selectedCategory, userEnergy, userPoints]);

  // Recommended rewards based on current state
  const recommendedRewards = useMemo(() => {
    return filteredRewards
      .filter(reward => {
        if (userEnergy <= 2) {
          return reward.energyImpact === 'positive';
        }
        return true;
      })
      .slice(0, 3);
  }, [filteredRewards, userEnergy]);

  return (
    <div className="space-y-6">
      {/* Points Balance */}
      <PointsBalance
        currentPoints={userPoints}
        recentEarnings={getRecentPointsEarnings()}
        nextMilestone={getNextPointsMilestone(userPoints)}
      />

      {/* Recommended Rewards */}
      {recommendedRewards.length > 0 && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Recommended for you
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {recommendedRewards.map(reward => (
              <RecommendedRewardCard
                key={reward.id}
                reward={reward}
                onClaim={() => onRewardClaim(reward.id)}
                userEnergy={userEnergy}
              />
            ))}
          </div>
        </div>
      )}

      {/* Category Filter */}
      <RewardCategoryFilter
        selectedCategory={selectedCategory}
        onCategoryChange={setSelectedCategory}
        availableCategories={getAvailableCategories(availableRewards)}
      />

      {/* Rewards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredRewards.map(reward => (
          <RewardCard
            key={reward.id}
            reward={reward}
            onClaim={() => onRewardClaim(reward.id)}
            canAfford={reward.cost <= userPoints}
            userEnergy={userEnergy}
          />
        ))}
      </div>

      {/* Custom Reward Creator */}
      <div className="border-t pt-6">
        <button
          onClick={() => setShowCustomReward(true)}
          className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-blue-300 hover:text-blue-600 transition-colors"
        >
          <PlusIcon className="w-6 h-6 mx-auto mb-2" />
          Create Custom Reward
        </button>
      </div>

      {/* Custom Reward Modal */}
      {showCustomReward && (
        <CustomRewardCreator
          userPoints={userPoints}
          onRewardCreate={(reward) => {
            createCustomReward(reward);
            setShowCustomReward(false);
          }}
          onClose={() => setShowCustomReward(false)}
        />
      )}
    </div>
  );
};

const RewardCard: React.FC<{
  reward: Reward;
  onClaim: () => void;
  canAfford: boolean;
  userEnergy: number;
}> = ({ reward, onClaim, canAfford, userEnergy }) => {
  const getEnergyImpactColor = (impact: string) => {
    const colors = {
      positive: 'text-green-600',
      neutral: 'text-gray-600',
      negative: 'text-orange-600'
    };
    return colors[impact] || 'text-gray-600';
  };

  const shouldRecommend = () => {
    if (userEnergy <= 2 && reward.energyImpact === 'positive') return true;
    if (userEnergy >= 4 && reward.category === 'activity') return true;
    return false;
  };

  return (
    <div className={cn(
      'bg-white rounded-lg border p-4 transition-all duration-200',
      canAfford
        ? 'hover:shadow-md hover:border-blue-300'
        : 'opacity-60',
      shouldRecommend() && 'ring-2 ring-blue-200 bg-blue-50'
    )}>
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{reward.icon}</span>
          <div>
            <h4 className="font-medium text-gray-900">{reward.title}</h4>
            <p className="text-sm text-gray-600">{reward.description}</p>
          </div>
        </div>
        {shouldRecommend() && (
          <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
            Recommended
          </span>
        )}
      </div>

      <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
        <span>{reward.estimatedTime} min</span>
        <span className={getEnergyImpactColor(reward.energyImpact)}>
          {reward.energyImpact === 'positive' && '⚡ Energizing'}
          {reward.energyImpact === 'neutral' && '➖ Neutral'}
          {reward.energyImpact === 'negative' && '😴 Relaxing'}
        </span>
      </div>

      {reward.adhdBenefit && (
        <div className="bg-purple-50 text-purple-700 text-xs p-2 rounded mb-3">
          💜 {reward.adhdBenefit}
        </div>
      )}

      <div className="flex items-center justify-between">
        <span className="font-semibold text-blue-600">
          {reward.cost} points
        </span>
        <button
          onClick={onClaim}
          disabled={!canAfford}
          className={cn(
            'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
            canAfford
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-200 text-gray-500 cursor-not-allowed'
          )}
        >
          {canAfford ? 'Claim' : 'Need more points'}
        </button>
      </div>
    </div>
  );
};
```

### 2.4 Enhanced Accessibility - Inclusive Design

#### Comprehensive Accessibility Framework
```typescript
// components/accessibility/AccessibilityProvider.tsx
interface AccessibilityContextType {
  preferences: AccessibilityPreferences;
  updatePreferences: (prefs: Partial<AccessibilityPreferences>) => void;
  announceToScreenReader: (message: string) => void;
  focusManagement: FocusManagement;
}

interface AccessibilityPreferences {
  reducedMotion: boolean;
  highContrast: boolean;
  fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  colorScheme: 'light' | 'dark' | 'auto';
  screenReaderOptimized: boolean;
  keyboardNavigation: boolean;
  audioDescriptions: boolean;
  captionsEnabled: boolean;
  focusIndicatorStyle: 'default' | 'high-contrast' | 'thick';
  animationSpeed: 'slow' | 'normal' | 'fast' | 'none';
}

const AccessibilityContext = createContext<AccessibilityContextType | null>(null);

export const AccessibilityProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [preferences, setPreferences] = useState<AccessibilityPreferences>(() => {
    // Load from localStorage and system preferences
    const saved = localStorage.getItem('accessibility-preferences');
    const systemPrefs = getSystemAccessibilityPreferences();

    return {
      ...getDefaultAccessibilityPreferences(),
      ...systemPrefs,
      ...(saved ? JSON.parse(saved) : {})
    };
  });

  const updatePreferences = useCallback((newPrefs: Partial<AccessibilityPreferences>) => {
    setPreferences(prev => {
      const updated = { ...prev, ...newPrefs };
      localStorage.setItem('accessibility-preferences', JSON.stringify(updated));
      applyAccessibilityPreferences(updated);
      return updated;
    });
  }, []);

  const announceToScreenReader = useCallback((message: string) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;

    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const focusManagement = useMemo(() => ({
    trapFocus: (container: HTMLElement) => {
      // Implementation for focus trapping
    },
    restoreFocus: (element: HTMLElement) => {
      // Implementation for focus restoration
    },
    skipToContent: () => {
      const main = document.querySelector('main');
      if (main) main.focus();
    }
  }), []);

  // Apply preferences to document
  useEffect(() => {
    applyAccessibilityPreferences(preferences);
  }, [preferences]);

  return (
    <AccessibilityContext.Provider value={{
      preferences,
      updatePreferences,
      announceToScreenReader,
      focusManagement
    }}>
      {children}
    </AccessibilityContext.Provider>
  );
};

function getSystemAccessibilityPreferences(): Partial<AccessibilityPreferences> {
  const prefs: Partial<AccessibilityPreferences> = {};

  // Check for reduced motion preference
  if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    prefs.reducedMotion = true;
    prefs.animationSpeed = 'none';
  }

  // Check for high contrast preference
  if (window.matchMedia('(prefers-contrast: high)').matches) {
    prefs.highContrast = true;
    prefs.focusIndicatorStyle = 'high-contrast';
  }

  // Check for color scheme preference
  if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
    prefs.colorScheme = 'dark';
  }

  return prefs;
}

function applyAccessibilityPreferences(prefs: AccessibilityPreferences) {
  const root = document.documentElement;

  // Apply CSS custom properties
  root.style.setProperty('--font-size-multiplier', getFontSizeMultiplier(prefs.fontSize));
  root.style.setProperty('--animation-duration', getAnimationDuration(prefs.animationSpeed));

  // Apply CSS classes
  root.classList.toggle('high-contrast', prefs.highContrast);
  root.classList.toggle('reduced-motion', prefs.reducedMotion);
  root.classList.toggle('screen-reader-optimized', prefs.screenReaderOptimized);

  // Set color scheme
  root.setAttribute('data-theme', prefs.colorScheme);

  // Configure focus indicators
  root.setAttribute('data-focus-style', prefs.focusIndicatorStyle);
}
```

---

## 3. Implementation Timeline

### Week 9: Body Doubling Foundation
- **Day 1-2**: WebSocket integration and session management
- **Day 3-4**: Session browser and creation interface
- **Day 5**: Active session interface and participant management

### Week 10: AI Integration Core
- **Day 1-2**: AI task chunking interface and processing
- **Day 3-4**: Smart suggestions system and contextual AI
- **Day 5**: AI performance optimization and error handling

### Week 11: Gamification System
- **Day 1-2**: Achievement system and progress tracking
- **Day 3-4**: Reward system and dopamine optimization
- **Day 5**: Custom rewards and gamification analytics

### Week 12: Accessibility Enhancement
- **Day 1-2**: Comprehensive accessibility framework
- **Day 3-4**: Screen reader optimization and keyboard navigation
- **Day 5**: Testing and WCAG AAA compliance verification

---

## 4. Success Criteria

### Feature Adoption Metrics
- ✅ 70% of users try body doubling within first week
- ✅ 85% of users utilize AI chunking for complex tasks
- ✅ 60% increase in task completion through gamification
- ✅ 100% WCAG AAA compliance across all features

### User Experience Goals
- ✅ Seamless real-time collaboration without lag
- ✅ AI responses within 3 seconds for all requests
- ✅ Gamification increases dopamine without addiction
- ✅ Full accessibility for diverse neurodivergent needs

This Phase 3 PRD establishes the advanced features that make Project Chronos a comprehensive ADHD productivity ecosystem.