Agent 8: Gamification & Motivation System
==========================================

.. currentmodule:: app.services

Agent 8 implements a comprehensive gamification and motivation system specifically designed for ADHD users. It addresses the dopamine deficit that makes task initiation difficult by providing intrinsic motivation through points, achievements, streaks, and a personalized "dopamine menu" system.

Overview
--------

The gamification system works with the ADHD brain rather than against it, providing:

- **Immediate Dopamine Rewards**: Points and achievements for any progress
- **Flexible Streak Systems**: Accommodating ADHD inconsistency patterns
- **Energy-Aware Multipliers**: Extra rewards during low-energy periods
- **Personalized Motivation**: Dopamine menu tailored to individual preferences

Key Features
------------

🎯 ADHD-Optimized Gamification
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Points System**
   Earn points for task completion with ADHD-friendly multipliers based on:
   
   - Task difficulty and energy requirements
   - User's current energy level
   - Time of day (bonus for difficult periods)
   - Context (first task of day, after break, etc.)

**Level Progression**
   Logarithmic leveling system that prevents stagnation:
   
   - Level 1-3: Quick early progression (100, 300, 600 points)
   - Level 4+: Logarithmic scaling to maintain achievability
   - Maximum level 50 to prevent infinite grinding

**Flexible Streaks**
   Streak system designed for ADHD users:
   
   - "Streak freeze" options for difficult days
   - Recovery mechanisms for broken streaks
   - Multiple streak types (tasks, focus sessions, login)
   - Consistency rewards that don't require perfection

🌟 Dopamine Menu System
~~~~~~~~~~~~~~~~~~~~~~~

**Personalized Activities**
   Curated list of quick, enjoyable pre-task activities:
   
   - 5-minute walk, deep breathing, favorite song
   - Filtered by energy level and available time
   - Custom user-created activities
   - Effectiveness tracking and learning

**Energy-Based Filtering**
   Activities matched to current state:
   
   - Low energy: Gentle movement, breathing exercises
   - Medium energy: Creative activities, light exercise
   - High energy: Dance, vigorous movement, challenging tasks

**Context Awareness**
   Suggestions based on environment:
   
   - Location (home, office, public)
   - Available equipment and space
   - Time constraints
   - Social context

📊 Motivation Analytics
~~~~~~~~~~~~~~~~~~~~~~

**Pattern Recognition**
   AI-driven insights into motivation patterns:
   
   - Most effective activities by time/energy
   - Mood improvement tracking
   - Completion rate analysis
   - Personalized recommendations

**Progress Visualization**
   Clear progress indicators:
   
   - Level progress bars
   - Achievement galleries
   - Streak calendars
   - Statistics dashboards

Architecture
------------

Database Models
~~~~~~~~~~~~~~~

Gamification Models
^^^^^^^^^^^^^^^^^^^

.. autoclass:: app.models.gamification.UserGamification
   :members:
   :show-inheritance:

.. autoclass:: app.models.gamification.PointsAward
   :members:
   :show-inheritance:

.. autoclass:: app.models.gamification.Achievement
   :members:
   :show-inheritance:

.. autoclass:: app.models.gamification.UserAchievement
   :members:
   :show-inheritance:

.. autoclass:: app.models.gamification.UserStreak
   :members:
   :show-inheritance:

Motivation Models
^^^^^^^^^^^^^^^^^

.. autoclass:: app.models.motivation.DopamineActivity
   :members:
   :show-inheritance:

.. autoclass:: app.models.motivation.UserDopaminePreference
   :members:
   :show-inheritance:

.. autoclass:: app.models.motivation.DopamineActivityCompletion
   :members:
   :show-inheritance:

.. autoclass:: app.models.motivation.MotivationInsight
   :members:
   :show-inheritance:

Services
~~~~~~~~

Core Services
^^^^^^^^^^^^^

.. autoclass:: app.services.gamification_service.GamificationService
   :members:
   :show-inheritance:

.. autoclass:: app.services.achievement_service.AchievementService
   :members:
   :show-inheritance:

.. autoclass:: app.services.motivation_service.MotivationService
   :members:
   :show-inheritance:

Integration Utilities
^^^^^^^^^^^^^^^^^^^^^

.. autoclass:: app.utils.gamification_utils.GamificationIntegration
   :members:
   :show-inheritance:

API Endpoints
~~~~~~~~~~~~~

Gamification Endpoints
^^^^^^^^^^^^^^^^^^^^^^

The gamification API provides endpoints for managing user gamification profiles, points, achievements, and streaks.

**Base URL**: ``/api/v1/gamification``

.. http:get:: /gamification/profile

   Get user's complete gamification profile.

   **Response**:

   .. code-block:: json

      {
        "id": "uuid",
        "user_id": "uuid",
        "total_points": 1250,
        "level": 5,
        "points_to_next_level": 150,
        "gamification_enabled": true,
        "celebration_style": "moderate",
        "preferred_rewards": {
          "types": ["badges", "themes"],
          "frequency": "immediate"
        }
      }

.. http:patch:: /gamification/profile

   Update user's gamification preferences.

   **Request Body**:

   .. code-block:: json

      {
        "celebration_style": "enthusiastic",
        "preferred_rewards": {
          "types": ["badges", "points", "themes"]
        }
      }

.. http:get:: /gamification/stats

   Get comprehensive gamification statistics.

   **Response**:

   .. code-block:: json

      {
        "total_points": 1250,
        "current_level": 5,
        "points_to_next_level": 150,
        "achievements_unlocked": 12,
        "total_achievements": 25,
        "active_streaks": [
          {
            "streak_type": "daily_tasks",
            "current_streak": 7,
            "longest_streak": 14
          }
        ],
        "level_progress_percentage": 75.5
      }

.. http:get:: /gamification/achievements

   Get user achievements with progress.

   **Query Parameters**:
   
   - ``unlocked_only`` (boolean): Return only unlocked achievements

.. http:post:: /gamification/streaks/update

   Update a user's streak.

   **Request Body**:

   .. code-block:: json

      {
        "streak_type": "daily_tasks",
        "action_completed": true
      }

Motivation Endpoints
^^^^^^^^^^^^^^^^^^^^

The motivation API provides endpoints for the dopamine menu system and activity tracking.

**Base URL**: ``/api/v1/motivation``

.. http:get:: /motivation/dopamine-menu

   Get personalized dopamine menu activities.

   **Query Parameters**:
   
   - ``energy_level`` (string): Current energy level (low/medium/high)
   - ``available_time`` (integer): Available time in minutes
   - ``context`` (string): Context for the activity
   - ``exclude_categories`` (array): Categories to exclude
   - ``preferred_categories`` (array): Preferred categories

   **Response**:

   .. code-block:: json

      {
        "activities": [
          {
            "id": "uuid",
            "name": "5-Minute Walk",
            "description": "Take a quick walk around the block",
            "category": "movement",
            "duration_min": 3,
            "duration_max": 10,
            "energy_requirement": "low",
            "energy_boost": "medium"
          }
        ],
        "personalization_score": 0.8,
        "context": "pre_task",
        "energy_level": "medium"
      }

.. http:post:: /motivation/dopamine-menu/complete

   Record completion of a dopamine activity.

   **Request Body**:

   .. code-block:: json

      {
        "activity_id": "uuid",
        "actual_duration": 5,
        "energy_before": "low",
        "energy_after": "medium",
        "mood_before": 3,
        "mood_after": 6,
        "satisfaction_rating": 4,
        "would_do_again": true,
        "context": "pre_task",
        "notes": "Felt refreshing"
      }

.. http:post:: /motivation/activities/custom

   Create a custom dopamine activity.

   **Request Body**:

   .. code-block:: json

      {
        "name": "My Custom Activity",
        "description": "A personalized activity",
        "category": "creative",
        "duration": 10,
        "energy_requirement": "medium",
        "tags": ["personal", "creative"]
      }

.. http:get:: /motivation/analytics

   Get motivation analytics for the user.

   **Response**:

   .. code-block:: json

      {
        "total_activities_completed": 45,
        "favorite_categories": ["movement", "creative"],
        "most_effective_activities": [...],
        "energy_patterns": {
          "morning": "high",
          "afternoon": "medium",
          "evening": "low"
        },
        "recommendations": [
          "Try movement activities when energy is low"
        ]
      }

ADHD-Specific Features
----------------------

Dopamine-Driven Design
~~~~~~~~~~~~~~~~~~~~~~~

**Immediate Rewards**
   Points awarded instantly for any progress, no matter how small:
   
   - 5 points for starting a task
   - 20-50+ points for completing tasks
   - Bonus multipliers for difficult circumstances

**Variable Rewards**
   Multipliers and bonuses create excitement and unpredictability:
   
   - Energy level multipliers (1.3x for low energy)
   - Time-based bonuses (morning/evening)
   - Context bonuses (first task, after break)
   - Achievement unlock celebrations

**Choice Architecture**
   Multiple paths to success and validation:
   
   - Different types of achievements
   - Various dopamine activities
   - Flexible streak definitions
   - Customizable reward preferences

Energy-Aware Systems
~~~~~~~~~~~~~~~~~~~~

**Low Energy Support**
   Extra rewards for pushing through difficult periods:
   
   - 1.3x multiplier for low energy task completion
   - Gentle dopamine activities (breathing, light movement)
   - Reduced expectations and increased validation

**High Energy Channeling**
   Productive use of hyperfocus and high energy:
   
   - Challenging task suggestions
   - Energetic dopamine activities
   - Achievement opportunities

**Adaptive Recommendations**
   System learns user patterns and adapts:
   
   - Best times for different activities
   - Most effective motivation strategies
   - Energy level predictions and planning

Overwhelming Task Support
~~~~~~~~~~~~~~~~~~~~~~~~~

**Task Initiation Help**
   Specific support for the hardest part - getting started:
   
   - Dopamine menu before difficult tasks
   - Points just for starting
   - Breaking down overwhelming tasks

**Chunked Task Bonuses**
   Extra rewards for using healthy coping strategies:
   
   - 5 bonus points for completing subtasks
   - Achievement for using AI chunking
   - Progress celebration for each chunk

**Flexible Expectations**
   System accommodates ADHD inconsistency:

   - Streak freezes for difficult days
   - Recovery options for broken streaks
   - Multiple ways to earn points and achievements

Usage Examples
--------------

Basic Gamification Flow
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from app.utils.gamification_utils import GamificationIntegration
   from app.models.task import Task

   # User completes a task
   task = Task(
       title="Important Project",
       priority="high",
       energy_level="medium",
       status="completed"
   )

   # System automatically handles gamification
   gamification = GamificationIntegration(db)
   results = await gamification.handle_task_completion(
       task=task,
       user_id=user_id,
       completion_context={
           "user_energy_level": "low",
           "first_task_of_day": True
       }
   )

   # Results include:
   # - Points awarded: 45 (30 base + 1.5x multiplier)
   # - Achievement unlocked: "Week Warrior"
   # - Level up: Welcome to Level 3!
   # - Streak updated: 7-day streak maintained

Dopamine Menu Usage
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from app.services.motivation_service import MotivationService
   from app.schemas.motivation import DopamineMenuRequest

   # User needs motivation before starting work
   motivation_service = MotivationService(db)

   menu_request = DopamineMenuRequest(
       energy_level="low",
       available_time=5,
       context="pre_task"
   )

   menu = await motivation_service.get_dopamine_menu(user_id, menu_request)

   # System suggests:
   # - 5-minute walk (outdoor, energy boost)
   # - Deep breathing (quick, calming)
   # - Favorite song (instant mood lift)

   # User completes activity
   completion_data = DopamineActivityCompletionCreate(
       user_id=user_id,
       activity_id=selected_activity.id,
       actual_duration=5,
       energy_before="low",
       energy_after="medium",
       satisfaction_rating=4,
       context="pre_task"
   )

   completion = await motivation_service.complete_dopamine_activity(completion_data)

   # System learns and improves future suggestions

Points and Achievements
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from app.services.gamification_service import GamificationService
   from app.services.achievement_service import AchievementService

   # Award points with context-aware multipliers
   gamification_service = GamificationService(db)

   award, level_up = await gamification_service.award_points(
       user_id=user_id,
       points=30,
       reason="Completed difficult task during low energy",
       multiplier=1.5,  # Low energy bonus
       metadata={
           "task_difficulty": "hard",
           "energy_level": "low",
           "time_of_day": "morning"
       }
   )

   # Check for new achievements
   achievement_service = AchievementService(db)
   achievements = await achievement_service.check_achievements(
       user_id=user_id,
       trigger_event="task_completed",
       event_data={"task_count": 10, "streak_length": 7}
   )

   if achievements:
       for achievement in achievements:
           print(f"🏆 Achievement Unlocked: {achievement.achievement.name}")

Custom Activities
~~~~~~~~~~~~~~~~~

.. code-block:: python

   from app.schemas.motivation import CustomActivityRequest

   # User creates personalized dopamine activity
   custom_activity = CustomActivityRequest(
       name="Pet My Cat",
       description="Spend time with my cat for instant mood boost",
       category="social",
       duration=3,
       energy_requirement="low",
       energy_boost="high",
       tags=["pets", "comfort", "quick"]
   )

   preference = await motivation_service.create_custom_activity(
       user_id=user_id,
       activity_request=custom_activity
   )

   # Activity becomes part of personalized dopamine menu

Integration with Task System
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from app.services.task_service import TaskService
   from app.schemas.task import TaskUpdate

   # Task completion automatically triggers gamification
   task_service = TaskService()

   # When user marks task as completed
   updated_task = await task_service.update_task(
       db=db,
       task_id=task_id,
       task_data=TaskUpdate(
           status="completed",
           completion_context={
               "user_energy_level": "medium",
               "difficulty_rating": "hard",
               "time_taken": 45
           }
       ),
       user_id=user_id
   )

   # Gamification automatically:
   # 1. Calculates appropriate points
   # 2. Applies ADHD-friendly multipliers
   # 3. Updates streaks
   # 4. Checks for achievements
   # 5. Triggers celebrations if applicable

Testing
-------

The gamification system includes comprehensive test coverage:

Unit Tests
~~~~~~~~~~

.. code-block:: bash

   # Run gamification service tests
   pytest tests/test_gamification_service.py -v

   # Run motivation service tests
   pytest tests/test_motivation_service.py -v

   # Run integration tests
   pytest tests/test_gamification_integration.py -v

Test Coverage Areas
~~~~~~~~~~~~~~~~~~~

**Gamification Service Tests**
   - Point calculation and multipliers
   - Level progression logic
   - Streak management with flexibility
   - User profile creation and updates

**Motivation Service Tests**
   - Dopamine menu personalization
   - Activity filtering and recommendations
   - Completion tracking and learning
   - Custom activity creation

**Integration Tests**
   - Task completion gamification flow
   - Achievement unlocking triggers
   - Real-time updates and notifications
   - Cross-service communication

Example Test
~~~~~~~~~~~~

.. code-block:: python

   async def test_task_completion_awards_points(
       gamification_integration, mock_user, sample_task, db_session
   ):
       """Test that completing a task awards points."""
       # Mark task as completed
       sample_task.mark_completed()

       # Handle gamification
       results = await gamification_integration.handle_task_completion(
           task=sample_task,
           user_id=mock_user.id
       )

       assert results["points_awarded"] > 0
       assert "achievements_unlocked" in results
       assert "streak_updated" in results

Configuration
-------------

Environment Variables
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Gamification settings
   GAMIFICATION_ENABLED=true
   CELEBRATION_STYLE=moderate  # minimal, moderate, enthusiastic
   ACHIEVEMENT_NOTIFICATIONS=true

   # Motivation settings
   DOPAMINE_MENU_SIZE=5
   PERSONALIZATION_LEARNING=true
   ACTIVITY_EFFECTIVENESS_TRACKING=true

User Preferences
~~~~~~~~~~~~~~~~

.. code-block:: python

   # Update user gamification preferences
   await gamification_service.update_profile(
       user_id=user_id,
       preferences={
           "celebration_style": "enthusiastic",
           "preferred_rewards": ["badges", "themes", "points"],
           "gamification_enabled": True,
           "streak_freeze_limit": 3
       }
   )

Initialization
--------------

Setup Script
~~~~~~~~~~~~

.. code-block:: bash

   # Initialize gamification system
   python scripts/init_gamification.py

Manual Initialization
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from app.services.achievement_service import AchievementService
   from app.services.motivation_service import MotivationService

   # Initialize achievements
   achievement_service = AchievementService(db)
   await achievement_service.initialize_achievements()

   # Initialize dopamine activities
   motivation_service = MotivationService(db)
   await motivation_service.initialize_default_activities()

Default Achievements
~~~~~~~~~~~~~~~~~~~~

The system includes pre-configured achievements:

**Task Completion**
   - "Getting Started": Complete first task (50 points)
   - "Task Tackler": Complete 10 tasks (150 points)
   - "Productivity Pro": Complete 50 tasks (300 points)
   - "Task Master": Complete 100 tasks (500 points)

**Consistency**
   - "Building Momentum": 3-day task streak (100 points)
   - "Week Warrior": 7-day task streak (200 points)
   - "Consistency Champion": 30-day task streak (500 points)

**Focus & Motivation**
   - "Focus Beginner": Complete first focus session (75 points)
   - "Focus Master": Complete 25 focus sessions (300 points)
   - "Dopamine Explorer": Try 5 different activities (100 points)

Default Dopamine Activities
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Pre-loaded activities include:

**Movement**
   - 5-minute walk (3-10 min, low energy → medium boost)
   - Quick stretch (3-15 min, low energy → medium boost)
   - Dance break (3-5 min, medium energy → high boost)

**Mental**
   - Deep breathing (2-10 min, low energy → low boost)
   - Meditation (5-20 min, medium energy → medium boost)

**Sensory**
   - Listen to favorite song (3-5 min, low energy → high boost)
   - Look out window (1-3 min, low energy → low boost)

**Creative**
   - Doodle or sketch (5-20 min, medium energy → medium boost)
   - Write in journal (5-15 min, medium energy → medium boost)

Performance Considerations
--------------------------

The gamification system is optimized for real-time performance:

**Database Optimization**
   - Indexed foreign keys for fast lookups
   - Efficient queries with proper joins
   - Caching for frequently accessed data

**Response Times**
   - Point calculations: < 100ms
   - Achievement checks: < 200ms
   - Dopamine menu generation: < 150ms
   - Real-time updates: < 50ms

**Scalability**
   - Async/await throughout for non-blocking operations
   - Background tasks for heavy computations
   - Redis caching for hot data
   - Database connection pooling

Future Enhancements
-------------------

Planned Features
~~~~~~~~~~~~~~~~

**Social Gamification**
   - Friend connections and leaderboards
   - Shared achievements and challenges
   - Body doubling integration with points
   - Team-based motivation systems

**Advanced AI**
   - Machine learning for activity recommendations
   - Predictive motivation insights
   - Personalized achievement suggestions
   - Adaptive difficulty scaling

**Real-World Integration**
   - Physical reward redemption
   - Calendar and habit tracker integration
   - Wearable device connectivity
   - Location-based activities

**Enhanced Visualization**
   - Interactive progress charts
   - Achievement galleries with animations
   - Streak calendars with visual indicators
   - Customizable dashboard widgets

Integration Roadmap
~~~~~~~~~~~~~~~~~~~

**Phase 1: Core Integration** ✅
   - Task management integration
   - Basic achievement system
   - Dopamine menu functionality

**Phase 2: Advanced Features** 🔄
   - Focus session gamification
   - Social features and body doubling
   - Advanced analytics and insights

**Phase 3: External Integration** 📋
   - Calendar and scheduling integration
   - Third-party app connections
   - Wearable device support
   - Real-world reward systems

Contributing
------------

When contributing to Agent 8:

**ADHD-First Design**
   Always consider the ADHD user experience:

   - Immediate feedback and validation
   - Flexible systems that accommodate inconsistency
   - Multiple paths to success and achievement
   - Gentle encouragement without pressure

**Testing Requirements**
   Include comprehensive tests:

   - Unit tests for all service methods
   - Integration tests for cross-system features
   - Performance tests for real-time operations
   - User experience tests for ADHD scenarios

**Documentation Standards**
   Document with ADHD users in mind:

   - Clear, concise explanations
   - Practical examples and use cases
   - Motivation behind design decisions
   - Accessibility considerations

**Code Quality**
   Maintain high standards:

   - Type hints for all functions
   - Comprehensive error handling
   - Async/await best practices
   - Database transaction management

Support and Troubleshooting
---------------------------

Common Issues
~~~~~~~~~~~~~

**Points Not Awarded**
   Check gamification integration in task service:

   .. code-block:: python

      # Ensure gamification is enabled for user
      profile = await gamification_service.get_or_create_user_gamification(user_id)
      assert profile.gamification_enabled == True

**Achievements Not Unlocking**
   Verify achievement initialization:

   .. code-block:: bash

      # Re-run initialization script
      python scripts/init_gamification.py

**Dopamine Menu Empty**
   Check activity filters and user preferences:

   .. code-block:: python

      # Debug activity filtering
      activities = await motivation_service._filter_activities(
           energy_level="low",
           available_time=10,
           exclude_categories=[],
           preferred_categories=[]
       )
       print(f"Found {len(activities)} activities")

**Performance Issues**
   Monitor database queries and caching:

   .. code-block:: python

      # Enable query logging
      import logging
      logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)

Getting Help
~~~~~~~~~~~~

For questions about the gamification system:

1. **Check the test files** for usage examples
2. **Review the PRD document** for design rationale
3. **Test with the initialization script** for setup issues
4. **Check the API documentation** for endpoint details
5. **Review the integration tests** for cross-system examples

The gamification system is designed to be the motivational heart of Project Chronos, providing the dopamine-driven engagement that ADHD users need to overcome task initiation barriers and maintain consistent productivity habits.
