# 🚀 Project Chronos + Speechbot Deployment Guide

**The World's Most Advanced ADHD-Optimized Voice Assistant Platform**

This guide will help you deploy the complete Project Chronos platform with Speechbot integration for ADHD-optimized productivity and voice assistance.

## 📋 Table of Contents

- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
- [Detailed Setup](#detailed-setup)
- [Configuration](#configuration)
- [Services Overview](#services-overview)
- [Troubleshooting](#troubleshooting)
- [Security Considerations](#security-considerations)
- [Performance Optimization](#performance-optimization)

## 🔧 Prerequisites

### System Requirements

**Minimum Requirements:**
- **OS**: Linux (Ubuntu 20.04+ recommended), macOS, or Windows with WSL2
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 50GB free space
- **CPU**: 4 cores minimum, 8 cores recommended
- **GPU**: NVIDIA GPU with 6GB+ VRAM (for optimal Speechbot performance)

**For Production:**
- **RAM**: 32GB+ recommended
- **Storage**: 200GB+ SSD storage
- **GPU**: NVIDIA RTX 3080/4080 or better
- **Network**: Stable internet connection for model downloads

### Software Dependencies

```bash
# Docker and Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Docker Compose (if not included)
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# NVIDIA Container Toolkit (for GPU support)
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
sudo apt-get update && sudo apt-get install -y nvidia-container-toolkit
sudo systemctl restart docker

# Git (for cloning repository)
sudo apt-get install git
```

## ⚡ Quick Start

### 1. Clone Repository

```bash
git clone https://github.com/forkrul/day1-idea.git
cd day1-idea
```

### 2. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

**Required Environment Variables:**
```bash
# Database
DATABASE_URL=postgresql+asyncpg://chronos:chronos_dev@postgres:5432/chronos
POSTGRES_PASSWORD=chronos_dev

# Security
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production-please
SECRET_KEY=your-application-secret-key-change-this

# Hugging Face (for AI models)
HF_TOKEN=your_huggingface_token_here

# Email (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Domain configuration
DOMAIN=autism.localhost
```

### 3. Launch Platform

```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f
```

### 4. Access Services

Once deployed, access the platform at:

- **🏠 Main Platform**: http://chronos.autism.localhost:8090
- **🎭 Speechbot API**: http://speechbot.autism.localhost:8090
- **📊 API Documentation**: http://api.autism.localhost:8090/docs
- **📈 Grafana Dashboard**: http://grafana.autism.localhost:8090
- **🔍 Prometheus Metrics**: http://prometheus.autism.localhost:8090
- **📧 Mailhog (Email Testing)**: http://mail.autism.localhost:8090
- **🗄️ MinIO Storage**: http://minio.autism.localhost:8090
- **🔀 Traefik Dashboard**: http://traefik.autism.localhost:8091

## 🔧 Detailed Setup

### Database Initialization

```bash
# Initialize database schema
docker-compose exec speechbot python migrations/create_tables.py

# Verify database health
docker-compose exec chronos-api python -c "
from app.core.database import engine
from sqlalchemy import text
with engine.connect() as conn:
    result = conn.execute(text('SELECT 1'))
    print('Database connection successful!')
"
```

### Speechbot Model Setup

```bash
# Download and initialize Dia TTS models
docker-compose exec speechbot python -c "
import asyncio
from speechbot.services.dia_engine import DiaEngine

async def setup():
    engine = DiaEngine()
    await engine.initialize()
    print('Speechbot models initialized successfully!')

asyncio.run(setup())
"
```

### User Account Creation

```bash
# Create admin user
docker-compose exec chronos-api python -c "
from app.models.user import User
from app.core.database import SessionLocal
from app.core.security import get_password_hash

db = SessionLocal()
admin_user = User(
    email='<EMAIL>',
    hashed_password=get_password_hash('admin123'),
    full_name='Admin User',
    is_active=True,
    is_superuser=True
)
db.add(admin_user)
db.commit()
print('Admin user created: <EMAIL> / admin123')
"
```

## ⚙️ Configuration

### Domain Configuration

For production deployment, update your DNS or `/etc/hosts`:

```bash
# For local development, add to /etc/hosts:
127.0.0.1 chronos.autism.localhost
127.0.0.1 api.autism.localhost
127.0.0.1 speechbot.autism.localhost
127.0.0.1 grafana.autism.localhost
127.0.0.1 prometheus.autism.localhost
127.0.0.1 mail.autism.localhost
127.0.0.1 minio.autism.localhost
127.0.0.1 traefik.autism.localhost
```

### SSL/TLS Configuration

For production with real domains:

```yaml
# Add to docker-compose.yml traefik service
command:
  - "--certificatesresolvers.letsencrypt.acme.email=<EMAIL>"
  - "--certificatesresolvers.letsencrypt.acme.storage=/acme.json"
  - "--certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web"

# Update service labels
labels:
  - "traefik.http.routers.chronos-ui.tls.certresolver=letsencrypt"
```

### GPU Configuration

Ensure NVIDIA GPU is properly configured:

```bash
# Test GPU access
docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi

# Verify Speechbot GPU access
docker-compose exec speechbot python -c "
import torch
print(f'CUDA available: {torch.cuda.is_available()}')
print(f'GPU count: {torch.cuda.device_count()}')
if torch.cuda.is_available():
    print(f'GPU name: {torch.cuda.get_device_name(0)}')
"
```

## 🏗️ Services Overview

### Core Services

| Service | Purpose | Port | Health Check |
|---------|---------|------|--------------|
| **chronos-api** | Main FastAPI backend | 8000 | `/health` |
| **chronos-ui** | Next.js frontend | 3000 | HTTP 200 |
| **speechbot** | Voice synthesis service | 8001 | `/health` |
| **postgres** | Primary database | 5432 | `pg_isready` |
| **redis** | Caching and sessions | 6379 | `redis-cli ping` |

### Supporting Services

| Service | Purpose | Port | Access |
|---------|---------|------|--------|
| **traefik** | Reverse proxy | 80/8080 | Dashboard |
| **prometheus** | Metrics collection | 9090 | Web UI |
| **grafana** | Monitoring dashboard | 3000 | admin/admin |
| **mailhog** | Email testing | 8025 | Web UI |
| **minio** | Object storage | 9000/9001 | Console |

### Service Dependencies

```mermaid
graph TD
    A[Traefik] --> B[Chronos UI]
    A --> C[Chronos API]
    A --> D[Speechbot]
    C --> E[PostgreSQL]
    C --> F[Redis]
    D --> E
    D --> F
    G[Prometheus] --> C
    G --> D
    H[Grafana] --> G
```

## 🔍 Health Checks

### Automated Health Verification

```bash
#!/bin/bash
# health_check.sh

echo "🔍 Checking Project Chronos Health..."

services=(
    "http://chronos.autism.localhost:8090"
    "http://api.autism.localhost:8090/health"
    "http://speechbot.autism.localhost:8090/health"
)

for service in "${services[@]}"; do
    if curl -f -s "$service" > /dev/null; then
        echo "✅ $service - OK"
    else
        echo "❌ $service - FAILED"
    fi
done

echo "🎭 Testing Speechbot capabilities..."
curl -s "http://speechbot.autism.localhost:8090/api/v1/tts/capabilities" | jq .

echo "📊 Database health..."
docker-compose exec -T chronos-api python -c "
from app.core.database import engine
from sqlalchemy import text
try:
    with engine.connect() as conn:
        conn.execute(text('SELECT 1'))
    print('✅ Database - OK')
except Exception as e:
    print(f'❌ Database - FAILED: {e}')
"
```

### Manual Testing

```bash
# Test voice synthesis
curl -X POST "http://speechbot.autism.localhost:8090/api/v1/tts/quick" \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello, this is a test of the ADHD voice assistant!", "mode": "calm"}' \
  --output test_audio.wav

# Test user registration
curl -X POST "http://api.autism.localhost:8090/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpass123",
    "full_name": "Test User"
  }'
```

## 🚨 Troubleshooting

### Common Issues

**1. Services Won't Start**
```bash
# Check Docker daemon
sudo systemctl status docker

# Check available resources
docker system df
docker system prune -f

# Restart services
docker-compose down
docker-compose up -d
```

**2. GPU Not Detected**
```bash
# Install NVIDIA drivers
sudo apt install nvidia-driver-525

# Restart Docker
sudo systemctl restart docker

# Verify GPU access
nvidia-smi
```

**3. Database Connection Issues**
```bash
# Check PostgreSQL logs
docker-compose logs postgres

# Reset database
docker-compose down -v
docker-compose up -d postgres
# Wait for startup, then run migrations
```

**4. Memory Issues**
```bash
# Check memory usage
docker stats

# Increase swap space
sudo fallocate -l 8G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

**5. Port Conflicts**
```bash
# Check port usage
sudo netstat -tulpn | grep :8090

# Kill conflicting processes
sudo fuser -k 8090/tcp
```

### Log Analysis

```bash
# View all logs
docker-compose logs -f

# Service-specific logs
docker-compose logs -f speechbot
docker-compose logs -f chronos-api

# Error filtering
docker-compose logs | grep -i error
```

## 🔒 Security Considerations

### Production Security Checklist

- [ ] **Change default passwords** in `.env` file
- [ ] **Generate strong JWT secrets** (32+ characters)
- [ ] **Enable SSL/TLS** with Let's Encrypt
- [ ] **Configure firewall** to restrict access
- [ ] **Update dependencies** regularly
- [ ] **Enable audit logging**
- [ ] **Backup encryption keys**
- [ ] **Monitor security alerts**

### Security Updates

```bash
# Update vulnerable dependencies
docker-compose exec chronos-ui npm audit fix
docker-compose exec speechbot pip install --upgrade -r requirements.txt

# Rebuild containers with updates
docker-compose build --no-cache
docker-compose up -d
```

## ⚡ Performance Optimization

### Production Optimizations

**1. Database Tuning**
```sql
-- PostgreSQL optimizations
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
SELECT pg_reload_conf();
```

**2. Redis Configuration**
```bash
# Add to docker-compose.yml redis service
command: redis-server --maxmemory 512mb --maxmemory-policy allkeys-lru
```

**3. Speechbot GPU Optimization**
```yaml
# In docker-compose.yml speechbot service
environment:
  - CUDA_VISIBLE_DEVICES=0
  - DIA_BATCH_SIZE=4
  - DIA_PRECISION=fp16
```

### Monitoring Setup

```bash
# Import Grafana dashboards
curl -X POST \
  http://admin:<EMAIL>:8090/api/dashboards/db \
  -H 'Content-Type: application/json' \
  -d @monitoring/grafana/dashboards/chronos-overview.json
```

## 🎯 Next Steps

After successful deployment:

1. **🧪 Run Integration Tests**
   ```bash
   python test_platform_integration.py
   ```

2. **👥 Set Up User Testing**
   ```bash
   python user_testing/testing_framework.py
   ```

3. **📊 Configure Monitoring**
   - Set up Grafana alerts
   - Configure Prometheus targets
   - Enable log aggregation

4. **🚀 Community Beta**
   - Invite ADHD community members
   - Collect feedback through testing framework
   - Iterate based on user needs

## 📞 Support

For deployment issues:

- **📖 Documentation**: Check this guide and inline comments
- **🐛 Issues**: Create GitHub issue with logs and system info
- **💬 Community**: Join ADHD tech community discussions
- **📧 Contact**: Reach out to development team

---

**🎉 Congratulations! You've deployed the world's most advanced ADHD-optimized voice assistant platform!**

The platform is now ready to revolutionize how ADHD individuals interact with voice technology. 🌟
