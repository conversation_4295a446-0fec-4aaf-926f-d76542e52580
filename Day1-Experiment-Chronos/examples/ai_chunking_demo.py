#!/usr/bin/env python3
"""
AI Chunking Demo for Project Chronos.

This script demonstrates the AI-powered task chunking functionality
specifically designed for ADHD users to break down overwhelming tasks.
"""

import asyncio
import json
import sys
import os

# Add parent directory to path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.ai_service import AIChunkingService


async def demo_ai_chunking():
    """
    Demonstrate AI chunking with sample ADHD-challenging tasks.
    
    Shows how the system breaks down overwhelming tasks into
    manageable, actionable steps for users with ADHD.
    """
    print("🧠 Project Chronos - AI Task Chunking Demo")
    print("=" * 50)
    print("Demonstrating ADHD-optimized task breakdown\n")
    
    # Initialize AI service
    ai_service = AIChunkingService()
    
    # Sample tasks that commonly cause paralysis for ADHD users
    sample_tasks = [
        {
            "title": "Prepare quarterly presentation",
            "description": "Create a comprehensive presentation for Q4 results including data analysis, charts, and recommendations",
            "chunk_size": "small",
            "context": "Work presentation for management team, due next week"
        },
        {
            "title": "Organize home office",
            "description": "Clean and organize the cluttered home office space with papers, books, and equipment everywhere",
            "chunk_size": "medium",
            "context": "Working from home, need focused environment"
        },
        {
            "title": "Plan vacation trip",
            "description": "Research destinations, book flights and hotels, plan activities, and prepare itinerary",
            "chunk_size": "small",
            "context": "Two-week vacation in Europe, traveling with family"
        }
    ]
    
    for i, task in enumerate(sample_tasks, 1):
        print(f"📋 Task {i}: {task['title']}")
        print(f"Description: {task['description']}")
        print(f"Chunk Size: {task['chunk_size']}")
        print(f"Context: {task['context']}")
        print("\n🤖 AI Chunking Result:")
        
        try:
            # Attempt to chunk the task
            chunks = await ai_service.chunk_task(
                title=task["title"],
                description=task["description"],
                chunk_size=task["chunk_size"],
                context=task["context"],
                user_preferences={
                    "adhd_diagnosis": True,
                    "preferred_duration": 15,
                    "energy_level": "medium"
                }
            )
            
            if chunks:
                for j, chunk in enumerate(chunks, 1):
                    print(f"  {j}. {chunk['title']}")
                    print(f"     📝 {chunk['description']}")
                    print(f"     ⏱️  {chunk['estimated_duration']} minutes")
                    print(f"     ⚡ Energy: {chunk['energy_level']}")
                    if chunk['context_tags']:
                        print(f"     🏷️  Tags: {', '.join(chunk['context_tags'])}")
                    print()
            else:
                print("  ❌ No chunks generated")
                
        except Exception as e:
            print(f"  ⚠️  AI chunking not available: {str(e)}")
            print("  💡 This demo requires OpenAI or Anthropic API keys")
            
            # Show what the output would look like
            print("  📝 Example chunking result:")
            if task["chunk_size"] == "small":
                example_chunks = [
                    {
                        "title": "Open presentation software",
                        "description": "Launch PowerPoint or Google Slides and create new presentation",
                        "estimated_duration": 5,
                        "energy_level": "low",
                        "context_tags": ["computer", "office"]
                    },
                    {
                        "title": "Create title slide",
                        "description": "Add presentation title, date, and your name to first slide",
                        "estimated_duration": 10,
                        "energy_level": "low",
                        "context_tags": ["computer", "creative"]
                    },
                    {
                        "title": "Draft outline",
                        "description": "List 3-5 main points to cover in the presentation",
                        "estimated_duration": 15,
                        "energy_level": "medium",
                        "context_tags": ["planning", "focused"]
                    }
                ]
            else:
                example_chunks = [
                    {
                        "title": "Gather all papers and documents",
                        "description": "Collect all loose papers, bills, and documents into one pile",
                        "estimated_duration": 20,
                        "energy_level": "medium",
                        "context_tags": ["home", "organizing"]
                    },
                    {
                        "title": "Sort documents into categories",
                        "description": "Create piles for bills, important docs, and items to discard",
                        "estimated_duration": 30,
                        "energy_level": "medium",
                        "context_tags": ["home", "focused"]
                    }
                ]
            
            for j, chunk in enumerate(example_chunks, 1):
                print(f"    {j}. {chunk['title']}")
                print(f"       📝 {chunk['description']}")
                print(f"       ⏱️  {chunk['estimated_duration']} minutes")
                print(f"       ⚡ Energy: {chunk['energy_level']}")
                print(f"       🏷️  Tags: {', '.join(chunk['context_tags'])}")
                print()
        
        print("-" * 50)
        print()
    
    print("✨ Demo Complete!")
    print("\n💡 Key ADHD Benefits:")
    print("• Removes decision paralysis by providing clear next steps")
    print("• Breaks overwhelming tasks into manageable chunks")
    print("• Includes time estimates to combat time blindness")
    print("• Energy level matching for current capacity")
    print("• Context tags for situational task selection")
    print("• Eliminates ambiguity with specific action items")


async def demo_adaptive_filtering():
    """
    Demonstrate adaptive filtering concepts for ADHD users.
    """
    print("\n🎯 Adaptive Filtering Demo")
    print("=" * 30)
    print("How tasks are filtered based on user state:\n")
    
    # Sample user states
    user_states = [
        {
            "energy_level": "low",
            "available_time": 15,
            "context": ["home", "tired"],
            "description": "End of long day, low energy"
        },
        {
            "energy_level": "high",
            "available_time": 90,
            "context": ["office", "focused"],
            "description": "Morning, well-rested, focused"
        },
        {
            "energy_level": "medium",
            "available_time": 30,
            "context": ["home", "computer"],
            "description": "Afternoon break, moderate energy"
        }
    ]
    
    for state in user_states:
        print(f"🧠 User State: {state['description']}")
        print(f"   ⚡ Energy: {state['energy_level']}")
        print(f"   ⏰ Time: {state['available_time']} minutes")
        print(f"   📍 Context: {', '.join(state['context'])}")
        print("\n   🎯 Recommended Tasks:")
        
        if state["energy_level"] == "low":
            tasks = [
                "Check and respond to 2-3 emails",
                "File downloaded documents",
                "Update calendar for tomorrow"
            ]
        elif state["energy_level"] == "high":
            tasks = [
                "Work on quarterly report analysis",
                "Tackle complex problem-solving task",
                "Plan next week's priorities"
            ]
        else:
            tasks = [
                "Review and organize task list",
                "Make important phone call",
                "Research topic for upcoming project"
            ]
        
        for task in tasks:
            print(f"     • {task}")
        
        print()


if __name__ == "__main__":
    print("Starting Project Chronos AI Chunking Demo...")
    print("This demonstrates ADHD-optimized task management features.\n")
    
    asyncio.run(demo_ai_chunking())
    asyncio.run(demo_adaptive_filtering())
