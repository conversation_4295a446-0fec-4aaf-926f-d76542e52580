# Chronos UI - ADHD-Optimized Task Management Interface

A modern, responsive web application built with Next.js 14, TypeScript, and Tailwind CSS, specifically designed for ADHD users with cognitive load awareness and adaptive interfaces.

## 🧠 ADHD-Optimized Features

### Core ADHD Adaptations
- **Cognitive Load Awareness**: Interface adapts based on mental fatigue levels
- **Energy Level Matching**: Task recommendations match current energy state
- **Focus Mode Support**: Different interfaces for different types of work
- **Sensory Sensitivity**: Reduced motion and high contrast options
- **Executive Function Support**: Clear navigation and simplified workflows

### Adaptive Interface Elements
- **Dynamic Layout**: Narrower layouts during high cognitive load
- **Color-Coded States**: Visual indicators for energy, cognitive load, and focus
- **Gentle Animations**: Reduced motion for sensory-sensitive users
- **ADHD-Friendly Error Messages**: Supportive, actionable feedback
- **Break Suggestions**: Intelligent recommendations based on usage patterns

## 🚀 Technology Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom ADHD-optimized design system
- **State Management**: Zustand + React Query
- **UI Components**: Headless UI + Custom components
- **Icons**: Heroicons
- **Forms**: React Hook Form + Zod validation
- **Animations**: Framer Motion (with reduced motion support)

## 📁 Project Structure

```
chronos-ui/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── layout.tsx         # Root layout with providers
│   │   ├── page.tsx           # Dashboard homepage
│   │   ├── globals.css        # Global styles and ADHD utilities
│   │   └── providers.tsx      # Context providers setup
│   ├── components/
│   │   ├── auth/              # Authentication components
│   │   │   └── LoginForm.tsx  # ADHD-friendly login form
│   │   ├── dashboard/         # Dashboard components
│   │   │   ├── WelcomeCard.tsx        # Personalized welcome with ADHD state
│   │   │   ├── EnergyLevelCard.tsx    # Energy level tracking and updates
│   │   │   ├── TaskOverviewCard.tsx   # Task statistics and energy matching
│   │   │   ├── FocusSessionCard.tsx   # Pomodoro and focus session timer
│   │   │   ├── UpcomingTasksCard.tsx  # Scheduled tasks overview
│   │   │   ├── MotivationCard.tsx     # Gamification and achievements
│   │   │   └── QuickActionsCard.tsx   # Common actions grid
│   │   ├── layout/            # Layout components
│   │   │   ├── DashboardLayout.tsx    # Main app layout
│   │   │   ├── Sidebar.tsx            # Navigation with ADHD state
│   │   │   └── Header.tsx             # Header with cognitive load alerts
│   │   └── ui/                # Reusable UI components
│   │       └── LoadingSpinner.tsx     # Accessible loading indicator
│   ├── contexts/              # React contexts
│   │   ├── AuthContext.tsx    # Authentication state management
│   │   ├── ADHDStateContext.tsx       # ADHD state tracking
│   │   └── ThemeContext.tsx   # Theme and accessibility preferences
│   ├── hooks/                 # Custom React hooks
│   │   ├── useAuth.ts         # Authentication hook
│   │   ├── useADHDState.ts    # ADHD state management hook
│   │   └── useTasks.ts        # Task management with React Query
│   ├── lib/                   # Utility libraries
│   │   ├── api.ts             # Axios configuration with ADHD-friendly errors
│   │   └── utils.ts           # Utility functions for ADHD features
│   └── types/                 # TypeScript type definitions
├── public/                    # Static assets
├── package.json              # Dependencies and scripts
├── tailwind.config.js        # Tailwind with ADHD color palette
├── next.config.js            # Next.js configuration
└── tsconfig.json             # TypeScript configuration
```

## 🎨 ADHD Design System

### Color Palette
- **Primary**: Blue tones for focus and clarity
- **Secondary**: Purple tones for creativity and calm
- **Success**: Green tones for achievements and positive feedback
- **Warning**: Yellow/orange for gentle alerts
- **Error**: Red tones for important issues (used sparingly)
- **Energy Indicators**: Red (low), Yellow (medium), Green (high)
- **Cognitive Load**: Green (low), Yellow (medium), Red (high)

### Typography
- **Font**: Inter for excellent readability
- **Sizes**: Carefully chosen for cognitive accessibility
- **Weights**: Strategic use of bold for hierarchy without overwhelm

### Spacing and Layout
- **Generous Spacing**: Reduces visual clutter
- **Adaptive Layouts**: Adjust based on cognitive load
- **Clear Hierarchy**: Easy to scan and understand

## 🔧 Getting Started

### Prerequisites
- Node.js 18+ 
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd chronos-ui
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Configure the following variables:
   ```env
   NEXT_PUBLIC_API_URL=http://localhost:8000
   NEXT_PUBLIC_WS_URL=ws://localhost:8000
   ```

4. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🧪 Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking
- `npm run test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage

### Code Quality
- **ESLint**: Configured with Next.js and TypeScript rules
- **Prettier**: Code formatting with Tailwind CSS plugin
- **TypeScript**: Strict type checking enabled
- **Testing**: Jest and React Testing Library setup

## 🎯 Key Features

### Dashboard
- **Personalized Welcome**: Greeting with ADHD state overview
- **Energy Level Tracking**: Interactive energy level updates with recommendations
- **Task Overview**: Statistics with energy-matched task suggestions
- **Focus Sessions**: Pomodoro timer with ADHD-optimized session types
- **Motivation System**: Gamification with achievements and progress tracking
- **Quick Actions**: Easy access to common tasks

### ADHD State Management
- **Real-time Tracking**: Energy level, cognitive load, focus mode, emotional state
- **Adaptive Recommendations**: Task and session suggestions based on current state
- **Break Suggestions**: Intelligent break timing based on cognitive load
- **State Persistence**: Automatic saving and loading of ADHD state

### Accessibility
- **WCAG 2.1 AA Compliance**: Full accessibility support
- **Keyboard Navigation**: Complete keyboard accessibility
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Reduced Motion**: Respects user's motion preferences
- **High Contrast**: Support for high contrast mode

### Performance
- **Optimized Loading**: Lazy loading and code splitting
- **Caching**: React Query for efficient data fetching
- **Bundle Size**: Optimized for fast loading
- **Core Web Vitals**: Excellent performance scores

## 🔌 API Integration

The UI connects to the Chronos backend API with the following endpoints:

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `GET /api/v1/auth/me` - Get current user
- `POST /api/v1/auth/logout` - User logout

### Tasks
- `GET /api/v1/tasks` - List tasks with filtering
- `POST /api/v1/tasks` - Create new task
- `PATCH /api/v1/tasks/:id` - Update task
- `DELETE /api/v1/tasks/:id` - Delete task
- `POST /api/v1/tasks/:id/complete` - Mark task complete

### ADHD State
- `GET /api/v1/adhd/state` - Get current ADHD state
- `PATCH /api/v1/adhd/state` - Update ADHD state
- `GET /api/v1/adhd/insights` - Get personalized insights
- `GET /api/v1/adhd/recommendations` - Get recommendations

### Focus Sessions
- `GET /api/v1/focus/sessions` - List focus sessions
- `POST /api/v1/focus/start` - Start new session
- `POST /api/v1/focus/:id/pause` - Pause session
- `POST /api/v1/focus/:id/complete` - Complete session

## 🌟 ADHD-Specific Implementation Details

### Cognitive Load Adaptation
```typescript
// Layout adapts based on cognitive load
const getLayoutClasses = () => {
  if (adhdState.cognitiveLoad > 0.7) {
    return 'max-w-4xl' // Narrower layout for high cognitive load
  }
  return 'max-w-7xl' // Full width for normal cognitive load
}
```

### Energy-Matched Task Recommendations
```typescript
// Filter tasks based on current energy level
const energyMatchedTasks = pendingTasks.filter((task: any) => {
  const taskEnergyRequired = task.energyRequired || 5
  const energyDiff = Math.abs(taskEnergyRequired - adhdState.energyLevel)
  return energyDiff <= 2 // Tasks within 2 energy levels
})
```

### Intelligent Break Suggestions
```typescript
// Suggest breaks based on multiple ADHD factors
export function shouldSuggestBreak(
  lastBreak: Date | null,
  currentCognitiveLoad: number,
  energyLevel: number
): boolean {
  const now = new Date()
  const timeSinceBreak = lastBreak ? now.getTime() - lastBreak.getTime() : Infinity
  const minutesSinceBreak = timeSinceBreak / (1000 * 60)

  // Multiple break triggers for ADHD needs
  if (currentCognitiveLoad > 0.7 && minutesSinceBreak > 20) return true
  if (currentCognitiveLoad > 0.4 && minutesSinceBreak > 45) return true
  if (energyLevel < 4 && minutesSinceBreak > 30) return true
  if (minutesSinceBreak > 90) return true

  return false
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### ADHD-Focused Development Guidelines
- Always consider cognitive load impact of new features
- Test with reduced motion and high contrast settings
- Ensure keyboard accessibility for all interactions
- Use encouraging, supportive language in user-facing text
- Consider executive function challenges in UX design

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with ADHD users in mind, incorporating research-based design principles
- Inspired by the ADHD community's feedback and needs
- Designed to reduce cognitive load while maintaining functionality
- Accessibility guidelines from WCAG 2.1 and ADHD-specific research

---

**Note**: This UI is designed to work with the Chronos backend API. Make sure the backend is running and accessible at the configured API URL for full functionality.
