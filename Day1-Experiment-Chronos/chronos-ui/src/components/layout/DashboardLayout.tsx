'use client'

import { useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useADHDState } from '@/hooks/useADHDState'
import { Sidebar } from './Sidebar'
import { Header } from './Header'
import { cn } from '@/lib/utils'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { user } = useAuth()
  const { adhdState } = useADHDState()

  // Adapt layout based on cognitive load
  const getLayoutClasses = () => {
    if (adhdState.cognitiveLoad > 0.7) {
      return 'max-w-4xl' // Narrower layout for high cognitive load
    }
    return 'max-w-7xl' // Full width for normal cognitive load
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Header */}
        <Header onMenuClick={() => setSidebarOpen(true)} />

        {/* Page content */}
        <main className="py-6">
          <div className={cn('mx-auto px-4 sm:px-6 lg:px-8', getLayoutClasses())}>
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
