'use client'

import { useState } from 'react'
import { useADHDState } from '@/hooks/useADHDState'
import { useAuth } from '@/hooks/useAuth'
import {
  Bars3Icon,
  BellIcon,
  MagnifyingGlassIcon,
  SunIcon,
  MoonIcon,
} from '@heroicons/react/24/outline'
import { useTheme } from '@/contexts/ThemeContext'
import { cn, shouldSuggestBreak } from '@/lib/utils'

interface HeaderProps {
  onMenuClick: () => void
}

export function Header({ onMenuClick }: HeaderProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const { user } = useAuth()
  const { adhdState } = useADHDState()
  const { theme, setTheme, isDark } = useTheme()

  // Check if we should suggest a break
  const suggestBreak = shouldSuggestBreak(
    null, // We'd need to track last break time
    adhdState.cognitiveLoad,
    adhdState.energyLevel
  )

  const toggleTheme = () => {
    setTheme(isDark ? 'light' : 'dark')
  }

  return (
    <header className="bg-white border-b border-gray-200 lg:border-none">
      <div className="mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Mobile menu button */}
          <button
            type="button"
            className="lg:hidden -m-2.5 p-2.5 text-gray-700"
            onClick={onMenuClick}
          >
            <Bars3Icon className="h-6 w-6" />
          </button>

          {/* Search */}
          <div className="flex flex-1 items-center justify-center lg:justify-start">
            <div className="w-full max-w-lg lg:max-w-xs">
              <label htmlFor="search" className="sr-only">
                Search
              </label>
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="search"
                  name="search"
                  className="block w-full rounded-md border-0 bg-gray-50 py-1.5 pl-10 pr-3 text-gray-900 placeholder:text-gray-400 focus:bg-white focus:ring-2 focus:ring-primary-500 sm:text-sm sm:leading-6"
                  placeholder="Search tasks, notes, or insights..."
                  type="search"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            {/* Break suggestion */}
            {suggestBreak && (
              <div className="hidden sm:block">
                <div className="flex items-center space-x-2 px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">
                  <span>🌱</span>
                  <span>Break time?</span>
                </div>
              </div>
            )}

            {/* ADHD State Quick View */}
            <div className="hidden md:flex items-center space-x-3">
              {/* Energy Level */}
              <div className="flex items-center space-x-1">
                <div className={cn(
                  'w-2 h-2 rounded-full',
                  adhdState.energyLevel <= 3 && 'bg-red-400',
                  adhdState.energyLevel > 3 && adhdState.energyLevel <= 6 && 'bg-yellow-400',
                  adhdState.energyLevel > 6 && 'bg-green-400'
                )} />
                <span className="text-sm text-gray-600">
                  {adhdState.energyLevel}/10
                </span>
              </div>

              {/* Cognitive Load */}
              <div className="flex items-center space-x-1">
                <div className={cn(
                  'w-2 h-2 rounded-full',
                  adhdState.cognitiveLoad >= 0.7 && 'bg-red-400',
                  adhdState.cognitiveLoad >= 0.4 && adhdState.cognitiveLoad < 0.7 && 'bg-yellow-400',
                  adhdState.cognitiveLoad < 0.4 && 'bg-green-400'
                )} />
                <span className="text-sm text-gray-600">
                  {Math.round(adhdState.cognitiveLoad * 100)}%
                </span>
              </div>
            </div>

            {/* Theme toggle */}
            <button
              type="button"
              onClick={toggleTheme}
              className="p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-md"
            >
              {isDark ? (
                <SunIcon className="h-5 w-5" />
              ) : (
                <MoonIcon className="h-5 w-5" />
              )}
            </button>

            {/* Notifications */}
            <button
              type="button"
              className="relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-md"
            >
              <BellIcon className="h-5 w-5" />
              {/* Notification badge */}
              <span className="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white" />
            </button>

            {/* User avatar */}
            <div className="flex items-center">
              <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-primary-600">
                  {user?.name?.charAt(0).toUpperCase()}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* ADHD-specific header alerts */}
      {adhdState.cognitiveLoad > 0.8 && (
        <div className="bg-red-50 border-b border-red-200 px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-red-600 mr-2">🧠</span>
              <span className="text-sm text-red-800">
                High cognitive load detected. Consider taking a break or switching to easier tasks.
              </span>
            </div>
            <button className="text-sm text-red-600 hover:text-red-800 font-medium">
              Take Break
            </button>
          </div>
        </div>
      )}

      {adhdState.energyLevel <= 2 && (
        <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-yellow-600 mr-2">⚡</span>
              <span className="text-sm text-yellow-800">
                Low energy detected. Try some light movement or a healthy snack.
              </span>
            </div>
            <button className="text-sm text-yellow-600 hover:text-yellow-800 font-medium">
              Energy Boost
            </button>
          </div>
        </div>
      )}
    </header>
  )
}
