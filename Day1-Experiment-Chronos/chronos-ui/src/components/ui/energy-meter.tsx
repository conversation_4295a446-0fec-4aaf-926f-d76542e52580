import * as React from "react"
import { cn } from "@/lib/utils"
import { Progress } from "./progress"
import { Battery, Zap, Coffee, Sparkles } from "lucide-react"

interface EnergyMeterProps {
  level: number // 1-10
  className?: string
  showIcon?: boolean
  showLabel?: boolean
  size?: "sm" | "md" | "lg"
  variant?: "default" | "colorful" | "minimal"
}

const EnergyMeter = React.forwardRef<
  HTMLDivElement,
  EnergyMeterProps
>(({ 
  level, 
  className, 
  showIcon = true, 
  showLabel = true, 
  size = "md",
  variant = "default",
  ...props 
}, ref) => {
  const percentage = Math.max(0, Math.min(100, (level / 10) * 100))
  
  const getEnergyColor = (level: number) => {
    if (level <= 3) return "text-red-500"
    if (level <= 6) return "text-yellow-500"
    if (level <= 8) return "text-green-500"
    return "text-emerald-500"
  }

  const getEnergyIcon = (level: number) => {
    if (level <= 3) return <Battery className="h-4 w-4" />
    if (level <= 6) return <Coffee className="h-4 w-4" />
    if (level <= 8) return <Zap className="h-4 w-4" />
    return <Sparkles className="h-4 w-4" />
  }

  const getEnergyLabel = (level: number) => {
    if (level <= 2) return "Depleted"
    if (level <= 4) return "Low Energy"
    if (level <= 6) return "Moderate"
    if (level <= 8) return "High Energy"
    return "Peak Energy"
  }

  const getProgressColor = (level: number) => {
    if (variant === "minimal") return ""
    
    if (level <= 3) return "bg-red-500"
    if (level <= 6) return "bg-yellow-500"
    if (level <= 8) return "bg-green-500"
    return "bg-emerald-500"
  }

  const sizeClasses = {
    sm: "h-2",
    md: "h-3",
    lg: "h-4"
  }

  return (
    <div
      ref={ref}
      className={cn("flex items-center space-x-3", className)}
      {...props}
    >
      {showIcon && (
        <div className={cn("flex-shrink-0", getEnergyColor(level))}>
          {getEnergyIcon(level)}
        </div>
      )}
      
      <div className="flex-1 space-y-1">
        {showLabel && (
          <div className="flex justify-between items-center text-sm">
            <span className="font-medium">{getEnergyLabel(level)}</span>
            <span className="text-muted-foreground">{level}/10</span>
          </div>
        )}
        
        <div className="relative">
          <Progress 
            value={percentage} 
            className={cn(sizeClasses[size])}
          />
          {variant === "colorful" && (
            <div 
              className={cn(
                "absolute top-0 left-0 h-full rounded-full transition-all",
                getProgressColor(level)
              )}
              style={{ width: `${percentage}%` }}
            />
          )}
        </div>
      </div>
    </div>
  )
})

EnergyMeter.displayName = "EnergyMeter"

export { EnergyMeter }
