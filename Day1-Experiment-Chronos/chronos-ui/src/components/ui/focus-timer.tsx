import * as React from "react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "./button"
import { Progress } from "./progress"
import { Play, Pause, Square, RotateCcw, Coffee } from "lucide-react"

interface FocusTimerProps {
  duration: number // in minutes
  elapsed: number // in minutes
  isActive: boolean
  isPaused: boolean
  onStart?: () => void
  onPause?: () => void
  onStop?: () => void
  onReset?: () => void
  onBreak?: () => void
  className?: string
  showControls?: boolean
  variant?: "default" | "minimal" | "detailed"
}

const FocusTimer = React.forwardRef<
  HTMLDivElement,
  FocusTimerProps
>(({ 
  duration,
  elapsed,
  isActive,
  isPaused,
  onStart,
  onPause,
  onStop,
  onReset,
  onBreak,
  className,
  showControls = true,
  variant = "default",
  ...props 
}, ref) => {
  const remaining = Math.max(0, duration - elapsed)
  const progress = duration > 0 ? (elapsed / duration) * 100 : 0
  
  const formatTime = (minutes: number) => {
    const mins = Math.floor(minutes)
    const secs = Math.floor((minutes - mins) * 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const getTimerColor = () => {
    if (!isActive) return "text-muted-foreground"
    if (remaining <= 5) return "text-red-500"
    if (remaining <= 10) return "text-yellow-500"
    return "text-green-500"
  }

  const getProgressColor = () => {
    if (remaining <= 5) return "bg-red-500"
    if (remaining <= 10) return "bg-yellow-500"
    return "bg-green-500"
  }

  if (variant === "minimal") {
    return (
      <div
        ref={ref}
        className={cn("flex items-center space-x-2", className)}
        {...props}
      >
        <div className={cn("font-mono text-lg font-bold", getTimerColor())}>
          {formatTime(remaining)}
        </div>
        <Progress value={progress} className="flex-1 h-2" />
      </div>
    )
  }

  return (
    <div
      ref={ref}
      className={cn("space-y-4", className)}
      {...props}
    >
      {/* Timer Display */}
      <div className="text-center space-y-2">
        <div className={cn("font-mono text-4xl font-bold", getTimerColor())}>
          {formatTime(remaining)}
        </div>
        
        {variant === "detailed" && (
          <div className="text-sm text-muted-foreground space-y-1">
            <div>Elapsed: {formatTime(elapsed)}</div>
            <div>Total: {formatTime(duration)}</div>
          </div>
        )}
      </div>

      {/* Progress Bar */}
      <div className="relative">
        <Progress value={progress} className="h-3" />
        <div 
          className={cn(
            "absolute top-0 left-0 h-full rounded-full transition-all",
            getProgressColor()
          )}
          style={{ width: `${Math.min(100, progress)}%` }}
        />
      </div>

      {/* Status Indicator */}
      <div className="text-center">
        {isActive && !isPaused && (
          <div className="flex items-center justify-center space-x-2 text-green-600">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className="text-sm font-medium">Focus Session Active</span>
          </div>
        )}
        
        {isPaused && (
          <div className="flex items-center justify-center space-x-2 text-yellow-600">
            <div className="w-2 h-2 bg-yellow-500 rounded-full" />
            <span className="text-sm font-medium">Session Paused</span>
          </div>
        )}
        
        {!isActive && (
          <div className="flex items-center justify-center space-x-2 text-muted-foreground">
            <div className="w-2 h-2 bg-gray-400 rounded-full" />
            <span className="text-sm font-medium">Ready to Start</span>
          </div>
        )}
      </div>

      {/* Controls */}
      {showControls && (
        <div className="flex justify-center space-x-2">
          {!isActive || isPaused ? (
            <Button
              onClick={onStart}
              size="sm"
              className="flex items-center space-x-2"
            >
              <Play className="h-4 w-4" />
              <span>{isPaused ? "Resume" : "Start"}</span>
            </Button>
          ) : (
            <Button
              onClick={onPause}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <Pause className="h-4 w-4" />
              <span>Pause</span>
            </Button>
          )}

          <Button
            onClick={onStop}
            variant="outline"
            size="sm"
            className="flex items-center space-x-2"
            disabled={!isActive}
          >
            <Square className="h-4 w-4" />
            <span>Stop</span>
          </Button>

          <Button
            onClick={onReset}
            variant="ghost"
            size="sm"
            className="flex items-center space-x-2"
          >
            <RotateCcw className="h-4 w-4" />
            <span>Reset</span>
          </Button>

          {onBreak && (
            <Button
              onClick={onBreak}
              variant="secondary"
              size="sm"
              className="flex items-center space-x-2"
            >
              <Coffee className="h-4 w-4" />
              <span>Break</span>
            </Button>
          )}
        </div>
      )}

      {/* ADHD-Friendly Encouragement */}
      {isActive && remaining <= 5 && (
        <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
          <p className="text-sm text-blue-800 font-medium">
            🎯 Almost there! You're doing great!
          </p>
        </div>
      )}
    </div>
  )
})

FocusTimer.displayName = "FocusTimer"

export { FocusTimer }
