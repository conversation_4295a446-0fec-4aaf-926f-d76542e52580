/**
 * Focus Session Dashboard Component
 * 
 * ADHD-optimized interface for managing focus sessions with 7 different session types,
 * real-time progress tracking, and gentle guidance.
 */

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FocusTimer } from '@/components/ui/focus-timer';
import { EnergyMeter } from '@/components/ui/energy-meter';
import { ComplexityIndicator, ComplexityLevel } from '@/components/ui/complexity-indicator';
import { 
  Brain, 
  Clock, 
  Coffee,
  Users,
  Sparkles,
  Zap,
  Heart,
  Play,
  Pause,
  RotateCcw,
  Target
} from 'lucide-react';

interface FocusSession {
  id: string;
  type: string;
  duration: number;
  elapsed: number;
  isActive: boolean;
  isPaused: boolean;
  taskDescription: string;
}

interface SessionType {
  id: string;
  name: string;
  duration: number;
  description: string;
  icon: React.ReactNode;
  color: string;
  bestFor: string[];
  flexibility: string;
}

const FocusSessionDashboard: React.FC = () => {
  const [currentSession, setCurrentSession] = useState<FocusSession | null>(null);
  const [energyLevel, setEnergyLevel] = useState<number>(7);
  const [taskComplexity, setTaskComplexity] = useState<ComplexityLevel>('moderate');
  const [availableTime, setAvailableTime] = useState<number>(60);
  const [recommendations, setRecommendations] = useState<any>(null);

  const sessionTypes: SessionType[] = [
    {
      id: 'pomodoro',
      name: 'Pomodoro',
      duration: 25,
      description: 'Classic 25-minute focused work session',
      icon: <Clock className="h-5 w-5" />,
      color: 'bg-red-100 text-red-800 border-red-200',
      bestFor: ['Routine tasks', 'Building focus habits', 'Moderate energy'],
      flexibility: 'Low'
    },
    {
      id: 'micro_focus',
      name: 'Micro Focus',
      duration: 10,
      description: 'Short burst for low energy or difficult tasks',
      icon: <Zap className="h-5 w-5" />,
      color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      bestFor: ['Low energy periods', 'Overwhelming tasks', 'Getting started'],
      flexibility: 'High'
    },
    {
      id: 'deep_work',
      name: 'Deep Work',
      duration: 60,
      description: 'Extended session for complex, engaging work',
      icon: <Brain className="h-5 w-5" />,
      color: 'bg-blue-100 text-blue-800 border-blue-200',
      bestFor: ['Complex problems', 'High energy periods', 'Creative projects'],
      flexibility: 'Medium'
    },
    {
      id: 'body_doubling',
      name: 'Body Doubling',
      duration: 45,
      description: 'Virtual co-working with others for accountability',
      icon: <Users className="h-5 w-5" />,
      color: 'bg-green-100 text-green-800 border-green-200',
      bestFor: ['Accountability needs', 'Boring tasks', 'Social motivation'],
      flexibility: 'High'
    },
    {
      id: 'creative_flow',
      name: 'Creative Flow',
      duration: 90,
      description: 'Open-ended creative work session',
      icon: <Sparkles className="h-5 w-5" />,
      color: 'bg-purple-100 text-purple-800 border-purple-200',
      bestFor: ['Artistic work', 'Open-ended projects', 'Hyperfocus periods'],
      flexibility: 'Very High'
    },
    {
      id: 'task_sprint',
      name: 'Task Sprint',
      duration: 15,
      description: 'Quick sprint to complete specific tasks',
      icon: <Target className="h-5 w-5" />,
      color: 'bg-orange-100 text-orange-800 border-orange-200',
      bestFor: ['Quick completions', 'Urgent deadlines', 'Small tasks'],
      flexibility: 'Low'
    },
    {
      id: 'gentle_focus',
      name: 'Gentle Focus',
      duration: 20,
      description: 'Low-pressure session with maximum flexibility',
      icon: <Heart className="h-5 w-5" />,
      color: 'bg-pink-100 text-pink-800 border-pink-200',
      bestFor: ['Difficult days', 'High stress periods', 'Recovery sessions'],
      flexibility: 'Very High'
    }
  ];

  const getRecommendations = async () => {
    // In a real app, this would call the API
    // For demo purposes, we'll simulate the response
    const mockRecommendations = {
      recommended_type: energyLevel >= 7 ? 'deep_work' : energyLevel >= 5 ? 'pomodoro' : 'micro_focus',
      confidence: 0.85,
      reasoning: energyLevel >= 7 
        ? "High energy is perfect for longer, deeper work"
        : energyLevel >= 5 
        ? "Moderate energy works well with structured sessions"
        : "Lower energy suggests shorter, gentler sessions"
    };
    
    setRecommendations(mockRecommendations);
  };

  const startSession = (sessionType: SessionType) => {
    const newSession: FocusSession = {
      id: Date.now().toString(),
      type: sessionType.id,
      duration: sessionType.duration,
      elapsed: 0,
      isActive: true,
      isPaused: false,
      taskDescription: "Focus session in progress"
    };
    
    setCurrentSession(newSession);
  };

  const pauseSession = () => {
    if (currentSession) {
      setCurrentSession({
        ...currentSession,
        isPaused: true,
        isActive: false
      });
    }
  };

  const resumeSession = () => {
    if (currentSession) {
      setCurrentSession({
        ...currentSession,
        isPaused: false,
        isActive: true
      });
    }
  };

  const stopSession = () => {
    setCurrentSession(null);
  };

  const resetSession = () => {
    if (currentSession) {
      setCurrentSession({
        ...currentSession,
        elapsed: 0,
        isActive: false,
        isPaused: false
      });
    }
  };

  useEffect(() => {
    getRecommendations();
  }, [energyLevel, taskComplexity, availableTime]);

  // Simulate timer progression
  useEffect(() => {
    if (currentSession?.isActive && !currentSession?.isPaused) {
      const interval = setInterval(() => {
        setCurrentSession(prev => {
          if (!prev) return null;
          const newElapsed = prev.elapsed + (1/60); // Add 1 second
          
          if (newElapsed >= prev.duration) {
            // Session completed
            return {
              ...prev,
              elapsed: prev.duration,
              isActive: false,
              isPaused: false
            };
          }
          
          return {
            ...prev,
            elapsed: newElapsed
          };
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [currentSession?.isActive, currentSession?.isPaused]);

  const recommendedSessionType = sessionTypes.find(
    type => type.id === recommendations?.recommended_type
  );

  return (
    <div className="space-y-6 p-6 max-w-6xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Focus Session Dashboard
        </h1>
        <p className="text-gray-600">
          ADHD-optimized focus sessions with flexible timing and gentle guidance
        </p>
      </div>

      {/* Current Session */}
      {currentSession && (
        <Card className="border-2 border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse" />
              Active Session: {sessionTypes.find(t => t.id === currentSession.type)?.name}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <FocusTimer
              duration={currentSession.duration}
              elapsed={currentSession.elapsed}
              isActive={currentSession.isActive}
              isPaused={currentSession.isPaused}
              onStart={resumeSession}
              onPause={pauseSession}
              onStop={stopSession}
              onReset={resetSession}
              variant="detailed"
            />
          </CardContent>
        </Card>
      )}

      {/* Current State Assessment */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Energy Level</CardTitle>
          </CardHeader>
          <CardContent>
            <EnergyMeter 
              level={energyLevel}
              variant="colorful"
              size="lg"
            />
            <div className="mt-4">
              <input
                type="range"
                min="1"
                max="10"
                value={energyLevel}
                onChange={(e) => setEnergyLevel(Number(e.target.value))}
                className="w-full"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Task Complexity</CardTitle>
          </CardHeader>
          <CardContent>
            <ComplexityIndicator 
              level={taskComplexity}
              variant="detailed"
              size="lg"
            />
            <div className="mt-4">
              <select
                value={taskComplexity}
                onChange={(e) => setTaskComplexity(e.target.value as ComplexityLevel)}
                className="w-full p-2 border rounded"
              >
                <option value="trivial">Trivial</option>
                <option value="simple">Simple</option>
                <option value="moderate">Moderate</option>
                <option value="complex">Complex</option>
                <option value="expert">Expert</option>
              </select>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Available Time</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {availableTime} min
              </div>
              <input
                type="range"
                min="5"
                max="120"
                step="5"
                value={availableTime}
                onChange={(e) => setAvailableTime(Number(e.target.value))}
                className="w-full"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recommendations */}
      {recommendations && recommendedSessionType && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-800">
              <Target className="h-5 w-5" />
              Recommended Session
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded ${recommendedSessionType.color}`}>
                  {recommendedSessionType.icon}
                </div>
                <div>
                  <h3 className="font-semibold">{recommendedSessionType.name}</h3>
                  <p className="text-sm text-gray-600">{recommendedSessionType.description}</p>
                </div>
              </div>
              <Button 
                onClick={() => startSession(recommendedSessionType)}
                disabled={!!currentSession}
                className="flex items-center gap-2"
              >
                <Play className="h-4 w-4" />
                Start Session
              </Button>
            </div>
            
            <div className="text-sm text-green-700 bg-green-100 p-3 rounded">
              <strong>Why this session?</strong> {recommendations.reasoning}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Session Types Grid */}
      <div>
        <h2 className="text-xl font-semibold mb-4">All Session Types</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {sessionTypes.map((sessionType) => (
            <Card 
              key={sessionType.id}
              className="hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => !currentSession && startSession(sessionType)}
            >
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-sm">
                  <div className={`p-1 rounded ${sessionType.color}`}>
                    {sessionType.icon}
                  </div>
                  {sessionType.name}
                  <Badge variant="outline" className="ml-auto">
                    {sessionType.duration}m
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-gray-600 mb-3">
                  {sessionType.description}
                </p>
                
                <div className="space-y-2">
                  <div className="text-xs">
                    <span className="font-medium">Best for:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {sessionType.bestFor.slice(0, 2).map((item, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {item}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="text-xs">
                    <span className="font-medium">Flexibility:</span> {sessionType.flexibility}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FocusSessionDashboard;
