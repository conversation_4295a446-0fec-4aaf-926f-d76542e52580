'use client'

import { useState } from 'react'
import { useADHDState } from '@/hooks/useADHDState'
import { FireIcon } from '@heroicons/react/24/outline'
import { cn } from '@/lib/utils'
import toast from 'react-hot-toast'

export function EnergyLevelCard() {
  const { adhdState, updateEnergyLevel } = useADHDState()
  const [isUpdating, setIsUpdating] = useState(false)

  const handleEnergyUpdate = async (newLevel: number) => {
    if (newLevel === adhdState.energyLevel) return

    try {
      setIsUpdating(true)
      await updateEnergyLevel(newLevel)
      
      // Provide encouraging feedback
      if (newLevel > adhdState.energyLevel) {
        toast.success('Energy boost recorded! 🚀')
      } else {
        toast.success('Thanks for the honest check-in 💙')
      }
    } catch (error) {
      toast.error('Failed to update energy level')
    } finally {
      setIsUpdating(false)
    }
  }

  const getEnergyDescription = (level: number): string => {
    if (level <= 2) return 'Very Low - Rest needed'
    if (level <= 4) return 'Low - Gentle tasks'
    if (level <= 6) return 'Moderate - Steady work'
    if (level <= 8) return 'High - Ready for challenges'
    return 'Very High - Peak performance'
  }

  const getEnergyColor = (level: number): string => {
    if (level <= 3) return 'text-red-500'
    if (level <= 6) return 'text-yellow-500'
    return 'text-green-500'
  }

  const getEnergyEmoji = (level: number): string => {
    if (level <= 2) return '😴'
    if (level <= 4) return '😌'
    if (level <= 6) return '😊'
    if (level <= 8) return '😄'
    return '🚀'
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="p-2 bg-orange-100 rounded-lg mr-3">
            <FireIcon className="w-5 h-5 text-orange-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Energy Level</h3>
            <p className="text-sm text-gray-600">How energized do you feel?</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-2xl">{getEnergyEmoji(adhdState.energyLevel)}</div>
          <div className={cn('text-2xl font-bold', getEnergyColor(adhdState.energyLevel))}>
            {adhdState.energyLevel}/10
          </div>
        </div>
      </div>

      <div className="mb-4">
        <p className="text-sm font-medium text-gray-700 mb-2">
          {getEnergyDescription(adhdState.energyLevel)}
        </p>
      </div>

      {/* Energy Level Selector */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-gray-700">
          Update your energy level:
        </label>
        
        <div className="grid grid-cols-5 gap-2">
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((level) => (
            <button
              key={level}
              onClick={() => handleEnergyUpdate(level)}
              disabled={isUpdating}
              className={cn(
                'h-10 rounded-lg text-sm font-medium transition-all duration-200',
                'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
                level === adhdState.energyLevel
                  ? 'bg-primary-500 text-white shadow-md'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200',
                isUpdating && 'opacity-50 cursor-not-allowed'
              )}
            >
              {level}
            </button>
          ))}
        </div>
      </div>

      {/* Energy-based recommendations */}
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">
          💡 Recommended for your energy level:
        </h4>
        <div className="space-y-1">
          {adhdState.energyLevel <= 3 && (
            <>
              <p className="text-sm text-gray-600">• Take a short break or nap</p>
              <p className="text-sm text-gray-600">• Do light, routine tasks</p>
              <p className="text-sm text-gray-600">• Practice self-care activities</p>
            </>
          )}
          {adhdState.energyLevel > 3 && adhdState.energyLevel <= 6 && (
            <>
              <p className="text-sm text-gray-600">• Work on medium-priority tasks</p>
              <p className="text-sm text-gray-600">• Take regular breaks</p>
              <p className="text-sm text-gray-600">• Focus on steady progress</p>
            </>
          )}
          {adhdState.energyLevel > 6 && (
            <>
              <p className="text-sm text-gray-600">• Tackle challenging projects</p>
              <p className="text-sm text-gray-600">• Start new initiatives</p>
              <p className="text-sm text-gray-600">• Engage in creative work</p>
            </>
          )}
        </div>
      </div>

      {/* Quick energy actions */}
      <div className="mt-4 flex space-x-2">
        {adhdState.energyLevel <= 4 && (
          <button className="btn-secondary text-xs px-3 py-1 flex-1">
            Energy Boost Tips
          </button>
        )}
        <button className="btn-primary text-xs px-3 py-1 flex-1">
          Match Tasks to Energy
        </button>
      </div>
    </div>
  )
}
