'use client'

import { useTasks } from '@/hooks/useTasks'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { CalendarIcon, ClockIcon } from '@heroicons/react/24/outline'
import { cn, getPriorityColor, formatDate, formatTime } from '@/lib/utils'

export function UpcomingTasksCard() {
  const { data: tasks, isLoading } = useTasks()

  if (isLoading) {
    return (
      <div className="card">
        <div className="flex items-center justify-center h-32">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    )
  }

  const upcomingTasks = tasks
    ?.filter((task: any) => !task.completed && task.dueDate)
    ?.sort((a: any, b: any) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
    ?.slice(0, 5) || []

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="p-2 bg-purple-100 rounded-lg mr-3">
            <CalendarIcon className="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Upcoming Tasks</h3>
            <p className="text-sm text-gray-600">Next 5 scheduled tasks</p>
          </div>
        </div>
      </div>

      {upcomingTasks.length === 0 ? (
        <div className="text-center py-8">
          <CalendarIcon className="w-12 h-12 text-gray-300 mx-auto mb-3" />
          <p className="text-gray-500">No upcoming scheduled tasks</p>
          <button className="btn-primary mt-3 text-sm">
            Schedule a Task
          </button>
        </div>
      ) : (
        <div className="space-y-3">
          {upcomingTasks.map((task: any) => (
            <div
              key={task.id}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 truncate">
                  {task.title}
                </h4>
                <div className="flex items-center mt-1 space-x-2">
                  <span className="text-xs text-gray-500">
                    {formatDate(task.dueDate)}
                  </span>
                  {task.estimatedDuration && (
                    <span className="text-xs text-gray-500 flex items-center">
                      <ClockIcon className="w-3 h-3 mr-1" />
                      {task.estimatedDuration}m
                    </span>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span className={cn(
                  'text-xs px-2 py-1 rounded-full',
                  getPriorityColor(task.priority)
                )}>
                  {task.priority}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
