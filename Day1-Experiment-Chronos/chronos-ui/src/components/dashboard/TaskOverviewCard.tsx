'use client'

import { useTasks } from '@/hooks/useTasks'
import { useADHDState } from '@/hooks/useADHDState'
import { CheckIcon, ClockIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { cn, getPriorityColor } from '@/lib/utils'

export function TaskOverviewCard() {
  const { data: tasks, isLoading } = useTasks()
  const { adhdState } = useADHDState()

  if (isLoading) {
    return (
      <div className="card">
        <div className="flex items-center justify-center h-32">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    )
  }

  const completedTasks = tasks?.filter((task: any) => task.completed) || []
  const pendingTasks = tasks?.filter((task: any) => !task.completed) || []
  const urgentTasks = pendingTasks.filter((task: any) => task.priority === 'urgent')
  const overdueTasks = pendingTasks.filter((task: any) => {
    if (!task.dueDate) return false
    return new Date(task.dueDate) < new Date()
  })

  // Filter tasks based on current energy level
  const energyMatchedTasks = pendingTasks.filter((task: any) => {
    const taskEnergyRequired = task.energyRequired || 5
    const energyDiff = Math.abs(taskEnergyRequired - adhdState.energyLevel)
    return energyDiff <= 2 // Tasks within 2 energy levels
  })

  const completionRate = tasks?.length > 0 
    ? Math.round((completedTasks.length / tasks.length) * 100) 
    : 0

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="p-2 bg-blue-100 rounded-lg mr-3">
            <CheckIcon className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Task Overview</h3>
            <p className="text-sm text-gray-600">Your productivity snapshot</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-blue-600">{completionRate}%</div>
          <div className="text-xs text-gray-500">completion rate</div>
        </div>
      </div>

      {/* Task Statistics */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="bg-green-50 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-green-800">Completed</span>
            <span className="text-lg font-bold text-green-600">
              {completedTasks.length}
            </span>
          </div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-800">Pending</span>
            <span className="text-lg font-bold text-gray-600">
              {pendingTasks.length}
            </span>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {urgentTasks.length > 0 && (
        <div className="mb-4 p-3 bg-red-50 rounded-lg border border-red-200">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="w-5 h-5 text-red-500 mr-2" />
            <span className="text-sm font-medium text-red-800">
              {urgentTasks.length} urgent task{urgentTasks.length > 1 ? 's' : ''} need attention
            </span>
          </div>
        </div>
      )}

      {overdueTasks.length > 0 && (
        <div className="mb-4 p-3 bg-orange-50 rounded-lg border border-orange-200">
          <div className="flex items-center">
            <ClockIcon className="w-5 h-5 text-orange-500 mr-2" />
            <span className="text-sm font-medium text-orange-800">
              {overdueTasks.length} overdue task{overdueTasks.length > 1 ? 's' : ''}
            </span>
          </div>
        </div>
      )}

      {/* Energy-matched tasks */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-900 mb-2">
          🎯 Tasks matching your energy level ({adhdState.energyLevel}/10):
        </h4>
        {energyMatchedTasks.length > 0 ? (
          <div className="space-y-2">
            {energyMatchedTasks.slice(0, 3).map((task: any) => (
              <div
                key={task.id}
                className="flex items-center justify-between p-2 bg-primary-50 rounded-lg"
              >
                <span className="text-sm text-primary-900 truncate">
                  {task.title}
                </span>
                <span className={cn(
                  'text-xs px-2 py-1 rounded-full',
                  getPriorityColor(task.priority)
                )}>
                  {task.priority}
                </span>
              </div>
            ))}
            {energyMatchedTasks.length > 3 && (
              <p className="text-xs text-gray-500">
                +{energyMatchedTasks.length - 3} more tasks
              </p>
            )}
          </div>
        ) : (
          <p className="text-sm text-gray-500">
            No tasks match your current energy level. Consider adjusting task priorities or taking a break.
          </p>
        )}
      </div>

      {/* Quick actions */}
      <div className="flex space-x-2">
        <button className="btn-primary text-sm px-3 py-2 flex-1">
          Add Task
        </button>
        <button className="btn-secondary text-sm px-3 py-2 flex-1">
          View All
        </button>
      </div>

      {/* ADHD-specific insights */}
      {adhdState.cognitiveLoad > 0.7 && pendingTasks.length > 5 && (
        <div className="mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
          <div className="flex items-start">
            <span className="text-yellow-600 mr-2">💡</span>
            <div>
              <h4 className="text-sm font-medium text-yellow-800 mb-1">
                ADHD Tip
              </h4>
              <p className="text-sm text-yellow-700">
                High cognitive load + many tasks = overwhelm risk. Try focusing on just 1-3 tasks today.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
