'use client'

import { useState, useEffect } from 'react'
import { useADHDState } from '@/hooks/useADHDState'
import { ClockIcon, PlayIcon, PauseIcon, StopIcon } from '@heroicons/react/24/outline'
import { cn, formatDuration } from '@/lib/utils'

export function FocusSessionCard() {
  const { adhdState } = useADHDState()
  const [isActive, setIsActive] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [timeRemaining, setTimeRemaining] = useState(25 * 60) // 25 minutes in seconds
  const [sessionType, setSessionType] = useState<'pomodoro' | 'deep_work' | 'creative' | 'break'>('pomodoro')

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null

    if (isActive && !isPaused && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining((time) => time - 1)
      }, 1000)
    } else if (timeRemaining === 0) {
      // Session completed
      setIsActive(false)
      setIsPaused(false)
      // Here you would typically show a notification or celebration
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isActive, isPaused, timeRemaining])

  const startSession = () => {
    setIsActive(true)
    setIsPaused(false)
  }

  const pauseSession = () => {
    setIsPaused(true)
  }

  const resumeSession = () => {
    setIsPaused(false)
  }

  const stopSession = () => {
    setIsActive(false)
    setIsPaused(false)
    resetTimer()
  }

  const resetTimer = () => {
    const durations = {
      pomodoro: 25 * 60,
      deep_work: 45 * 60,
      creative: 30 * 60,
      break: 5 * 60,
    }
    setTimeRemaining(durations[sessionType])
  }

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const getSessionTypeColor = (type: string): string => {
    const colors = {
      pomodoro: 'bg-red-100 text-red-800',
      deep_work: 'bg-blue-100 text-blue-800',
      creative: 'bg-purple-100 text-purple-800',
      break: 'bg-green-100 text-green-800',
    }
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  const getRecommendedSessionType = (): string => {
    if (adhdState.energyLevel <= 3) return 'break'
    if (adhdState.cognitiveLoad > 0.7) return 'break'
    if (adhdState.focusMode === 'deep_work') return 'deep_work'
    if (adhdState.focusMode === 'creative') return 'creative'
    return 'pomodoro'
  }

  const recommendedType = getRecommendedSessionType()

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="p-2 bg-indigo-100 rounded-lg mr-3">
            <ClockIcon className="w-5 h-5 text-indigo-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Focus Session</h3>
            <p className="text-sm text-gray-600">Structured focus time</p>
          </div>
        </div>
        <div className={cn(
          'px-2 py-1 rounded-full text-xs font-medium',
          getSessionTypeColor(sessionType)
        )}>
          {sessionType.replace('_', ' ')}
        </div>
      </div>

      {/* Timer Display */}
      <div className="text-center mb-6">
        <div className={cn(
          'text-4xl font-mono font-bold mb-2',
          isActive && !isPaused ? 'text-primary-600' : 'text-gray-700'
        )}>
          {formatTime(timeRemaining)}
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={cn(
              'h-2 rounded-full transition-all duration-1000',
              isActive ? 'bg-primary-500' : 'bg-gray-400'
            )}
            style={{
              width: `${((25 * 60 - timeRemaining) / (25 * 60)) * 100}%`
            }}
          />
        </div>
      </div>

      {/* Session Type Selector */}
      <div className="mb-4">
        <label className="text-sm font-medium text-gray-700 mb-2 block">
          Session Type:
        </label>
        <div className="grid grid-cols-2 gap-2">
          {[
            { type: 'pomodoro', label: '🍅 Pomodoro (25m)', duration: 25 },
            { type: 'deep_work', label: '🧠 Deep Work (45m)', duration: 45 },
            { type: 'creative', label: '🎨 Creative (30m)', duration: 30 },
            { type: 'break', label: '🌱 Break (5m)', duration: 5 },
          ].map(({ type, label, duration }) => (
            <button
              key={type}
              onClick={() => {
                setSessionType(type as any)
                setTimeRemaining(duration * 60)
              }}
              disabled={isActive}
              className={cn(
                'text-xs p-2 rounded-lg border transition-colors',
                sessionType === type
                  ? 'bg-primary-100 border-primary-300 text-primary-800'
                  : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100',
                isActive && 'opacity-50 cursor-not-allowed'
              )}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* Recommendation */}
      {recommendedType !== sessionType && !isActive && (
        <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start">
            <span className="text-blue-600 mr-2">💡</span>
            <div>
              <h4 className="text-sm font-medium text-blue-800 mb-1">
                Recommendation
              </h4>
              <p className="text-sm text-blue-700">
                Based on your current state, a {recommendedType.replace('_', ' ')} session might work better.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="flex space-x-2">
        {!isActive ? (
          <button
            onClick={startSession}
            className="btn-primary flex-1 flex items-center justify-center"
          >
            <PlayIcon className="w-4 h-4 mr-2" />
            Start
          </button>
        ) : (
          <>
            {isPaused ? (
              <button
                onClick={resumeSession}
                className="btn-primary flex-1 flex items-center justify-center"
              >
                <PlayIcon className="w-4 h-4 mr-2" />
                Resume
              </button>
            ) : (
              <button
                onClick={pauseSession}
                className="btn-warning flex-1 flex items-center justify-center"
              >
                <PauseIcon className="w-4 h-4 mr-2" />
                Pause
              </button>
            )}
            <button
              onClick={stopSession}
              className="btn-secondary flex items-center justify-center px-3"
            >
              <StopIcon className="w-4 h-4" />
            </button>
          </>
        )}
      </div>

      {/* Session Stats */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-gray-900">3</div>
            <div className="text-xs text-gray-500">Today</div>
          </div>
          <div>
            <div className="text-lg font-bold text-gray-900">18</div>
            <div className="text-xs text-gray-500">This Week</div>
          </div>
          <div>
            <div className="text-lg font-bold text-gray-900">7</div>
            <div className="text-xs text-gray-500">Streak</div>
          </div>
        </div>
      </div>
    </div>
  )
}
