'use client'

import {
  PlusIcon,
  ClockIcon,
  CpuChipIcon,
  ChartBarIcon,
  CogIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline'

export function QuickActionsCard() {
  const quickActions = [
    {
      id: 'add-task',
      title: 'Add Task',
      description: 'Create a new task',
      icon: PlusIcon,
      color: 'bg-blue-100 text-blue-600',
      action: () => console.log('Add task'),
    },
    {
      id: 'start-focus',
      title: 'Start Focus',
      description: 'Begin a focus session',
      icon: ClockIcon,
      color: 'bg-green-100 text-green-600',
      action: () => console.log('Start focus'),
    },
    {
      id: 'adhd-check',
      title: 'ADHD Check-in',
      description: 'Update your state',
      icon: CpuChipIcon,
      color: 'bg-purple-100 text-purple-600',
      action: () => console.log('ADHD check-in'),
    },
    {
      id: 'view-analytics',
      title: 'View Analytics',
      description: 'See your progress',
      icon: ChartBarIcon,
      color: 'bg-orange-100 text-orange-600',
      action: () => console.log('View analytics'),
    },
    {
      id: 'settings',
      title: 'Settings',
      description: 'Customize your experience',
      icon: CogIcon,
      color: 'bg-gray-100 text-gray-600',
      action: () => console.log('Settings'),
    },
    {
      id: 'tips',
      title: 'ADHD Tips',
      description: 'Get personalized tips',
      icon: LightBulbIcon,
      color: 'bg-yellow-100 text-yellow-600',
      action: () => console.log('ADHD tips'),
    },
  ]

  return (
    <div className="card">
      <div className="flex items-center mb-4">
        <div className="p-2 bg-indigo-100 rounded-lg mr-3">
          <LightBulbIcon className="w-5 h-5 text-indigo-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
          <p className="text-sm text-gray-600">Common tasks and tools</p>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3">
        {quickActions.map((action) => (
          <button
            key={action.id}
            onClick={action.action}
            className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            <div className={`p-2 rounded-lg mb-2 ${action.color}`}>
              <action.icon className="w-5 h-5" />
            </div>
            <h4 className="text-sm font-medium text-gray-900 text-center">
              {action.title}
            </h4>
            <p className="text-xs text-gray-500 text-center mt-1">
              {action.description}
            </p>
          </button>
        ))}
      </div>
    </div>
  )
}
