'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useAuth } from '@/hooks/useAuth'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'

const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
})

type LoginFormData = z.infer<typeof loginSchema>

export function LoginForm() {
  const [isLogin, setIsLogin] = useState(true)
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { login, register } = useAuth()

  const {
    register: registerField,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<LoginFormData & { name?: string }>({
    resolver: zodResolver(
      isLogin
        ? loginSchema
        : loginSchema.extend({
            name: z.string().min(2, 'Name must be at least 2 characters'),
          })
    ),
  })

  const onSubmit = async (data: LoginFormData & { name?: string }) => {
    try {
      setIsLoading(true)
      if (isLogin) {
        await login(data.email, data.password)
      } else {
        await register(data.email, data.password, data.name!)
      }
    } catch (error) {
      // Error handling is done in the auth context
    } finally {
      setIsLoading(false)
    }
  }

  const toggleMode = () => {
    setIsLogin(!isLogin)
    reset()
  }

  return (
    <div className="card max-w-md mx-auto">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">
          {isLogin ? 'Welcome back!' : 'Join Chronos'}
        </h2>
        <p className="text-gray-600 mt-2">
          {isLogin
            ? 'Sign in to your ADHD-optimized workspace'
            : 'Create your personalized productivity hub'}
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {!isLogin && (
          <div>
            <label htmlFor="name" className="form-label">
              Full Name
            </label>
            <input
              {...registerField('name')}
              type="text"
              id="name"
              className="form-input"
              placeholder="Enter your full name"
              disabled={isLoading}
            />
            {errors.name && (
              <p className="form-error">{errors.name.message}</p>
            )}
          </div>
        )}

        <div>
          <label htmlFor="email" className="form-label">
            Email Address
          </label>
          <input
            {...registerField('email')}
            type="email"
            id="email"
            className="form-input"
            placeholder="Enter your email"
            disabled={isLoading}
          />
          {errors.email && (
            <p className="form-error">{errors.email.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="password" className="form-label">
            Password
          </label>
          <div className="relative">
            <input
              {...registerField('password')}
              type={showPassword ? 'text' : 'password'}
              id="password"
              className="form-input pr-10"
              placeholder="Enter your password"
              disabled={isLoading}
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeSlashIcon className="h-5 w-5 text-gray-400" />
              ) : (
                <EyeIcon className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="form-error">{errors.password.message}</p>
          )}
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className="btn-primary w-full flex items-center justify-center"
        >
          {isLoading ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              {isLogin ? 'Signing in...' : 'Creating account...'}
            </>
          ) : (
            <>{isLogin ? 'Sign In' : 'Create Account'}</>
          )}
        </button>
      </form>

      <div className="mt-6 text-center">
        <p className="text-gray-600">
          {isLogin ? "Don't have an account?" : 'Already have an account?'}
          <button
            type="button"
            onClick={toggleMode}
            className="ml-1 text-primary-600 hover:text-primary-500 font-medium"
            disabled={isLoading}
          >
            {isLogin ? 'Sign up' : 'Sign in'}
          </button>
        </p>
      </div>

      {/* ADHD-friendly features notice */}
      <div className="mt-6 p-4 bg-primary-50 rounded-lg">
        <h3 className="text-sm font-medium text-primary-800 mb-2">
          🧠 ADHD-Optimized Features
        </h3>
        <ul className="text-xs text-primary-700 space-y-1">
          <li>• Adaptive interface based on your energy levels</li>
          <li>• Smart break reminders and focus sessions</li>
          <li>• Gamified progress tracking</li>
          <li>• Cognitive load-aware task management</li>
        </ul>
      </div>
    </div>
  )
}
