import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { tasksAPI } from '@/lib/api'
import toast from 'react-hot-toast'

export interface Task {
  id: string
  title: string
  description?: string
  completed: boolean
  priority: 'low' | 'medium' | 'high' | 'urgent'
  dueDate?: string
  estimatedDuration?: number // minutes
  actualDuration?: number // minutes
  tags: string[]
  energyRequired: number // 1-10
  cognitiveLoad: number // 0-1
  createdAt: string
  updatedAt: string
  completedAt?: string
}

export function useTasks(filters?: any) {
  return useQuery({
    queryKey: ['tasks', filters],
    queryFn: () => tasksAPI.list(filters).then(res => res.data),
    staleTime: 30 * 1000, // 30 seconds
  })
}

export function useTask(id: string) {
  return useQuery({
    queryKey: ['task', id],
    queryFn: () => tasksAPI.get(id).then(res => res.data),
    enabled: !!id,
  })
}

export function useCreateTask() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (task: Partial<Task>) => tasksAPI.create(task),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] })
      toast.success('Task created! 🎯')
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to create task'
      toast.error(message)
    },
  })
}

export function useUpdateTask() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<Task> }) =>
      tasksAPI.update(id, updates),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] })
      queryClient.invalidateQueries({ queryKey: ['task', id] })
      toast.success('Task updated! ✨')
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to update task'
      toast.error(message)
    },
  })
}

export function useDeleteTask() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => tasksAPI.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] })
      toast.success('Task deleted')
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to delete task'
      toast.error(message)
    },
  })
}

export function useCompleteTask() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => tasksAPI.complete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] })
      toast.success('Great job! Task completed! 🎉')
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to complete task'
      toast.error(message)
    },
  })
}

export function useUncompleteTask() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => tasksAPI.uncomplete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] })
      toast.success('Task marked as incomplete')
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to uncomplete task'
      toast.error(message)
    },
  })
}
