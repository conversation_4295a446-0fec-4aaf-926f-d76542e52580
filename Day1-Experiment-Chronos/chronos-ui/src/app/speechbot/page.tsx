'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { SpeechbotDashboard } from '@/components/speechbot/SpeechbotDashboard'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  MicrophoneIcon, 
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline'

export default function SpeechbotPage() {
  const { user } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [serviceStatus, setServiceStatus] = useState<'checking' | 'available' | 'unavailable'>('checking')
  const [capabilities, setCapabilities] = useState<any>(null)

  useEffect(() => {
    checkSpeechbotStatus()
  }, [])

  const checkSpeechbotStatus = async () => {
    try {
      setIsLoading(true)
      setServiceStatus('checking')

      // Check if Speechbot service is available
      const response = await fetch('/api/speechbot/health', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const healthData = await response.json()
        
        // Get capabilities
        const capResponse = await fetch('/api/speechbot/capabilities')
        if (capResponse.ok) {
          const capData = await capResponse.json()
          setCapabilities(capData)
        }

        setServiceStatus('available')
      } else {
        setServiceStatus('unavailable')
      }
    } catch (error) {
      console.error('Failed to check Speechbot status:', error)
      setServiceStatus('unavailable')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Checking Speechbot availability...</p>
          </div>
        </div>
      </div>
    )
  }

  if (serviceStatus === 'unavailable') {
    return (
      <div className="container mx-auto p-6">
        <div className="max-w-2xl mx-auto">
          <Card className="border-orange-200">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-orange-800">
                <ExclamationTriangleIcon className="h-6 w-6" />
                <span>Speechbot Service Unavailable</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <ExclamationTriangleIcon className="h-4 w-4" />
                <AlertDescription>
                  The Speechbot voice synthesis service is currently unavailable. 
                  This could be due to:
                </AlertDescription>
              </Alert>

              <ul className="list-disc list-inside space-y-2 text-sm text-gray-700">
                <li>Service is starting up (this can take 1-2 minutes)</li>
                <li>GPU resources are being allocated</li>
                <li>Dia TTS model is being loaded</li>
                <li>Network connectivity issues</li>
              </ul>

              <div className="flex space-x-3">
                <Button onClick={checkSpeechbotStatus} variant="outline">
                  <ClockIcon className="h-4 w-4 mr-2" />
                  Check Again
                </Button>
                <Button 
                  onClick={() => window.location.href = '/'}
                  variant="outline"
                >
                  Return to Dashboard
                </Button>
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h3 className="font-semibold text-blue-900 mb-2">What is Speechbot?</h3>
                <p className="text-sm text-blue-800">
                  Speechbot is Project Chronos's revolutionary ADHD-optimized voice assistant. 
                  It provides personalized voice synthesis, body doubling companions, and 
                  therapeutic dialogue generation to support your productivity and well-being.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <MicrophoneIcon className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Speechbot</h1>
                <p className="text-sm text-gray-600">ADHD-Optimized Voice Assistant</p>
              </div>
            </div>

            {capabilities && (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon className="h-5 w-5 text-green-500" />
                  <span className="text-sm text-gray-600">Service Online</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {capabilities.engine}
                  </div>
                  <div className="text-xs text-gray-500">
                    {capabilities.model_size}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Welcome Message for First-Time Users */}
      <div className="container mx-auto px-6 py-6">
        <Alert className="mb-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <MicrophoneIcon className="h-4 w-4" />
          <AlertDescription>
            <div className="flex items-center justify-between">
              <div>
                <strong>Welcome to Speechbot!</strong> Your ADHD-optimized voice assistant is ready. 
                Create personalized voices, generate supportive dialogue, and experience virtual body doubling.
              </div>
              <Button 
                size="sm" 
                className="ml-4"
                onClick={() => {
                  // Scroll to voice creation section
                  const element = document.querySelector('[data-tab="voices"]')
                  if (element) {
                    element.scrollIntoView({ behavior: 'smooth' })
                  }
                }}
              >
                Get Started
              </Button>
            </div>
          </AlertDescription>
        </Alert>

        {/* Main Speechbot Dashboard */}
        {user && (
          <SpeechbotDashboard userId={user.id} />
        )}
      </div>

      {/* Feature Highlights */}
      <div className="bg-white border-t border-gray-200 mt-12">
        <div className="container mx-auto px-6 py-12">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Revolutionary ADHD Voice Technology
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Speechbot combines cutting-edge AI with ADHD research to provide 
              personalized voice assistance that adapts to your cognitive state and needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MicrophoneIcon className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Voice Cloning</h3>
              <p className="text-gray-600 text-sm">
                Create personalized voices from just 5-10 seconds of audio. 
                Use your own voice for self-compassion and motivation.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Body Doubling</h3>
              <p className="text-gray-600 text-sm">
                Virtual companions provide accountability and presence during work sessions, 
                reducing isolation and improving focus.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">ADHD Optimization</h3>
              <p className="text-gray-600 text-sm">
                Five emotional modes (calm, excited, focused, overwhelmed, motivated) 
                adapt speech to your current cognitive state.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
