'use client'

import { useAuth } from '@/hooks/useAuth'
import { useADHDState } from '@/hooks/useADHDState'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { WelcomeCard } from '@/components/dashboard/WelcomeCard'
import { EnergyLevelCard } from '@/components/dashboard/EnergyLevelCard'
import { TaskOverviewCard } from '@/components/dashboard/TaskOverviewCard'
import { FocusSessionCard } from '@/components/dashboard/FocusSessionCard'
import { UpcomingTasksCard } from '@/components/dashboard/UpcomingTasksCard'
import { MotivationCard } from '@/components/dashboard/MotivationCard'
import { QuickActionsCard } from '@/components/dashboard/QuickActionsCard'
import { LoginForm } from '@/components/auth/LoginForm'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

export default function HomePage() {
  const { user, isLoading: authLoading } = useAuth()
  const { adhdState } = useADHDState()

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Welcome to Chronos
            </h1>
            <p className="text-lg text-gray-600">
              ADHD-optimized task management for better focus and productivity
            </p>
          </div>
          <LoginForm />
        </div>
      </div>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <WelcomeCard user={user} adhdState={adhdState} />
          </div>
          <div>
            <EnergyLevelCard />
          </div>
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          <TaskOverviewCard />
          <FocusSessionCard />
          <MotivationCard />
        </div>

        {/* Secondary Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <UpcomingTasksCard />
          <QuickActionsCard />
        </div>
      </div>
    </DashboardLayout>
  )
}
