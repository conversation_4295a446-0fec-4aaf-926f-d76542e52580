'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { api } from '@/lib/api'
import toast from 'react-hot-toast'

interface User {
  id: string
  email: string
  name: string
  adhdProfile?: {
    energyLevel: number
    cognitiveLoad: number
    focusMode: string
    sensoryPreferences: Record<string, any>
  }
  preferences: {
    theme: 'light' | 'dark' | 'auto'
    notifications: boolean
    soundEnabled: boolean
  }
  createdAt: string
  lastLoginAt: string
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
  register: (email: string, password: string, name: string) => Promise<void>
  updateProfile: (updates: Partial<User>) => Promise<void>
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('chronos_token')
      if (!token) {
        setIsLoading(false)
        return
      }

      const response = await api.get('/auth/me')
      setUser(response.data)
    } catch (error) {
      localStorage.removeItem('chronos_token')
      console.error('Auth check failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    try {
      const response = await api.post('/auth/login', { email, password })
      const { token, user: userData } = response.data
      
      localStorage.setItem('chronos_token', token)
      setUser(userData)
      
      toast.success(`Welcome back, ${userData.name}! 🎉`)
    } catch (error: any) {
      const message = error.response?.data?.message || 'Login failed'
      toast.error(message)
      throw error
    }
  }

  const register = async (email: string, password: string, name: string) => {
    try {
      const response = await api.post('/auth/register', { email, password, name })
      const { token, user: userData } = response.data
      
      localStorage.setItem('chronos_token', token)
      setUser(userData)
      
      toast.success(`Welcome to Chronos, ${userData.name}! 🚀`)
    } catch (error: any) {
      const message = error.response?.data?.message || 'Registration failed'
      toast.error(message)
      throw error
    }
  }

  const logout = async () => {
    try {
      await api.post('/auth/logout')
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      localStorage.removeItem('chronos_token')
      setUser(null)
      toast.success('Logged out successfully')
    }
  }

  const updateProfile = async (updates: Partial<User>) => {
    try {
      const response = await api.patch('/auth/profile', updates)
      setUser(response.data)
      toast.success('Profile updated successfully! ✨')
    } catch (error: any) {
      const message = error.response?.data?.message || 'Profile update failed'
      toast.error(message)
      throw error
    }
  }

  const value = {
    user,
    isLoading,
    login,
    logout,
    register,
    updateProfile,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}


