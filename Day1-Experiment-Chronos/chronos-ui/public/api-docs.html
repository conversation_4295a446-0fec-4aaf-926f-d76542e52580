<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Chronos + Speechbot API Documentation</title>
    <meta name="description" content="Interactive API documentation for the world's most advanced ADHD voice assistant platform">
    
    <!-- ADHD-optimized styling -->
    <style>
        :root {
            --adhd-bg-primary: #1a1a1a;
            --adhd-bg-secondary: #2d2d2d;
            --adhd-text-primary: #e8e8e8;
            --adhd-text-secondary: #b8b8b8;
            --adhd-accent-calm: #4a90e2;
            --adhd-accent-excited: #f5a623;
            --adhd-border: #4a4a4a;
            --adhd-hover: #404040;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            background-color: var(--adhd-bg-primary);
            color: var(--adhd-text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background-color: var(--adhd-bg-secondary);
            border-radius: 12px;
            border: 2px solid var(--adhd-border);
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            color: var(--adhd-accent-calm);
            margin-bottom: 1rem;
        }

        .header p {
            font-size: 1.2rem;
            color: var(--adhd-text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .api-card {
            background-color: var(--adhd-bg-secondary);
            border: 2px solid var(--adhd-border);
            border-radius: 12px;
            padding: 2rem;
            transition: all 0.3s ease;
        }

        .api-card:hover {
            border-color: var(--adhd-accent-calm);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(74, 144, 226, 0.2);
        }

        .api-card h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--adhd-accent-calm);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .api-card p {
            color: var(--adhd-text-secondary);
            margin-bottom: 1.5rem;
        }

        .api-links {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .api-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background-color: var(--adhd-accent-calm);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            transition: all 0.2s ease;
            min-height: 44px;
        }

        .api-link:hover {
            background-color: #3a7bc8;
            transform: translateY(-1px);
        }

        .api-link.secondary {
            background-color: var(--adhd-bg-primary);
            border: 2px solid var(--adhd-border);
            color: var(--adhd-text-primary);
        }

        .api-link.secondary:hover {
            border-color: var(--adhd-accent-calm);
            background-color: var(--adhd-hover);
        }

        .features {
            background-color: var(--adhd-bg-secondary);
            border-radius: 12px;
            border: 2px solid var(--adhd-border);
            padding: 2rem;
            margin-bottom: 3rem;
        }

        .features h2 {
            color: var(--adhd-accent-excited);
            font-size: 2rem;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .feature {
            padding: 1rem;
            background-color: var(--adhd-bg-primary);
            border-radius: 8px;
            border: 1px solid var(--adhd-border);
        }

        .feature h3 {
            color: var(--adhd-accent-calm);
            margin-bottom: 0.5rem;
        }

        .feature p {
            color: var(--adhd-text-secondary);
            font-size: 0.9rem;
        }

        .quick-start {
            background-color: var(--adhd-bg-secondary);
            border-radius: 12px;
            border: 2px solid var(--adhd-border);
            padding: 2rem;
        }

        .quick-start h2 {
            color: var(--adhd-accent-excited);
            font-size: 2rem;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .quick-start ol {
            list-style: none;
            counter-reset: step-counter;
        }

        .quick-start li {
            counter-increment: step-counter;
            margin-bottom: 1rem;
            padding: 1rem;
            background-color: var(--adhd-bg-primary);
            border-radius: 8px;
            border: 1px solid var(--adhd-border);
            position: relative;
            padding-left: 3rem;
        }

        .quick-start li::before {
            content: counter(step-counter);
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background-color: var(--adhd-accent-calm);
            color: white;
            width: 1.5rem;
            height: 1.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.8rem;
        }

        .code {
            background-color: var(--adhd-bg-primary);
            border: 1px solid var(--adhd-border);
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
            font-family: 'JetBrains Mono', 'Fira Code', monospace;
            font-size: 0.9rem;
            color: var(--adhd-accent-calm);
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .api-grid {
                grid-template-columns: 1fr;
            }
            
            .api-links {
                flex-direction: column;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* High contrast support */
        @media (prefers-contrast: high) {
            :root {
                --adhd-bg-primary: #000000;
                --adhd-bg-secondary: #1a1a1a;
                --adhd-text-primary: #ffffff;
                --adhd-border: #666666;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🧠🎭 API Documentation</h1>
            <p>Interactive documentation for Project Chronos + Speechbot - The world's most advanced ADHD-optimized voice assistant platform</p>
        </header>

        <div class="api-grid">
            <div class="api-card">
                <h2>🧠 Chronos API</h2>
                <p>Core productivity platform with ADHD-focused task management, time blocking, and focus sessions.</p>
                <div class="api-links">
                    <a href="http://api.autism.localhost:8090/docs" class="api-link">
                        📖 Interactive Docs
                    </a>
                    <a href="http://api.autism.localhost:8090/redoc" class="api-link secondary">
                        📚 Reference
                    </a>
                    <a href="http://api.autism.localhost:8090/openapi.json" class="api-link secondary">
                        🔧 OpenAPI
                    </a>
                </div>
            </div>

            <div class="api-card">
                <h2>🎭 Speechbot API</h2>
                <p>Advanced voice synthesis with 5 ADHD emotional modes, real-time streaming, and personal voice cloning.</p>
                <div class="api-links">
                    <a href="http://speechbot.autism.localhost:8090/docs" class="api-link">
                        📖 Interactive Docs
                    </a>
                    <a href="http://speechbot.autism.localhost:8090/redoc" class="api-link secondary">
                        📚 Reference
                    </a>
                    <a href="http://speechbot.autism.localhost:8090/openapi.json" class="api-link secondary">
                        🔧 OpenAPI
                    </a>
                </div>
            </div>
        </div>

        <section class="features">
            <h2>🌟 Platform Features</h2>
            <div class="features-grid">
                <div class="feature">
                    <h3>🎤 Voice Synthesis</h3>
                    <p>5 ADHD emotional modes with professional-grade Dia TTS engine</p>
                </div>
                <div class="feature">
                    <h3>⚡ Real-Time Streaming</h3>
                    <p>Sub-second latency audio generation for immediate feedback</p>
                </div>
                <div class="feature">
                    <h3>🤝 Body Doubling</h3>
                    <p>Virtual companions for accountability and work sessions</p>
                </div>
                <div class="feature">
                    <h3>🧠 Emotion Detection</h3>
                    <p>Automatic ADHD mode selection based on text analysis</p>
                </div>
                <div class="feature">
                    <h3>🎯 Task Management</h3>
                    <p>AI-powered task chunking and energy-based scheduling</p>
                </div>
                <div class="feature">
                    <h3>♿ Accessibility</h3>
                    <p>WCAG 2.1 AA compliant with ADHD-specific optimizations</p>
                </div>
            </div>
        </section>

        <section class="quick-start">
            <h2>🚀 Quick Start</h2>
            <ol>
                <li>
                    <strong>Explore the APIs:</strong> Use the interactive documentation to test endpoints directly in your browser
                </li>
                <li>
                    <strong>Try Voice Synthesis:</strong> Test <span class="code">POST /api/v1/tts/quick</span> with different ADHD modes
                </li>
                <li>
                    <strong>Create Voice Profile:</strong> Upload a 5-10 second audio sample for personal voice cloning
                </li>
                <li>
                    <strong>Start Body Doubling:</strong> Begin a virtual work session with <span class="code">POST /api/v1/dialogue/body-doubling</span>
                </li>
                <li>
                    <strong>Integrate:</strong> Use the comprehensive API reference to build your own ADHD-optimized applications
                </li>
            </ol>
        </section>
    </div>
</body>
</html>
