"""
Notification workers for Project Chronos.

This module provides Celery workers for background notification processing
including delivery, escalation, and persistence checking.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from uuid import UUID
from typing import List, Optional

from app.workers.celery_app import celery_app
from app.core.database import get_db_session
from app.services.notification_service import NotificationService
from app.models.notification import Notification, NotificationStatus

logger = logging.getLogger(__name__)


@celery_app.task(name='deliver_notification', bind=True)
def deliver_notification_task(self, notification_id: str, delivery_stage: str = "primary"):
    """
    Background task for notification delivery.
    
    Args:
        notification_id: ID of notification to deliver
        delivery_stage: Stage of delivery (primary, reminder, escalation)
    """
    async def _deliver():
        try:
            async with get_db_session() as db:
                notification_service = NotificationService(db, celery_app)
                success = await notification_service.deliver_notification(
                    UUID(notification_id), 
                    delivery_stage
                )
                
                if not success:
                    logger.warning(f"Failed to deliver notification {notification_id}")
                    # Retry with exponential backoff
                    raise self.retry(countdown=60 * (2 ** self.request.retries))
                
                return {"status": "delivered", "notification_id": notification_id}
                
        except Exception as e:
            logger.error(f"Error delivering notification {notification_id}: {e}")
            # Retry up to 3 times with exponential backoff
            if self.request.retries < 3:
                raise self.retry(countdown=60 * (2 ** self.request.retries))
            else:
                # Mark as failed after max retries
                async with get_db_session() as db:
                    notification_service = NotificationService(db, celery_app)
                    notification = await notification_service._get_notification(UUID(notification_id))
                    if notification:
                        notification.status = NotificationStatus.EXPIRED
                        notification.delivery_errors.append(f"Max retries exceeded: {str(e)}")
                        await db.commit()
                raise
    
    return asyncio.run(_deliver())


@celery_app.task(name='check_notification_acknowledgment', bind=True)
def check_notification_acknowledgment_task(self, notification_id: str):
    """
    Check if persistent notification was acknowledged and escalate if needed.
    
    Args:
        notification_id: ID of notification to check
    """
    async def _check():
        try:
            async with get_db_session() as db:
                notification_service = NotificationService(db, celery_app)
                notification = await notification_service._get_notification(UUID(notification_id))
                
                if not notification:
                    logger.warning(f"Notification {notification_id} not found for acknowledgment check")
                    return
                
                # Check if notification was acknowledged
                if notification.acknowledged_at:
                    logger.info(f"Notification {notification_id} was acknowledged")
                    return {"status": "acknowledged", "notification_id": notification_id}
                
                # Check if notification expired
                if notification.is_expired():
                    notification.status = NotificationStatus.EXPIRED
                    await db.commit()
                    logger.info(f"Notification {notification_id} expired")
                    return {"status": "expired", "notification_id": notification_id}
                
                # Escalate notification
                await escalate_notification(notification, notification_service)
                return {"status": "escalated", "notification_id": notification_id}
                
        except Exception as e:
            logger.error(f"Error checking notification acknowledgment {notification_id}: {e}")
            raise
    
    return asyncio.run(_check())


async def escalate_notification(notification: Notification, notification_service: NotificationService):
    """
    Escalate unacknowledged persistent notification.
    
    Args:
        notification: Notification to escalate
        notification_service: Service instance for operations
    """
    # Get user preferences for escalation
    prefs = await notification_service._get_user_preferences(notification.user_id)
    
    if not prefs or not prefs.escalation_enabled:
        logger.info(f"Escalation disabled for user {notification.user_id}")
        return
    
    # Check escalation attempts
    escalation_count = notification.notification_data.get("escalation_count", 0)
    if escalation_count >= prefs.max_escalation_attempts:
        logger.info(f"Max escalation attempts reached for notification {notification.id}")
        notification.status = NotificationStatus.EXPIRED
        await notification_service.db.commit()
        return
    
    # Escalate through additional channels
    escalation_channels = prefs.escalation_channels or ["email"]
    
    # Add escalation channels to notification
    current_channels = set(notification.delivery_channels)
    escalation_channels_set = set(escalation_channels)
    new_channels = list(current_channels.union(escalation_channels_set))
    
    notification.delivery_channels = new_channels
    notification.notification_data["escalation_count"] = escalation_count + 1
    notification.notification_data["escalated_at"] = datetime.utcnow().isoformat()
    
    # Update message to indicate escalation
    notification.title = f"🔔 REMINDER: {notification.title}"
    notification.message = f"This is an important reminder that requires your attention.\n\n{notification.message}"
    
    await notification_service.db.commit()
    
    # Deliver escalated notification
    await notification_service.deliver_notification(notification.id, "escalation")
    
    # Schedule next escalation check if needed
    next_escalation_time = notification.get_next_escalation_time()
    if next_escalation_time and escalation_count + 1 < prefs.max_escalation_attempts:
        celery_app.send_task(
            'check_notification_acknowledgment',
            args=[str(notification.id)],
            eta=next_escalation_time
        )
    
    logger.info(f"Escalated notification {notification.id} (attempt {escalation_count + 1})")


@celery_app.task(name='process_notification_batches')
def process_notification_batches_task():
    """
    Process batched notifications for delivery at designated times.
    """
    async def _process():
        try:
            async with get_db_session() as db:
                notification_service = NotificationService(db, celery_app)
                
                # Get batched notifications ready for delivery
                from sqlalchemy import select, and_
                
                now = datetime.utcnow()
                batch_window = timedelta(minutes=30)  # 30-minute window for batch delivery
                
                result = await db.execute(
                    select(Notification).where(
                        and_(
                            Notification.status == NotificationStatus.SCHEDULED,
                            Notification.scheduled_for <= now + batch_window,
                            Notification.notification_data["batched"].astext == "true"
                        )
                    ).limit(50)  # Process in batches of 50
                )
                
                notifications = result.scalars().all()
                
                if not notifications:
                    logger.info("No batched notifications to process")
                    return {"processed": 0}
                
                # Group notifications by user
                user_batches = {}
                for notification in notifications:
                    user_id = notification.user_id
                    if user_id not in user_batches:
                        user_batches[user_id] = []
                    user_batches[user_id].append(notification)
                
                # Process each user's batch
                total_processed = 0
                for user_id, user_notifications in user_batches.items():
                    try:
                        # Create batch notification
                        batch_notification = await create_batch_notification(
                            user_id, user_notifications, notification_service
                        )
                        
                        # Deliver batch
                        await notification_service.deliver_notification(
                            batch_notification.id, "batch"
                        )
                        
                        # Mark individual notifications as delivered
                        for notification in user_notifications:
                            notification.status = NotificationStatus.DELIVERED
                            notification.delivered_at = now
                        
                        await db.commit()
                        total_processed += len(user_notifications)
                        
                    except Exception as e:
                        logger.error(f"Error processing batch for user {user_id}: {e}")
                        continue
                
                logger.info(f"Processed {total_processed} batched notifications")
                return {"processed": total_processed}
                
        except Exception as e:
            logger.error(f"Error processing notification batches: {e}")
            raise
    
    return asyncio.run(_process())


async def create_batch_notification(
    user_id: UUID, 
    notifications: List[Notification], 
    notification_service: NotificationService
) -> Notification:
    """
    Create a single batch notification from multiple notifications.
    
    Args:
        user_id: User to receive the batch
        notifications: Notifications to batch together
        notification_service: Service instance
        
    Returns:
        Notification: Created batch notification
    """
    # Count notifications by priority
    priority_counts = {}
    for notification in notifications:
        priority = notification.priority.value
        priority_counts[priority] = priority_counts.get(priority, 0) + 1
    
    # Create batch title and message
    total_count = len(notifications)
    if total_count == 1:
        # Single notification, use original
        return notifications[0]
    
    title = f"📋 You have {total_count} notifications"
    
    # Create summary message
    message_parts = [f"Here's a summary of your {total_count} notifications:"]
    
    for i, notification in enumerate(notifications[:5], 1):  # Show first 5
        message_parts.append(f"{i}. {notification.title}")
    
    if total_count > 5:
        message_parts.append(f"... and {total_count - 5} more")
    
    message_parts.append("\nTap to view all notifications in the app.")
    message = "\n".join(message_parts)
    
    # Create batch notification
    from app.schemas.notification import NotificationCreate
    
    batch_data = NotificationCreate(
        type=notifications[0].type,  # Use type of first notification
        title=title,
        message=message,
        scheduled_for=datetime.utcnow(),
        priority=max(n.priority for n in notifications),  # Use highest priority
        delivery_channels=["push"],  # Batches are push-only
        is_persistent=any(n.is_persistent for n in notifications),
        respect_focus_mode=False,  # Batches ignore focus mode
    )
    
    batch_notification = await notification_service.create_notification(user_id, batch_data)
    
    # Store batch metadata
    batch_notification.notification_data.update({
        "is_batch": True,
        "batch_size": total_count,
        "priority_breakdown": priority_counts,
        "original_notification_ids": [str(n.id) for n in notifications]
    })
    
    await notification_service.db.commit()
    
    return batch_notification


@celery_app.task(name='check_overdue_notifications')
def check_overdue_notifications_task():
    """
    Check for overdue notifications and handle them appropriately.
    """
    async def _check():
        try:
            async with get_db_session() as db:
                notification_service = NotificationService(db, celery_app)
                
                # Get overdue notifications
                from sqlalchemy import select, and_
                
                now = datetime.utcnow()
                result = await db.execute(
                    select(Notification).where(
                        and_(
                            Notification.status == NotificationStatus.SCHEDULED,
                            Notification.scheduled_for < now - timedelta(minutes=5)  # 5 minutes overdue
                        )
                    ).limit(100)
                )
                
                overdue_notifications = result.scalars().all()
                
                if not overdue_notifications:
                    return {"overdue_count": 0}
                
                # Process overdue notifications
                processed = 0
                for notification in overdue_notifications:
                    try:
                        # Attempt immediate delivery
                        success = await notification_service.deliver_notification(
                            notification.id, "overdue"
                        )
                        
                        if success:
                            processed += 1
                        else:
                            # Mark as expired if delivery fails
                            notification.status = NotificationStatus.EXPIRED
                            notification.delivery_errors.append("Overdue delivery failed")
                    
                    except Exception as e:
                        logger.error(f"Error processing overdue notification {notification.id}: {e}")
                        continue
                
                await db.commit()
                
                logger.info(f"Processed {processed} overdue notifications")
                return {"overdue_count": len(overdue_notifications), "processed": processed}
                
        except Exception as e:
            logger.error(f"Error checking overdue notifications: {e}")
            raise
    
    return asyncio.run(_check())


@celery_app.task(name='send_email_notification', bind=True)
def send_email_notification_task(self, notification_id: str, email_data: dict):
    """
    Send email notification with ADHD-friendly formatting.
    
    Args:
        notification_id: ID of notification
        email_data: Email content and configuration
    """
    async def _send():
        try:
            # Implementation would send actual email
            # This is a placeholder for the email sending logic
            
            logger.info(f"Sending email for notification {notification_id}")
            
            # Simulate email sending
            import time
            time.sleep(1)  # Simulate email sending delay
            
            return {"status": "sent", "notification_id": notification_id}
            
        except Exception as e:
            logger.error(f"Error sending email for notification {notification_id}: {e}")
            if self.request.retries < 3:
                raise self.retry(countdown=60 * (2 ** self.request.retries))
            raise
    
    return asyncio.run(_send())


@celery_app.task(name='send_sms_notification', bind=True)
def send_sms_notification_task(self, notification_id: str, sms_data: dict):
    """
    Send SMS notification for urgent reminders.
    
    Args:
        notification_id: ID of notification
        sms_data: SMS content and configuration
    """
    async def _send():
        try:
            # Implementation would send actual SMS
            # This is a placeholder for the SMS sending logic
            
            logger.info(f"Sending SMS for notification {notification_id}")
            
            # Simulate SMS sending
            import time
            time.sleep(0.5)  # Simulate SMS sending delay
            
            return {"status": "sent", "notification_id": notification_id}
            
        except Exception as e:
            logger.error(f"Error sending SMS for notification {notification_id}: {e}")
            if self.request.retries < 2:  # Fewer retries for SMS
                raise self.retry(countdown=30 * (2 ** self.request.retries))
            raise
    
    return asyncio.run(_send())
