"""
Gamification utilities for Project Chronos.

This module provides utility functions for integrating gamification
features throughout the application.
"""

import logging
from datetime import datetime, time
from typing import Dict, Optional, Tuple
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from app.models.task import Task
from app.services.gamification_service import GamificationService
from app.services.achievement_service import AchievementService

logger = logging.getLogger(__name__)


class GamificationIntegration:
    """Integration utility for gamification features."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.gamification_service = GamificationService(db)
        self.achievement_service = AchievementService(db)
    
    async def handle_task_completion(
        self,
        task: Task,
        user_id: UUID,
        completion_context: Optional[Dict] = None
    ) -> Dict:
        """
        Handle gamification aspects of task completion.
        
        Args:
            task: The completed task
            user_id: User who completed the task
            completion_context: Additional context about completion
            
        Returns:
            Dict containing gamification results
        """
        if completion_context is None:
            completion_context = {}
        
        results = {
            "points_awarded": 0,
            "level_up": None,
            "achievements_unlocked": [],
            "streak_updated": None
        }
        
        try:
            # Calculate base points for task completion
            base_points = self._calculate_base_points(task)
            
            # Calculate multiplier based on task and context
            multiplier = await self._calculate_task_multiplier(task, completion_context)
            
            # Award points
            award, level_up = await self.gamification_service.award_points(
                user_id=user_id,
                points=base_points,
                reason=f"Completed task: {task.title}",
                task_id=task.id,
                multiplier=multiplier,
                metadata={
                    "task_priority": task.priority,
                    "task_energy_level": task.energy_level,
                    "estimated_duration": task.estimated_duration,
                    "actual_duration": task.actual_duration,
                    **completion_context
                }
            )
            
            results["points_awarded"] = award.points_awarded
            results["level_up"] = level_up
            
            # Update task completion streak
            streak, streak_changed = await self.gamification_service.update_streak(
                user_id=user_id,
                streak_type="daily_tasks",
                action_completed=True
            )
            results["streak_updated"] = streak if streak_changed else None
            
            # Check for achievements
            achievement_data = {
                "task_id": str(task.id),
                "task_priority": task.priority,
                "task_energy_level": task.energy_level,
                "points_awarded": award.points_awarded,
                "streak_length": streak.current_streak if streak_changed else 0
            }
            
            # Check task completion achievements
            task_achievements = await self.achievement_service.check_achievements(
                user_id=user_id,
                trigger_event="task_completed",
                event_data=achievement_data
            )
            results["achievements_unlocked"].extend(task_achievements)
            
            # Check streak achievements if streak changed
            if streak_changed:
                streak_achievements = await self.achievement_service.check_achievements(
                    user_id=user_id,
                    trigger_event="streak_milestone",
                    event_data={"streak_length": streak.current_streak, "streak_type": "daily_tasks"}
                )
                results["achievements_unlocked"].extend(streak_achievements)
            
            logger.info(f"Gamification handled for task {task.id}: {award.points_awarded} points, {len(results['achievements_unlocked'])} achievements")
            
        except Exception as e:
            logger.error(f"Error handling gamification for task {task.id}: {e}")
            # Don't fail task completion if gamification fails
        
        return results
    
    async def handle_task_start(
        self,
        task: Task,
        user_id: UUID,
        start_context: Optional[Dict] = None
    ) -> Dict:
        """
        Handle gamification aspects of task start.
        
        Args:
            task: The started task
            user_id: User who started the task
            start_context: Additional context about starting
            
        Returns:
            Dict containing gamification results
        """
        if start_context is None:
            start_context = {}
        
        results = {
            "points_awarded": 0,
            "achievements_unlocked": []
        }
        
        try:
            # Award small points for starting a task (helps with initiation)
            base_points = 5  # Small reward for starting
            
            # Bonus for starting difficult or high-energy tasks
            multiplier = 1.0
            if task.priority in ["high", "urgent"]:
                multiplier += 0.2
            if task.energy_level == "high":
                multiplier += 0.2
            
            # Bonus for starting tasks during low energy periods
            current_hour = datetime.now().hour
            if current_hour < 9 or current_hour > 18:  # Early morning or evening
                multiplier += 0.3
                start_context["time_bonus"] = "off_hours"
            
            award, _ = await self.gamification_service.award_points(
                user_id=user_id,
                points=base_points,
                reason=f"Started task: {task.title}",
                task_id=task.id,
                multiplier=multiplier,
                metadata={
                    "action": "task_start",
                    "task_priority": task.priority,
                    "task_energy_level": task.energy_level,
                    **start_context
                }
            )
            
            results["points_awarded"] = award.points_awarded
            
            logger.info(f"Task start gamification for {task.id}: {award.points_awarded} points")
            
        except Exception as e:
            logger.error(f"Error handling task start gamification for task {task.id}: {e}")
        
        return results
    
    def _calculate_base_points(self, task: Task) -> int:
        """Calculate base points for a task based on its properties."""
        base_points = 20  # Base completion points
        
        # Priority multiplier
        priority_points = {
            "low": 0,
            "medium": 10,
            "high": 20,
            "urgent": 30
        }
        base_points += priority_points.get(task.priority, 10)
        
        # Energy level points
        energy_points = {
            "low": 5,
            "medium": 10,
            "high": 20
        }
        base_points += energy_points.get(task.energy_level, 10)
        
        # Duration-based points
        if task.estimated_duration:
            if task.estimated_duration <= 15:  # Quick tasks
                base_points += 5
            elif task.estimated_duration <= 60:  # Medium tasks
                base_points += 15
            else:  # Long tasks
                base_points += 30
        
        # Bonus for chunked tasks (encourages breaking down overwhelming tasks)
        if task.parent_task_id:
            base_points += 5
        
        return base_points
    
    async def _calculate_task_multiplier(
        self,
        task: Task,
        context: Dict
    ) -> float:
        """Calculate multiplier for task completion based on context."""
        multiplier = 1.0
        
        # Time-based multipliers
        current_time = datetime.now().time()
        
        # Early morning bonus (ADHD users often struggle with mornings)
        if time(6, 0) <= current_time <= time(9, 0):
            multiplier += 0.3
        
        # Late evening bonus (reward for evening productivity)
        elif time(20, 0) <= current_time <= time(23, 59):
            multiplier += 0.2
        
        # Energy level context
        user_energy = context.get("user_energy_level")
        if user_energy == "low" and task.energy_level in ["medium", "high"]:
            multiplier += 0.4  # Big bonus for pushing through low energy
        
        # Overdue task bonus
        if task.due_date and task.due_date < datetime.now():
            multiplier += 0.2
        
        # Quick completion bonus
        if task.estimated_duration and task.actual_duration:
            if task.actual_duration <= task.estimated_duration * 0.8:
                multiplier += 0.2  # Completed faster than expected
        
        # Context-specific bonuses
        if context.get("after_break"):
            multiplier += 0.1
        
        if context.get("first_task_of_day"):
            multiplier += 0.2
        
        if context.get("difficult_start"):  # User indicated it was hard to start
            multiplier += 0.3
        
        return multiplier
    
    async def get_user_motivation_boost(
        self,
        user_id: UUID,
        current_energy: str = "medium"
    ) -> Dict:
        """
        Get motivation boost suggestions for a user.
        
        Args:
            user_id: User ID
            current_energy: Current energy level
            
        Returns:
            Dict with motivation suggestions
        """
        try:
            # Get user's gamification profile
            profile = await self.gamification_service.get_or_create_user_gamification(user_id)
            
            # Get current stats
            stats = await self.gamification_service.get_gamification_stats(user_id)
            
            suggestions = []
            
            # Level progress motivation
            if stats.level_progress_percentage > 80:
                suggestions.append({
                    "type": "level_progress",
                    "message": f"You're {stats.points_to_next_level} points away from level {stats.current_level + 1}!",
                    "action": "Complete a task to level up"
                })
            
            # Streak motivation
            active_streaks = [s for s in stats.active_streaks if s.current_streak > 0]
            if active_streaks:
                best_streak = max(active_streaks, key=lambda s: s.current_streak)
                suggestions.append({
                    "type": "streak_maintenance",
                    "message": f"Keep your {best_streak.current_streak}-day streak going!",
                    "action": "Complete a task today"
                })
            
            # Energy-based suggestions
            if current_energy == "low":
                suggestions.append({
                    "type": "energy_boost",
                    "message": "Low energy tasks earn bonus points!",
                    "action": "Try a quick, easy task for extra rewards"
                })
            
            return {
                "current_level": stats.current_level,
                "total_points": stats.total_points,
                "points_to_next_level": stats.points_to_next_level,
                "level_progress_percentage": stats.level_progress_percentage,
                "suggestions": suggestions
            }
            
        except Exception as e:
            logger.error(f"Error getting motivation boost for user {user_id}: {e}")
            return {"suggestions": []}
