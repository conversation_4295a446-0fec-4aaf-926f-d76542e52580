"""
Notification schemas for Project Chronos.

This module defines Pydantic schemas for ADHD-optimized notifications,
reminders, and background task management.
"""

from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict, validator

from app.models.notification import (
    NotificationType,
    NotificationPriority,
    NotificationStatus,
)


class NotificationBase(BaseModel):
    """Base schema for notifications."""
    
    type: NotificationType = Field(description="Type of notification")
    title: str = Field(max_length=200, description="Notification title")
    message: str = Field(description="Notification message content")
    action_text: Optional[str] = Field(
        None, max_length=100,
        description="Text for primary action button"
    )
    action_url: Optional[str] = Field(
        None, max_length=500,
        description="URL for primary action"
    )
    scheduled_for: datetime = Field(description="When notification should be delivered")
    priority: NotificationPriority = Field(
        default=NotificationPriority.NORMAL,
        description="Notification priority level"
    )
    delivery_channels: List[str] = Field(
        default_factory=lambda: ["push"],
        description="Delivery channels (push, email, sms)"
    )
    is_persistent: bool = Field(
        default=False,
        description="Whether notification requires acknowledgment"
    )
    respect_focus_mode: bool = Field(
        default=True,
        description="Whether to defer during focus sessions"
    )
    max_snooze_count: int = Field(
        default=3, ge=0, le=10,
        description="Maximum number of times notification can be snoozed"
    )
    expires_at: Optional[datetime] = Field(
        None,
        description="When notification expires"
    )


class NotificationCreate(NotificationBase):
    """Schema for creating a notification."""
    
    task_id: Optional[UUID] = Field(None, description="Associated task ID")
    time_block_id: Optional[UUID] = Field(None, description="Associated time block ID")
    focus_session_id: Optional[UUID] = Field(None, description="Associated focus session ID")
    reminder_stages: Optional[List[int]] = Field(
        None,
        description="Minutes before event for staggered reminders"
    )
    
    @validator('delivery_channels')
    def validate_delivery_channels(cls, v):
        valid_channels = ['push', 'email', 'sms']
        for channel in v:
            if channel not in valid_channels:
                raise ValueError(f'Invalid delivery channel: {channel}')
        return v
    
    @validator('expires_at')
    def validate_expires_at(cls, v, values):
        scheduled_for = values.get('scheduled_for')
        if v and scheduled_for and v <= scheduled_for:
            raise ValueError('Expiration time must be after scheduled time')
        return v


class NotificationUpdate(BaseModel):
    """Schema for updating a notification."""
    
    title: Optional[str] = Field(None, max_length=200)
    message: Optional[str] = None
    action_text: Optional[str] = Field(None, max_length=100)
    action_url: Optional[str] = Field(None, max_length=500)
    scheduled_for: Optional[datetime] = None
    priority: Optional[NotificationPriority] = None
    delivery_channels: Optional[List[str]] = None
    is_persistent: Optional[bool] = None
    respect_focus_mode: Optional[bool] = None
    max_snooze_count: Optional[int] = Field(None, ge=0, le=10)
    expires_at: Optional[datetime] = None


class NotificationResponse(NotificationBase):
    """Schema for notification responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    task_id: Optional[UUID] = None
    time_block_id: Optional[UUID] = None
    focus_session_id: Optional[UUID] = None
    status: NotificationStatus
    snooze_count: int = 0
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    acknowledgment_action: Optional[str] = None
    snoozed_until: Optional[datetime] = None
    delivery_attempts: int = 0
    last_delivery_attempt: Optional[datetime] = None
    delivery_errors: List[str] = Field(default_factory=list)
    created_at: datetime
    updated_at: datetime
    
    # Calculated fields
    is_overdue: Optional[bool] = None
    can_be_snoozed: Optional[bool] = None
    is_expired: Optional[bool] = None
    next_escalation_time: Optional[datetime] = None


class NotificationAcknowledge(BaseModel):
    """Schema for acknowledging a notification."""
    
    action: str = Field(
        default="acknowledged",
        description="Action taken when acknowledging"
    )
    notes: Optional[str] = Field(
        None, max_length=500,
        description="Optional notes about the acknowledgment"
    )


class NotificationSnooze(BaseModel):
    """Schema for snoozing a notification."""
    
    snooze_duration: int = Field(
        ge=1, le=1440,
        description="Snooze duration in minutes"
    )
    snooze_reason: Optional[str] = Field(
        None, max_length=200,
        description="Reason for snoozing"
    )


class NotificationPreferencesBase(BaseModel):
    """Base schema for notification preferences."""
    
    enabled: bool = Field(default=True, description="Whether notifications are enabled")
    quiet_hours_start: Optional[str] = Field(
        None, regex=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$",
        description="Start of quiet hours (HH:MM)"
    )
    quiet_hours_end: Optional[str] = Field(
        None, regex=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$",
        description="End of quiet hours (HH:MM)"
    )
    push_enabled: bool = Field(default=True, description="Enable push notifications")
    email_enabled: bool = Field(default=True, description="Enable email notifications")
    sms_enabled: bool = Field(default=False, description="Enable SMS notifications")
    persistent_reminders: bool = Field(
        default=True,
        description="Enable persistent reminders that require acknowledgment"
    )
    respect_focus_mode: bool = Field(
        default=True,
        description="Defer non-urgent notifications during focus sessions"
    )
    batch_non_urgent: bool = Field(
        default=True,
        description="Batch non-urgent notifications for designated times"
    )
    batch_times: List[str] = Field(
        default_factory=lambda: ["09:00", "13:00", "17:00"],
        description="Times for batched notification delivery"
    )
    escalation_enabled: bool = Field(
        default=True,
        description="Enable notification escalation for unacknowledged reminders"
    )
    escalation_channels: List[str] = Field(
        default_factory=lambda: ["push", "email"],
        description="Channels to use for escalation"
    )
    max_escalation_attempts: int = Field(
        default=3, ge=1, le=10,
        description="Maximum escalation attempts"
    )
    type_preferences: Dict[str, Any] = Field(
        default_factory=dict,
        description="Preferences for specific notification types"
    )
    settings: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional notification preferences"
    )


class NotificationPreferencesCreate(NotificationPreferencesBase):
    """Schema for creating notification preferences."""
    pass


class NotificationPreferencesUpdate(BaseModel):
    """Schema for updating notification preferences."""
    
    enabled: Optional[bool] = None
    quiet_hours_start: Optional[str] = Field(
        None, regex=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$"
    )
    quiet_hours_end: Optional[str] = Field(
        None, regex=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$"
    )
    push_enabled: Optional[bool] = None
    email_enabled: Optional[bool] = None
    sms_enabled: Optional[bool] = None
    persistent_reminders: Optional[bool] = None
    respect_focus_mode: Optional[bool] = None
    batch_non_urgent: Optional[bool] = None
    batch_times: Optional[List[str]] = None
    escalation_enabled: Optional[bool] = None
    escalation_channels: Optional[List[str]] = None
    max_escalation_attempts: Optional[int] = Field(None, ge=1, le=10)
    type_preferences: Optional[Dict[str, Any]] = None
    settings: Optional[Dict[str, Any]] = None


class NotificationPreferencesResponse(NotificationPreferencesBase):
    """Schema for notification preferences responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime


class NotificationTemplateBase(BaseModel):
    """Base schema for notification templates."""
    
    name: str = Field(max_length=100, description="Template name")
    type: NotificationType = Field(description="Notification type this template is for")
    title_template: str = Field(
        max_length=200,
        description="Title template with placeholders"
    )
    message_template: str = Field(description="Message template with placeholders")
    action_text_template: Optional[str] = Field(
        None, max_length=100,
        description="Action button text template"
    )
    default_priority: NotificationPriority = Field(
        default=NotificationPriority.NORMAL,
        description="Default priority for this template"
    )
    default_channels: List[str] = Field(
        default_factory=lambda: ["push"],
        description="Default delivery channels"
    )
    is_persistent: bool = Field(
        default=False,
        description="Whether notifications from this template are persistent"
    )
    description: Optional[str] = Field(None, description="Template description")


class NotificationTemplateCreate(NotificationTemplateBase):
    """Schema for creating a notification template."""
    pass


class NotificationTemplateResponse(NotificationTemplateBase):
    """Schema for notification template responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    is_system_template: bool = True
    created_at: datetime
    updated_at: datetime


class NotificationBatch(BaseModel):
    """Schema for batched notification delivery."""
    
    batch_id: str = Field(description="Unique batch identifier")
    user_id: UUID = Field(description="User receiving the batch")
    notifications: List[NotificationResponse] = Field(
        description="Notifications in this batch"
    )
    batch_time: datetime = Field(description="When batch was created")
    total_count: int = Field(description="Total notifications in batch")
    priority_breakdown: Dict[str, int] = Field(
        description="Count by priority level"
    )


class NotificationStats(BaseModel):
    """Schema for notification statistics."""
    
    total_sent: int
    total_delivered: int
    total_acknowledged: int
    total_snoozed: int
    total_expired: int
    acknowledgment_rate: float
    average_acknowledgment_time: Optional[float]  # in minutes
    most_effective_channels: List[str]
    peak_notification_times: List[str]
    escalation_frequency: float


class NotificationAnalytics(BaseModel):
    """Schema for detailed notification analytics."""
    
    user_id: UUID
    date_range: Dict[str, datetime]
    stats: NotificationStats
    type_breakdown: Dict[str, int]
    channel_effectiveness: Dict[str, float]
    timing_patterns: Dict[str, Any]
    recommendations: List[str]


class ReminderStageCreate(BaseModel):
    """Schema for creating reminder stages."""
    
    minutes_before: int = Field(
        ge=1, le=10080,  # Up to 1 week
        description="Minutes before event to send reminder"
    )
    message_override: Optional[str] = Field(
        None,
        description="Custom message for this reminder stage"
    )
    channels_override: Optional[List[str]] = Field(
        None,
        description="Custom channels for this reminder stage"
    )


class BulkNotificationCreate(BaseModel):
    """Schema for creating multiple notifications."""
    
    notifications: List[NotificationCreate] = Field(
        description="List of notifications to create"
    )
    batch_processing: bool = Field(
        default=False,
        description="Whether to process as a batch"
    )
    batch_delay: Optional[int] = Field(
        None, ge=0, le=3600,
        description="Delay between notifications in seconds"
    )


class NotificationDeliveryStatus(BaseModel):
    """Schema for notification delivery status."""
    
    notification_id: UUID
    status: NotificationStatus
    delivery_channel: str
    delivered_at: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    next_retry_at: Optional[datetime] = None
