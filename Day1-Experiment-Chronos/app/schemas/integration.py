"""
Integration schemas for Project Chronos.

This module defines Pydantic schemas for external service integrations,
OAuth flows, and synchronization operations.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict, validator

from app.models.integration import (
    IntegrationType,
    IntegrationStatus,
    SyncStatus,
)


class IntegrationBase(BaseModel):
    """Base schema for integrations."""
    
    integration_type: IntegrationType = Field(description="Type of external service")
    name: str = Field(max_length=100, description="User-friendly name for this integration")
    description: Optional[str] = Field(None, description="Optional description")
    config: Dict[str, Any] = Field(
        default_factory=dict,
        description="Integration-specific configuration"
    )
    sync_settings: Dict[str, Any] = Field(
        default_factory=dict,
        description="Synchronization preferences and settings"
    )


class IntegrationCreate(IntegrationBase):
    """Schema for creating an integration."""
    
    external_id: Optional[str] = Field(None, description="External service user/account ID")
    webhook_secret: Optional[str] = Field(None, description="Webhook verification secret")


class IntegrationUpdate(BaseModel):
    """Schema for updating an integration."""
    
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    sync_settings: Optional[Dict[str, Any]] = None
    status: Optional[IntegrationStatus] = None


class IntegrationResponse(IntegrationBase):
    """Schema for integration responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    external_id: Optional[str] = None
    status: IntegrationStatus
    last_sync_at: Optional[datetime] = None
    last_error: Optional[str] = None
    error_count: int = 0
    total_syncs: int = 0
    successful_syncs: int = 0
    webhook_id: Optional[str] = None
    token_expires_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    # Calculated fields
    is_token_expired: Optional[bool] = None
    needs_refresh: Optional[bool] = None
    success_rate: Optional[float] = None
    last_sync_status: Optional[str] = None


class OAuthInitiate(BaseModel):
    """Schema for initiating OAuth flow."""
    
    integration_type: IntegrationType = Field(description="Type of service to connect")
    redirect_uri: str = Field(description="OAuth redirect URI")
    scopes: Optional[List[str]] = Field(
        None,
        description="Requested OAuth scopes"
    )
    state: Optional[str] = Field(
        None,
        description="OAuth state parameter for security"
    )


class OAuthCallback(BaseModel):
    """Schema for OAuth callback handling."""
    
    code: str = Field(description="OAuth authorization code")
    state: Optional[str] = Field(None, description="OAuth state parameter")
    error: Optional[str] = Field(None, description="OAuth error if any")
    error_description: Optional[str] = Field(None, description="OAuth error description")


class OAuthTokens(BaseModel):
    """Schema for OAuth token response."""
    
    access_token: str = Field(description="OAuth access token")
    refresh_token: Optional[str] = Field(None, description="OAuth refresh token")
    token_type: str = Field(default="Bearer", description="Token type")
    expires_in: Optional[int] = Field(None, description="Token expiration in seconds")
    scope: Optional[str] = Field(None, description="Granted scopes")


class SyncLogBase(BaseModel):
    """Base schema for sync logs."""
    
    operation_type: str = Field(max_length=50, description="Type of sync operation")
    items_processed: int = Field(default=0, ge=0, description="Number of items processed")
    items_created: int = Field(default=0, ge=0, description="Number of items created")
    items_updated: int = Field(default=0, ge=0, description="Number of items updated")
    items_deleted: int = Field(default=0, ge=0, description="Number of items deleted")
    conflicts_detected: int = Field(default=0, ge=0, description="Number of conflicts detected")
    sync_data: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional sync operation data"
    )


class SyncLogCreate(SyncLogBase):
    """Schema for creating a sync log."""
    pass


class SyncLogUpdate(BaseModel):
    """Schema for updating a sync log."""
    
    status: Optional[SyncStatus] = None
    items_processed: Optional[int] = Field(None, ge=0)
    items_created: Optional[int] = Field(None, ge=0)
    items_updated: Optional[int] = Field(None, ge=0)
    items_deleted: Optional[int] = Field(None, ge=0)
    conflicts_detected: Optional[int] = Field(None, ge=0)
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    sync_data: Optional[Dict[str, Any]] = None


class SyncLogResponse(SyncLogBase):
    """Schema for sync log responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    integration_id: UUID
    status: SyncStatus
    started_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    error_details: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime
    updated_at: datetime
    
    # Calculated fields
    duration_seconds: Optional[float] = None
    success_rate: Optional[float] = None


class WebhookEventBase(BaseModel):
    """Base schema for webhook events."""
    
    integration_type: IntegrationType = Field(description="Type of external service")
    event_type: str = Field(max_length=100, description="Type of webhook event")
    external_id: Optional[str] = Field(None, description="External event or resource ID")
    payload: Dict[str, Any] = Field(
        default_factory=dict,
        description="Raw webhook payload"
    )
    headers: Dict[str, Any] = Field(
        default_factory=dict,
        description="HTTP headers from webhook request"
    )


class WebhookEventCreate(WebhookEventBase):
    """Schema for creating a webhook event."""
    pass


class WebhookEventResponse(WebhookEventBase):
    """Schema for webhook event responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    integration_id: Optional[UUID] = None
    processed: bool = False
    processed_at: Optional[datetime] = None
    processing_error: Optional[str] = None
    created_at: datetime
    updated_at: datetime


class SyncRequest(BaseModel):
    """Schema for requesting synchronization."""
    
    operation_type: str = Field(description="Type of sync operation")
    force: bool = Field(default=False, description="Force sync even if recently synced")
    dry_run: bool = Field(default=False, description="Perform dry run without changes")
    filters: Optional[Dict[str, Any]] = Field(
        None,
        description="Filters for selective synchronization"
    )


class SyncResponse(BaseModel):
    """Schema for sync operation response."""
    
    sync_id: UUID = Field(description="Sync operation ID")
    status: SyncStatus = Field(description="Current sync status")
    message: str = Field(description="Human-readable status message")
    estimated_duration: Optional[int] = Field(
        None,
        description="Estimated duration in seconds"
    )


class IntegrationStats(BaseModel):
    """Schema for integration statistics."""
    
    total_integrations: int
    active_integrations: int
    failed_integrations: int
    total_syncs_today: int
    successful_syncs_today: int
    average_sync_duration: Optional[float]
    most_active_integration: Optional[str]
    recent_errors: List[str]


class CalendarEventSync(BaseModel):
    """Schema for calendar event synchronization."""
    
    external_id: str = Field(description="External calendar event ID")
    title: str = Field(description="Event title")
    description: Optional[str] = Field(None, description="Event description")
    start_time: datetime = Field(description="Event start time")
    end_time: datetime = Field(description="Event end time")
    all_day: bool = Field(default=False, description="Whether event is all day")
    location: Optional[str] = Field(None, description="Event location")
    attendees: List[str] = Field(default_factory=list, description="Event attendees")
    calendar_id: Optional[str] = Field(None, description="External calendar ID")
    
    # ADHD-specific fields
    energy_level: Optional[str] = Field(None, description="Required energy level")
    buffer_before: int = Field(default=0, description="Buffer time before event")
    buffer_after: int = Field(default=0, description="Buffer time after event")
    is_focus_time: bool = Field(default=False, description="Whether this is focus time")


class TaskSync(BaseModel):
    """Schema for task synchronization."""
    
    external_id: str = Field(description="External task ID")
    title: str = Field(description="Task title")
    description: Optional[str] = Field(None, description="Task description")
    due_date: Optional[datetime] = Field(None, description="Task due date")
    completed: bool = Field(default=False, description="Whether task is completed")
    priority: Optional[str] = Field(None, description="Task priority")
    labels: List[str] = Field(default_factory=list, description="Task labels/tags")
    project_id: Optional[str] = Field(None, description="External project ID")
    
    # ADHD-specific fields
    energy_level: Optional[str] = Field(None, description="Required energy level")
    complexity: Optional[str] = Field(None, description="Task complexity")
    estimated_duration: Optional[int] = Field(None, description="Estimated duration in minutes")
    chunk_size: Optional[int] = Field(None, description="Preferred chunk size for breaking down")


class IntegrationHealth(BaseModel):
    """Schema for integration health check."""
    
    integration_id: UUID
    integration_type: IntegrationType
    status: IntegrationStatus
    is_healthy: bool
    last_successful_sync: Optional[datetime]
    error_count: int
    token_status: str  # "valid", "expired", "expiring_soon", "missing"
    webhook_status: str  # "active", "inactive", "error", "not_configured"
    recommendations: List[str]


class BulkSyncRequest(BaseModel):
    """Schema for bulk synchronization request."""
    
    integration_ids: List[UUID] = Field(description="Integration IDs to sync")
    operation_type: str = Field(description="Type of sync operation")
    force: bool = Field(default=False, description="Force sync even if recently synced")
    parallel: bool = Field(default=True, description="Run syncs in parallel")
    max_concurrent: int = Field(default=3, ge=1, le=10, description="Max concurrent syncs")


class BulkSyncResponse(BaseModel):
    """Schema for bulk synchronization response."""
    
    batch_id: UUID = Field(description="Bulk sync batch ID")
    total_integrations: int = Field(description="Total integrations to sync")
    started_syncs: List[UUID] = Field(description="Successfully started sync IDs")
    failed_starts: List[Dict[str, Any]] = Field(description="Failed to start syncs")
    estimated_completion: Optional[datetime] = Field(description="Estimated completion time")


class ConflictResolution(BaseModel):
    """Schema for sync conflict resolution."""
    
    conflict_id: str = Field(description="Unique conflict identifier")
    conflict_type: str = Field(description="Type of conflict")
    local_item: Dict[str, Any] = Field(description="Local item data")
    remote_item: Dict[str, Any] = Field(description="Remote item data")
    resolution_strategy: str = Field(description="How to resolve the conflict")
    # Strategies: "use_local", "use_remote", "merge", "skip", "manual"
    merge_fields: Optional[Dict[str, str]] = Field(
        None,
        description="Field-level merge instructions"
    )
