"""
Focus session schemas for Project Chronos.

This module defines Pydantic schemas for focus sessions, including
Pomodoro technique, deep work sessions, and ADHD-specific focus patterns.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class FocusSessionBase(BaseModel):
    """Base schema for focus session data."""
    
    title: Optional[str] = Field(
        None,
        max_length=200,
        description="Optional session title/description"
    )
    
    session_type: str = Field(
        default="pomodoro",
        description="Session type: pomodoro, deep_work, sprint, custom"
    )
    
    planned_duration: int = Field(
        ...,
        ge=1,
        le=480,  # Max 8 hours
        description="Planned session duration in minutes"
    )
    
    break_duration: int = Field(
        default=5,
        ge=1,
        le=60,
        description="Break duration in minutes"
    )
    
    focus_mode_settings: Dict[str, Any] = Field(
        default_factory=dict,
        description="Focus mode configuration and preferences"
    )
    
    is_group_session: bool = Field(
        default=False,
        description="Whether this is a synchronized group focus session"
    )
    
    @validator('session_type')
    def validate_session_type(cls, v):
        """Validate session type."""
        allowed_types = ['pomodoro', 'deep_work', 'sprint', 'custom']
        if v not in allowed_types:
            raise ValueError(f'Session type must be one of: {allowed_types}')
        return v


class FocusSessionCreate(FocusSessionBase):
    """Schema for creating a new focus session."""
    
    task_id: Optional[UUID] = Field(
        None,
        description="Associated task ID (optional)"
    )
    
    body_doubling_session_id: Optional[UUID] = Field(
        None,
        description="Associated body doubling session for group focus"
    )


class FocusSessionUpdate(BaseModel):
    """Schema for updating a focus session."""
    
    title: Optional[str] = Field(
        None,
        max_length=200,
        description="Session title/description"
    )
    
    status: Optional[str] = Field(
        None,
        description="Session status: planned, active, paused, completed, cancelled"
    )
    
    actual_duration: Optional[int] = Field(
        None,
        ge=0,
        description="Actual session duration in minutes"
    )
    
    hyperfocus_detected: Optional[bool] = Field(
        None,
        description="Whether hyperfocus was detected"
    )
    
    hyperfocus_duration: Optional[int] = Field(
        None,
        ge=0,
        description="Duration of hyperfocus in minutes"
    )
    
    distraction_count: Optional[int] = Field(
        None,
        ge=0,
        description="Number of distractions during session"
    )
    
    energy_level_start: Optional[int] = Field(
        None,
        ge=1,
        le=5,
        description="Energy level at session start (1-5)"
    )
    
    energy_level_end: Optional[int] = Field(
        None,
        ge=1,
        le=5,
        description="Energy level at session end (1-5)"
    )
    
    session_notes: Optional[str] = Field(
        None,
        description="Optional notes about the session"
    )
    
    @validator('status')
    def validate_status(cls, v):
        """Validate session status."""
        if v is not None:
            allowed_statuses = ['planned', 'active', 'paused', 'completed', 'cancelled']
            if v not in allowed_statuses:
                raise ValueError(f'Status must be one of: {allowed_statuses}')
        return v


class FocusSessionResponse(BaseModel):
    """Schema for focus session responses."""
    
    id: UUID
    user_id: UUID
    task_id: Optional[UUID]
    body_doubling_session_id: Optional[UUID]
    session_type: str
    title: Optional[str]
    planned_duration: int
    actual_duration: Optional[int]
    break_duration: int
    status: str
    focus_mode_settings: Dict[str, Any]
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    paused_at: Optional[datetime]
    is_group_session: bool
    group_session_leader: Optional[UUID]
    hyperfocus_detected: bool
    hyperfocus_duration: Optional[int]
    distraction_count: int
    energy_level_start: Optional[int]
    energy_level_end: Optional[int]
    session_notes: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    # Computed fields
    elapsed_minutes: Optional[int] = None
    remaining_minutes: Optional[int] = None
    progress_percentage: Optional[float] = None
    
    class Config:
        from_attributes = True


class FocusSessionParticipantCreate(BaseModel):
    """Schema for joining a group focus session."""
    
    personal_task_id: Optional[UUID] = Field(
        None,
        description="Personal task to work on during this session"
    )
    
    progress_notes: Optional[str] = Field(
        None,
        description="Personal progress notes"
    )


class FocusSessionParticipantUpdate(BaseModel):
    """Schema for updating focus session participation."""
    
    status: Optional[str] = Field(
        None,
        description="Participant status: active, paused, completed, left"
    )
    
    progress_notes: Optional[str] = Field(
        None,
        description="Personal progress notes"
    )
    
    @validator('status')
    def validate_status(cls, v):
        """Validate participant status."""
        if v is not None:
            allowed_statuses = ['active', 'paused', 'completed', 'left']
            if v not in allowed_statuses:
                raise ValueError(f'Status must be one of: {allowed_statuses}')
        return v


class FocusSessionParticipantResponse(BaseModel):
    """Schema for focus session participant responses."""
    
    id: UUID
    focus_session_id: UUID
    user_id: UUID
    personal_task_id: Optional[UUID]
    joined_at: datetime
    left_at: Optional[datetime]
    status: str
    progress_notes: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class FocusSessionStats(BaseModel):
    """Schema for focus session statistics."""
    
    total_sessions: int
    completed_sessions: int
    total_focus_time: int  # in minutes
    average_session_duration: float
    hyperfocus_sessions: int
    group_sessions: int
    completion_rate: float
    most_productive_time: Optional[str]
    favorite_session_type: Optional[str]


class GroupFocusSessionCreate(FocusSessionBase):
    """Schema for creating a group focus session within a body doubling session."""
    
    body_doubling_session_id: UUID = Field(
        ...,
        description="Body doubling session to run the focus session in"
    )
    
    auto_start: bool = Field(
        default=False,
        description="Whether to start the session immediately"
    )


class FocusSessionCommand(BaseModel):
    """Schema for focus session commands (start, pause, resume, complete)."""
    
    command: str = Field(
        ...,
        description="Command to execute: start, pause, resume, complete, cancel"
    )
    
    notes: Optional[str] = Field(
        None,
        description="Optional notes about the command"
    )
    
    @validator('command')
    def validate_command(cls, v):
        """Validate session command."""
        allowed_commands = ['start', 'pause', 'resume', 'complete', 'cancel']
        if v not in allowed_commands:
            raise ValueError(f'Command must be one of: {allowed_commands}')
        return v
