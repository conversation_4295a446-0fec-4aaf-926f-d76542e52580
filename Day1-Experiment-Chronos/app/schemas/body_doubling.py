"""
Pydantic schemas for body doubling sessions and real-time collaboration.

This module provides request/response schemas for virtual body doubling
sessions, participant management, and WebSocket communication.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class BodyDoublingSessionBase(BaseModel):
    """Base schema for body doubling sessions."""
    
    title: str = Field(..., min_length=1, max_length=200, description="Session title")
    description: Optional[str] = Field(None, description="Optional session description")
    max_participants: int = Field(4, ge=2, le=10, description="Maximum participants")
    session_type: str = Field("open", description="Session type")
    is_public: bool = Field(True, description="Whether session is publicly discoverable")
    requires_approval: bool = Field(False, description="Whether host approval is required")
    password_protected: bool = Field(False, description="Whether session requires password")
    scheduled_start: Optional[datetime] = Field(None, description="Scheduled start time")
    scheduled_end: Optional[datetime] = Field(None, description="Scheduled end time")
    session_settings: Dict[str, Any] = Field(default_factory=dict, description="Session configuration")
    
    @validator("session_type")
    def validate_session_type(cls, v):
        """Validate session type."""
        allowed_types = ["open", "focused", "study", "work", "creative"]
        if v not in allowed_types:
            raise ValueError(f"Session type must be one of: {allowed_types}")
        return v


class BodyDoublingSessionCreate(BodyDoublingSessionBase):
    """Schema for creating body doubling sessions."""
    
    session_password: Optional[str] = Field(None, min_length=4, max_length=50, description="Session password")


class BodyDoublingSessionUpdate(BaseModel):
    """Schema for updating body doubling sessions."""
    
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None)
    max_participants: Optional[int] = Field(None, ge=2, le=10)
    is_public: Optional[bool] = Field(None)
    requires_approval: Optional[bool] = Field(None)
    session_settings: Optional[Dict[str, Any]] = Field(None)


class BodyDoublingSessionResponse(BaseModel):
    """Schema for body doubling session responses."""

    id: UUID
    host_user_id: UUID
    title: str
    description: Optional[str]
    max_participants: int
    current_participants: int
    session_type: str
    is_public: bool
    requires_approval: bool
    password_protected: bool
    scheduled_start: Optional[datetime]
    scheduled_end: Optional[datetime]
    actual_start: Optional[datetime]
    actual_end: Optional[datetime]
    status: str
    session_settings: Dict[str, Any]
    current_focus_session_id: Optional[UUID]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class SessionParticipantBase(BaseModel):
    """Base schema for session participants."""
    
    share_progress: bool = Field(True, description="Whether to share task progress")
    anonymous_mode: bool = Field(False, description="Whether to participate anonymously")


class SessionParticipantCreate(SessionParticipantBase):
    """Schema for joining a session."""
    
    session_password: Optional[str] = Field(None, description="Session password if required")


class SessionParticipantUpdate(BaseModel):
    """Schema for updating participant settings."""
    
    status: Optional[str] = Field(None, description="Participant status")
    share_progress: Optional[bool] = Field(None)
    anonymous_mode: Optional[bool] = Field(None)
    
    @validator("status")
    def validate_status(cls, v):
        """Validate participant status."""
        if v is not None:
            allowed_statuses = ["active", "away", "focused", "break", "left"]
            if v not in allowed_statuses:
                raise ValueError(f"Status must be one of: {allowed_statuses}")
        return v


class SessionParticipantResponse(SessionParticipantBase):
    """Schema for session participant responses."""
    
    id: UUID
    session_id: UUID
    user_id: UUID
    joined_at: datetime
    left_at: Optional[datetime]
    status: str
    last_activity: datetime
    
    class Config:
        from_attributes = True


class SessionMessageBase(BaseModel):
    """Base schema for session messages."""
    
    message_type: str = Field("chat", description="Message type")
    content: str = Field(..., min_length=1, max_length=1000, description="Message content")
    message_data: Dict[str, Any] = Field(default_factory=dict, description="Additional message data")
    
    @validator("message_type")
    def validate_message_type(cls, v):
        """Validate message type."""
        allowed_types = ["chat", "encouragement", "system", "progress_update"]
        if v not in allowed_types:
            raise ValueError(f"Message type must be one of: {allowed_types}")
        return v


class SessionMessageCreate(SessionMessageBase):
    """Schema for creating session messages."""
    pass


class SessionMessageResponse(SessionMessageBase):
    """Schema for session message responses."""
    
    id: UUID
    session_id: UUID
    sender_id: Optional[UUID]
    created_at: datetime
    
    class Config:
        from_attributes = True


class GroupFocusRequest(BaseModel):
    """Schema for starting a group focus session."""
    
    focus_duration: int = Field(..., ge=5, le=180, description="Focus duration in minutes")
    break_duration: int = Field(5, ge=1, le=30, description="Break duration in minutes")
    session_type: str = Field("pomodoro", description="Focus session type")
    focus_settings: Dict[str, Any] = Field(default_factory=dict, description="Focus session settings")
    
    @validator("session_type")
    def validate_focus_session_type(cls, v):
        """Validate focus session type."""
        allowed_types = ["pomodoro", "deep_work", "sprint", "custom"]
        if v not in allowed_types:
            raise ValueError(f"Focus session type must be one of: {allowed_types}")
        return v


class SessionListResponse(BaseModel):
    """Schema for session list responses."""
    
    sessions: List[BodyDoublingSessionResponse]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class SessionJoinResponse(BaseModel):
    """Schema for session join responses."""
    
    session: BodyDoublingSessionResponse
    participant: SessionParticipantResponse
    websocket_url: str
    session_token: str


class WebSocketMessage(BaseModel):
    """Schema for WebSocket messages."""
    
    type: str = Field(..., description="Message type")
    data: Dict[str, Any] = Field(default_factory=dict, description="Message data")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Message timestamp")
    
    @validator("type")
    def validate_websocket_message_type(cls, v):
        """Validate WebSocket message type."""
        allowed_types = [
            "participant_joined", "participant_left", "participant_status_changed",
            "task_progress", "encouragement", "chat_message", "focus_session_started",
            "focus_session_ended", "session_status_changed", "heartbeat", "error"
        ]
        if v not in allowed_types:
            raise ValueError(f"WebSocket message type must be one of: {allowed_types}")
        return v


class ParticipantStatusUpdate(BaseModel):
    """Schema for participant status updates."""
    
    participant_id: UUID
    status: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    @validator("status")
    def validate_status(cls, v):
        """Validate participant status."""
        allowed_statuses = ["active", "away", "focused", "break", "left"]
        if v not in allowed_statuses:
            raise ValueError(f"Status must be one of: {allowed_statuses}")
        return v


class TaskProgressUpdate(BaseModel):
    """Schema for task progress updates."""
    
    task_id: UUID
    progress_type: str = Field(..., description="Type of progress update")
    progress_data: Dict[str, Any] = Field(default_factory=dict, description="Progress details")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    @validator("progress_type")
    def validate_progress_type(cls, v):
        """Validate progress type."""
        allowed_types = ["started", "completed", "milestone", "stuck", "breakthrough"]
        if v not in allowed_types:
            raise ValueError(f"Progress type must be one of: {allowed_types}")
        return v


class EncouragementMessage(BaseModel):
    """Schema for encouragement messages."""
    
    encouragement_type: str = Field(..., description="Type of encouragement")
    target_participant_id: Optional[UUID] = Field(None, description="Target participant (if specific)")
    message: Optional[str] = Field(None, description="Optional custom message")
    emoji: str = Field("👍", description="Encouragement emoji")
    
    @validator("encouragement_type")
    def validate_encouragement_type(cls, v):
        """Validate encouragement type."""
        allowed_types = ["thumbs_up", "fire", "heart", "star", "clap", "custom"]
        if v not in allowed_types:
            raise ValueError(f"Encouragement type must be one of: {allowed_types}")
        return v


class SessionStatsResponse(BaseModel):
    """Schema for session statistics."""
    
    session_id: UUID
    total_participants: int
    active_participants: int
    session_duration_minutes: int
    total_messages: int
    focus_sessions_completed: int
    average_participant_duration: float
    encouragements_given: int
