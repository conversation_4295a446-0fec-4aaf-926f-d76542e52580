"""
Focus session schemas for Project Chronos.

This module defines Pydantic schemas for ADHD-optimized focus sessions,
Pomodoro timers, and flow state management.
"""

from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict, validator

from app.models.focus_session import FocusSessionType, FocusSessionStatus


class FocusSessionBase(BaseModel):
    """Base schema for focus sessions."""
    
    session_type: FocusSessionType = Field(
        default=FocusSessionType.POMODORO,
        description="Type of focus session"
    )
    title: Optional[str] = Field(None, max_length=200, description="Session title")
    description: Optional[str] = Field(None, description="Session description")
    planned_duration: int = Field(
        ge=1, le=480,
        description="Planned duration in minutes"
    )
    break_duration: int = Field(
        default=5, ge=1, le=60,
        description="Break duration in minutes"
    )
    task_id: Optional[UUID] = Field(None, description="Associated task ID")
    focus_mode_id: Optional[UUID] = Field(None, description="Focus mode to use")


class FocusSessionCreate(FocusSessionBase):
    """Schema for creating a focus session."""
    
    @validator('planned_duration')
    def validate_duration_by_type(cls, v, values):
        session_type = values.get('session_type')
        
        # Validate duration based on session type
        if session_type == FocusSessionType.POMODORO and v != 25:
            # Allow flexibility but suggest standard Pomodoro
            pass
        elif session_type == FocusSessionType.SPRINT and v > 30:
            raise ValueError('Sprint sessions should be 30 minutes or less')
        elif session_type == FocusSessionType.DEEP_WORK and v < 45:
            raise ValueError('Deep work sessions should be at least 45 minutes')
        
        return v


class FocusSessionUpdate(BaseModel):
    """Schema for updating a focus session."""
    
    title: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = None
    planned_duration: Optional[int] = Field(None, ge=1, le=480)
    break_duration: Optional[int] = Field(None, ge=1, le=60)
    completion_notes: Optional[str] = None
    productivity_rating: Optional[int] = Field(None, ge=1, le=5)
    focus_quality_rating: Optional[int] = Field(None, ge=1, le=5)


class FocusSessionResponse(FocusSessionBase):
    """Schema for focus session responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    status: FocusSessionStatus
    started_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None
    paused_at: Optional[datetime] = None
    total_paused_duration: int = 0
    actual_duration: Optional[int] = None
    completion_notes: Optional[str] = None
    productivity_rating: Optional[int] = None
    focus_quality_rating: Optional[int] = None
    hyperfocus_detected: bool = False
    hyperfocus_duration: Optional[int] = None
    break_reminders_sent: int = 0
    elapsed_time: Optional[str] = None  # Human-readable elapsed time
    remaining_time: Optional[str] = None  # Human-readable remaining time
    is_overdue: bool = False
    created_at: datetime
    updated_at: datetime


class FocusSessionStart(BaseModel):
    """Schema for starting a focus session."""
    
    focus_mode_id: Optional[UUID] = Field(None, description="Focus mode to activate")
    enable_notifications: bool = Field(
        default=False,
        description="Enable break reminder notifications"
    )


class FocusSessionPause(BaseModel):
    """Schema for pausing a focus session."""
    
    reason: Optional[str] = Field(None, description="Reason for pausing")


class FocusSessionResume(BaseModel):
    """Schema for resuming a focus session."""
    
    extend_duration: Optional[int] = Field(
        None, ge=0, le=120,
        description="Additional minutes to add to session"
    )


class FocusSessionComplete(BaseModel):
    """Schema for completing a focus session."""
    
    completion_notes: Optional[str] = Field(None, description="Session completion notes")
    productivity_rating: Optional[int] = Field(
        None, ge=1, le=5,
        description="Productivity rating (1-5)"
    )
    focus_quality_rating: Optional[int] = Field(
        None, ge=1, le=5,
        description="Focus quality rating (1-5)"
    )
    achievements_unlocked: List[str] = Field(
        default_factory=list,
        description="Achievements unlocked during session"
    )


class FocusModeBase(BaseModel):
    """Base schema for focus modes."""
    
    name: str = Field(max_length=100, description="Focus mode name")
    description: Optional[str] = Field(None, description="Focus mode description")
    block_notifications: bool = Field(
        default=True,
        description="Block non-essential notifications"
    )
    allowed_contacts: List[str] = Field(
        default_factory=list,
        description="Emergency contacts allowed through"
    )
    default_duration: int = Field(
        default=25, ge=5, le=240,
        description="Default session duration in minutes"
    )
    break_duration: int = Field(
        default=5, ge=1, le=30,
        description="Default break duration in minutes"
    )
    enable_break_reminders: bool = Field(
        default=True,
        description="Enable gentle break reminders"
    )
    break_reminder_interval: int = Field(
        default=25, ge=5, le=120,
        description="Break reminder interval in minutes"
    )
    enable_hyperfocus_protection: bool = Field(
        default=True,
        description="Enable hyperfocus detection and protection"
    )
    hyperfocus_threshold: int = Field(
        default=90, ge=30, le=300,
        description="Hyperfocus detection threshold in minutes"
    )
    settings: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional focus mode settings"
    )


class FocusModeCreate(FocusModeBase):
    """Schema for creating a focus mode."""
    pass


class FocusModeUpdate(BaseModel):
    """Schema for updating a focus mode."""
    
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    block_notifications: Optional[bool] = None
    allowed_contacts: Optional[List[str]] = None
    default_duration: Optional[int] = Field(None, ge=5, le=240)
    break_duration: Optional[int] = Field(None, ge=1, le=30)
    enable_break_reminders: Optional[bool] = None
    break_reminder_interval: Optional[int] = Field(None, ge=5, le=120)
    enable_hyperfocus_protection: Optional[bool] = None
    hyperfocus_threshold: Optional[int] = Field(None, ge=30, le=300)
    settings: Optional[Dict[str, Any]] = None


class FocusModeResponse(FocusModeBase):
    """Schema for focus mode responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    is_default: bool = False
    usage_count: int = 0
    last_used: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


class FocusBreakBase(BaseModel):
    """Base schema for focus breaks."""
    
    planned_duration: int = Field(
        ge=1, le=60,
        description="Planned break duration in minutes"
    )
    break_type: str = Field(
        default="scheduled",
        description="Type of break"
    )
    break_reason: Optional[str] = Field(
        None, max_length=200,
        description="Reason for taking break"
    )
    activities: List[str] = Field(
        default_factory=list,
        description="Activities during break"
    )


class FocusBreakCreate(FocusBreakBase):
    """Schema for creating a focus break."""
    pass


class FocusBreakUpdate(BaseModel):
    """Schema for updating a focus break."""
    
    activities: Optional[List[str]] = None
    effectiveness_rating: Optional[int] = Field(None, ge=1, le=5)


class FocusBreakResponse(FocusBreakBase):
    """Schema for focus break responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    focus_session_id: UUID
    started_at: datetime
    ended_at: Optional[datetime] = None
    actual_duration: Optional[int] = None
    effectiveness_rating: Optional[int] = None
    created_at: datetime
    updated_at: datetime


class FocusSessionStats(BaseModel):
    """Schema for focus session statistics."""
    
    total_sessions: int
    total_focus_time: int  # in minutes
    average_session_duration: float
    completion_rate: float
    most_productive_times: List[str]
    favorite_session_types: List[str]
    hyperfocus_frequency: float
    break_effectiveness: float
    productivity_trends: Dict[str, Any]


class FocusSessionSuggestion(BaseModel):
    """Schema for focus session suggestions."""
    
    suggested_type: FocusSessionType
    suggested_duration: int
    suggested_focus_mode: Optional[UUID] = None
    reasoning: str
    confidence_score: float = Field(ge=0.0, le=1.0)
    optimal_timing: Optional[str] = None


class FocusSessionDashboard(BaseModel):
    """Schema for focus session dashboard."""
    
    current_session: Optional[FocusSessionResponse] = None
    recent_sessions: List[FocusSessionResponse]
    stats: FocusSessionStats
    suggestions: List[FocusSessionSuggestion]
    focus_modes: List[FocusModeResponse]
    upcoming_breaks: List[Dict[str, Any]]


class HyperfocusAlert(BaseModel):
    """Schema for hyperfocus detection alerts."""
    
    session_id: UUID
    duration_minutes: int
    alert_type: str = Field(description="Type of hyperfocus alert")
    message: str = Field(description="Alert message")
    suggested_actions: List[str] = Field(description="Suggested actions")
    severity: str = Field(description="Alert severity level")


class BreakReminder(BaseModel):
    """Schema for break reminder notifications."""
    
    session_id: UUID
    elapsed_minutes: int
    reminder_type: str = Field(description="Type of break reminder")
    message: str = Field(description="Reminder message")
    suggested_break_duration: int = Field(description="Suggested break length")
    break_activities: List[str] = Field(description="Suggested break activities")


class FocusSessionAnalytics(BaseModel):
    """Schema for detailed focus session analytics."""
    
    productivity_by_time: Dict[str, float]
    focus_quality_trends: Dict[str, float]
    session_type_effectiveness: Dict[str, float]
    break_pattern_analysis: Dict[str, Any]
    hyperfocus_patterns: Dict[str, Any]
    recommendations: List[str]
    insights: List[str]
