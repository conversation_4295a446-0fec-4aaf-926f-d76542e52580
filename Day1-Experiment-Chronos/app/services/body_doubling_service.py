"""
Body doubling service for Project Chronos.

This service handles virtual body doubling sessions, participant management,
and real-time coordination for ADHD users who benefit from social accountability.
"""

import logging
from datetime import datetime, timezone
from typing import List, Optional
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload

from app.models.body_doubling import BodyDoublingSession, SessionParticipant, SessionMessage
from app.models.user import User
from app.schemas.body_doubling import (
    BodyDoublingSessionCreate,
    BodyDoublingSessionUpdate,
    SessionParticipantCreate,
    SessionMessageCreate,
    GroupFocusRequest
)
from app.core.security import security

logger = logging.getLogger(__name__)


class BodyDoublingService:
    """
    Service for managing virtual body doubling sessions.
    
    Provides CRUD operations and business logic for body doubling sessions,
    participant management, and session coordination.
    """
    
    async def create_session(
        self,
        db: AsyncSession,
        session_data: BodyDoublingSessionCreate,
        host_user: User
    ) -> BodyDoublingSession:
        """
        Create a new body doubling session.
        
        Args:
            db: Database session
            session_data: Session creation data
            host_user: User creating the session
            
        Returns:
            Created session
        """
        # Hash password if provided
        hashed_password = None
        if session_data.password_protected and session_data.session_password:
            hashed_password = security.hash_password(session_data.session_password)
        
        # Create session
        session = BodyDoublingSession(
            host_user_id=host_user.id,
            title=session_data.title,
            description=session_data.description,
            max_participants=session_data.max_participants,
            session_type=session_data.session_type,
            is_public=session_data.is_public,
            requires_approval=session_data.requires_approval,
            password_protected=session_data.password_protected,
            session_password=hashed_password,
            scheduled_start=session_data.scheduled_start,
            scheduled_end=session_data.scheduled_end,
            session_settings=session_data.session_settings,
            current_participants=1  # Host is automatically a participant
        )

        db.add(session)
        await db.flush()  # Flush to get the session ID

        # Add host as first participant in the same transaction
        participant = SessionParticipant(
            session_id=session.id,
            user_id=host_user.id,
            share_progress=True,
            anonymous_mode=False
        )

        db.add(participant)

        await db.commit()
        await db.refresh(session)

        logger.info(f"Created body doubling session {session.id} by user {host_user.id}")
        return session
    
    async def get_session_by_id(
        self,
        db: AsyncSession,
        session_id: UUID,
        include_participants: bool = False,
        include_messages: bool = False
    ) -> Optional[BodyDoublingSession]:
        """
        Get a session by ID with optional related data.
        
        Args:
            db: Database session
            session_id: Session ID
            include_participants: Whether to include participants
            include_messages: Whether to include messages
            
        Returns:
            Session if found, None otherwise
        """
        query = select(BodyDoublingSession).where(BodyDoublingSession.id == session_id)
        
        if include_participants:
            query = query.options(selectinload(BodyDoublingSession.participants))
        
        if include_messages:
            query = query.options(selectinload(BodyDoublingSession.messages))
        
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def list_public_sessions(
        self,
        db: AsyncSession,
        skip: int = 0,
        limit: int = 20,
        session_type: Optional[str] = None,
        status: Optional[str] = None
    ) -> List[BodyDoublingSession]:
        """
        List public sessions with filtering.
        
        Args:
            db: Database session
            skip: Number of sessions to skip
            limit: Maximum number of sessions to return
            session_type: Optional session type filter
            status: Optional status filter
            
        Returns:
            List of public sessions
        """
        query = select(BodyDoublingSession).where(
            BodyDoublingSession.is_public == True
        )
        
        if session_type:
            query = query.where(BodyDoublingSession.session_type == session_type)
        
        if status:
            query = query.where(BodyDoublingSession.status == status)
        else:
            # Default to active sessions
            query = query.where(BodyDoublingSession.status.in_(["waiting", "active"]))
        
        query = query.offset(skip).limit(limit).order_by(BodyDoublingSession.created_at.desc())
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def update_session(
        self,
        db: AsyncSession,
        session_id: UUID,
        session_data: BodyDoublingSessionUpdate,
        user: User
    ) -> Optional[BodyDoublingSession]:
        """
        Update a session (host only).
        
        Args:
            db: Database session
            session_id: Session ID
            session_data: Update data
            user: User making the update
            
        Returns:
            Updated session if successful, None otherwise
        """
        session = await self.get_session_by_id(db, session_id)
        if not session or session.host_user_id != user.id:
            return None
        
        # Update fields
        update_data = session_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(session, field, value)
        
        await db.commit()
        await db.refresh(session)
        
        logger.info(f"Updated session {session_id} by user {user.id}")
        return session
    
    async def start_session(
        self,
        db: AsyncSession,
        session_id: UUID,
        user: User
    ) -> Optional[BodyDoublingSession]:
        """
        Start a session (host only).
        
        Args:
            db: Database session
            session_id: Session ID
            user: User starting the session
            
        Returns:
            Started session if successful, None otherwise
        """
        session = await self.get_session_by_id(db, session_id)
        if not session or session.host_user_id != user.id:
            return None
        
        if session.status != "waiting":
            return None
        
        session.status = "active"
        session.actual_start = datetime.now(timezone.utc)
        
        await db.commit()
        await db.refresh(session)
        
        logger.info(f"Started session {session_id} by user {user.id}")
        return session
    
    async def end_session(
        self,
        db: AsyncSession,
        session_id: UUID,
        user: User
    ) -> Optional[BodyDoublingSession]:
        """
        End a session (host only).
        
        Args:
            db: Database session
            session_id: Session ID
            user: User ending the session
            
        Returns:
            Ended session if successful, None otherwise
        """
        session = await self.get_session_by_id(db, session_id)
        if not session or session.host_user_id != user.id:
            return None
        
        if session.status not in ["waiting", "active", "paused"]:
            return None
        
        session.status = "completed"
        session.actual_end = datetime.now(timezone.utc)
        
        await db.commit()
        await db.refresh(session)
        
        logger.info(f"Ended session {session_id} by user {user.id}")
        return session
    
    async def add_participant(
        self,
        db: AsyncSession,
        session_id: UUID,
        user: User,
        participant_data: SessionParticipantCreate
    ) -> Optional[SessionParticipant]:
        """
        Add a participant to a session.
        
        Args:
            db: Database session
            session_id: Session ID
            user: User joining the session
            participant_data: Participant data
            
        Returns:
            Created participant if successful, None otherwise
        """
        session = await self.get_session_by_id(db, session_id)
        if not session or not session.can_join():
            return None
        
        # Check if user is already a participant
        existing = await db.execute(
            select(SessionParticipant).where(
                and_(
                    SessionParticipant.session_id == session_id,
                    SessionParticipant.user_id == user.id,
                    SessionParticipant.status != "left"
                )
            )
        )
        if existing.scalar_one_or_none():
            return None  # Already participating
        
        # Verify password if required
        if session.password_protected and participant_data.session_password:
            if not security.verify_password(
                participant_data.session_password,
                session.session_password
            ):
                return None  # Wrong password
        
        # Create participant
        participant = SessionParticipant(
            session_id=session_id,
            user_id=user.id,
            share_progress=participant_data.share_progress,
            anonymous_mode=participant_data.anonymous_mode
        )
        
        db.add(participant)
        
        # Update session participant count
        session.current_participants += 1
        
        await db.commit()
        await db.refresh(participant)
        
        logger.info(f"User {user.id} joined session {session_id}")
        return participant
    
    async def remove_participant(
        self,
        db: AsyncSession,
        session_id: UUID,
        user: User
    ) -> bool:
        """
        Remove a participant from a session.
        
        Args:
            db: Database session
            session_id: Session ID
            user: User leaving the session
            
        Returns:
            True if successful, False otherwise
        """
        participant = await db.execute(
            select(SessionParticipant).where(
                and_(
                    SessionParticipant.session_id == session_id,
                    SessionParticipant.user_id == user.id,
                    SessionParticipant.status != "left"
                )
            )
        )
        participant = participant.scalar_one_or_none()
        
        if not participant:
            return False
        
        # Mark as left
        participant.status = "left"
        participant.left_at = datetime.now(timezone.utc)
        
        # Update session participant count
        session = await self.get_session_by_id(db, session_id)
        if session:
            session.current_participants = max(0, session.current_participants - 1)
        
        await db.commit()
        
        logger.info(f"User {user.id} left session {session_id}")
        return True
