"""
Achievement service for Project Chronos.

This module provides achievement tracking, rule evaluation, and unlocking
for ADHD-optimized gamification features.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from uuid import UUID

from sqlalchemy import select, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.gamification import Achievement, UserAchievement, UserGamification
from app.models.task import Task
from app.schemas.gamification import AchievementCreate, AchievementUnlockResponse
from app.core.exceptions import AchievementError

logger = logging.getLogger(__name__)


class AchievementRule:
    """Achievement rule definition for evaluation."""
    
    def __init__(
        self,
        achievement_key: str,
        name: str,
        description: str,
        category: str,
        trigger_event: str,
        condition: Callable[[UUID, Dict[str, Any]], bool],
        reward_points: int,
        badge_icon: str,
        is_hidden: bool = False
    ):
        self.achievement_key = achievement_key
        self.name = name
        self.description = description
        self.category = category
        self.trigger_event = trigger_event
        self.condition = condition
        self.reward_points = reward_points
        self.badge_icon = badge_icon
        self.is_hidden = is_hidden


class AchievementService:
    """Service for managing achievements and unlockables."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.achievement_rules = self._load_achievement_rules()
    
    async def initialize_achievements(self) -> None:
        """Initialize achievement definitions in the database."""
        for rule in self.achievement_rules:
            # Check if achievement already exists
            result = await self.db.execute(
                select(Achievement).where(Achievement.achievement_key == rule.achievement_key)
            )
            existing = result.scalar_one_or_none()
            
            if not existing:
                achievement = Achievement(
                    achievement_key=rule.achievement_key,
                    name=rule.name,
                    description=rule.description,
                    category=rule.category,
                    trigger_event=rule.trigger_event,
                    requirement_data={},  # Store rule data as needed
                    reward_points=rule.reward_points,
                    badge_icon=rule.badge_icon,
                    is_hidden=rule.is_hidden,
                    is_active=True
                )
                self.db.add(achievement)
        
        await self.db.commit()
        logger.info(f"Initialized {len(self.achievement_rules)} achievement definitions")
    
    async def check_achievements(
        self,
        user_id: UUID,
        trigger_event: str,
        event_data: Dict[str, Any]
    ) -> List[AchievementUnlockResponse]:
        """
        Check if user has unlocked new achievements.
        
        Args:
            user_id: User to check achievements for
            trigger_event: Event that triggered the check
            event_data: Data associated with the event
            
        Returns:
            List of newly unlocked achievements
        """
        newly_unlocked = []
        
        # Get user's existing achievements
        user_achievements_result = await self.db.execute(
            select(UserAchievement.achievement_id).where(
                and_(
                    UserAchievement.user_id == user_id,
                    UserAchievement.is_unlocked == True
                )
            )
        )
        unlocked_achievement_ids = {row[0] for row in user_achievements_result.fetchall()}
        
        # Get achievements for this trigger event
        achievements_result = await self.db.execute(
            select(Achievement).where(
                and_(
                    Achievement.trigger_event == trigger_event,
                    Achievement.is_active == True
                )
            )
        )
        achievements = achievements_result.scalars().all()
        
        for achievement in achievements:
            if achievement.id in unlocked_achievement_ids:
                continue  # Already unlocked
            
            # Find matching rule
            rule = next(
                (r for r in self.achievement_rules if r.achievement_key == achievement.achievement_key),
                None
            )
            
            if rule and await self._evaluate_achievement_rule(user_id, rule, event_data):
                unlock_response = await self._unlock_achievement(user_id, achievement, rule)
                newly_unlocked.append(unlock_response)
        
        return newly_unlocked
    
    async def get_user_achievements(
        self,
        user_id: UUID,
        unlocked_only: bool = False
    ) -> List[UserAchievement]:
        """
        Get user's achievements with progress.
        
        Args:
            user_id: User ID
            unlocked_only: Whether to return only unlocked achievements
            
        Returns:
            List of user achievements
        """
        query = select(UserAchievement).options(
            selectinload(UserAchievement.achievement)
        ).where(UserAchievement.user_id == user_id)
        
        if unlocked_only:
            query = query.where(UserAchievement.is_unlocked == True)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_achievement_progress(
        self,
        user_id: UUID,
        achievement_key: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get user's progress toward a specific achievement.
        
        Args:
            user_id: User ID
            achievement_key: Achievement key to check
            
        Returns:
            Progress data or None if not found
        """
        result = await self.db.execute(
            select(UserAchievement)
            .join(Achievement)
            .where(
                and_(
                    UserAchievement.user_id == user_id,
                    Achievement.achievement_key == achievement_key
                )
            )
        )
        user_achievement = result.scalar_one_or_none()
        
        if not user_achievement:
            return None
        
        return {
            "is_unlocked": user_achievement.is_unlocked,
            "unlocked_at": user_achievement.unlocked_at,
            "progress_data": user_achievement.progress_data
        }
    
    async def _evaluate_achievement_rule(
        self,
        user_id: UUID,
        rule: AchievementRule,
        event_data: Dict[str, Any]
    ) -> bool:
        """
        Evaluate if an achievement rule condition is met.
        
        Args:
            user_id: User ID
            rule: Achievement rule to evaluate
            event_data: Event data for evaluation
            
        Returns:
            Whether the rule condition is met
        """
        try:
            # Get additional data needed for evaluation
            evaluation_data = await self._get_evaluation_data(user_id, rule.achievement_key)
            evaluation_data.update(event_data)
            
            return rule.condition(user_id, evaluation_data)
        except Exception as e:
            logger.error(f"Error evaluating achievement rule {rule.achievement_key}: {e}")
            return False
    
    async def _get_evaluation_data(self, user_id: UUID, achievement_key: str) -> Dict[str, Any]:
        """Get data needed for achievement evaluation."""
        data = {}
        
        # Get task completion count
        task_count_result = await self.db.execute(
            select(func.count(Task.id)).where(
                and_(
                    Task.user_id == user_id,
                    Task.status == "completed"
                )
            )
        )
        data["task_count"] = task_count_result.scalar() or 0
        
        # Add more evaluation data as needed for different achievements
        
        return data
    
    async def _unlock_achievement(
        self,
        user_id: UUID,
        achievement: Achievement,
        rule: AchievementRule
    ) -> AchievementUnlockResponse:
        """
        Unlock an achievement for a user.
        
        Args:
            user_id: User ID
            achievement: Achievement to unlock
            rule: Achievement rule
            
        Returns:
            Achievement unlock response
        """
        # Get or create user gamification profile
        from app.services.gamification_service import GamificationService
        gamification_service = GamificationService(self.db)
        user_gamification = await gamification_service.get_or_create_user_gamification(user_id)
        
        # Check if user achievement record exists
        result = await self.db.execute(
            select(UserAchievement).where(
                and_(
                    UserAchievement.user_id == user_id,
                    UserAchievement.achievement_id == achievement.id
                )
            )
        )
        user_achievement = result.scalar_one_or_none()
        
        if not user_achievement:
            user_achievement = UserAchievement(
                user_id=user_id,
                user_gamification_id=user_gamification.id,
                achievement_id=achievement.id,
                is_unlocked=False,
                progress_data={}
            )
            self.db.add(user_achievement)
        
        # Unlock the achievement
        user_achievement.is_unlocked = True
        user_achievement.unlocked_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(user_achievement)
        
        # Award points for the achievement
        if rule.reward_points > 0:
            await gamification_service.award_points(
                user_id=user_id,
                points=rule.reward_points,
                reason=f"Achievement unlocked: {achievement.name}",
                metadata={"achievement_key": achievement.achievement_key}
            )
        
        logger.info(f"User {user_id} unlocked achievement: {achievement.achievement_key}")
        
        return AchievementUnlockResponse(
            achievement=achievement,  # Will be converted to response schema
            points_awarded=rule.reward_points,
            celebration_message=f"🏆 Achievement Unlocked: {achievement.name}!",
            is_first_unlock=True,  # TODO: Check if this is user's first achievement
            unlock_timestamp=user_achievement.unlocked_at
        )
    
    def _load_achievement_rules(self) -> List[AchievementRule]:
        """Load achievement rules with ADHD-friendly milestones."""
        return [
            # Task completion achievements
            AchievementRule(
                achievement_key="first_task",
                name="Getting Started",
                description="Complete your first task",
                category="task_completion",
                trigger_event="task_completed",
                condition=lambda user_id, data: data.get("task_count", 0) >= 1,
                reward_points=50,
                badge_icon="🎯"
            ),
            AchievementRule(
                achievement_key="task_streak_3",
                name="Building Momentum",
                description="Complete tasks for 3 days in a row",
                category="consistency",
                trigger_event="streak_milestone",
                condition=lambda user_id, data: data.get("streak_length", 0) >= 3,
                reward_points=100,
                badge_icon="🔥"
            ),
            AchievementRule(
                achievement_key="task_streak_7",
                name="Week Warrior",
                description="Complete tasks for 7 days in a row",
                category="consistency",
                trigger_event="streak_milestone",
                condition=lambda user_id, data: data.get("streak_length", 0) >= 7,
                reward_points=200,
                badge_icon="🔥"
            ),
            AchievementRule(
                achievement_key="task_streak_30",
                name="Consistency Champion",
                description="Complete tasks for 30 days in a row",
                category="consistency",
                trigger_event="streak_milestone",
                condition=lambda user_id, data: data.get("streak_length", 0) >= 30,
                reward_points=500,
                badge_icon="👑"
            ),
            AchievementRule(
                achievement_key="tasks_10",
                name="Task Tackler",
                description="Complete 10 tasks",
                category="task_completion",
                trigger_event="task_completed",
                condition=lambda user_id, data: data.get("task_count", 0) >= 10,
                reward_points=150,
                badge_icon="✅"
            ),
            AchievementRule(
                achievement_key="tasks_50",
                name="Productivity Pro",
                description="Complete 50 tasks",
                category="task_completion",
                trigger_event="task_completed",
                condition=lambda user_id, data: data.get("task_count", 0) >= 50,
                reward_points=300,
                badge_icon="⭐"
            ),
            AchievementRule(
                achievement_key="tasks_100",
                name="Task Master",
                description="Complete 100 tasks",
                category="task_completion",
                trigger_event="task_completed",
                condition=lambda user_id, data: data.get("task_count", 0) >= 100,
                reward_points=500,
                badge_icon="🏆"
            ),
            # Focus session achievements (placeholder for future integration)
            AchievementRule(
                achievement_key="focus_first",
                name="Focus Beginner",
                description="Complete your first focus session",
                category="focus_time",
                trigger_event="focus_completed",
                condition=lambda user_id, data: data.get("focus_sessions", 0) >= 1,
                reward_points=75,
                badge_icon="🧘"
            ),
            AchievementRule(
                achievement_key="focus_master",
                name="Focus Master",
                description="Complete 25 focus sessions",
                category="focus_time",
                trigger_event="focus_completed",
                condition=lambda user_id, data: data.get("focus_sessions", 0) >= 25,
                reward_points=300,
                badge_icon="🧘‍♂️"
            ),
            # Motivation achievements
            AchievementRule(
                achievement_key="dopamine_explorer",
                name="Dopamine Explorer",
                description="Try 5 different dopamine activities",
                category="motivation",
                trigger_event="dopamine_activity_completed",
                condition=lambda user_id, data: data.get("unique_activities", 0) >= 5,
                reward_points=100,
                badge_icon="🌟"
            ),
        ]
