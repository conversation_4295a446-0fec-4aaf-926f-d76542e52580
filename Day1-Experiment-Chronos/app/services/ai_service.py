"""
AI-powered task chunking service for Project Chronos.

This module provides intelligent task breakdown using OpenAI and Anthropic APIs,
specifically optimized for ADHD users to combat task paralysis.
"""

import json
import logging
from typing import List, Dict, Any, Optional
from openai import AsyncOpenAI
from anthropic import AsyncAnthropic
try:
    import redis.asyncio as redis
except ImportError:
    import redis
from app.core.config import settings
from app.core.exceptions import AIServiceException, AIServiceUnavailableError, AIChunkingError


logger = logging.getLogger(__name__)


class AIChunkingService:
    """
    AI-powered task chunking service for ADHD users.
    
    Breaks down overwhelming tasks into manageable, actionable subtasks
    using advanced AI models with ADHD-specific prompting.
    """
    
    def __init__(self):
        """Initialize AI clients and cache."""
        self.openai_client = None
        self.anthropic_client = None
        self.cache = None
        
        # Initialize OpenAI if API key is available
        if settings.openai_api_key:
            self.openai_client = AsyncOpenAI(api_key=settings.openai_api_key)
        
        # Initialize Anthropic if API key is available
        if settings.anthropic_api_key:
            self.anthropic_client = AsyncAnthropic(api_key=settings.anthropic_api_key)
        
        # Initialize Redis cache
        try:
            self.cache = redis.from_url(settings.redis_url)
        except Exception as e:
            logger.warning(f"Failed to initialize Redis cache: {e}")
    
    async def chunk_task(
        self,
        title: str,
        description: Optional[str] = None,
        chunk_size: str = "small",
        context: Optional[str] = None,
        user_preferences: Optional[Dict] = None
    ) -> List[Dict[str, Any]]:
        """
        Break down a large task into smaller, actionable subtasks.
        
        Args:
            title: Main task title
            description: Detailed task description
            chunk_size: Preferred chunk size (small/medium/large)
            context: Additional context for better chunking
            user_preferences: User's ADHD-specific preferences
            
        Returns:
            List of subtask dictionaries with titles, descriptions, and metadata
            
        Raises:
            AIServiceUnavailableError: If no AI service is available
            AIChunkingError: If chunking fails
        """
        # Check cache first
        cache_key = self._generate_cache_key(title, description, chunk_size, context)
        cached_result = await self._get_cached_result(cache_key)
        if cached_result:
            logger.info(f"Returning cached chunking result for task: {title}")
            return cached_result
        
        # Build prompt
        prompt = self._build_chunking_prompt(
            title, description, chunk_size, context, user_preferences
        )
        
        # Try AI services in order of preference
        result = None
        errors = []
        
        # Try OpenAI first
        if self.openai_client:
            try:
                result = await self._generate_chunks_openai(prompt, chunk_size)
                logger.info(f"Successfully chunked task using OpenAI: {title}")
            except Exception as e:
                errors.append(f"OpenAI: {str(e)}")
                logger.warning(f"OpenAI chunking failed: {e}")
        
        # Try Anthropic as fallback
        if not result and self.anthropic_client:
            try:
                result = await self._generate_chunks_anthropic(prompt, chunk_size)
                logger.info(f"Successfully chunked task using Anthropic: {title}")
            except Exception as e:
                errors.append(f"Anthropic: {str(e)}")
                logger.warning(f"Anthropic chunking failed: {e}")
        
        # If no AI service worked, raise error
        if not result:
            if not self.openai_client and not self.anthropic_client:
                raise AIServiceUnavailableError(
                    "AI Chunking", "No AI API keys configured"
                )
            else:
                raise AIChunkingError(
                    title, f"All AI services failed: {'; '.join(errors)}"
                )
        
        # Cache the result
        await self._cache_result(cache_key, result)
        
        return result
    
    async def _generate_chunks_openai(self, prompt: str, chunk_size: str) -> List[Dict]:
        """Generate task chunks using OpenAI GPT-4."""
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an ADHD-specialized task management assistant. "
                                 "Break down tasks into clear, actionable steps that reduce "
                                 "overwhelm and decision fatigue."
                    },
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,  # Lower temperature for more consistent results
                max_tokens=1000,
                response_format={"type": "json_object"}
            )
            
            content = response.choices[0].message.content
            parsed_response = json.loads(content)
            
            return self._validate_and_format_chunks(parsed_response.get("subtasks", []))
            
        except Exception as e:
            raise AIServiceException(f"OpenAI API error: {str(e)}")
    
    async def _generate_chunks_anthropic(self, prompt: str, chunk_size: str) -> List[Dict]:
        """Generate task chunks using Anthropic Claude."""
        try:
            response = await self.anthropic_client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=1000,
                temperature=0.3,
                messages=[
                    {
                        "role": "user",
                        "content": f"{prompt}\n\nPlease respond with valid JSON only."
                    }
                ]
            )
            
            content = response.content[0].text
            # Extract JSON from response
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            json_content = content[start_idx:end_idx]
            
            parsed_response = json.loads(json_content)
            
            return self._validate_and_format_chunks(parsed_response.get("subtasks", []))
            
        except Exception as e:
            raise AIServiceException(f"Anthropic API error: {str(e)}")
    
    def _build_chunking_prompt(
        self,
        title: str,
        description: Optional[str],
        chunk_size: str,
        context: Optional[str],
        user_preferences: Optional[Dict]
    ) -> str:
        """Build context-aware prompt for AI chunking."""
        
        # Base prompt templates for different chunk sizes
        chunk_instructions = {
            "small": """
            Break down this task into very small, specific actions that take 5-15 minutes each.
            Each subtask should be a single, concrete action that someone with ADHD can start immediately.
            Focus on removing ambiguity and decision-making from each step.
            Aim for 3-7 subtasks.
            """,
            "medium": """
            Break down this task into medium-sized chunks that take 15-45 minutes each.
            Each subtask should represent a meaningful unit of work while remaining manageable.
            Consider the cognitive load and provide clear success criteria.
            Aim for 2-5 subtasks.
            """,
            "large": """
            Break down this task into larger phases that take 45-90 minutes each.
            Each subtask should represent a significant milestone while remaining achievable.
            Focus on logical groupings and natural break points.
            Aim for 2-4 subtasks.
            """
        }
        
        prompt = f"""
        {chunk_instructions.get(chunk_size, chunk_instructions["small"])}
        
        Task: {title}
        Description: {description or "No additional description provided"}
        """
        
        if context:
            prompt += f"\nContext: {context}"
        
        if user_preferences:
            prompt += f"\nUser Preferences: {json.dumps(user_preferences)}"
        
        prompt += """
        
        Return your response as JSON in this exact format:
        {
            "subtasks": [
                {
                    "title": "Specific action title",
                    "description": "Clear, unambiguous description",
                    "estimated_duration": 10,
                    "energy_level": "low",
                    "context_tags": ["relevant", "tags"]
                }
            ]
        }
        
        Guidelines for ADHD users:
        - Use action verbs to start each title
        - Be specific about what "done" looks like
        - Avoid vague terms like "research" or "plan"
        - Include any tools or resources needed
        - Consider energy levels realistically
        """
        
        return prompt
    
    def _validate_and_format_chunks(self, chunks: List[Dict]) -> List[Dict]:
        """Validate and format AI-generated chunks."""
        validated_chunks = []
        
        for chunk in chunks:
            # Ensure required fields
            if not chunk.get("title"):
                continue
            
            formatted_chunk = {
                "title": chunk.get("title", "").strip(),
                "description": chunk.get("description", "").strip(),
                "estimated_duration": max(5, min(480, chunk.get("estimated_duration", 15))),
                "energy_level": chunk.get("energy_level", "medium"),
                "context_tags": chunk.get("context_tags", [])
            }
            
            # Validate energy level
            if formatted_chunk["energy_level"] not in ["low", "medium", "high"]:
                formatted_chunk["energy_level"] = "medium"
            
            # Clean context tags
            if isinstance(formatted_chunk["context_tags"], list):
                formatted_chunk["context_tags"] = [
                    tag.strip().lower() for tag in formatted_chunk["context_tags"]
                    if isinstance(tag, str) and tag.strip()
                ][:5]  # Limit to 5 tags
            else:
                formatted_chunk["context_tags"] = []
            
            validated_chunks.append(formatted_chunk)
        
        return validated_chunks
    
    def _generate_cache_key(
        self,
        title: str,
        description: Optional[str],
        chunk_size: str,
        context: Optional[str]
    ) -> str:
        """Generate cache key for chunking request."""
        import hashlib
        
        content = f"{title}|{description or ''}|{chunk_size}|{context or ''}"
        return f"chunk:{hashlib.md5(content.encode()).hexdigest()}"
    
    async def _get_cached_result(self, cache_key: str) -> Optional[List[Dict]]:
        """Get cached chunking result."""
        if not self.cache:
            return None
        
        try:
            cached = await self.cache.get(cache_key)
            if cached:
                return json.loads(cached)
        except Exception as e:
            logger.warning(f"Cache retrieval failed: {e}")
        
        return None
    
    async def _cache_result(self, cache_key: str, result: List[Dict]) -> None:
        """Cache chunking result."""
        if not self.cache:
            return
        
        try:
            await self.cache.setex(
                cache_key,
                settings.ai_cache_ttl,
                json.dumps(result)
            )
        except Exception as e:
            logger.warning(f"Cache storage failed: {e}")
