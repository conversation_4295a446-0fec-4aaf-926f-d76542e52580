"""
Time blocking service for Project Chronos.

This module provides ADHD-optimized time blocking and scheduling services
including visual time management and intelligent scheduling algorithms.
"""

import logging
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import select, and_, func, desc, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.time_block import (
    TimeBlock,
    ScheduleTemplate,
    SchedulingPreference,
    TimeBlockType,
    TimeBlockStatus,
)
from app.models.user import User
from app.models.task import Task
from app.schemas.time_block import (
    TimeBlockCreate,
    TimeBlockUpdate,
    TimeBlockMove,
    ScheduleConflict,
    ScheduleValidationResponse,
    TimeSlotSuggestion,
    CircularCalendarView,
    TimelineView,
)
from app.core.exceptions import (
    ValidationError,
    UserNotFoundError,
)

logger = logging.getLogger(__name__)


class TimeBlockingService:
    """Service for managing ADHD-optimized time blocks and scheduling."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_time_block(
        self,
        user_id: UUID,
        block_data: TimeBlockCreate
    ) -> TimeBlock:
        """
        Create a new time block with ADHD optimizations.
        
        Args:
            user_id: User creating the time block
            block_data: Time block configuration data
            
        Returns:
            TimeBlock: Created time block
            
        Raises:
            UserNotFoundError: If user doesn't exist
            ValidationError: If block data is invalid or conflicts exist
        """
        # Verify user exists
        user_result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = user_result.scalar_one_or_none()
        if not user:
            raise UserNotFoundError(f"User {user_id} not found")
        
        # Validate task association if provided
        if block_data.task_id:
            task_result = await self.db.execute(
                select(Task).where(
                    and_(Task.id == block_data.task_id, Task.user_id == user_id)
                )
            )
            task = task_result.scalar_one_or_none()
            if not task:
                raise ValidationError("Task not found or not owned by user")
        
        # Calculate duration
        duration_minutes = int((block_data.end_time - block_data.start_time).total_seconds() / 60)
        
        # Check for conflicts
        conflicts = await self._detect_conflicts(
            user_id, block_data.start_time, block_data.end_time,
            block_data.buffer_before, block_data.buffer_after
        )
        
        if conflicts:
            conflict_messages = [c.message for c in conflicts]
            raise ValidationError(f"Scheduling conflicts detected: {'; '.join(conflict_messages)}")
        
        # Apply automatic buffer times if user has preferences
        buffer_before = block_data.buffer_before
        buffer_after = block_data.buffer_after
        
        if buffer_before == 0 or buffer_after == 0:
            prefs = await self._get_user_preferences(user_id)
            if prefs:
                if buffer_before == 0:
                    if block_data.block_type == TimeBlockType.MEETING:
                        buffer_before = prefs.meeting_buffer_before
                    else:
                        buffer_before = prefs.default_buffer_before
                
                if buffer_after == 0:
                    if block_data.block_type == TimeBlockType.MEETING:
                        buffer_after = prefs.meeting_buffer_after
                    else:
                        buffer_after = prefs.default_buffer_after
        
        # Create time block
        time_block = TimeBlock(
            user_id=user_id,
            task_id=block_data.task_id,
            title=block_data.title,
            description=block_data.description,
            block_type=block_data.block_type,
            start_time=block_data.start_time,
            end_time=block_data.end_time,
            duration_minutes=duration_minutes,
            is_flexible=block_data.is_flexible,
            buffer_before=buffer_before,
            buffer_after=buffer_after,
            energy_level_required=block_data.energy_level_required,
            color=block_data.color,
            category=block_data.category,
            status=TimeBlockStatus.PLANNED,
            block_data={
                "created_with_adhd_accommodations": True,
                "auto_buffer_applied": buffer_before > block_data.buffer_before or buffer_after > block_data.buffer_after
            }
        )
        
        self.db.add(time_block)
        await self.db.commit()
        await self.db.refresh(time_block)
        
        logger.info(f"Created time block {time_block.id} for user {user_id}")
        return time_block
    
    async def get_daily_schedule(
        self,
        user_id: UUID,
        date: datetime,
        include_buffers: bool = True,
        include_completed: bool = True
    ) -> List[TimeBlock]:
        """
        Get complete daily schedule with time blocks.
        
        Args:
            user_id: User to get schedule for
            date: Date for the schedule
            include_buffers: Whether to include buffer time blocks
            include_completed: Whether to include completed blocks
            
        Returns:
            List[TimeBlock]: Ordered list of time blocks for the day
        """
        # Calculate day boundaries
        day_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
        day_end = day_start + timedelta(days=1)
        
        # Build query
        query = select(TimeBlock).where(
            and_(
                TimeBlock.user_id == user_id,
                TimeBlock.start_time >= day_start,
                TimeBlock.start_time < day_end
            )
        )
        
        # Filter by status if needed
        if not include_completed:
            query = query.where(TimeBlock.status != TimeBlockStatus.COMPLETED)
        
        # Filter buffer blocks if needed
        if not include_buffers:
            query = query.where(TimeBlock.block_type != TimeBlockType.BUFFER)
        
        # Order by start time
        query = query.order_by(TimeBlock.start_time)
        
        result = await self.db.execute(query)
        time_blocks = result.scalars().all()
        
        return list(time_blocks)
    
    async def validate_schedule(
        self,
        user_id: UUID,
        date: datetime
    ) -> ScheduleValidationResponse:
        """
        Validate daily schedule for over-scheduling and conflicts.
        
        Args:
            user_id: User to validate schedule for
            date: Date to validate
            
        Returns:
            ScheduleValidationResponse: Validation results with warnings and suggestions
        """
        time_blocks = await self.get_daily_schedule(user_id, date)
        
        # Calculate total scheduled time
        total_scheduled = sum(block.duration_minutes for block in time_blocks)
        
        # Get user preferences for available time
        prefs = await self._get_user_preferences(user_id)
        available_time = 16 * 60  # Default 16 hours available
        
        if prefs and prefs.work_start_time and prefs.work_end_time:
            start_hour, start_min = map(int, prefs.work_start_time.split(':'))
            end_hour, end_min = map(int, prefs.work_end_time.split(':'))
            available_time = (end_hour * 60 + end_min) - (start_hour * 60 + start_min)
        
        # Detect conflicts
        conflicts = []
        for i, block1 in enumerate(time_blocks):
            for block2 in time_blocks[i+1:]:
                if block1.overlaps_with(block2):
                    conflicts.append(ScheduleConflict(
                        conflict_type="overlap",
                        severity="high",
                        message=f"'{block1.title}' overlaps with '{block2.title}'",
                        affected_blocks=[block1.id, block2.id],
                        suggestions=[
                            "Move one of the blocks to a different time",
                            "Reduce the duration of one block",
                            "Add buffer time between blocks"
                        ]
                    ))
        
        # Check for over-scheduling
        over_scheduled_by = max(0, total_scheduled - available_time)
        
        # Generate warnings
        warnings = []
        if over_scheduled_by > 0:
            warnings.append(f"Schedule is over-booked by {over_scheduled_by} minutes")
        
        if len(time_blocks) > 10:
            warnings.append("High number of time blocks may cause decision fatigue")
        
        # Generate suggestions
        suggestions = []
        if over_scheduled_by > 0:
            suggestions.append("Consider moving some tasks to another day")
            suggestions.append("Break large tasks into smaller chunks")
        
        if not any(block.block_type == TimeBlockType.BREAK for block in time_blocks):
            suggestions.append("Consider adding break time blocks for better focus")
        
        return ScheduleValidationResponse(
            is_valid=len(conflicts) == 0 and over_scheduled_by == 0,
            total_scheduled_time=total_scheduled,
            available_time=available_time,
            over_scheduled_by=over_scheduled_by,
            conflicts=conflicts,
            warnings=warnings,
            suggestions=suggestions
        )
    
    async def move_time_block(
        self,
        block_id: UUID,
        user_id: UUID,
        move_data: TimeBlockMove
    ) -> TimeBlock:
        """
        Move a time block to a new time slot with conflict checking.
        
        Args:
            block_id: ID of block to move
            user_id: User moving the block
            move_data: New timing information
            
        Returns:
            TimeBlock: Updated time block
            
        Raises:
            ValidationError: If move would cause conflicts
        """
        # Get the time block
        block_result = await self.db.execute(
            select(TimeBlock).where(
                and_(TimeBlock.id == block_id, TimeBlock.user_id == user_id)
            )
        )
        time_block = block_result.scalar_one_or_none()
        
        if not time_block:
            raise ValidationError("Time block not found")
        
        # Calculate new end time
        if move_data.maintain_duration:
            duration = time_block.duration_minutes
        else:
            duration = move_data.new_duration or time_block.duration_minutes
        
        new_end_time = move_data.new_start_time + timedelta(minutes=duration)
        
        # Check for conflicts (excluding the current block)
        conflicts = await self._detect_conflicts(
            user_id, move_data.new_start_time, new_end_time,
            time_block.buffer_before, time_block.buffer_after,
            exclude_block_id=block_id
        )
        
        if conflicts:
            conflict_messages = [c.message for c in conflicts]
            raise ValidationError(f"Move would cause conflicts: {'; '.join(conflict_messages)}")
        
        # Update the time block
        time_block.start_time = move_data.new_start_time
        time_block.end_time = new_end_time
        time_block.duration_minutes = duration
        
        await self.db.commit()
        await self.db.refresh(time_block)
        
        logger.info(f"Moved time block {block_id} to {move_data.new_start_time}")
        return time_block
    
    async def suggest_optimal_times(
        self,
        user_id: UUID,
        task: Task,
        date: datetime,
        duration_minutes: int
    ) -> List[TimeSlotSuggestion]:
        """
        Suggest optimal time slots for a task based on user patterns.
        
        Args:
            user_id: User to suggest times for
            task: Task to schedule
            date: Date to find slots for
            duration_minutes: Required duration
            
        Returns:
            List[TimeSlotSuggestion]: Ordered list of time slot suggestions
        """
        # Get existing schedule
        existing_blocks = await self.get_daily_schedule(user_id, date)
        
        # Get user preferences
        prefs = await self._get_user_preferences(user_id)
        
        # Generate potential time slots
        suggestions = []
        day_start = date.replace(hour=6, minute=0, second=0, microsecond=0)  # Start at 6 AM
        day_end = date.replace(hour=22, minute=0, second=0, microsecond=0)   # End at 10 PM
        
        # Try every 15-minute interval
        current_time = day_start
        while current_time + timedelta(minutes=duration_minutes) <= day_end:
            end_time = current_time + timedelta(minutes=duration_minutes)
            
            # Check if slot is free
            is_free = True
            for block in existing_blocks:
                if not (end_time <= block.start_time or current_time >= block.end_time):
                    is_free = False
                    break
            
            if is_free:
                # Calculate confidence score based on various factors
                confidence = self._calculate_time_slot_confidence(
                    current_time, task, prefs
                )
                
                suggestions.append(TimeSlotSuggestion(
                    start_time=current_time,
                    end_time=end_time,
                    confidence_score=confidence,
                    reasoning=self._generate_time_slot_reasoning(current_time, task, prefs),
                    energy_match=self._get_energy_match(current_time, prefs),
                    conflicts=[]
                ))
            
            current_time += timedelta(minutes=15)
        
        # Sort by confidence score
        suggestions.sort(key=lambda x: x.confidence_score, reverse=True)
        
        return suggestions[:5]  # Return top 5 suggestions
    
    async def _detect_conflicts(
        self,
        user_id: UUID,
        start_time: datetime,
        end_time: datetime,
        buffer_before: int = 0,
        buffer_after: int = 0,
        exclude_block_id: Optional[UUID] = None
    ) -> List[ScheduleConflict]:
        """Detect scheduling conflicts for a time range."""
        # Expand time range to include buffers
        buffered_start = start_time - timedelta(minutes=buffer_before)
        buffered_end = end_time + timedelta(minutes=buffer_after)
        
        # Query for overlapping blocks
        query = select(TimeBlock).where(
            and_(
                TimeBlock.user_id == user_id,
                TimeBlock.status != TimeBlockStatus.CANCELLED,
                or_(
                    and_(TimeBlock.start_time < buffered_end, TimeBlock.end_time > buffered_start),
                    and_(TimeBlock.start_time < end_time, TimeBlock.end_time > start_time)
                )
            )
        )
        
        if exclude_block_id:
            query = query.where(TimeBlock.id != exclude_block_id)
        
        result = await self.db.execute(query)
        conflicting_blocks = result.scalars().all()
        
        conflicts = []
        for block in conflicting_blocks:
            conflicts.append(ScheduleConflict(
                conflict_type="time_overlap",
                severity="high",
                message=f"Conflicts with existing block: {block.title}",
                affected_blocks=[block.id],
                suggestions=[
                    "Choose a different time slot",
                    "Reduce buffer times",
                    "Move the conflicting block"
                ]
            ))
        
        return conflicts
    
    async def _get_user_preferences(self, user_id: UUID) -> Optional[SchedulingPreference]:
        """Get user's scheduling preferences."""
        result = await self.db.execute(
            select(SchedulingPreference).where(SchedulingPreference.user_id == user_id)
        )
        return result.scalar_one_or_none()
    
    def _calculate_time_slot_confidence(
        self,
        start_time: datetime,
        task: Task,
        prefs: Optional[SchedulingPreference]
    ) -> float:
        """Calculate confidence score for a time slot."""
        confidence = 0.5  # Base confidence
        
        # Time of day factors
        hour = start_time.hour
        
        # Morning boost for most people
        if 9 <= hour <= 11:
            confidence += 0.2
        
        # Afternoon dip
        if 13 <= hour <= 15:
            confidence -= 0.1
        
        # Late evening penalty
        if hour >= 20:
            confidence -= 0.3
        
        # Energy level matching
        if prefs and task.energy_level:
            energy_match = self._get_energy_match(start_time, prefs)
            if energy_match == task.energy_level:
                confidence += 0.3
            elif energy_match and energy_match != task.energy_level:
                confidence -= 0.2
        
        # Task complexity vs time available
        if task.complexity == "high" and hour >= 16:
            confidence -= 0.2  # Complex tasks better earlier
        
        return max(0.0, min(1.0, confidence))
    
    def _generate_time_slot_reasoning(
        self,
        start_time: datetime,
        task: Task,
        prefs: Optional[SchedulingPreference]
    ) -> str:
        """Generate human-readable reasoning for time slot suggestion."""
        hour = start_time.hour
        reasons = []
        
        if 9 <= hour <= 11:
            reasons.append("Morning hours are typically high-energy")
        
        if prefs:
            energy_match = self._get_energy_match(start_time, prefs)
            if energy_match:
                reasons.append(f"Matches your {energy_match} energy time")
        
        if task.complexity == "high" and hour <= 14:
            reasons.append("Good time for complex tasks")
        
        if not reasons:
            reasons.append("Available time slot")
        
        return "; ".join(reasons)
    
    def _get_energy_match(
        self,
        start_time: datetime,
        prefs: Optional[SchedulingPreference]
    ) -> Optional[str]:
        """Get energy level match for a time."""
        if not prefs:
            return None
        
        time_str = start_time.strftime("%H:%M")
        
        # Check peak energy times
        for peak_time in prefs.peak_energy_times:
            if self._time_in_range(time_str, peak_time):
                return "high"
        
        # Check low energy times
        for low_time in prefs.low_energy_times:
            if self._time_in_range(time_str, low_time):
                return "low"
        
        return "medium"
    
    def _time_in_range(self, time_str: str, time_range: str) -> bool:
        """Check if time falls within a time range."""
        try:
            if "-" in time_range:
                start_str, end_str = time_range.split("-")
                start_time = datetime.strptime(start_str.strip(), "%H:%M").time()
                end_time = datetime.strptime(end_str.strip(), "%H:%M").time()
                check_time = datetime.strptime(time_str, "%H:%M").time()
                
                if start_time <= end_time:
                    return start_time <= check_time <= end_time
                else:  # Crosses midnight
                    return check_time >= start_time or check_time <= end_time
        except ValueError:
            pass
        
        return False


class TimeVisualizationService:
    """Service for generating visual representations of time for ADHD users."""

    def generate_circular_view(
        self,
        time_blocks: List[TimeBlock],
        date: datetime,
        center_x: float = 200,
        center_y: float = 200,
        radius: float = 150
    ) -> CircularCalendarView:
        """
        Generate 24-hour circular clock view data.

        Args:
            time_blocks: List of time blocks for the day
            date: Target date
            center_x: Circle center X coordinate
            center_y: Circle center Y coordinate
            radius: Circle radius

        Returns:
            CircularCalendarView: Circular view data with proportional segments
        """
        import math

        segments = []
        hour_markers = []

        # Generate hour markers
        for hour in range(24):
            angle = (hour * 15) - 90  # Start at top (12 o'clock position)
            angle_rad = math.radians(angle)

            # Outer marker position
            outer_x = center_x + (radius + 20) * math.cos(angle_rad)
            outer_y = center_y + (radius + 20) * math.sin(angle_rad)

            # Inner marker position
            inner_x = center_x + (radius - 10) * math.cos(angle_rad)
            inner_y = center_y + (radius - 10) * math.sin(angle_rad)

            hour_markers.append({
                "hour": hour,
                "angle": angle,
                "outer_x": outer_x,
                "outer_y": outer_y,
                "inner_x": inner_x,
                "inner_y": inner_y,
                "label": f"{hour:02d}:00"
            })

        # Generate segments for time blocks
        for block in time_blocks:
            start_hour = block.start_time.hour + block.start_time.minute / 60
            end_hour = block.end_time.hour + block.end_time.minute / 60

            # Handle blocks that cross midnight
            if end_hour < start_hour:
                end_hour += 24

            start_angle = (start_hour * 15) - 90
            end_angle = (end_hour * 15) - 90

            # Calculate arc path for SVG
            start_rad = math.radians(start_angle)
            end_rad = math.radians(end_angle)

            # Inner and outer arc points
            inner_radius = radius - 30
            outer_radius = radius

            start_inner_x = center_x + inner_radius * math.cos(start_rad)
            start_inner_y = center_y + inner_radius * math.sin(start_rad)
            start_outer_x = center_x + outer_radius * math.cos(start_rad)
            start_outer_y = center_y + outer_radius * math.sin(start_rad)

            end_inner_x = center_x + inner_radius * math.cos(end_rad)
            end_inner_y = center_y + inner_radius * math.sin(end_rad)
            end_outer_x = center_x + outer_radius * math.cos(end_rad)
            end_outer_y = center_y + outer_radius * math.sin(end_rad)

            # Large arc flag for SVG path
            large_arc = 1 if (end_angle - start_angle) > 180 else 0

            segments.append({
                "block_id": str(block.id),
                "title": block.title,
                "block_type": block.block_type.value,
                "color": block.color or self._get_default_color(block.block_type),
                "start_angle": start_angle,
                "end_angle": end_angle,
                "duration_minutes": block.duration_minutes,
                "path": f"M {start_inner_x} {start_inner_y} "
                       f"L {start_outer_x} {start_outer_y} "
                       f"A {outer_radius} {outer_radius} 0 {large_arc} 1 {end_outer_x} {end_outer_y} "
                       f"L {end_inner_x} {end_inner_y} "
                       f"A {inner_radius} {inner_radius} 0 {large_arc} 0 {start_inner_x} {start_inner_y} Z"
            })

        # Current time indicator
        now = datetime.now()
        current_time_indicator = None

        if now.date() == date.date():
            current_hour = now.hour + now.minute / 60
            current_angle = (current_hour * 15) - 90
            current_rad = math.radians(current_angle)

            current_x = center_x + radius * math.cos(current_rad)
            current_y = center_y + radius * math.sin(current_rad)

            current_time_indicator = {
                "x": current_x,
                "y": current_y,
                "angle": current_angle,
                "time": now.strftime("%H:%M")
            }

        return CircularCalendarView(
            center_x=center_x,
            center_y=center_y,
            radius=radius,
            segments=segments,
            hour_markers=hour_markers,
            current_time_indicator=current_time_indicator
        )

    def generate_timeline_view(
        self,
        time_blocks: List[TimeBlock],
        date: datetime,
        hour_height: float = 60,
        start_hour: int = 6,
        end_hour: int = 22
    ) -> TimelineView:
        """
        Generate linear timeline view data.

        Args:
            time_blocks: List of time blocks for the day
            date: Target date
            hour_height: Height per hour in pixels
            start_hour: Starting hour for the timeline
            end_hour: Ending hour for the timeline

        Returns:
            TimelineView: Timeline view data with positioning information
        """
        total_hours = end_hour - start_hour
        total_height = total_hours * hour_height

        # Generate hour lines
        hour_lines = []
        for hour in range(start_hour, end_hour + 1):
            y_position = (hour - start_hour) * hour_height
            hour_lines.append({
                "hour": hour,
                "y": y_position,
                "label": f"{hour:02d}:00",
                "is_major": hour % 3 == 0  # Major lines every 3 hours
            })

        # Position time blocks
        positioned_blocks = []
        day_start = date.replace(hour=start_hour, minute=0, second=0, microsecond=0)

        for block in time_blocks:
            # Skip blocks outside the timeline range
            if block.end_time.hour < start_hour or block.start_time.hour > end_hour:
                continue

            # Calculate position
            start_minutes = (block.start_time - day_start).total_seconds() / 60
            height = block.duration_minutes * (hour_height / 60)

            # Handle blocks that start before timeline
            if start_minutes < 0:
                height += start_minutes * (hour_height / 60)
                start_minutes = 0

            # Handle blocks that extend beyond timeline
            max_minutes = total_hours * 60
            if start_minutes + (block.duration_minutes) > max_minutes:
                height = (max_minutes - start_minutes) * (hour_height / 60)

            y_position = start_minutes * (hour_height / 60)

            positioned_blocks.append({
                "block_id": str(block.id),
                "title": block.title,
                "description": block.description,
                "block_type": block.block_type.value,
                "color": block.color or self._get_default_color(block.block_type),
                "y": y_position,
                "height": height,
                "width": 200,  # Fixed width for now
                "x": 50,       # Fixed x position for now
                "start_time": block.start_time.strftime("%H:%M"),
                "end_time": block.end_time.strftime("%H:%M"),
                "duration_text": self._format_duration(block.duration_minutes),
                "buffer_before": block.buffer_before,
                "buffer_after": block.buffer_after,
                "is_flexible": block.is_flexible,
                "status": block.status.value
            })

        # Current time line
        now = datetime.now()
        current_time_line = None

        if now.date() == date.date() and start_hour <= now.hour <= end_hour:
            current_minutes = (now - day_start).total_seconds() / 60
            current_y = current_minutes * (hour_height / 60)

            current_time_line = {
                "y": current_y,
                "time": now.strftime("%H:%M"),
                "is_current": True
            }

        return TimelineView(
            hour_height=hour_height,
            total_height=total_height,
            time_blocks=positioned_blocks,
            hour_lines=hour_lines,
            current_time_line=current_time_line
        )

    def calculate_time_proportions(
        self,
        time_blocks: List[TimeBlock]
    ) -> Dict[str, float]:
        """
        Calculate time proportions by category for visual feedback.

        Args:
            time_blocks: List of time blocks to analyze

        Returns:
            Dict[str, float]: Proportions by category/type
        """
        if not time_blocks:
            return {}

        total_time = sum(block.duration_minutes for block in time_blocks)
        if total_time == 0:
            return {}

        # Group by block type
        type_totals = {}
        for block in time_blocks:
            block_type = block.block_type.value
            type_totals[block_type] = type_totals.get(block_type, 0) + block.duration_minutes

        # Calculate proportions
        proportions = {}
        for block_type, minutes in type_totals.items():
            proportions[block_type] = minutes / total_time

        return proportions

    def _get_default_color(self, block_type: TimeBlockType) -> str:
        """Get default color for a block type."""
        color_map = {
            TimeBlockType.TASK: "#3B82F6",      # Blue
            TimeBlockType.BREAK: "#10B981",     # Green
            TimeBlockType.BUFFER: "#F59E0B",    # Amber
            TimeBlockType.EVENT: "#8B5CF6",     # Purple
            TimeBlockType.MEETING: "#EF4444",   # Red
            TimeBlockType.TRAVEL: "#6B7280",    # Gray
            TimeBlockType.PERSONAL: "#EC4899",  # Pink
            TimeBlockType.FLEXIBLE: "#14B8A6",  # Teal
        }
        return color_map.get(block_type, "#6B7280")

    def _format_duration(self, minutes: int) -> str:
        """Format duration in a human-readable way."""
        if minutes < 60:
            return f"{minutes}m"
        else:
            hours = minutes // 60
            remaining_minutes = minutes % 60
            if remaining_minutes == 0:
                return f"{hours}h"
            else:
                return f"{hours}h {remaining_minutes}m"
