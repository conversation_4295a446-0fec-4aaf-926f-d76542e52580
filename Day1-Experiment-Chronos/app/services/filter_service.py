"""
Adaptive task filtering service for Project Chronos.

This module provides context-aware task filtering and the "task jar"
feature to reduce decision fatigue for ADHD users.
"""

import logging
import random
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID
from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from app.models.task import Task
from app.models.user import User
from app.schemas.task import TaskFilterRequest


logger = logging.getLogger(__name__)


class AdaptiveFilterService:
    """
    Context-aware task filtering for ADHD users.
    
    Provides intelligent task selection based on user state, energy levels,
    and historical patterns to reduce overwhelm and decision fatigue.
    """
    
    async def get_filtered_tasks(
        self,
        db: AsyncSession,
        user_id: UUID,
        energy_level: Optional[str] = None,
        max_duration: Optional[int] = None,
        context_tags: Optional[List[str]] = None,
        current_time: Optional[datetime] = None
    ) -> List[Task]:
        """
        Get tasks filtered by user's current state and preferences.
        
        Args:
            db: Database session
            user_id: User identifier
            energy_level: Current energy level (low/medium/high)
            max_duration: Maximum task duration in minutes
            context_tags: Context tags to match (e.g., ["home", "computer"])
            current_time: Current time for time-based filtering
            
        Returns:
            Filtered and prioritized list of tasks
        """
        current_time = current_time or datetime.utcnow()
        
        # Get user preferences
        user = await self._get_user_preferences(db, user_id)
        
        # Build base query
        stmt = select(Task).where(
            and_(
                Task.user_id == user_id,
                Task.status.in_(["pending", "in_progress"]),
                Task.deleted_at.is_(None)
            )
        )
        
        # Apply energy level filtering
        if energy_level:
            stmt = self._apply_energy_filter(stmt, energy_level)
        
        # Apply duration filtering
        if max_duration:
            stmt = stmt.where(
                or_(
                    Task.estimated_duration <= max_duration,
                    Task.estimated_duration.is_(None)
                )
            )
        
        # Apply context filtering
        if context_tags:
            stmt = self._apply_context_filter(stmt, context_tags)
        
        # Apply time-based filtering
        stmt = self._apply_time_filter(stmt, current_time, user)
        
        # Execute query
        result = await db.execute(stmt)
        tasks = result.scalars().all()
        
        # Score and sort tasks
        scored_tasks = []
        for task in tasks:
            score = await self._calculate_task_score(
                task, user, current_time, energy_level, context_tags
            )
            scored_tasks.append((task, score))
        
        # Sort by score (highest first)
        scored_tasks.sort(key=lambda x: x[1], reverse=True)
        
        return [task for task, _ in scored_tasks]
    
    async def get_task_jar_selection(
        self,
        db: AsyncSession,
        user_id: UUID,
        jar_size: int = 5,
        filters: Optional[TaskFilterRequest] = None
    ) -> List[Task]:
        """
        Get random selection of tasks for decision fatigue reduction.
        
        Args:
            db: Database session
            user_id: User identifier
            jar_size: Number of tasks to include in selection
            filters: Optional filters to apply before randomization
            
        Returns:
            Random selection of suitable tasks
        """
        # Get filtered tasks
        if filters:
            tasks = await self.get_filtered_tasks(
                db=db,
                user_id=user_id,
                energy_level=filters.energy_level,
                max_duration=filters.max_duration,
                context_tags=filters.context_tags
            )
        else:
            # Get all available tasks
            tasks = await self.get_filtered_tasks(db=db, user_id=user_id)
        
        # Exclude recently completed or skipped tasks
        tasks = await self._exclude_recent_tasks(db, tasks, user_id)
        
        # If we have fewer tasks than jar size, return all
        if len(tasks) <= jar_size:
            return tasks
        
        # Weighted random selection based on urgency and priority
        weighted_tasks = []
        for task in tasks:
            weight = self._calculate_selection_weight(task)
            weighted_tasks.extend([task] * max(1, int(weight * 10)))
        
        # Random selection
        selected = []
        available = weighted_tasks.copy()
        
        for _ in range(jar_size):
            if not available:
                break
            
            task = random.choice(available)
            if task not in selected:
                selected.append(task)
            
            # Remove all instances of selected task
            available = [t for t in available if t.id != task.id]
        
        logger.info(f"Generated task jar with {len(selected)} tasks for user {user_id}")
        return selected
    
    async def _get_user_preferences(self, db: AsyncSession, user_id: UUID) -> User:
        """Get user preferences for adaptive filtering."""
        stmt = select(User).where(User.id == user_id)
        result = await db.execute(stmt)
        return result.scalar_one()
    
    def _apply_energy_filter(self, stmt, energy_level: str):
        """Apply energy level filtering with smart matching."""
        if energy_level == "low":
            # When energy is low, show low and medium energy tasks
            return stmt.where(Task.energy_level.in_(["low", "medium"]))
        elif energy_level == "medium":
            # When energy is medium, show all tasks
            return stmt
        else:  # high energy
            # When energy is high, prefer medium and high energy tasks
            return stmt.where(Task.energy_level.in_(["medium", "high"]))
    
    def _apply_context_filter(self, stmt, context_tags: List[str]):
        """Apply context tag filtering."""
        conditions = []
        for tag in context_tags:
            conditions.append(Task.context_tags.contains([tag]))
        
        if conditions:
            stmt = stmt.where(or_(*conditions))
        
        return stmt
    
    def _apply_time_filter(self, stmt, current_time: datetime, user: User):
        """Apply time-based filtering based on user patterns."""
        # Prioritize tasks due soon
        urgent_threshold = current_time + timedelta(days=1)
        
        # Don't filter out by time, but we'll use this in scoring
        return stmt
    
    async def _calculate_task_score(
        self,
        task: Task,
        user: User,
        current_time: datetime,
        energy_level: Optional[str] = None,
        context_tags: Optional[List[str]] = None
    ) -> float:
        """
        Calculate relevance score for task based on current context.
        
        Higher scores indicate better matches for current user state.
        """
        score = 0.0
        
        # Base urgency score
        score += task.calculate_urgency_score()
        
        # Energy level matching
        if energy_level:
            if task.energy_level == energy_level:
                score += 2.0
            elif energy_level == "low" and task.energy_level == "medium":
                score += 1.0
            elif energy_level == "high" and task.energy_level == "medium":
                score += 1.0
        
        # Context tag matching
        if context_tags and task.context_tags:
            matching_tags = set(context_tags) & set(task.context_tags)
            score += len(matching_tags) * 1.5
        
        # Duration preference (shorter tasks get slight boost for ADHD)
        if task.estimated_duration:
            if task.estimated_duration <= 15:  # Quick wins
                score += 1.0
            elif task.estimated_duration <= 30:
                score += 0.5
        
        # Time-based scoring
        if task.due_date:
            days_until_due = (task.due_date - current_time).days
            if days_until_due <= 0:
                score += 5.0  # Overdue
            elif days_until_due <= 1:
                score += 3.0  # Due today/tomorrow
            elif days_until_due <= 7:
                score += 1.0  # Due this week
        
        # Boost for tasks that haven't been worked on recently
        if not task.started_at:
            score += 0.5
        elif task.started_at < current_time - timedelta(days=7):
            score += 0.3
        
        # Slight penalty for very long tasks to avoid overwhelm
        if task.estimated_duration and task.estimated_duration > 120:
            score -= 0.5
        
        return max(0.0, score)
    
    def _calculate_selection_weight(self, task: Task) -> float:
        """Calculate weight for random selection in task jar."""
        weight = 1.0
        
        # Higher weight for higher priority
        priority_weights = {"low": 0.5, "medium": 1.0, "high": 1.5, "urgent": 2.0}
        weight *= priority_weights.get(task.priority, 1.0)
        
        # Higher weight for overdue tasks
        if task.is_overdue:
            weight *= 2.0
        
        # Higher weight for shorter tasks (quick wins)
        if task.estimated_duration and task.estimated_duration <= 30:
            weight *= 1.3
        
        return weight
    
    async def _exclude_recent_tasks(
        self,
        db: AsyncSession,
        tasks: List[Task],
        user_id: UUID,
        hours: int = 24
    ) -> List[Task]:
        """Exclude tasks that were recently completed or worked on."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        # Get recently completed tasks
        recent_completed_stmt = select(Task.id).where(
            and_(
                Task.user_id == user_id,
                Task.completed_at >= cutoff_time
            )
        )
        result = await db.execute(recent_completed_stmt)
        recent_completed_ids = {row[0] for row in result.fetchall()}
        
        # Filter out recent tasks
        filtered_tasks = [
            task for task in tasks
            if task.id not in recent_completed_ids
        ]
        
        return filtered_tasks
