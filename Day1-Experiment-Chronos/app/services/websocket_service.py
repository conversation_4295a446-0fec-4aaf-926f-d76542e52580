"""
WebSocket service for Project Chronos.

This module provides real-time communication functionality for body doubling
sessions, focus synchronization, and live collaboration features.
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Set
from uuid import UUID

from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas.body_doubling import (
    RealTimeMessage,
    WebSocketMessageType,
    WebSocketResponse,
)
from app.services.body_doubling_service import BodyDoublingService
from app.core.exceptions import WebSocketError

logger = logging.getLogger(__name__)


class ConnectionManager:
    """Manages WebSocket connections for real-time features."""
    
    def __init__(self):
        # Active connections by session ID
        self.session_connections: Dict[UUID, Set[WebSocket]] = {}
        # User to WebSocket mapping
        self.user_connections: Dict[UUID, WebSocket] = {}
        # WebSocket to user mapping
        self.connection_users: Dict[WebSocket, UUID] = {}
        # Session membership tracking
        self.user_sessions: Dict[UUID, Set[UUID]] = {}
    
    async def connect(
        self,
        websocket: WebSocket,
        user_id: UUID,
        session_id: UUID
    ) -> None:
        """
        Connect a user to a session via WebSocket.
        
        Args:
            websocket: WebSocket connection
            user_id: User connecting
            session_id: Session to connect to
        """
        await websocket.accept()
        
        # Store connection mappings
        self.user_connections[user_id] = websocket
        self.connection_users[websocket] = user_id
        
        # Add to session connections
        if session_id not in self.session_connections:
            self.session_connections[session_id] = set()
        self.session_connections[session_id].add(websocket)
        
        # Track user session membership
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = set()
        self.user_sessions[user_id].add(session_id)
        
        logger.info(f"User {user_id} connected to session {session_id}")
        
        # Notify other participants
        await self.broadcast_to_session(
            session_id,
            WebSocketResponse(
                message_type=WebSocketMessageType.PARTICIPANT_JOIN,
                data={
                    "user_id": str(user_id),
                    "session_id": str(session_id),
                    "timestamp": datetime.utcnow().isoformat()
                }
            ),
            exclude_user=user_id
        )
    
    async def disconnect(self, websocket: WebSocket) -> None:
        """
        Disconnect a WebSocket and clean up.
        
        Args:
            websocket: WebSocket to disconnect
        """
        user_id = self.connection_users.get(websocket)
        if not user_id:
            return
        
        # Get user's sessions
        user_session_ids = self.user_sessions.get(user_id, set()).copy()
        
        # Clean up connection mappings
        self.user_connections.pop(user_id, None)
        self.connection_users.pop(websocket, None)
        
        # Remove from session connections
        for session_id in user_session_ids:
            if session_id in self.session_connections:
                self.session_connections[session_id].discard(websocket)
                if not self.session_connections[session_id]:
                    del self.session_connections[session_id]
            
            # Notify other participants
            await self.broadcast_to_session(
                session_id,
                WebSocketResponse(
                    message_type=WebSocketMessageType.PARTICIPANT_LEAVE,
                    data={
                        "user_id": str(user_id),
                        "session_id": str(session_id),
                        "timestamp": datetime.utcnow().isoformat()
                    }
                ),
                exclude_user=user_id
            )
        
        # Clean up user session tracking
        self.user_sessions.pop(user_id, None)
        
        logger.info(f"User {user_id} disconnected from {len(user_session_ids)} sessions")
    
    async def send_to_user(
        self,
        user_id: UUID,
        message: WebSocketResponse
    ) -> bool:
        """
        Send message to a specific user.
        
        Args:
            user_id: Target user
            message: Message to send
            
        Returns:
            bool: Whether message was sent successfully
        """
        websocket = self.user_connections.get(user_id)
        if not websocket:
            return False
        
        try:
            await websocket.send_text(message.model_dump_json())
            return True
        except Exception as e:
            logger.error(f"Error sending message to user {user_id}: {e}")
            await self.disconnect(websocket)
            return False
    
    async def broadcast_to_session(
        self,
        session_id: UUID,
        message: WebSocketResponse,
        exclude_user: Optional[UUID] = None
    ) -> int:
        """
        Broadcast message to all users in a session.
        
        Args:
            session_id: Target session
            message: Message to broadcast
            exclude_user: User to exclude from broadcast
            
        Returns:
            int: Number of users message was sent to
        """
        connections = self.session_connections.get(session_id, set()).copy()
        sent_count = 0
        
        for websocket in connections:
            user_id = self.connection_users.get(websocket)
            if user_id and user_id != exclude_user:
                if await self.send_to_user(user_id, message):
                    sent_count += 1
        
        return sent_count
    
    def get_session_participants(self, session_id: UUID) -> List[UUID]:
        """Get list of connected users in a session."""
        connections = self.session_connections.get(session_id, set())
        return [
            self.connection_users[ws] 
            for ws in connections 
            if ws in self.connection_users
        ]
    
    def is_user_connected(self, user_id: UUID) -> bool:
        """Check if user has an active WebSocket connection."""
        return user_id in self.user_connections


class WebSocketService:
    """Service for handling WebSocket communication and real-time features."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.body_doubling_service = BodyDoublingService(db)
        self.connection_manager = ConnectionManager()
    
    async def handle_connection(
        self,
        websocket: WebSocket,
        user_id: UUID,
        session_id: UUID
    ) -> None:
        """
        Handle new WebSocket connection.
        
        Args:
            websocket: WebSocket connection
            user_id: Connecting user
            session_id: Session to connect to
        """
        try:
            await self.connection_manager.connect(websocket, user_id, session_id)
            
            # Send connection confirmation
            await self.connection_manager.send_to_user(
                user_id,
                WebSocketResponse(
                    message_type=WebSocketMessageType.CONNECT,
                    data={
                        "session_id": str(session_id),
                        "connected_participants": [
                            str(uid) for uid in self.connection_manager.get_session_participants(session_id)
                        ]
                    }
                )
            )
            
            # Listen for messages
            await self._listen_for_messages(websocket, user_id, session_id)
            
        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected for user {user_id}")
        except Exception as e:
            logger.error(f"WebSocket error for user {user_id}: {e}")
        finally:
            await self.connection_manager.disconnect(websocket)
    
    async def _listen_for_messages(
        self,
        websocket: WebSocket,
        user_id: UUID,
        session_id: UUID
    ) -> None:
        """
        Listen for incoming WebSocket messages.
        
        Args:
            websocket: WebSocket connection
            user_id: User sending messages
            session_id: Current session
        """
        while True:
            try:
                # Receive message
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                # Create message object
                message = RealTimeMessage(**message_data)
                message.sender_id = user_id
                message.session_id = session_id
                
                # Handle message based on type
                await self._handle_message(message)
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await self._send_error(websocket, "Invalid JSON format")
            except Exception as e:
                logger.error(f"Error handling message from user {user_id}: {e}")
                await self._send_error(websocket, "Message processing error")
    
    async def _handle_message(self, message: RealTimeMessage) -> None:
        """
        Handle incoming WebSocket message.
        
        Args:
            message: Received message
        """
        try:
            if message.message_type == WebSocketMessageType.HEARTBEAT:
                await self._handle_heartbeat(message)
            
            elif message.message_type == WebSocketMessageType.ENCOURAGEMENT:
                await self._handle_encouragement(message)
            
            elif message.message_type == WebSocketMessageType.PROGRESS_SHARE:
                await self._handle_progress_share(message)
            
            elif message.message_type == WebSocketMessageType.FOCUS_START:
                await self._handle_focus_start(message)
            
            elif message.message_type == WebSocketMessageType.FOCUS_BREAK:
                await self._handle_focus_break(message)
            
            elif message.message_type == WebSocketMessageType.CHAT_MESSAGE:
                await self._handle_chat_message(message)
            
            else:
                logger.warning(f"Unknown message type: {message.message_type}")
        
        except Exception as e:
            logger.error(f"Error handling message type {message.message_type}: {e}")
    
    async def _handle_heartbeat(self, message: RealTimeMessage) -> None:
        """Handle heartbeat message."""
        if message.sender_id:
            await self.connection_manager.send_to_user(
                message.sender_id,
                WebSocketResponse(
                    message_type=WebSocketMessageType.HEARTBEAT,
                    data={"timestamp": datetime.utcnow().isoformat()}
                )
            )
    
    async def _handle_encouragement(self, message: RealTimeMessage) -> None:
        """Handle encouragement message."""
        # Broadcast encouragement to session
        response = WebSocketResponse(
            message_type=WebSocketMessageType.ENCOURAGEMENT,
            data={
                "sender_id": str(message.sender_id) if message.sender_id else None,
                "message": message.data.get("message", ""),
                "message_type": message.data.get("message_type", "encouragement"),
                "timestamp": message.timestamp.isoformat()
            }
        )
        
        await self.connection_manager.broadcast_to_session(
            message.session_id,
            response,
            exclude_user=message.sender_id
        )
    
    async def _handle_progress_share(self, message: RealTimeMessage) -> None:
        """Handle progress sharing message."""
        # Broadcast progress update to session
        response = WebSocketResponse(
            message_type=WebSocketMessageType.PROGRESS_SHARE,
            data={
                "user_id": str(message.sender_id),
                "progress_type": message.data.get("progress_type"),
                "description": message.data.get("description"),
                "completion_percentage": message.data.get("completion_percentage"),
                "timestamp": message.timestamp.isoformat()
            }
        )
        
        await self.connection_manager.broadcast_to_session(
            message.session_id,
            response,
            exclude_user=message.sender_id
        )
    
    async def _handle_focus_start(self, message: RealTimeMessage) -> None:
        """Handle focus session start."""
        # Broadcast focus start to all participants
        response = WebSocketResponse(
            message_type=WebSocketMessageType.FOCUS_START,
            data={
                "started_by": str(message.sender_id),
                "focus_duration": message.data.get("focus_duration", 25),
                "break_duration": message.data.get("break_duration", 5),
                "timestamp": message.timestamp.isoformat()
            }
        )
        
        await self.connection_manager.broadcast_to_session(
            message.session_id,
            response
        )
    
    async def _handle_focus_break(self, message: RealTimeMessage) -> None:
        """Handle focus session break."""
        # Broadcast break start to all participants
        response = WebSocketResponse(
            message_type=WebSocketMessageType.FOCUS_BREAK,
            data={
                "break_duration": message.data.get("break_duration", 5),
                "cycle_number": message.data.get("cycle_number", 1),
                "timestamp": message.timestamp.isoformat()
            }
        )
        
        await self.connection_manager.broadcast_to_session(
            message.session_id,
            response
        )
    
    async def _handle_chat_message(self, message: RealTimeMessage) -> None:
        """Handle chat message."""
        # Broadcast chat message to session
        response = WebSocketResponse(
            message_type=WebSocketMessageType.CHAT_MESSAGE,
            data={
                "sender_id": str(message.sender_id),
                "message": message.data.get("message", ""),
                "timestamp": message.timestamp.isoformat()
            }
        )
        
        await self.connection_manager.broadcast_to_session(
            message.session_id,
            response,
            exclude_user=message.sender_id
        )
    
    async def _send_error(self, websocket: WebSocket, error_message: str) -> None:
        """Send error message to WebSocket."""
        try:
            error_response = WebSocketResponse(
                message_type=WebSocketMessageType.ERROR,
                success=False,
                error=error_message
            )
            await websocket.send_text(error_response.model_dump_json())
        except Exception as e:
            logger.error(f"Error sending error message: {e}")


# Global connection manager instance
connection_manager = ConnectionManager()
