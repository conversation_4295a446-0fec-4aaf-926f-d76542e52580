"""
Time tracking service for ADHD time blindness support.

This service provides time estimation, tracking, and visual indicators
specifically designed to help ADHD users manage time blindness.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from uuid import UUID
import asyncio
import json

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_

from app.models.user import User
from app.models.task import Task
from app.core.metrics import metrics_collector


class TimeBlindnessHelper:
    """Helper class for managing ADHD time blindness features."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.active_timers: Dict[str, datetime] = {}
        self.time_estimates: Dict[str, float] = {}
    
    async def estimate_task_duration(self, user_id: UUID, task_description: str, 
                                   complexity: str, user_energy_level: str) -> Dict:
        """
        Estimate task duration based on user history and ADHD patterns.
        
        Args:
            user_id: User's unique identifier
            task_description: Description of the task
            complexity: Task complexity (low, medium, high)
            user_energy_level: Current energy level (low, medium, high)
            
        Returns:
            Dictionary with time estimates and confidence levels
        """
        # Get user's historical data for similar tasks
        historical_data = await self._get_historical_task_data(user_id, complexity)
        
        # Base estimates by complexity (in minutes)
        base_estimates = {
            "low": 15,
            "medium": 45,
            "high": 120
        }
        
        # Energy level multipliers for ADHD users
        energy_multipliers = {
            "low": 1.5,    # Tasks take longer when energy is low
            "medium": 1.0,
            "high": 0.8    # Tasks are faster when energy is high
        }
        
        base_estimate = base_estimates.get(complexity, 45)
        energy_multiplier = energy_multipliers.get(user_energy_level, 1.0)
        
        # Adjust based on historical data
        if historical_data:
            avg_actual = sum(historical_data) / len(historical_data)
            # Blend historical average with base estimate
            adjusted_estimate = (base_estimate * 0.3) + (avg_actual * 0.7)
        else:
            adjusted_estimate = base_estimate
        
        # Apply energy level adjustment
        final_estimate = adjusted_estimate * energy_multiplier
        
        # Add ADHD-specific buffer (time blindness compensation)
        adhd_buffer = final_estimate * 0.3  # 30% buffer for ADHD users
        
        # Calculate confidence based on historical data availability
        confidence = min(len(historical_data) * 10, 90) if historical_data else 30
        
        return {
            "estimated_minutes": round(final_estimate),
            "with_buffer_minutes": round(final_estimate + adhd_buffer),
            "confidence_percentage": confidence,
            "breakdown": {
                "base_estimate": base_estimate,
                "energy_adjustment": energy_multiplier,
                "historical_factor": len(historical_data),
                "adhd_buffer": round(adhd_buffer)
            }
        }
    
    async def start_task_timer(self, task_id: str, estimated_duration: float) -> Dict:
        """
        Start a timer for a task with ADHD-friendly features.
        
        Args:
            task_id: Task identifier
            estimated_duration: Estimated duration in minutes
            
        Returns:
            Timer information with visual cues
        """
        start_time = datetime.utcnow()
        self.active_timers[task_id] = start_time
        self.time_estimates[task_id] = estimated_duration
        
        # Calculate milestone markers for visual progress
        milestones = self._calculate_time_milestones(estimated_duration)
        
        return {
            "task_id": task_id,
            "start_time": start_time.isoformat(),
            "estimated_duration_minutes": estimated_duration,
            "milestones": milestones,
            "visual_cues": {
                "progress_bar_segments": 10,
                "color_transitions": [
                    {"at_percent": 0, "color": "#22c55e"},    # Green
                    {"at_percent": 50, "color": "#eab308"},   # Yellow
                    {"at_percent": 80, "color": "#f97316"},   # Orange
                    {"at_percent": 100, "color": "#ef4444"}   # Red
                ]
            }
        }
    
    async def get_timer_status(self, task_id: str) -> Optional[Dict]:
        """
        Get current timer status with ADHD-friendly visual indicators.
        
        Args:
            task_id: Task identifier
            
        Returns:
            Current timer status or None if not found
        """
        if task_id not in self.active_timers:
            return None
        
        start_time = self.active_timers[task_id]
        estimated_duration = self.time_estimates.get(task_id, 60)
        
        current_time = datetime.utcnow()
        elapsed_minutes = (current_time - start_time).total_seconds() / 60
        
        # Calculate progress percentage
        progress_percent = min((elapsed_minutes / estimated_duration) * 100, 100)
        
        # Determine current phase
        phase = self._determine_time_phase(progress_percent)
        
        # Calculate remaining time
        remaining_minutes = max(estimated_duration - elapsed_minutes, 0)
        
        return {
            "task_id": task_id,
            "elapsed_minutes": round(elapsed_minutes, 1),
            "remaining_minutes": round(remaining_minutes, 1),
            "progress_percent": round(progress_percent, 1),
            "phase": phase,
            "visual_status": {
                "color": self._get_progress_color(progress_percent),
                "urgency_level": self._get_urgency_level(progress_percent),
                "should_show_warning": progress_percent > 80,
                "motivational_message": self._get_motivational_message(phase, progress_percent)
            }
        }
    
    async def complete_task_timer(self, task_id: str) -> Dict:
        """
        Complete a task timer and record accuracy metrics.
        
        Args:
            task_id: Task identifier
            
        Returns:
            Completion summary with time accuracy analysis
        """
        if task_id not in self.active_timers:
            raise ValueError(f"No active timer found for task {task_id}")
        
        start_time = self.active_timers[task_id]
        estimated_duration = self.time_estimates.get(task_id, 60)
        
        end_time = datetime.utcnow()
        actual_duration = (end_time - start_time).total_seconds() / 60
        
        # Calculate accuracy metrics
        accuracy_ratio = actual_duration / estimated_duration if estimated_duration > 0 else 1.0
        accuracy_category = self._categorize_time_accuracy(accuracy_ratio)
        
        # Record metrics
        metrics_collector.record_task_completion(
            task_id=task_id,
            complexity="medium",  # Would be passed from task data
            estimated_duration=estimated_duration,
            actual_duration=actual_duration
        )
        
        # Clean up
        del self.active_timers[task_id]
        del self.time_estimates[task_id]
        
        return {
            "task_id": task_id,
            "estimated_minutes": estimated_duration,
            "actual_minutes": round(actual_duration, 1),
            "accuracy_ratio": round(accuracy_ratio, 2),
            "accuracy_category": accuracy_category,
            "feedback": self._generate_time_feedback(accuracy_ratio),
            "learning_insight": self._generate_learning_insight(accuracy_ratio)
        }
    
    def _calculate_time_milestones(self, duration_minutes: float) -> List[Dict]:
        """Calculate milestone markers for visual progress tracking."""
        milestones = []
        
        # Standard milestones at 25%, 50%, 75%, 100%
        for percent in [25, 50, 75, 100]:
            milestone_time = (duration_minutes * percent) / 100
            milestones.append({
                "percent": percent,
                "time_minutes": round(milestone_time, 1),
                "label": f"{percent}% Complete",
                "celebration_level": "small" if percent < 100 else "major"
            })
        
        return milestones
    
    def _determine_time_phase(self, progress_percent: float) -> str:
        """Determine the current phase of task execution."""
        if progress_percent < 25:
            return "getting_started"
        elif progress_percent < 50:
            return "building_momentum"
        elif progress_percent < 75:
            return "steady_progress"
        elif progress_percent < 100:
            return "final_push"
        else:
            return "overtime"
    
    def _get_progress_color(self, progress_percent: float) -> str:
        """Get color for progress indicator based on percentage."""
        if progress_percent < 50:
            return "#22c55e"  # Green
        elif progress_percent < 80:
            return "#eab308"  # Yellow
        elif progress_percent < 100:
            return "#f97316"  # Orange
        else:
            return "#ef4444"  # Red
    
    def _get_urgency_level(self, progress_percent: float) -> str:
        """Determine urgency level for notifications."""
        if progress_percent < 60:
            return "low"
        elif progress_percent < 85:
            return "medium"
        else:
            return "high"
    
    def _get_motivational_message(self, phase: str, progress_percent: float) -> str:
        """Generate ADHD-friendly motivational messages."""
        messages = {
            "getting_started": [
                "Great job starting! You've got this! 🚀",
                "The hardest part is behind you - you've begun! ✨",
                "One step at a time, you're making progress! 💪"
            ],
            "building_momentum": [
                "You're in the flow now! Keep it up! 🌊",
                "Momentum is building - you're doing amazing! ⚡",
                "Look at you go! You're crushing this! 🔥"
            ],
            "steady_progress": [
                "Halfway there! You're doing fantastic! 🎯",
                "Steady wins the race - you're nailing it! 🏃‍♀️",
                "Your focus is paying off! Keep going! 💎"
            ],
            "final_push": [
                "Almost there! You can see the finish line! 🏁",
                "Final stretch - you've got this in the bag! 🎉",
                "So close! Your persistence is inspiring! 🌟"
            ],
            "overtime": [
                "Taking a bit longer? That's totally okay! 💙",
                "Quality over speed - you're doing great! 🎨",
                "Extra time means extra care - well done! 🌈"
            ]
        }
        
        import random
        return random.choice(messages.get(phase, ["You're doing great! 💪"]))
    
    async def _get_historical_task_data(self, user_id: UUID, complexity: str) -> List[float]:
        """Get historical task completion times for similar complexity."""
        # This would query the database for historical task data
        # For now, return mock data
        return [30, 45, 35, 50, 40] if complexity == "medium" else []
    
    def _categorize_time_accuracy(self, accuracy_ratio: float) -> str:
        """Categorize time estimation accuracy."""
        if 0.8 <= accuracy_ratio <= 1.2:
            return "excellent"
        elif 0.6 <= accuracy_ratio <= 1.5:
            return "good"
        elif 0.4 <= accuracy_ratio <= 2.0:
            return "fair"
        else:
            return "needs_improvement"
    
    def _generate_time_feedback(self, accuracy_ratio: float) -> str:
        """Generate encouraging feedback about time estimation."""
        if accuracy_ratio < 0.8:
            return "You finished faster than expected! Your efficiency is improving! 🚀"
        elif accuracy_ratio <= 1.2:
            return "Spot on with your time estimate! Your planning skills are excellent! 🎯"
        elif accuracy_ratio <= 1.5:
            return "Close to your estimate! Time awareness is a skill that improves with practice! 📈"
        else:
            return "This took longer than expected, and that's completely normal! Every task teaches us something! 💡"
    
    def _generate_learning_insight(self, accuracy_ratio: float) -> str:
        """Generate learning insights for future time estimation."""
        if accuracy_ratio < 0.8:
            return "Consider if you can apply this efficiency to similar tasks in the future."
        elif accuracy_ratio <= 1.2:
            return "Your time estimation skills are developing well! Keep using this approach."
        else:
            return "For similar tasks, consider adding a bit more buffer time. This helps reduce stress!"


class TimeTrackingService:
    """Main service for time tracking functionality."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.helper = TimeBlindnessHelper(db)
    
    async def create_time_estimate(self, user_id: UUID, task_data: Dict) -> Dict:
        """Create a time estimate for a new task."""
        return await self.helper.estimate_task_duration(
            user_id=user_id,
            task_description=task_data.get("description", ""),
            complexity=task_data.get("complexity", "medium"),
            user_energy_level=task_data.get("energy_level", "medium")
        )
    
    async def start_timer(self, task_id: str, estimated_duration: float) -> Dict:
        """Start a timer for a task."""
        return await self.helper.start_task_timer(task_id, estimated_duration)
    
    async def get_timer_status(self, task_id: str) -> Optional[Dict]:
        """Get current timer status."""
        return await self.helper.get_timer_status(task_id)
    
    async def complete_timer(self, task_id: str) -> Dict:
        """Complete a timer and get feedback."""
        return await self.helper.complete_task_timer(task_id)
