"""
Integration service for Project Chronos.

This module provides services for managing external service integrations,
OAuth flows, and synchronization operations.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import select, and_, or_, desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.integration import (
    Integration,
    SyncLog,
    WebhookEvent,
    IntegrationType,
    IntegrationStatus,
    SyncStatus,
)
from app.models.user import User
from app.schemas.integration import (
    IntegrationCreate,
    IntegrationUpdate,
    IntegrationResponse,
    SyncLogCreate,
    SyncLogResponse,
    SyncRequest,
    SyncResponse,
    IntegrationStats,
    IntegrationHealth,
    BulkSyncRequest,
    BulkSyncResponse,
)
from app.core.exceptions import (
    NotFoundError,
    ValidationError,
    IntegrationError,
    ConflictError,
)

logger = logging.getLogger(__name__)


class IntegrationService:
    """Service for managing external service integrations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_integration(
        self,
        user_id: UUID,
        integration_data: IntegrationCreate
    ) -> IntegrationResponse:
        """
        Create a new integration for a user.
        
        Args:
            user_id: User creating the integration
            integration_data: Integration configuration data
            
        Returns:
            Created integration
            
        Raises:
            ValidationError: If integration data is invalid
            ConflictError: If integration already exists
        """
        
        # Check if user already has this integration type
        existing = await self.db.execute(
            select(Integration).where(
                and_(
                    Integration.user_id == user_id,
                    Integration.integration_type == integration_data.integration_type,
                    Integration.external_id == integration_data.external_id
                )
            )
        )
        
        if existing.scalar_one_or_none():
            raise ConflictError(
                f"Integration {integration_data.integration_type} already exists for this user"
            )
        
        # Create integration
        integration = Integration(
            user_id=user_id,
            integration_type=integration_data.integration_type,
            name=integration_data.name,
            description=integration_data.description,
            external_id=integration_data.external_id,
            config=integration_data.config,
            sync_settings=integration_data.sync_settings,
            webhook_secret=integration_data.webhook_secret,
            status=IntegrationStatus.ACTIVE
        )
        
        self.db.add(integration)
        await self.db.commit()
        await self.db.refresh(integration)
        
        logger.info(f"Created integration {integration.id} for user {user_id}")
        
        return await self._to_response(integration)
    
    async def get_user_integrations(
        self,
        user_id: UUID,
        integration_type: Optional[IntegrationType] = None,
        status: Optional[IntegrationStatus] = None
    ) -> List[IntegrationResponse]:
        """
        Get user's integrations with optional filtering.
        
        Args:
            user_id: User to get integrations for
            integration_type: Optional filter by integration type
            status: Optional filter by status
            
        Returns:
            List of user's integrations
        """
        
        query = select(Integration).where(Integration.user_id == user_id)
        
        if integration_type:
            query = query.where(Integration.integration_type == integration_type)
        
        if status:
            query = query.where(Integration.status == status)
        
        query = query.order_by(desc(Integration.created_at))
        
        result = await self.db.execute(query)
        integrations = result.scalars().all()
        
        return [await self._to_response(integration) for integration in integrations]
    
    async def get_integration(
        self,
        user_id: UUID,
        integration_id: UUID
    ) -> IntegrationResponse:
        """
        Get a specific integration for a user.
        
        Args:
            user_id: User who owns the integration
            integration_id: Integration to retrieve
            
        Returns:
            Integration details
            
        Raises:
            NotFoundError: If integration doesn't exist or doesn't belong to user
        """
        
        result = await self.db.execute(
            select(Integration).where(
                and_(
                    Integration.id == integration_id,
                    Integration.user_id == user_id
                )
            )
        )
        
        integration = result.scalar_one_or_none()
        if not integration:
            raise NotFoundError(f"Integration {integration_id} not found")
        
        return await self._to_response(integration)
    
    async def update_integration(
        self,
        user_id: UUID,
        integration_id: UUID,
        update_data: IntegrationUpdate
    ) -> IntegrationResponse:
        """
        Update an integration.
        
        Args:
            user_id: User who owns the integration
            integration_id: Integration to update
            update_data: Update data
            
        Returns:
            Updated integration
            
        Raises:
            NotFoundError: If integration doesn't exist
        """
        
        result = await self.db.execute(
            select(Integration).where(
                and_(
                    Integration.id == integration_id,
                    Integration.user_id == user_id
                )
            )
        )
        
        integration = result.scalar_one_or_none()
        if not integration:
            raise NotFoundError(f"Integration {integration_id} not found")
        
        # Update fields
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(integration, field, value)
        
        integration.updated_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(integration)
        
        logger.info(f"Updated integration {integration_id}")
        
        return await self._to_response(integration)
    
    async def delete_integration(
        self,
        user_id: UUID,
        integration_id: UUID
    ) -> bool:
        """
        Delete an integration.
        
        Args:
            user_id: User who owns the integration
            integration_id: Integration to delete
            
        Returns:
            True if deleted successfully
            
        Raises:
            NotFoundError: If integration doesn't exist
        """
        
        result = await self.db.execute(
            select(Integration).where(
                and_(
                    Integration.id == integration_id,
                    Integration.user_id == user_id
                )
            )
        )
        
        integration = result.scalar_one_or_none()
        if not integration:
            raise NotFoundError(f"Integration {integration_id} not found")
        
        await self.db.delete(integration)
        await self.db.commit()
        
        logger.info(f"Deleted integration {integration_id}")
        
        return True
    
    async def start_sync(
        self,
        user_id: UUID,
        integration_id: UUID,
        sync_request: SyncRequest
    ) -> SyncResponse:
        """
        Start a synchronization operation.
        
        Args:
            user_id: User requesting the sync
            integration_id: Integration to sync
            sync_request: Sync configuration
            
        Returns:
            Sync operation details
            
        Raises:
            NotFoundError: If integration doesn't exist
            IntegrationError: If integration is not healthy
        """
        
        # Get integration
        integration = await self._get_user_integration(user_id, integration_id)
        
        # Check if integration is healthy
        if integration.status != IntegrationStatus.ACTIVE:
            raise IntegrationError(
                f"Integration {integration_id} is not active (status: {integration.status})"
            )
        
        # Check if sync is already in progress
        if not sync_request.force:
            recent_sync = await self._get_recent_sync(integration_id, sync_request.operation_type)
            if recent_sync and recent_sync.status == SyncStatus.IN_PROGRESS:
                raise IntegrationError(
                    f"Sync operation {sync_request.operation_type} is already in progress"
                )
        
        # Create sync log
        sync_log = SyncLog(
            integration_id=integration_id,
            operation_type=sync_request.operation_type,
            status=SyncStatus.PENDING,
            sync_data={
                "force": sync_request.force,
                "dry_run": sync_request.dry_run,
                "filters": sync_request.filters or {},
                "user_id": str(user_id)
            }
        )
        
        self.db.add(sync_log)
        await self.db.commit()
        await self.db.refresh(sync_log)
        
        # Start background sync task
        # Note: In a real implementation, this would queue a Celery task
        asyncio.create_task(
            self._execute_sync(integration, sync_log, sync_request)
        )
        
        logger.info(f"Started sync {sync_log.id} for integration {integration_id}")
        
        return SyncResponse(
            sync_id=sync_log.id,
            status=SyncStatus.PENDING,
            message=f"Sync operation {sync_request.operation_type} started",
            estimated_duration=self._estimate_sync_duration(
                integration.integration_type,
                sync_request.operation_type
            )
        )
    
    async def get_sync_logs(
        self,
        user_id: UUID,
        integration_id: Optional[UUID] = None,
        operation_type: Optional[str] = None,
        status: Optional[SyncStatus] = None,
        limit: int = 50
    ) -> List[SyncLogResponse]:
        """
        Get sync logs for user's integrations.
        
        Args:
            user_id: User to get logs for
            integration_id: Optional filter by integration
            operation_type: Optional filter by operation type
            status: Optional filter by status
            limit: Maximum number of logs to return
            
        Returns:
            List of sync logs
        """
        
        # Build query
        query = (
            select(SyncLog)
            .join(Integration)
            .where(Integration.user_id == user_id)
        )
        
        if integration_id:
            query = query.where(SyncLog.integration_id == integration_id)
        
        if operation_type:
            query = query.where(SyncLog.operation_type == operation_type)
        
        if status:
            query = query.where(SyncLog.status == status)
        
        query = query.order_by(desc(SyncLog.started_at)).limit(limit)
        
        result = await self.db.execute(query)
        sync_logs = result.scalars().all()
        
        return [self._sync_log_to_response(log) for log in sync_logs]
    
    async def get_integration_stats(self, user_id: UUID) -> IntegrationStats:
        """
        Get integration statistics for a user.
        
        Args:
            user_id: User to get stats for
            
        Returns:
            Integration statistics
        """
        
        # Get integration counts
        integration_counts = await self.db.execute(
            select(
                func.count(Integration.id).label("total"),
                func.count(Integration.id).filter(
                    Integration.status == IntegrationStatus.ACTIVE
                ).label("active"),
                func.count(Integration.id).filter(
                    Integration.status == IntegrationStatus.ERROR
                ).label("failed")
            ).where(Integration.user_id == user_id)
        )
        
        counts = integration_counts.first()
        
        # Get today's sync stats
        today = datetime.utcnow().date()
        sync_stats = await self.db.execute(
            select(
                func.count(SyncLog.id).label("total_syncs"),
                func.count(SyncLog.id).filter(
                    SyncLog.status == SyncStatus.COMPLETED
                ).label("successful_syncs"),
                func.avg(
                    func.extract('epoch', SyncLog.completed_at - SyncLog.started_at)
                ).label("avg_duration")
            )
            .join(Integration)
            .where(
                and_(
                    Integration.user_id == user_id,
                    func.date(SyncLog.started_at) == today
                )
            )
        )
        
        sync_data = sync_stats.first()
        
        # Get recent errors
        recent_errors = await self.db.execute(
            select(SyncLog.error_message)
            .join(Integration)
            .where(
                and_(
                    Integration.user_id == user_id,
                    SyncLog.status == SyncStatus.FAILED,
                    SyncLog.error_message.isnot(None)
                )
            )
            .order_by(desc(SyncLog.started_at))
            .limit(5)
        )
        
        errors = [row[0] for row in recent_errors.fetchall()]
        
        return IntegrationStats(
            total_integrations=counts.total or 0,
            active_integrations=counts.active or 0,
            failed_integrations=counts.failed or 0,
            total_syncs_today=sync_data.total_syncs or 0,
            successful_syncs_today=sync_data.successful_syncs or 0,
            average_sync_duration=sync_data.avg_duration,
            most_active_integration=None,  # TODO: Implement
            recent_errors=errors
        )
    
    async def check_integration_health(
        self,
        user_id: UUID,
        integration_id: UUID
    ) -> IntegrationHealth:
        """
        Check the health of an integration.
        
        Args:
            user_id: User who owns the integration
            integration_id: Integration to check
            
        Returns:
            Integration health status
        """
        
        integration = await self._get_user_integration(user_id, integration_id)
        
        # Check token status
        token_status = "missing"
        if integration.access_token:
            if integration.is_token_expired():
                token_status = "expired"
            elif integration.needs_refresh():
                token_status = "expiring_soon"
            else:
                token_status = "valid"
        
        # Check webhook status
        webhook_status = "not_configured"
        if integration.webhook_id:
            webhook_status = "active"  # TODO: Verify webhook is actually active
        
        # Determine overall health
        is_healthy = (
            integration.status == IntegrationStatus.ACTIVE and
            token_status in ["valid", "expiring_soon"] and
            integration.error_count < 3
        )
        
        # Generate recommendations
        recommendations = []
        if token_status == "expired":
            recommendations.append("Refresh OAuth token")
        elif token_status == "expiring_soon":
            recommendations.append("Token expires soon - consider refreshing")
        
        if integration.error_count > 0:
            recommendations.append(f"Recent errors detected ({integration.error_count})")
        
        if webhook_status == "not_configured":
            recommendations.append("Configure webhook for real-time sync")
        
        return IntegrationHealth(
            integration_id=integration_id,
            integration_type=integration.integration_type,
            status=integration.status,
            is_healthy=is_healthy,
            last_successful_sync=integration.last_sync_at,
            error_count=integration.error_count,
            token_status=token_status,
            webhook_status=webhook_status,
            recommendations=recommendations
        )
    
    # Private helper methods
    
    async def _get_user_integration(self, user_id: UUID, integration_id: UUID) -> Integration:
        """Get integration ensuring it belongs to the user."""
        result = await self.db.execute(
            select(Integration).where(
                and_(
                    Integration.id == integration_id,
                    Integration.user_id == user_id
                )
            )
        )
        
        integration = result.scalar_one_or_none()
        if not integration:
            raise NotFoundError(f"Integration {integration_id} not found")
        
        return integration
    
    async def _to_response(self, integration: Integration) -> IntegrationResponse:
        """Convert integration model to response schema."""
        response_data = {
            "id": integration.id,
            "user_id": integration.user_id,
            "integration_type": integration.integration_type,
            "name": integration.name,
            "description": integration.description,
            "external_id": integration.external_id,
            "config": integration.config,
            "sync_settings": integration.sync_settings,
            "status": integration.status,
            "last_sync_at": integration.last_sync_at,
            "last_error": integration.last_error,
            "error_count": integration.error_count,
            "total_syncs": integration.total_syncs,
            "successful_syncs": integration.successful_syncs,
            "webhook_id": integration.webhook_id,
            "token_expires_at": integration.token_expires_at,
            "created_at": integration.created_at,
            "updated_at": integration.updated_at,
            "is_token_expired": integration.is_token_expired(),
            "needs_refresh": integration.needs_refresh(),
            "success_rate": (
                integration.successful_syncs / integration.total_syncs
                if integration.total_syncs > 0 else None
            )
        }
        
        return IntegrationResponse(**response_data)
    
    def _sync_log_to_response(self, sync_log: SyncLog) -> SyncLogResponse:
        """Convert sync log model to response schema."""
        return SyncLogResponse(
            id=sync_log.id,
            integration_id=sync_log.integration_id,
            operation_type=sync_log.operation_type,
            status=sync_log.status,
            started_at=sync_log.started_at,
            completed_at=sync_log.completed_at,
            items_processed=sync_log.items_processed,
            items_created=sync_log.items_created,
            items_updated=sync_log.items_updated,
            items_deleted=sync_log.items_deleted,
            conflicts_detected=sync_log.conflicts_detected,
            error_message=sync_log.error_message,
            error_details=sync_log.error_details,
            sync_data=sync_log.sync_data,
            created_at=sync_log.created_at,
            updated_at=sync_log.updated_at,
            duration_seconds=sync_log.duration_seconds
        )
    
    async def _get_recent_sync(
        self,
        integration_id: UUID,
        operation_type: str,
        minutes: int = 5
    ) -> Optional[SyncLog]:
        """Get recent sync operation if any."""
        cutoff = datetime.utcnow() - timedelta(minutes=minutes)
        
        result = await self.db.execute(
            select(SyncLog).where(
                and_(
                    SyncLog.integration_id == integration_id,
                    SyncLog.operation_type == operation_type,
                    SyncLog.started_at >= cutoff
                )
            ).order_by(desc(SyncLog.started_at))
        )
        
        return result.scalar_one_or_none()
    
    def _estimate_sync_duration(
        self,
        integration_type: IntegrationType,
        operation_type: str
    ) -> Optional[int]:
        """Estimate sync duration based on integration type and operation."""
        # Simple estimation - in real implementation, this would use historical data
        estimates = {
            IntegrationType.GOOGLE_CALENDAR: {"import": 30, "export": 15},
            IntegrationType.TODOIST: {"import": 45, "export": 20},
            IntegrationType.NOTION: {"import": 60, "export": 30},
        }
        
        return estimates.get(integration_type, {}).get(operation_type, 30)
    
    async def _execute_sync(
        self,
        integration: Integration,
        sync_log: SyncLog,
        sync_request: SyncRequest
    ):
        """Execute the actual sync operation (placeholder)."""
        # This is a placeholder - in real implementation, this would:
        # 1. Update sync_log status to IN_PROGRESS
        # 2. Call the appropriate integration service
        # 3. Process the results
        # 4. Update sync_log with results
        # 5. Update integration last_sync_at
        
        try:
            sync_log.status = SyncStatus.IN_PROGRESS
            await self.db.commit()
            
            # Simulate sync work
            await asyncio.sleep(2)
            
            # Mark as completed
            sync_log.mark_completed()
            sync_log.items_processed = 10
            sync_log.items_created = 5
            sync_log.items_updated = 3
            
            integration.last_sync_at = datetime.utcnow()
            integration.total_syncs += 1
            integration.successful_syncs += 1
            integration.error_count = 0
            
            await self.db.commit()
            
        except Exception as e:
            sync_log.mark_failed(str(e))
            integration.error_count += 1
            integration.last_error = str(e)
            
            await self.db.commit()
            logger.error(f"Sync failed for integration {integration.id}: {e}")
