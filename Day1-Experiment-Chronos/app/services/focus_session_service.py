"""
Focus session service for ADHD-optimized productivity sessions.

This service provides flexible focus sessions, body doubling support,
hyperfocus management, and interruption-friendly timing designed for ADHD users.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from uuid import UUID, uuid4
from enum import Enum
import asyncio
import json

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_

from app.models.user import User
from app.core.metrics import metrics_collector


class FocusSessionType(str, Enum):
    """Types of focus sessions optimized for ADHD."""
    POMODORO = "pomodoro"              # Traditional 25-min focused work
    MICRO_FOCUS = "micro_focus"        # 5-15 min for low energy
    DEEP_WORK = "deep_work"            # 45-90 min for hyperfocus
    BODY_DOUBLING = "body_doubling"    # Virtual co-working session
    CREATIVE_FLOW = "creative_flow"    # Open-ended creative work
    TASK_SPRINT = "task_sprint"        # Quick task completion burst
    GENTLE_FOCUS = "gentle_focus"      # Low-pressure, flexible timing


class SessionState(str, Enum):
    """Focus session states."""
    PLANNING = "planning"              # Setting up the session
    ACTIVE = "active"                  # Currently in focus mode
    BREAK = "break"                    # Taking a break
    PAUSED = "paused"                  # Temporarily paused
    COMPLETED = "completed"            # Successfully finished
    INTERRUPTED = "interrupted"        # Ended due to interruption
    EXTENDED = "extended"              # Extended beyond planned time


class InterruptionType(str, Enum):
    """Types of interruptions during focus sessions."""
    EXTERNAL = "external"              # Phone, person, notification
    INTERNAL = "internal"              # Mind wandering, distraction
    URGENT = "urgent"                  # Genuine urgent matter
    HYPERFOCUS_BREAK = "hyperfocus_break"  # Intentional break during hyperfocus
    ENERGY_DEPLETION = "energy_depletion"  # Ran out of mental energy


class FocusSession:
    """Represents an active focus session."""
    
    def __init__(self, session_id: str, user_id: UUID, session_type: FocusSessionType,
                 planned_duration: int, task_description: str = ""):
        self.session_id = session_id
        self.user_id = user_id
        self.session_type = session_type
        self.planned_duration = planned_duration  # in minutes
        self.task_description = task_description
        
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.state = SessionState.PLANNING
        
        self.interruptions: List[Dict] = []
        self.breaks: List[Dict] = []
        self.extensions: List[Dict] = []
        
        self.actual_focus_time = 0  # minutes of actual focused work
        self.total_session_time = 0  # total time including breaks
        
        self.energy_levels: List[Dict] = []  # Track energy throughout session
        self.productivity_rating: Optional[int] = None  # 1-10 post-session rating


class FocusSessionManager:
    """Manages ADHD-optimized focus sessions."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.active_sessions: Dict[str, FocusSession] = {}
        self.session_templates = self._create_session_templates()
    
    def _create_session_templates(self) -> Dict[FocusSessionType, Dict]:
        """Create templates for different session types."""
        return {
            FocusSessionType.POMODORO: {
                "default_duration": 25,
                "break_duration": 5,
                "long_break_after": 4,
                "long_break_duration": 15,
                "flexibility": "low",
                "interruption_tolerance": "medium",
                "description": "Classic 25-minute focused work session"
            },
            FocusSessionType.MICRO_FOCUS: {
                "default_duration": 10,
                "break_duration": 3,
                "long_break_after": 3,
                "long_break_duration": 10,
                "flexibility": "high",
                "interruption_tolerance": "high",
                "description": "Short burst for low energy or difficult tasks"
            },
            FocusSessionType.DEEP_WORK: {
                "default_duration": 60,
                "break_duration": 10,
                "long_break_after": 2,
                "long_break_duration": 30,
                "flexibility": "medium",
                "interruption_tolerance": "low",
                "description": "Extended session for complex, engaging work"
            },
            FocusSessionType.BODY_DOUBLING: {
                "default_duration": 45,
                "break_duration": 10,
                "long_break_after": 3,
                "long_break_duration": 20,
                "flexibility": "high",
                "interruption_tolerance": "high",
                "description": "Virtual co-working with others for accountability"
            },
            FocusSessionType.CREATIVE_FLOW: {
                "default_duration": 90,
                "break_duration": 15,
                "long_break_after": 1,
                "long_break_duration": 30,
                "flexibility": "very_high",
                "interruption_tolerance": "low",
                "description": "Open-ended creative work session"
            },
            FocusSessionType.TASK_SPRINT: {
                "default_duration": 15,
                "break_duration": 5,
                "long_break_after": 2,
                "long_break_duration": 15,
                "flexibility": "low",
                "interruption_tolerance": "medium",
                "description": "Quick sprint to complete specific tasks"
            },
            FocusSessionType.GENTLE_FOCUS: {
                "default_duration": 20,
                "break_duration": 10,
                "long_break_after": 2,
                "long_break_duration": 20,
                "flexibility": "very_high",
                "interruption_tolerance": "very_high",
                "description": "Low-pressure session with maximum flexibility"
            }
        }
    
    async def recommend_session_type(self, user_id: UUID, current_energy: int,
                                   task_complexity: str, available_time: int) -> Dict:
        """
        Recommend optimal focus session type based on current state.
        
        Args:
            user_id: User identifier
            current_energy: Energy level 1-10
            task_complexity: Task complexity level
            available_time: Available time in minutes
            
        Returns:
            Recommended session configuration
        """
        recommendations = []
        
        # Energy-based recommendations
        if current_energy <= 3:
            recommendations.extend([
                (FocusSessionType.MICRO_FOCUS, 0.9),
                (FocusSessionType.GENTLE_FOCUS, 0.8)
            ])
        elif current_energy <= 6:
            recommendations.extend([
                (FocusSessionType.POMODORO, 0.8),
                (FocusSessionType.TASK_SPRINT, 0.7),
                (FocusSessionType.BODY_DOUBLING, 0.6)
            ])
        else:
            recommendations.extend([
                (FocusSessionType.DEEP_WORK, 0.9),
                (FocusSessionType.CREATIVE_FLOW, 0.8),
                (FocusSessionType.POMODORO, 0.7)
            ])
        
        # Task complexity adjustments
        if task_complexity in ["complex", "expert"]:
            # Boost deep work and creative flow for complex tasks
            recommendations = [(t, s + 0.1 if t in [FocusSessionType.DEEP_WORK, 
                                                   FocusSessionType.CREATIVE_FLOW] else s) 
                             for t, s in recommendations]
        
        # Time constraint adjustments
        recommendations = [(t, s if self.session_templates[t]["default_duration"] <= available_time else s * 0.5) 
                          for t, s in recommendations]
        
        # Sort by score and get top recommendation
        recommendations.sort(key=lambda x: x[1], reverse=True)
        top_recommendation = recommendations[0][0] if recommendations else FocusSessionType.POMODORO
        
        template = self.session_templates[top_recommendation]
        
        return {
            "recommended_type": top_recommendation,
            "confidence": recommendations[0][1] if recommendations else 0.5,
            "template": template,
            "alternatives": [{"type": t, "score": s} for t, s in recommendations[1:3]],
            "reasoning": self._generate_recommendation_reasoning(
                top_recommendation, current_energy, task_complexity, available_time
            )
        }
    
    async def start_session(self, user_id: UUID, session_type: FocusSessionType,
                          duration: int, task_description: str = "",
                          custom_settings: Dict = None) -> Dict:
        """
        Start a new focus session.
        
        Args:
            user_id: User identifier
            session_type: Type of focus session
            duration: Planned duration in minutes
            task_description: Description of work to be done
            custom_settings: Custom session settings
            
        Returns:
            Session information and guidance
        """
        session_id = str(uuid4())
        session = FocusSession(
            session_id=session_id,
            user_id=user_id,
            session_type=session_type,
            planned_duration=duration,
            task_description=task_description
        )
        
        session.start_time = datetime.utcnow()
        session.state = SessionState.ACTIVE
        
        self.active_sessions[session_id] = session
        
        # Record metrics
        metrics_collector.record_focus_session(
            duration=duration,
            session_type=session_type.value,
            interruption_count=0
        )
        
        # Generate session guidance
        template = self.session_templates[session_type]
        guidance = self._generate_session_guidance(session_type, template)
        
        return {
            "session_id": session_id,
            "session_type": session_type.value,
            "planned_duration": duration,
            "start_time": session.start_time.isoformat(),
            "state": session.state.value,
            "guidance": guidance,
            "break_schedule": self._calculate_break_schedule(duration, template),
            "success_tips": self._get_session_tips(session_type),
            "interruption_handling": self._get_interruption_guidance(session_type)
        }
    
    async def record_interruption(self, session_id: str, interruption_type: InterruptionType,
                                description: str = "", duration_minutes: int = 0) -> Dict:
        """
        Record an interruption during a focus session.
        
        Args:
            session_id: Session identifier
            interruption_type: Type of interruption
            description: Description of what happened
            duration_minutes: How long the interruption lasted
            
        Returns:
            Guidance for handling the interruption
        """
        if session_id not in self.active_sessions:
            raise ValueError(f"No active session found: {session_id}")
        
        session = self.active_sessions[session_id]
        
        interruption = {
            "type": interruption_type.value,
            "timestamp": datetime.utcnow().isoformat(),
            "description": description,
            "duration_minutes": duration_minutes
        }
        
        session.interruptions.append(interruption)
        
        # Generate appropriate response based on interruption type and session type
        response = self._handle_interruption(session, interruption_type, duration_minutes)
        
        return response
    
    async def take_break(self, session_id: str, break_type: str = "scheduled") -> Dict:
        """
        Start a break during a focus session.
        
        Args:
            session_id: Session identifier
            break_type: Type of break (scheduled, needed, hyperfocus)
            
        Returns:
            Break guidance and timing
        """
        if session_id not in self.active_sessions:
            raise ValueError(f"No active session found: {session_id}")
        
        session = self.active_sessions[session_id]
        session.state = SessionState.BREAK
        
        break_info = {
            "start_time": datetime.utcnow().isoformat(),
            "type": break_type,
            "session_id": session_id
        }
        
        session.breaks.append(break_info)
        
        template = self.session_templates[session.session_type]
        break_duration = template["break_duration"]
        
        # Adjust break duration based on session progress and type
        if break_type == "hyperfocus":
            break_duration = max(break_duration, 15)  # Longer breaks for hyperfocus
        elif break_type == "needed":
            break_duration = template["break_duration"] + 5  # Extra time when needed
        
        return {
            "break_type": break_type,
            "recommended_duration": break_duration,
            "activities": self._get_break_activities(session.session_type, break_type),
            "return_guidance": self._get_return_guidance(session.session_type),
            "session_progress": self._calculate_session_progress(session)
        }
    
    async def complete_session(self, session_id: str, productivity_rating: int = None,
                             notes: str = "") -> Dict:
        """
        Complete a focus session and generate insights.
        
        Args:
            session_id: Session identifier
            productivity_rating: User's productivity rating 1-10
            notes: Additional notes about the session
            
        Returns:
            Session summary and insights
        """
        if session_id not in self.active_sessions:
            raise ValueError(f"No active session found: {session_id}")
        
        session = self.active_sessions[session_id]
        session.end_time = datetime.utcnow()
        session.state = SessionState.COMPLETED
        session.productivity_rating = productivity_rating
        
        # Calculate session metrics
        total_time = (session.end_time - session.start_time).total_seconds() / 60
        interruption_time = sum(i.get("duration_minutes", 0) for i in session.interruptions)
        actual_focus_time = total_time - interruption_time
        
        session.actual_focus_time = actual_focus_time
        session.total_session_time = total_time
        
        # Record final metrics
        metrics_collector.record_focus_session(
            duration=actual_focus_time,
            session_type=session.session_type.value,
            interruption_count=len(session.interruptions)
        )
        
        # Generate insights
        insights = self._generate_session_insights(session)
        
        # Clean up
        del self.active_sessions[session_id]
        
        return {
            "session_id": session_id,
            "session_type": session.session_type.value,
            "planned_duration": session.planned_duration,
            "actual_focus_time": round(actual_focus_time, 1),
            "total_session_time": round(total_time, 1),
            "interruption_count": len(session.interruptions),
            "productivity_rating": productivity_rating,
            "efficiency_percentage": round((actual_focus_time / session.planned_duration) * 100, 1),
            "insights": insights,
            "achievements": self._calculate_achievements(session),
            "recommendations": self._generate_next_session_recommendations(session)
        }
    
    def _generate_recommendation_reasoning(self, session_type: FocusSessionType,
                                         energy: int, complexity: str, time: int) -> str:
        """Generate reasoning for session type recommendation."""
        reasons = []
        
        if energy <= 3:
            reasons.append("Low energy suggests shorter, gentler sessions")
        elif energy >= 8:
            reasons.append("High energy is perfect for longer, deeper work")
        
        if complexity in ["complex", "expert"]:
            reasons.append("Complex tasks benefit from extended focus periods")
        
        if time < 30:
            reasons.append("Limited time available suggests shorter sessions")
        
        return "; ".join(reasons) if reasons else "Balanced approach for current conditions"
    
    def _generate_session_guidance(self, session_type: FocusSessionType, template: Dict) -> Dict:
        """Generate guidance for starting a session."""
        base_guidance = {
            "preparation": [
                "Clear your workspace of distractions",
                "Have water and any needed materials ready",
                "Set your phone to do not disturb mode",
                "Take a few deep breaths to center yourself"
            ],
            "during_session": [
                "Focus on one task at a time",
                "If your mind wanders, gently redirect attention",
                "Use the interruption log if needed",
                "Remember: progress over perfection"
            ]
        }
        
        # Add session-specific guidance
        if session_type == FocusSessionType.POMODORO:
            base_guidance["specific"] = [
                "Work with intensity for the full 25 minutes",
                "Don't check email or messages during focus time",
                "Take the full break - your brain needs it"
            ]
        elif session_type == FocusSessionType.MICRO_FOCUS:
            base_guidance["specific"] = [
                "Even 10 minutes of focus is valuable",
                "Choose one small, specific task",
                "Celebrate completing this short session"
            ]
        elif session_type == FocusSessionType.DEEP_WORK:
            base_guidance["specific"] = [
                "Settle in for extended focus",
                "Choose your most important or complex work",
                "Trust the process - deep work takes time to develop"
            ]
        
        return base_guidance
    
    def _calculate_break_schedule(self, duration: int, template: Dict) -> List[Dict]:
        """Calculate when breaks should occur during the session."""
        breaks = []
        break_interval = template["default_duration"]
        break_duration = template["break_duration"]
        
        current_time = 0
        break_count = 0
        
        while current_time + break_interval < duration:
            current_time += break_interval
            break_count += 1
            
            # Determine if this should be a long break
            is_long_break = (break_count % template["long_break_after"]) == 0
            break_length = template["long_break_duration"] if is_long_break else break_duration
            
            breaks.append({
                "time_minutes": current_time,
                "duration": break_length,
                "type": "long" if is_long_break else "short"
            })
        
        return breaks
    
    def _get_session_tips(self, session_type: FocusSessionType) -> List[str]:
        """Get tips specific to the session type."""
        tips = {
            FocusSessionType.POMODORO: [
                "The timer is your friend - trust it completely",
                "If you finish early, use remaining time to review or plan",
                "Don't extend the session - take your earned break"
            ],
            FocusSessionType.MICRO_FOCUS: [
                "Small sessions build momentum for bigger ones",
                "Perfect for when you're feeling overwhelmed",
                "Success here counts just as much as longer sessions"
            ],
            FocusSessionType.DEEP_WORK: [
                "Allow yourself time to get into the flow",
                "Complex problems often need this extended time",
                "Your best insights often come in the second half"
            ]
        }
        
        return tips.get(session_type, [
            "Stay present and focused on your chosen task",
            "Be kind to yourself if attention wanders",
            "Every moment of focus is progress"
        ])
    
    def _get_interruption_guidance(self, session_type: FocusSessionType) -> Dict:
        """Get guidance for handling interruptions."""
        return {
            "external": "Politely defer if possible, or pause timer and handle quickly",
            "internal": "Note the thought and gently return to your task",
            "urgent": "Handle immediately, then decide whether to continue or restart",
            "general": "Interruptions are normal - the key is how you respond to them"
        }
    
    def _handle_interruption(self, session: FocusSession, interruption_type: InterruptionType,
                           duration: int) -> Dict:
        """Handle different types of interruptions."""
        template = self.session_templates[session.session_type]
        tolerance = template["interruption_tolerance"]
        
        if interruption_type == InterruptionType.EXTERNAL:
            if duration <= 2 and tolerance in ["medium", "high", "very_high"]:
                return {
                    "action": "continue",
                    "message": "Quick interruption handled well! Continue with your session.",
                    "tip": "You're building resilience to distractions."
                }
            else:
                return {
                    "action": "take_break",
                    "message": "That was a significant interruption. Take a short break to reset.",
                    "tip": "Use this break to clear your mind before refocusing."
                }
        
        elif interruption_type == InterruptionType.INTERNAL:
            return {
                "action": "note_and_continue",
                "message": "Mind wandering is normal! Gently return to your task.",
                "tip": "Each time you notice and redirect is building your focus muscle."
            }
        
        elif interruption_type == InterruptionType.URGENT:
            return {
                "action": "pause_and_handle",
                "message": "Handle the urgent matter, then decide whether to continue or restart.",
                "tip": "Urgent matters happen - don't let them derail your entire day."
            }
        
        return {
            "action": "assess",
            "message": "Take a moment to assess how you're feeling and what you need.",
            "tip": "Self-awareness is key to successful focus sessions."
        }
    
    def _get_break_activities(self, session_type: FocusSessionType, break_type: str) -> List[str]:
        """Get appropriate break activities."""
        activities = {
            "short": [
                "Stand up and stretch",
                "Take 5 deep breaths",
                "Look out a window",
                "Drink some water",
                "Do a quick body scan"
            ],
            "long": [
                "Take a short walk",
                "Do some light exercise",
                "Have a healthy snack",
                "Practice mindfulness",
                "Chat with a friend briefly"
            ],
            "hyperfocus": [
                "Step away from your workspace completely",
                "Get some fresh air",
                "Do something physical",
                "Eat something if needed",
                "Check in with your body's needs"
            ]
        }
        
        return activities.get(break_type, activities["short"])
    
    def _get_return_guidance(self, session_type: FocusSessionType) -> str:
        """Get guidance for returning from break."""
        guidance = {
            FocusSessionType.POMODORO: "Take a moment to refocus, then dive back in with energy",
            FocusSessionType.DEEP_WORK: "Ease back into your work - deep focus takes a moment to rebuild",
            FocusSessionType.MICRO_FOCUS: "You're refreshed and ready for another focused burst"
        }
        
        return guidance.get(session_type, "Take a breath and gently return to your task")
    
    def _calculate_session_progress(self, session: FocusSession) -> Dict:
        """Calculate current session progress."""
        if not session.start_time:
            return {"progress": 0, "time_remaining": session.planned_duration}
        
        elapsed = (datetime.utcnow() - session.start_time).total_seconds() / 60
        progress = min((elapsed / session.planned_duration) * 100, 100)
        remaining = max(session.planned_duration - elapsed, 0)
        
        return {
            "progress_percentage": round(progress, 1),
            "time_elapsed": round(elapsed, 1),
            "time_remaining": round(remaining, 1),
            "interruption_count": len(session.interruptions)
        }
    
    def _generate_session_insights(self, session: FocusSession) -> Dict:
        """Generate insights from completed session."""
        insights = []
        
        # Efficiency analysis
        efficiency = (session.actual_focus_time / session.planned_duration) * 100
        if efficiency >= 90:
            insights.append("Excellent focus! You stayed on task almost the entire time.")
        elif efficiency >= 70:
            insights.append("Good focus session with some natural breaks.")
        else:
            insights.append("This session had challenges, but every bit of focus counts.")
        
        # Interruption analysis
        if len(session.interruptions) == 0:
            insights.append("Perfect! No interruptions during this session.")
        elif len(session.interruptions) <= 2:
            insights.append("Minimal interruptions - you handled distractions well.")
        else:
            insights.append("Several interruptions occurred. Consider strategies to minimize them.")
        
        return {
            "efficiency_insights": insights,
            "focus_quality": "excellent" if efficiency >= 85 else "good" if efficiency >= 60 else "developing",
            "interruption_management": "excellent" if len(session.interruptions) <= 1 else "good" if len(session.interruptions) <= 3 else "needs_improvement"
        }
    
    def _calculate_achievements(self, session: FocusSession) -> List[Dict]:
        """Calculate achievements earned during the session."""
        achievements = []
        
        if len(session.interruptions) == 0:
            achievements.append({
                "name": "Distraction-Free Zone",
                "description": "Completed entire session without interruptions",
                "icon": "🎯"
            })
        
        if session.actual_focus_time >= session.planned_duration * 0.9:
            achievements.append({
                "name": "Focus Master",
                "description": "Maintained focus for 90%+ of planned time",
                "icon": "🧠"
            })
        
        if session.session_type == FocusSessionType.DEEP_WORK and session.actual_focus_time >= 45:
            achievements.append({
                "name": "Deep Diver",
                "description": "Completed extended deep work session",
                "icon": "🏊‍♀️"
            })
        
        return achievements
    
    def _generate_next_session_recommendations(self, session: FocusSession) -> Dict:
        """Generate recommendations for the next session."""
        recommendations = []
        
        efficiency = (session.actual_focus_time / session.planned_duration) * 100
        
        if efficiency >= 90:
            recommendations.append("You're in great flow! Consider a slightly longer session next time.")
        elif efficiency < 60:
            recommendations.append("Try a shorter session or different session type next time.")
        
        if len(session.interruptions) > 3:
            recommendations.append("Focus on minimizing distractions for your next session.")
        
        return {
            "next_session_type": session.session_type.value,  # Same type if successful
            "recommended_adjustments": recommendations,
            "optimal_timing": "Try scheduling your next session during your peak energy hours"
        }


class FocusSessionService:
    """Main service for focus session functionality."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.manager = FocusSessionManager(db)
    
    async def get_session_recommendations(self, user_id: UUID, current_energy: int,
                                        task_complexity: str, available_time: int) -> Dict:
        """Get session type recommendations."""
        return await self.manager.recommend_session_type(
            user_id, current_energy, task_complexity, available_time
        )
    
    async def start_focus_session(self, user_id: UUID, session_type: str,
                                duration: int, task_description: str = "") -> Dict:
        """Start a new focus session."""
        session_type_enum = FocusSessionType(session_type)
        return await self.manager.start_session(
            user_id, session_type_enum, duration, task_description
        )
    
    async def record_interruption(self, session_id: str, interruption_type: str,
                                description: str = "", duration: int = 0) -> Dict:
        """Record an interruption."""
        interruption_enum = InterruptionType(interruption_type)
        return await self.manager.record_interruption(
            session_id, interruption_enum, description, duration
        )
    
    async def take_break(self, session_id: str, break_type: str = "scheduled") -> Dict:
        """Take a break during session."""
        return await self.manager.take_break(session_id, break_type)
    
    async def complete_session(self, session_id: str, productivity_rating: int = None,
                             notes: str = "") -> Dict:
        """Complete a focus session."""
        return await self.manager.complete_session(session_id, productivity_rating, notes)

    async def get_active_sessions(self, user_id: UUID) -> Dict:
        """Get all active sessions for a user."""
        user_sessions = {
            session_id: {
                "session_id": session_id,
                "session_type": session.session_type.value,
                "planned_duration": session.planned_duration,
                "start_time": session.start_time.isoformat() if session.start_time else None,
                "state": session.state.value,
                "progress": self.manager._calculate_session_progress(session)
            }
            for session_id, session in self.manager.active_sessions.items()
            if session.user_id == user_id
        }

        return {
            "active_sessions": list(user_sessions.values()),
            "total_count": len(user_sessions)
        }
