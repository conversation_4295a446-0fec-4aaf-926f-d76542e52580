"""
Todoist integration for Project Chronos.

This module provides integration with Todoist API for
bidirectional synchronization of tasks and projects.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from urllib.parse import urlencode

from app.integrations.base import BaseIntegration, SyncResult, IntegrationError
from app.schemas.integration import OAuthTokens, TaskSync
from app.core.config import settings

logger = logging.getLogger(__name__)


class TodoistIntegration(BaseIntegration):
    """Todoist API integration."""
    
    def __init__(self):
        super().__init__()
        self.base_url = "https://api.todoist.com/rest/v2"
        self.oauth_url = "https://todoist.com/oauth/access_token"
        self.auth_url = "https://todoist.com/oauth/authorize"
        self.scopes = ["data:read_write"]
    
    async def authenticate(self, credentials: Dict[str, Any]) -> OAuthTokens:
        """
        Exchange OAuth authorization code for access tokens.
        
        Args:
            credentials: Dict containing 'code', 'redirect_uri', and optionally 'state'
            
        Returns:
            OAuth tokens for API access
        """
        
        token_data = {
            "client_id": settings.TODOIST_CLIENT_ID,
            "client_secret": settings.TODOIST_CLIENT_SECRET,
            "code": credentials["code"],
            "redirect_uri": credentials["redirect_uri"]
        }
        
        try:
            response = await self._make_request(
                method="POST",
                url=self.oauth_url,
                json_data=token_data
            )
            
            return OAuthTokens(
                access_token=response["access_token"],
                token_type="Bearer",
                # Todoist tokens don't expire, but we'll set a long expiration
                expires_in=365 * 24 * 3600  # 1 year
            )
            
        except Exception as e:
            logger.error(f"Todoist authentication failed: {e}")
            raise IntegrationError(f"Authentication failed: {e}")
    
    async def refresh_token(self, refresh_token: str) -> OAuthTokens:
        """
        Todoist tokens don't expire, so this is a no-op.
        
        Args:
            refresh_token: Not used for Todoist
            
        Returns:
            Original token information
        """
        
        raise IntegrationError("Todoist tokens do not require refresh")
    
    async def test_connection(self, access_token: str) -> bool:
        """
        Test connection to Todoist API.
        
        Args:
            access_token: OAuth access token
            
        Returns:
            True if connection is successful
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            await self._make_request(
                method="GET",
                url=f"{self.base_url}/projects",
                headers=headers,
                params={"limit": 1}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Todoist connection test failed: {e}")
            return False
    
    async def get_projects(self, access_token: str) -> List[Dict[str, Any]]:
        """
        Get list of user's projects.
        
        Args:
            access_token: OAuth access token
            
        Returns:
            List of project information
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            response = await self._make_request(
                method="GET",
                url=f"{self.base_url}/projects",
                headers=headers
            )
            
            projects = []
            for project in response:
                projects.append({
                    "id": project["id"],
                    "name": project["name"],
                    "color": project.get("color", "grey"),
                    "is_favorite": project.get("is_favorite", False),
                    "is_shared": project.get("is_shared", False),
                    "view_style": project.get("view_style", "list"),
                    "comment_count": project.get("comment_count", 0)
                })
            
            return projects
            
        except Exception as e:
            logger.error(f"Failed to get Todoist projects: {e}")
            raise IntegrationError(f"Failed to get projects: {e}")
    
    async def import_tasks(
        self,
        access_token: str,
        project_ids: Optional[List[str]] = None,
        completed: Optional[bool] = None
    ) -> SyncResult:
        """
        Import tasks from Todoist.
        
        Args:
            access_token: OAuth access token
            project_ids: Specific project IDs to import from
            completed: Filter by completion status
            
        Returns:
            Sync result with imported tasks
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            # Get all tasks
            params = {}
            if project_ids:
                params["project_id"] = project_ids[0]  # Todoist API takes single project_id
            
            all_tasks = []
            errors = []
            
            if project_ids:
                # Import from specific projects
                for project_id in project_ids:
                    try:
                        tasks = await self._get_tasks_for_project(access_token, project_id)
                        all_tasks.extend(tasks)
                    except Exception as e:
                        logger.error(f"Failed to import from project {project_id}: {e}")
                        errors.append(f"Project {project_id}: {e}")
            else:
                # Import all tasks
                response = await self._make_request(
                    method="GET",
                    url=f"{self.base_url}/tasks",
                    headers=headers
                )
                all_tasks = response
            
            # Filter by completion status if specified
            if completed is not None:
                all_tasks = [task for task in all_tasks if task.get("is_completed", False) == completed]
            
            # Convert to TaskSync format
            converted_tasks = []
            for task in all_tasks:
                try:
                    converted_task = self._convert_todoist_task(task)
                    if converted_task:
                        converted_tasks.append(converted_task)
                except Exception as e:
                    logger.error(f"Failed to convert task {task.get('id')}: {e}")
                    errors.append(f"Task conversion error: {e}")
            
            return SyncResult(
                success=len(errors) == 0,
                items_processed=len(all_tasks),
                items_created=len(converted_tasks),
                errors=errors,
                metadata={
                    "project_ids": project_ids,
                    "completed_filter": completed,
                    "tasks": converted_tasks
                }
            )
            
        except Exception as e:
            logger.error(f"Todoist import failed: {e}")
            return SyncResult(
                success=False,
                errors=[f"Import failed: {e}"]
            )
    
    async def export_tasks(
        self,
        access_token: str,
        tasks: List[TaskSync],
        project_id: Optional[str] = None
    ) -> SyncResult:
        """
        Export tasks to Todoist.
        
        Args:
            access_token: OAuth access token
            tasks: Tasks to export
            project_id: Target project ID (defaults to Inbox)
            
        Returns:
            Sync result with export status
        """
        
        try:
            headers = self._add_auth_header({}, access_token)
            
            # Get target project (default to Inbox)
            if not project_id:
                projects = await self.get_projects(access_token)
                inbox_projects = [p for p in projects if p["name"].lower() == "inbox"]
                if inbox_projects:
                    project_id = inbox_projects[0]["id"]
                else:
                    # Create Chronos project
                    project_data = {
                        "name": "Project Chronos",
                        "color": "blue"
                    }
                    project_response = await self._make_request(
                        method="POST",
                        url=f"{self.base_url}/projects",
                        headers=headers,
                        json_data=project_data
                    )
                    project_id = project_response["id"]
            
            created_count = 0
            updated_count = 0
            errors = []
            
            for task in tasks:
                try:
                    todoist_task = self._convert_to_todoist_task(task, project_id)
                    
                    if task.external_id:
                        # Update existing task
                        await self._make_request(
                            method="POST",
                            url=f"{self.base_url}/tasks/{task.external_id}",
                            headers=headers,
                            json_data=todoist_task
                        )
                        updated_count += 1
                    else:
                        # Create new task
                        response = await self._make_request(
                            method="POST",
                            url=f"{self.base_url}/tasks",
                            headers=headers,
                            json_data=todoist_task
                        )
                        created_count += 1
                        
                        # Store the Todoist task ID for future updates
                        task.external_id = response["id"]
                        
                except Exception as e:
                    logger.error(f"Failed to export task {task.title}: {e}")
                    errors.append(f"Task '{task.title}': {e}")
            
            return SyncResult(
                success=len(errors) == 0,
                items_processed=len(tasks),
                items_created=created_count,
                items_updated=updated_count,
                errors=errors,
                metadata={
                    "project_id": project_id,
                    "exported_tasks": [
                        {"title": task.title, "external_id": task.external_id}
                        for task in tasks
                    ]
                }
            )
            
        except Exception as e:
            logger.error(f"Todoist export failed: {e}")
            return SyncResult(
                success=False,
                errors=[f"Export failed: {e}"]
            )
    
    # Private helper methods
    
    async def _get_tasks_for_project(
        self,
        access_token: str,
        project_id: str
    ) -> List[Dict[str, Any]]:
        """Get tasks for a specific project."""
        
        headers = self._add_auth_header({}, access_token)
        
        response = await self._make_request(
            method="GET",
            url=f"{self.base_url}/tasks",
            headers=headers,
            params={"project_id": project_id}
        )
        
        return response
    
    def _convert_todoist_task(self, todoist_task: Dict[str, Any]) -> Optional[TaskSync]:
        """Convert Todoist task to TaskSync."""
        
        try:
            # Parse due date
            due_date = None
            if todoist_task.get("due"):
                due_info = todoist_task["due"]
                if due_info.get("datetime"):
                    due_date = self._parse_datetime(due_info["datetime"])
                elif due_info.get("date"):
                    due_date = self._parse_datetime(due_info["date"] + "T23:59:59Z")
            
            # Map priority (Todoist uses 1-4, we use low/medium/high/urgent)
            priority_map = {1: "low", 2: "medium", 3: "high", 4: "urgent"}
            priority = priority_map.get(todoist_task.get("priority", 1), "medium")
            
            # Extract labels
            labels = todoist_task.get("labels", [])
            
            # ADHD-specific field mapping
            energy_level = "medium"  # Default
            complexity = "medium"    # Default
            estimated_duration = None
            
            # Check task content and labels for ADHD keywords
            content = todoist_task.get("content", "")
            description = todoist_task.get("description", "")
            
            # Energy level detection
            if any(keyword in content.lower() for keyword in ["focus", "deep", "complex", "difficult"]):
                energy_level = "high"
                complexity = "high"
            elif any(keyword in content.lower() for keyword in ["quick", "easy", "simple", "5min"]):
                energy_level = "low"
                complexity = "low"
                estimated_duration = 15
            
            # Duration estimation from labels or content
            for label in labels:
                if "min" in label.lower():
                    try:
                        duration = int(''.join(filter(str.isdigit, label)))
                        if duration > 0:
                            estimated_duration = duration
                    except ValueError:
                        pass
            
            # Check for time estimates in content
            import re
            time_match = re.search(r'(\d+)\s*(min|hour|hr)', content.lower())
            if time_match and not estimated_duration:
                duration = int(time_match.group(1))
                unit = time_match.group(2)
                if unit in ["hour", "hr"]:
                    duration *= 60
                estimated_duration = duration
            
            return TaskSync(
                external_id=todoist_task["id"],
                title=content,
                description=description,
                due_date=due_date,
                completed=todoist_task.get("is_completed", False),
                priority=priority,
                labels=labels,
                project_id=todoist_task.get("project_id"),
                energy_level=energy_level,
                complexity=complexity,
                estimated_duration=estimated_duration,
                chunk_size=30 if complexity == "high" else None  # 30-min chunks for complex tasks
            )
            
        except Exception as e:
            logger.error(f"Error converting Todoist task: {e}")
            return None
    
    def _convert_to_todoist_task(self, task: TaskSync, project_id: str) -> Dict[str, Any]:
        """Convert TaskSync to Todoist task format."""
        
        todoist_task = {
            "content": task.title,
            "project_id": project_id
        }
        
        if task.description:
            todoist_task["description"] = task.description
        
        if task.due_date:
            todoist_task["due_string"] = task.due_date.strftime("%Y-%m-%d %H:%M")
        
        # Map priority
        priority_map = {"low": 1, "medium": 2, "high": 3, "urgent": 4}
        todoist_task["priority"] = priority_map.get(task.priority, 2)
        
        # Add ADHD-specific information to description
        adhd_info = []
        if task.energy_level:
            adhd_info.append(f"Energy Level: {task.energy_level}")
        if task.complexity:
            adhd_info.append(f"Complexity: {task.complexity}")
        if task.estimated_duration:
            adhd_info.append(f"Estimated Duration: {task.estimated_duration} minutes")
        if task.chunk_size:
            adhd_info.append(f"Chunk Size: {task.chunk_size} minutes")
        
        if adhd_info:
            description = todoist_task.get("description", "")
            if description:
                description += "\n\n"
            description += "ADHD Info:\n" + "\n".join(adhd_info)
            todoist_task["description"] = description
        
        # Add labels for ADHD metadata
        labels = list(task.labels) if task.labels else []
        if task.energy_level and task.energy_level != "medium":
            labels.append(f"energy-{task.energy_level}")
        if task.estimated_duration:
            labels.append(f"{task.estimated_duration}min")
        
        if labels:
            todoist_task["labels"] = labels
        
        return todoist_task
    
    def get_oauth_url(self, redirect_uri: str, state: Optional[str] = None) -> str:
        """Generate OAuth authorization URL."""
        
        params = {
            "client_id": settings.TODOIST_CLIENT_ID,
            "scope": ",".join(self.scopes),
            "state": state or "default"
        }
        
        return f"{self.auth_url}?{urlencode(params)}"
