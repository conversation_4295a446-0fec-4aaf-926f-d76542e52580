"""
Base integration class for Project Chronos.

This module provides the abstract base class that all external service
integrations should inherit from, ensuring consistent interfaces.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

import aiohttp
from pydantic import BaseModel

from app.core.config import settings
from app.schemas.integration import (
    OAuthTokens,
    CalendarEventSync,
    TaskSync,
    ConflictResolution,
)

logger = logging.getLogger(__name__)


class IntegrationError(Exception):
    """Base exception for integration errors."""
    pass


class AuthenticationError(IntegrationError):
    """Authentication-related integration error."""
    pass


class RateLimitError(IntegrationError):
    """Rate limiting error from external service."""
    pass


class SyncResult(BaseModel):
    """Result of a synchronization operation."""
    
    success: bool
    items_processed: int = 0
    items_created: int = 0
    items_updated: int = 0
    items_deleted: int = 0
    conflicts: List[ConflictResolution] = []
    errors: List[str] = []
    metadata: Dict[str, Any] = {}


class BaseIntegration(ABC):
    """
    Abstract base class for external service integrations.
    
    All integration classes should inherit from this base class
    and implement the required abstract methods.
    """
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.base_url: str = ""
        self.api_version: str = "v1"
        self.rate_limit_delay: float = 0.1  # Default delay between requests
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self._create_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self._close_session()
    
    async def _create_session(self):
        """Create HTTP session for API requests."""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers=self._get_default_headers()
            )
    
    async def _close_session(self):
        """Close HTTP session."""
        if self.session:
            await self.session.close()
            self.session = None
    
    def _get_default_headers(self) -> Dict[str, str]:
        """Get default HTTP headers for API requests."""
        return {
            "User-Agent": f"ProjectChronos/{settings.VERSION}",
            "Accept": "application/json",
            "Content-Type": "application/json",
        }
    
    async def _make_request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        retry_count: int = 3
    ) -> Dict[str, Any]:
        """
        Make HTTP request with error handling and retries.
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            url: Request URL
            headers: Additional headers
            params: Query parameters
            json_data: JSON request body
            retry_count: Number of retries on failure
            
        Returns:
            Response JSON data
            
        Raises:
            AuthenticationError: If authentication fails
            RateLimitError: If rate limited
            IntegrationError: For other API errors
        """
        
        if not self.session:
            await self._create_session()
        
        request_headers = self._get_default_headers()
        if headers:
            request_headers.update(headers)
        
        for attempt in range(retry_count + 1):
            try:
                # Add rate limiting delay
                if attempt > 0:
                    await asyncio.sleep(self.rate_limit_delay * (2 ** attempt))
                
                async with self.session.request(
                    method=method,
                    url=url,
                    headers=request_headers,
                    params=params,
                    json=json_data
                ) as response:
                    
                    # Handle rate limiting
                    if response.status == 429:
                        retry_after = int(response.headers.get("Retry-After", 60))
                        if attempt < retry_count:
                            logger.warning(f"Rate limited, retrying after {retry_after}s")
                            await asyncio.sleep(retry_after)
                            continue
                        else:
                            raise RateLimitError("Rate limit exceeded")
                    
                    # Handle authentication errors
                    if response.status == 401:
                        raise AuthenticationError("Authentication failed")
                    
                    # Handle other client errors
                    if 400 <= response.status < 500:
                        error_text = await response.text()
                        raise IntegrationError(f"Client error {response.status}: {error_text}")
                    
                    # Handle server errors
                    if response.status >= 500:
                        if attempt < retry_count:
                            logger.warning(f"Server error {response.status}, retrying...")
                            continue
                        else:
                            error_text = await response.text()
                            raise IntegrationError(f"Server error {response.status}: {error_text}")
                    
                    # Success - return JSON data
                    return await response.json()
                    
            except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                if attempt < retry_count:
                    logger.warning(f"Request failed, retrying: {e}")
                    continue
                else:
                    raise IntegrationError(f"Request failed after {retry_count} retries: {e}")
        
        raise IntegrationError("Request failed after all retries")
    
    # Abstract methods that must be implemented by subclasses
    
    @abstractmethod
    async def authenticate(self, credentials: Dict[str, Any]) -> OAuthTokens:
        """
        Authenticate with the external service.
        
        Args:
            credentials: Authentication credentials (OAuth code, tokens, etc.)
            
        Returns:
            OAuth tokens for API access
        """
        pass
    
    @abstractmethod
    async def refresh_token(self, refresh_token: str) -> OAuthTokens:
        """
        Refresh OAuth access token.
        
        Args:
            refresh_token: OAuth refresh token
            
        Returns:
            New OAuth tokens
        """
        pass
    
    @abstractmethod
    async def test_connection(self, access_token: str) -> bool:
        """
        Test if the connection to the external service is working.
        
        Args:
            access_token: OAuth access token
            
        Returns:
            True if connection is successful
        """
        pass
    
    # Optional methods that can be overridden by subclasses
    
    async def import_calendar_events(
        self,
        access_token: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        calendar_ids: Optional[List[str]] = None
    ) -> SyncResult:
        """
        Import calendar events from external service.
        
        Args:
            access_token: OAuth access token
            start_date: Start date for event import
            end_date: End date for event import
            calendar_ids: Specific calendar IDs to import from
            
        Returns:
            Sync result with imported events
        """
        
        return SyncResult(
            success=False,
            errors=["Calendar import not implemented for this integration"]
        )
    
    async def export_calendar_events(
        self,
        access_token: str,
        events: List[CalendarEventSync],
        calendar_id: Optional[str] = None
    ) -> SyncResult:
        """
        Export calendar events to external service.
        
        Args:
            access_token: OAuth access token
            events: Events to export
            calendar_id: Target calendar ID
            
        Returns:
            Sync result with export status
        """
        
        return SyncResult(
            success=False,
            errors=["Calendar export not implemented for this integration"]
        )
    
    async def import_tasks(
        self,
        access_token: str,
        project_ids: Optional[List[str]] = None,
        completed: Optional[bool] = None
    ) -> SyncResult:
        """
        Import tasks from external service.
        
        Args:
            access_token: OAuth access token
            project_ids: Specific project IDs to import from
            completed: Filter by completion status
            
        Returns:
            Sync result with imported tasks
        """
        
        return SyncResult(
            success=False,
            errors=["Task import not implemented for this integration"]
        )
    
    async def export_tasks(
        self,
        access_token: str,
        tasks: List[TaskSync],
        project_id: Optional[str] = None
    ) -> SyncResult:
        """
        Export tasks to external service.
        
        Args:
            access_token: OAuth access token
            tasks: Tasks to export
            project_id: Target project ID
            
        Returns:
            Sync result with export status
        """
        
        return SyncResult(
            success=False,
            errors=["Task export not implemented for this integration"]
        )
    
    async def setup_webhook(
        self,
        access_token: str,
        webhook_url: str,
        events: List[str]
    ) -> str:
        """
        Set up webhook for real-time notifications.
        
        Args:
            access_token: OAuth access token
            webhook_url: URL to receive webhook notifications
            events: List of events to subscribe to
            
        Returns:
            Webhook ID for management
        """
        
        raise IntegrationError("Webhook setup not implemented for this integration")
    
    async def delete_webhook(
        self,
        access_token: str,
        webhook_id: str
    ) -> bool:
        """
        Delete a webhook subscription.
        
        Args:
            access_token: OAuth access token
            webhook_id: Webhook ID to delete
            
        Returns:
            True if deletion was successful
        """
        
        raise IntegrationError("Webhook deletion not implemented for this integration")
    
    async def process_webhook_event(
        self,
        event_data: Dict[str, Any],
        headers: Dict[str, str]
    ) -> SyncResult:
        """
        Process incoming webhook event.
        
        Args:
            event_data: Webhook event payload
            headers: HTTP headers from webhook request
            
        Returns:
            Sync result from processing the event
        """
        
        return SyncResult(
            success=False,
            errors=["Webhook processing not implemented for this integration"]
        )
    
    # Utility methods
    
    def _add_auth_header(self, headers: Dict[str, str], access_token: str) -> Dict[str, str]:
        """Add OAuth authorization header."""
        headers = headers.copy()
        headers["Authorization"] = f"Bearer {access_token}"
        return headers
    
    def _parse_datetime(self, date_string: str) -> Optional[datetime]:
        """Parse datetime string from external service."""
        if not date_string:
            return None
        
        # Common datetime formats
        formats = [
            "%Y-%m-%dT%H:%M:%S.%fZ",  # ISO with microseconds
            "%Y-%m-%dT%H:%M:%SZ",     # ISO without microseconds
            "%Y-%m-%dT%H:%M:%S%z",    # ISO with timezone
            "%Y-%m-%d %H:%M:%S",      # Simple format
            "%Y-%m-%d",               # Date only
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(date_string, fmt)
            except ValueError:
                continue
        
        logger.warning(f"Could not parse datetime: {date_string}")
        return None
    
    def _format_datetime(self, dt: datetime) -> str:
        """Format datetime for external service."""
        return dt.isoformat() + "Z"
    
    def _detect_conflicts(
        self,
        local_items: List[Dict[str, Any]],
        remote_items: List[Dict[str, Any]],
        id_field: str = "id"
    ) -> List[ConflictResolution]:
        """
        Detect conflicts between local and remote items.
        
        Args:
            local_items: Items from Project Chronos
            remote_items: Items from external service
            id_field: Field name for item ID
            
        Returns:
            List of detected conflicts
        """
        
        conflicts = []
        local_by_id = {item[id_field]: item for item in local_items}
        
        for remote_item in remote_items:
            item_id = remote_item.get(id_field)
            if item_id in local_by_id:
                local_item = local_by_id[item_id]
                
                # Simple conflict detection based on modification time
                local_modified = local_item.get("updated_at")
                remote_modified = remote_item.get("updated_at")
                
                if local_modified and remote_modified:
                    local_dt = self._parse_datetime(local_modified)
                    remote_dt = self._parse_datetime(remote_modified)
                    
                    if local_dt and remote_dt and abs((local_dt - remote_dt).total_seconds()) > 60:
                        conflicts.append(ConflictResolution(
                            conflict_id=f"conflict_{item_id}",
                            conflict_type="modification_time",
                            local_item=local_item,
                            remote_item=remote_item,
                            resolution_strategy="manual"
                        ))
        
        return conflicts
