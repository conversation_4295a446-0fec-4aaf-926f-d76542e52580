"""
WebSocket endpoints for Project Chronos.

This module provides WebSocket endpoints for real-time communication
in body doubling sessions and collaborative features.
"""

import logging
from uuid import UUID

from fastapi import WebSocket, WebSocketDisconnect, Depends, Query, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth import get_current_user_from_token
from app.services.websocket_service import WebSocketService
from app.core.exceptions import AuthenticationError

logger = logging.getLogger(__name__)


async def authenticate_websocket_user(
    token: str,
    db: AsyncSession = Depends(get_db)
):
    """
    Authenticate user from WebSocket token.
    
    Args:
        token: Authentication token
        db: Database session
        
    Returns:
        User: Authenticated user
        
    Raises:
        AuthenticationError: If authentication fails
    """
    try:
        # For now, extract user_id from token (in production, use proper JWT)
        if not token.startswith("ws_token_"):
            raise AuthenticationError("Invalid token format")
        
        # Extract user_id from token (placeholder implementation)
        parts = token.split("_")
        if len(parts) < 3:
            raise AuthenticationError("Invalid token format")
        
        user_id = UUID(parts[2])
        
        # In production, verify JWT token and get user from database
        # For now, return user_id (this is a simplified implementation)
        return user_id
        
    except (ValueError, IndexError) as e:
        raise AuthenticationError(f"Invalid token: {e}")


async def websocket_body_doubling_session(
    websocket: WebSocket,
    session_id: UUID,
    token: str = Query(..., description="Authentication token"),
    db: AsyncSession = Depends(get_db)
):
    """
    WebSocket endpoint for body doubling sessions.
    
    Provides real-time communication for virtual body doubling,
    including progress sharing, encouragement, and focus synchronization.
    
    Args:
        websocket: WebSocket connection
        session_id: Body doubling session ID
        token: Authentication token
        db: Database session
    """
    try:
        # Authenticate user
        user_id = await authenticate_websocket_user(token, db)
        
        # Create WebSocket service
        ws_service = WebSocketService(db)
        
        # Handle the connection
        await ws_service.handle_connection(websocket, user_id, session_id)
        
    except AuthenticationError as e:
        logger.warning(f"WebSocket authentication failed: {e}")
        await websocket.close(code=4001, reason="Authentication failed")
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for session {session_id}")
    except Exception as e:
        logger.error(f"WebSocket error for session {session_id}: {e}")
        try:
            await websocket.close(code=1011, reason="Internal server error")
        except:
            pass


async def websocket_general_updates(
    websocket: WebSocket,
    token: str = Query(..., description="Authentication token"),
    db: AsyncSession = Depends(get_db)
):
    """
    WebSocket endpoint for general real-time updates.
    
    Provides real-time notifications, system updates, and general
    communication outside of specific body doubling sessions.
    
    Args:
        websocket: WebSocket connection
        token: Authentication token
        db: Database session
    """
    try:
        # Authenticate user
        user_id = await authenticate_websocket_user(token, db)
        
        # Accept connection
        await websocket.accept()
        
        logger.info(f"General WebSocket connected for user {user_id}")
        
        # Send connection confirmation
        await websocket.send_json({
            "message_type": "connect",
            "success": True,
            "data": {
                "user_id": str(user_id),
                "connection_type": "general"
            }
        })
        
        # Listen for messages (basic implementation)
        while True:
            try:
                data = await websocket.receive_text()
                logger.debug(f"Received general message from user {user_id}: {data}")
                
                # Echo back for now (in production, handle different message types)
                await websocket.send_json({
                    "message_type": "echo",
                    "success": True,
                    "data": {"received": data}
                })
                
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"Error handling general WebSocket message: {e}")
                await websocket.send_json({
                    "message_type": "error",
                    "success": False,
                    "error": "Message processing error"
                })
        
    except AuthenticationError as e:
        logger.warning(f"General WebSocket authentication failed: {e}")
        await websocket.close(code=4001, reason="Authentication failed")
    except WebSocketDisconnect:
        logger.info(f"General WebSocket disconnected for user")
    except Exception as e:
        logger.error(f"General WebSocket error: {e}")
        try:
            await websocket.close(code=1011, reason="Internal server error")
        except:
            pass
    finally:
        logger.info(f"General WebSocket connection closed")


# WebSocket route registration helper
def register_websocket_routes(app):
    """
    Register WebSocket routes with the FastAPI app.
    
    Args:
        app: FastAPI application instance
    """
    
    @app.websocket("/ws/body-doubling/{session_id}")
    async def body_doubling_websocket_endpoint(
        websocket: WebSocket,
        session_id: UUID,
        token: str = Query(...),
        db: AsyncSession = Depends(get_db)
    ):
        """Body doubling session WebSocket endpoint."""
        await websocket_body_doubling_session(websocket, session_id, token, db)
    
    @app.websocket("/ws/general")
    async def general_websocket_endpoint(
        websocket: WebSocket,
        token: str = Query(...),
        db: AsyncSession = Depends(get_db)
    ):
        """General updates WebSocket endpoint."""
        await websocket_general_updates(websocket, token, db)
    
    logger.info("WebSocket routes registered")


# Connection health check
async def websocket_health_check():
    """
    Check WebSocket service health.
    
    Returns:
        Dict: Health status information
    """
    from app.services.websocket_service import connection_manager
    
    return {
        "websocket_service": "healthy",
        "active_connections": len(connection_manager.user_connections),
        "active_sessions": len(connection_manager.session_connections),
        "total_user_sessions": sum(
            len(sessions) for sessions in connection_manager.user_sessions.values()
        )
    }
