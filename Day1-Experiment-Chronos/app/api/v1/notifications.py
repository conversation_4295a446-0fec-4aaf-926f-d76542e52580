"""
Notification API endpoints for Project Chronos.

This module provides ADHD-optimized notification endpoints including
persistent reminders, context-aware delivery, and preference management.
"""

import logging
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.schemas.notification import (
    NotificationCreate,
    NotificationUpdate,
    NotificationResponse,
    NotificationAcknowledge,
    NotificationSnooze,
    NotificationPreferencesCreate,
    NotificationPreferencesUpdate,
    NotificationPreferencesResponse,
    NotificationStats,
    NotificationBatch,
    BulkNotificationCreate,
)
from app.services.notification_service import NotificationService
from app.workers.celery_app import celery_app
from app.core.exceptions import (
    ValidationError,
    UserNotFoundError,
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/notifications", tags=["notifications"])


@router.post("/", response_model=NotificationResponse)
async def create_notification(
    notification_data: NotificationCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new ADHD-optimized notification.
    
    Creates a notification with context-aware timing, persistent reminders,
    and multi-channel delivery based on user preferences.
    """
    try:
        notification_service = NotificationService(db, celery_app)
        notification = await notification_service.create_notification(
            current_user.id, notification_data
        )
        
        # Convert to response format with calculated fields
        response_data = NotificationResponse.model_validate(notification)
        response_data.is_overdue = notification.is_overdue()
        response_data.can_be_snoozed = notification.can_be_snoozed()
        response_data.is_expired = notification.is_expired()
        response_data.next_escalation_time = notification.get_next_escalation_time()
        
        return response_data
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating notification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create notification"
        )


@router.post("/bulk", response_model=List[NotificationResponse])
async def create_bulk_notifications(
    bulk_data: BulkNotificationCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create multiple notifications with optional batch processing.
    
    Useful for creating reminder sequences or multiple related notifications
    with ADHD-friendly staggered delivery.
    """
    try:
        notification_service = NotificationService(db, celery_app)
        created_notifications = []
        
        for notification_data in bulk_data.notifications:
            notification = await notification_service.create_notification(
                current_user.id, notification_data
            )
            
            response_data = NotificationResponse.model_validate(notification)
            response_data.is_overdue = notification.is_overdue()
            response_data.can_be_snoozed = notification.can_be_snoozed()
            response_data.is_expired = notification.is_expired()
            response_data.next_escalation_time = notification.get_next_escalation_time()
            
            created_notifications.append(response_data)
            
            # Add delay between notifications if specified
            if bulk_data.batch_delay and len(created_notifications) < len(bulk_data.notifications):
                import asyncio
                await asyncio.sleep(bulk_data.batch_delay)
        
        return created_notifications
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating bulk notifications: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create bulk notifications"
        )


@router.get("/", response_model=List[NotificationResponse])
async def list_notifications(
    unread_only: bool = Query(False, description="Return only unread notifications"),
    limit: int = Query(50, ge=1, le=100, description="Number of notifications to return"),
    offset: int = Query(0, ge=0, description="Number of notifications to skip"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    List user's notifications with ADHD-friendly filtering.
    
    Returns notifications with calculated fields for UI display
    and interaction capabilities.
    """
    try:
        notification_service = NotificationService(db, celery_app)
        notifications = await notification_service.get_user_notifications(
            current_user.id, unread_only, limit, offset
        )
        
        # Convert to response format with calculated fields
        response_notifications = []
        for notification in notifications:
            response_data = NotificationResponse.model_validate(notification)
            response_data.is_overdue = notification.is_overdue()
            response_data.can_be_snoozed = notification.can_be_snoozed()
            response_data.is_expired = notification.is_expired()
            response_data.next_escalation_time = notification.get_next_escalation_time()
            response_notifications.append(response_data)
        
        return response_notifications
        
    except Exception as e:
        logger.error(f"Error listing notifications: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list notifications"
        )


@router.get("/{notification_id}", response_model=NotificationResponse)
async def get_notification(
    notification_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get detailed information about a specific notification.
    
    Returns notification details with ADHD-relevant interaction options
    and status information.
    """
    try:
        notification_service = NotificationService(db, celery_app)
        notification = await notification_service._get_user_notification(
            notification_id, current_user.id
        )
        
        if not notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found"
            )
        
        response_data = NotificationResponse.model_validate(notification)
        response_data.is_overdue = notification.is_overdue()
        response_data.can_be_snoozed = notification.can_be_snoozed()
        response_data.is_expired = notification.is_expired()
        response_data.next_escalation_time = notification.get_next_escalation_time()
        
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting notification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get notification"
        )


@router.post("/{notification_id}/acknowledge", response_model=NotificationResponse)
async def acknowledge_notification(
    notification_id: UUID,
    acknowledge_data: NotificationAcknowledge,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Acknowledge a notification to stop persistence.
    
    ADHD-friendly acknowledgment that stops persistent reminders
    and provides positive feedback for task completion.
    """
    try:
        notification_service = NotificationService(db, celery_app)
        notification = await notification_service.acknowledge_notification(
            current_user.id, notification_id, acknowledge_data
        )
        
        response_data = NotificationResponse.model_validate(notification)
        response_data.is_overdue = notification.is_overdue()
        response_data.can_be_snoozed = notification.can_be_snoozed()
        response_data.is_expired = notification.is_expired()
        response_data.next_escalation_time = notification.get_next_escalation_time()
        
        return response_data
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error acknowledging notification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to acknowledge notification"
        )


@router.post("/{notification_id}/snooze", response_model=NotificationResponse)
async def snooze_notification(
    notification_id: UUID,
    snooze_data: NotificationSnooze,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Snooze a notification with ADHD-friendly options.
    
    Provides flexible snoozing that accommodates ADHD attention patterns
    and time perception challenges.
    """
    try:
        notification_service = NotificationService(db, celery_app)
        notification = await notification_service.snooze_notification(
            current_user.id, notification_id, snooze_data
        )
        
        response_data = NotificationResponse.model_validate(notification)
        response_data.is_overdue = notification.is_overdue()
        response_data.can_be_snoozed = notification.can_be_snoozed()
        response_data.is_expired = notification.is_expired()
        response_data.next_escalation_time = notification.get_next_escalation_time()
        
        return response_data
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error snoozing notification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to snooze notification"
        )


@router.put("/{notification_id}", response_model=NotificationResponse)
async def update_notification(
    notification_id: UUID,
    update_data: NotificationUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a notification with ADHD accommodations.
    
    Allows flexible updates while maintaining notification integrity
    and user preferences.
    """
    try:
        # This would be implemented in the service
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Update notification not yet implemented"
        )
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{notification_id}")
async def delete_notification(
    notification_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a notification.
    
    ADHD-friendly deletion that cancels scheduled delivery
    and provides clear confirmation.
    """
    try:
        # This would be implemented in the service
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Delete notification not yet implemented"
        )
        
    except Exception as e:
        logger.error(f"Error deleting notification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete notification"
        )


@router.get("/stats/summary", response_model=NotificationStats)
async def get_notification_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get notification statistics for the user.
    
    Provides ADHD-friendly insights into notification patterns
    and effectiveness without overwhelming detail.
    """
    try:
        notification_service = NotificationService(db, celery_app)
        stats = await notification_service.get_notification_stats(current_user.id)
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting notification stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get notification statistics"
        )


@router.get("/preferences", response_model=NotificationPreferencesResponse)
async def get_notification_preferences(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get user's notification preferences.
    
    Returns ADHD-optimized notification settings and delivery preferences.
    """
    try:
        notification_service = NotificationService(db, celery_app)
        prefs = await notification_service._get_user_preferences(current_user.id)
        
        if not prefs:
            # Return default preferences
            from app.models.notification import NotificationPreference
            prefs = NotificationPreference(user_id=current_user.id)
        
        return NotificationPreferencesResponse.model_validate(prefs)
        
    except Exception as e:
        logger.error(f"Error getting notification preferences: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get notification preferences"
        )


@router.put("/preferences", response_model=NotificationPreferencesResponse)
async def update_notification_preferences(
    preferences_data: NotificationPreferencesUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update user's notification preferences.
    
    Allows customization of ADHD-specific notification behaviors
    including timing, channels, and persistence settings.
    """
    try:
        # This would be implemented in the service
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Update notification preferences not yet implemented"
        )
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
