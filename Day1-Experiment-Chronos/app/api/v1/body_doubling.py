"""
REST API endpoints for body doubling sessions.

This module provides HTTP endpoints for managing virtual body doubling sessions,
participant management, and session coordination.
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.services.body_doubling_service import BodyDoublingService
from app.schemas.body_doubling import (
    BodyDoublingSessionCreate,
    BodyDoublingSessionResponse,
    BodyDoublingSessionUpdate,
    SessionParticipantCreate,
    SessionParticipantResponse,
    SessionMessageCreate,
    SessionMessageResponse,
    SessionListResponse,
    SessionJoinResponse,
    SessionStatsResponse,
    GroupFocusRequest
)

router = APIRouter()
service = BodyDoublingService()


@router.post("/sessions")
async def create_session(
    session_data: BodyDoublingSessionCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new body doubling session.

    Creates a virtual body doubling session where users can work together
    for accountability and shared focus.
    """
    try:
        session = await service.create_session(db, session_data, current_user)
        # Return raw dict to debug the response structure
        return {
            "id": str(session.id),
            "host_user_id": str(session.host_user_id),
            "title": session.title,
            "description": session.description,
            "max_participants": session.max_participants,
            "current_participants": session.current_participants,
            "session_type": session.session_type,
            "is_public": session.is_public,
            "requires_approval": session.requires_approval,
            "password_protected": session.password_protected,
            "scheduled_start": session.scheduled_start.isoformat() if session.scheduled_start else None,
            "scheduled_end": session.scheduled_end.isoformat() if session.scheduled_end else None,
            "actual_start": session.actual_start.isoformat() if session.actual_start else None,
            "actual_end": session.actual_end.isoformat() if session.actual_end else None,
            "status": session.status,
            "session_settings": session.session_settings,
            "current_focus_session_id": str(session.current_focus_session_id) if session.current_focus_session_id else None,
            "created_at": session.created_at.isoformat(),
            "updated_at": session.updated_at.isoformat()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create session: {str(e)}"
        )


@router.get("/sessions", response_model=SessionListResponse)
async def list_sessions(
    skip: int = Query(0, ge=0, description="Number of sessions to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of sessions to return"),
    session_type: Optional[str] = Query(None, description="Filter by session type"),
    status: Optional[str] = Query(None, description="Filter by session status"),
    db: AsyncSession = Depends(get_db)
):
    """
    List public body doubling sessions.
    
    Returns a paginated list of public sessions that users can join.
    """
    try:
        sessions = await service.list_public_sessions(
            db, skip=skip, limit=limit, session_type=session_type, status=status
        )
        
        # Get total count for pagination
        total = len(sessions)  # Simplified - in production, would do separate count query
        
        return SessionListResponse(
            sessions=sessions,
            total=total,
            page=skip // limit + 1,
            per_page=limit,
            has_next=len(sessions) == limit,
            has_prev=skip > 0
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list sessions: {str(e)}"
        )


@router.get("/sessions/{session_id}", response_model=BodyDoublingSessionResponse)
async def get_session(
    session_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific body doubling session.
    
    Returns detailed information about a session, including participants
    if the user has access.
    """
    session = await service.get_session_by_id(db, session_id, include_participants=True)
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    
    # Check access permissions
    if not session.is_public and session.host_user_id != current_user.id:
        # Check if user is a participant
        is_participant = any(
            p.user_id == current_user.id and p.is_active()
            for p in session.participants
        )
        if not is_participant:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to private session"
            )
    
    return session


@router.put("/sessions/{session_id}", response_model=BodyDoublingSessionResponse)
async def update_session(
    session_id: UUID,
    session_data: BodyDoublingSessionUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a body doubling session.
    
    Only the session host can update session settings.
    """
    session = await service.update_session(db, session_id, session_data, current_user)
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found or access denied"
        )
    
    return session


@router.post("/sessions/{session_id}/start", response_model=BodyDoublingSessionResponse)
async def start_session(
    session_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Start a body doubling session.
    
    Only the session host can start the session.
    """
    session = await service.start_session(db, session_id, current_user)
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found, access denied, or cannot be started"
        )
    
    return session


@router.post("/sessions/{session_id}/end", response_model=BodyDoublingSessionResponse)
async def end_session(
    session_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    End a body doubling session.
    
    Only the session host can end the session.
    """
    session = await service.end_session(db, session_id, current_user)
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found, access denied, or cannot be ended"
        )
    
    return session


@router.post("/sessions/{session_id}/join", response_model=SessionJoinResponse)
async def join_session(
    session_id: UUID,
    participant_data: SessionParticipantCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Join a body doubling session.
    
    Adds the current user as a participant in the session.
    """
    participant = await service.add_participant(db, session_id, current_user, participant_data)
    
    if not participant:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot join session - session full, wrong password, or already participating"
        )
    
    # Get updated session
    session = await service.get_session_by_id(db, session_id)
    
    # Generate WebSocket URL and session token
    websocket_url = f"/ws/body-doubling/{session_id}"
    session_token = "temp_token"  # In production, generate proper session token
    
    return SessionJoinResponse(
        session=session,
        participant=participant,
        websocket_url=websocket_url,
        session_token=session_token
    )


@router.post("/sessions/{session_id}/leave")
async def leave_session(
    session_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Leave a body doubling session.
    
    Removes the current user from the session.
    """
    success = await service.remove_participant(db, session_id, current_user)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Not participating in session or already left"
        )
    
    return {"message": "Successfully left session"}


@router.get("/sessions/{session_id}/participants", response_model=List[SessionParticipantResponse])
async def get_session_participants(
    session_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get participants in a session.
    
    Returns list of active participants if user has access to the session.
    """
    session = await service.get_session_by_id(db, session_id, include_participants=True)
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    
    # Check access permissions
    if not session.is_public and session.host_user_id != current_user.id:
        is_participant = any(
            p.user_id == current_user.id and p.is_active()
            for p in session.participants
        )
        if not is_participant:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to session participants"
            )
    
    # Return only active participants
    active_participants = [p for p in session.participants if p.is_active()]
    return active_participants


@router.get("/sessions/{session_id}/stats", response_model=SessionStatsResponse)
async def get_session_stats(
    session_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get session statistics.
    
    Returns statistics about the session for participants and host.
    """
    session = await service.get_session_by_id(db, session_id, include_participants=True)
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    
    # Check access permissions
    if session.host_user_id != current_user.id:
        is_participant = any(
            p.user_id == current_user.id and p.is_active()
            for p in session.participants
        )
        if not is_participant:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to session stats"
            )
    
    # Calculate statistics
    total_participants = len(session.participants)
    active_participants = len([p for p in session.participants if p.is_active()])
    session_duration = session.get_duration_minutes() or 0
    
    # Calculate average participant duration
    participant_durations = [p.get_session_duration_minutes() for p in session.participants]
    avg_duration = sum(participant_durations) / len(participant_durations) if participant_durations else 0
    
    return SessionStatsResponse(
        session_id=session_id,
        total_participants=total_participants,
        active_participants=active_participants,
        session_duration_minutes=session_duration,
        total_messages=0,  # Would count from messages table
        focus_sessions_completed=0,  # Would count from focus sessions
        average_participant_duration=avg_duration,
        encouragements_given=0  # Would count from messages table
    )
