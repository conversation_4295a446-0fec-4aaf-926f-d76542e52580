"""
Focus session API endpoints for ADHD-optimized productivity sessions.

This module provides REST API endpoints for managing focus sessions,
including Pomodoro, deep work, body doubling, and other ADHD-friendly formats.
"""

from typing import Dict, Optional, List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.services.focus_session_service import FocusSessionService, FocusSessionType, InterruptionType
from sqlalchemy.ext.asyncio import AsyncSession


router = APIRouter()


@router.post("/sessions", response_model=FocusSessionResponse)
async def create_focus_session(
    session_data: FocusSessionCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new ADHD-optimized focus session.
    
    Creates a focus session with flexible timing and ADHD accommodations
    including hyperfocus protection and gentle break reminders.
    """
    try:
        focus_service = FocusService(db)
        session = await focus_service.create_focus_session(current_user.id, session_data)
        
        # Convert to response format with calculated fields
        response_data = FocusSessionResponse.model_validate(session)
        response_data.elapsed_time = None
        response_data.remaining_time = None
        response_data.is_overdue = False
        
        return response_data
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating focus session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create focus session"
        )


@router.post("/sessions/{session_id}/start", response_model=FocusSessionResponse)
async def start_focus_session(
    session_id: UUID,
    start_data: Optional[FocusSessionStart] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Start a focus session with environment setup.
    
    Activates the focus session with optional focus mode and
    notification management for distraction-free work.
    """
    try:
        focus_service = FocusService(db)
        session = await focus_service.start_focus_session(
            session_id, current_user.id, start_data
        )
        
        # Convert to response format with real-time data
        response_data = FocusSessionResponse.model_validate(session)
        
        # Add real-time calculated fields
        elapsed = session.get_elapsed_time()
        remaining = session.get_remaining_time()
        
        response_data.elapsed_time = str(elapsed) if elapsed else None
        response_data.remaining_time = str(remaining) if remaining else None
        response_data.is_overdue = session.is_overdue()
        
        return response_data
        
    except FocusSessionError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error starting focus session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start focus session"
        )


@router.post("/sessions/{session_id}/pause", response_model=FocusSessionResponse)
async def pause_focus_session(
    session_id: UUID,
    reason: Optional[str] = Query(None, description="Reason for pausing"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Pause an active focus session.
    
    ADHD-friendly pausing that preserves progress and allows
    flexible resumption without judgment.
    """
    try:
        focus_service = FocusService(db)
        session = await focus_service.pause_focus_session(
            session_id, current_user.id, reason
        )
        
        response_data = FocusSessionResponse.model_validate(session)
        
        # Add calculated fields
        elapsed = session.get_elapsed_time()
        response_data.elapsed_time = str(elapsed) if elapsed else None
        response_data.remaining_time = None  # No remaining time when paused
        response_data.is_overdue = False
        
        return response_data
        
    except FocusSessionError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/sessions/{session_id}/resume", response_model=FocusSessionResponse)
async def resume_focus_session(
    session_id: UUID,
    extend_duration: Optional[int] = Query(
        None, ge=0, le=120,
        description="Additional minutes to add to session"
    ),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Resume a paused focus session.
    
    Flexible resumption with optional duration extension
    to accommodate ADHD time perception challenges.
    """
    try:
        focus_service = FocusService(db)
        session = await focus_service.resume_focus_session(
            session_id, current_user.id, extend_duration
        )
        
        response_data = FocusSessionResponse.model_validate(session)
        
        # Add real-time calculated fields
        elapsed = session.get_elapsed_time()
        remaining = session.get_remaining_time()
        
        response_data.elapsed_time = str(elapsed) if elapsed else None
        response_data.remaining_time = str(remaining) if remaining else None
        response_data.is_overdue = session.is_overdue()
        
        return response_data
        
    except FocusSessionError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/sessions/{session_id}/complete", response_model=FocusSessionResponse)
async def complete_focus_session(
    session_id: UUID,
    completion_data: Optional[FocusSessionComplete] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Complete a focus session with optional feedback.
    
    Celebrates completion and collects optional feedback
    for improving future focus session recommendations.
    """
    try:
        focus_service = FocusService(db)
        session = await focus_service.complete_focus_session(
            session_id, current_user.id, completion_data
        )
        
        response_data = FocusSessionResponse.model_validate(session)
        
        # Add final calculated fields
        elapsed = session.get_elapsed_time()
        response_data.elapsed_time = str(elapsed) if elapsed else None
        response_data.remaining_time = None
        response_data.is_overdue = False
        
        return response_data
        
    except FocusSessionError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/sessions/{session_id}", response_model=FocusSessionResponse)
async def get_focus_session(
    session_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get detailed information about a focus session.
    
    Returns current session state with real-time progress
    and ADHD-relevant metrics.
    """
    try:
        focus_service = FocusService(db)
        session = await focus_service._get_user_session(session_id, current_user.id)
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Focus session not found"
            )
        
        response_data = FocusSessionResponse.model_validate(session)
        
        # Add real-time calculated fields
        elapsed = session.get_elapsed_time()
        remaining = session.get_remaining_time()
        
        response_data.elapsed_time = str(elapsed) if elapsed else None
        response_data.remaining_time = str(remaining) if remaining else None
        response_data.is_overdue = session.is_overdue()
        
        return response_data
        
    except Exception as e:
        logger.error(f"Error getting focus session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get focus session"
        )


@router.get("/sessions", response_model=List[FocusSessionResponse])
async def list_focus_sessions(
    limit: int = Query(20, ge=1, le=100, description="Number of sessions to return"),
    offset: int = Query(0, ge=0, description="Number of sessions to skip"),
    status: Optional[str] = Query(None, description="Filter by session status"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    List user's focus sessions with filtering and pagination.
    
    Returns recent focus sessions with ADHD-friendly filtering
    and progress information.
    """
    try:
        # This would be implemented in the focus service
        # For now, return empty list
        return []
        
    except Exception as e:
        logger.error(f"Error listing focus sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list focus sessions"
        )


@router.get("/sessions/{session_id}/hyperfocus-check", response_model=Optional[HyperfocusAlert])
async def check_hyperfocus(
    session_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Check for hyperfocus and get protection alerts.
    
    ADHD-specific feature that monitors session duration
    and provides gentle alerts for hyperfocus protection.
    """
    try:
        focus_service = FocusService(db)
        alert = await focus_service.detect_hyperfocus(session_id)
        
        return alert
        
    except Exception as e:
        logger.error(f"Error checking hyperfocus: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check hyperfocus"
        )


@router.get("/sessions/{session_id}/break-suggestion", response_model=Optional[BreakReminder])
async def get_break_suggestion(
    session_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get break suggestions for active sessions.
    
    Provides gentle, ADHD-friendly break reminders with
    suggested activities and timing.
    """
    try:
        focus_service = FocusService(db)
        reminder = await focus_service.suggest_break(session_id)
        
        return reminder
        
    except Exception as e:
        logger.error(f"Error getting break suggestion: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get break suggestion"
        )


@router.post("/modes", response_model=FocusModeResponse)
async def create_focus_mode(
    mode_data: FocusModeCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a custom focus mode.
    
    Allows users to create personalized focus environments
    with specific notification and timing settings.
    """
    try:
        focus_service = FocusService(db)
        focus_mode = await focus_service.create_focus_mode(current_user.id, mode_data)
        
        return FocusModeResponse.model_validate(focus_mode)
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating focus mode: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create focus mode"
        )


@router.get("/stats", response_model=FocusSessionStats)
async def get_focus_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get comprehensive focus session statistics.
    
    Provides ADHD-friendly analytics focusing on patterns
    and insights rather than judgment or pressure.
    """
    try:
        focus_service = FocusService(db)
        stats = await focus_service.get_focus_session_stats(current_user.id)
        
        return FocusSessionStats(**stats)
        
    except Exception as e:
        logger.error(f"Error getting focus stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get focus statistics"
        )
