"""
Gamification API endpoints for Project Chronos.

This module provides ADHD-optimized gamification endpoints including
points, achievements, streaks, and user gamification profiles.
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.schemas.gamification import (
    GamificationProfileResponse,
    GamificationProfileUpdate,
    PointsAwardCreate,
    PointsAwardResponse,
    AchievementResponse,
    UserAchievementResponse,
    StreakResponse,
    StreakUpdate,
    GamificationStatsResponse,
    GamificationDashboardResponse,
    PointsCalculationRequest,
    PointsCalculationResponse,
)
from app.services.gamification_service import GamificationService
from app.services.achievement_service import AchievementService
from app.core.exceptions import (
    GamificationError,
    AchievementError,
    UserNotFoundError,
)

router = APIRouter(prefix="/gamification", tags=["gamification"])


@router.get("/profile", response_model=GamificationProfileResponse)
async def get_gamification_profile(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's complete gamification profile."""
    try:
        gamification_service = GamificationService(db)
        profile = await gamification_service.get_or_create_user_gamification(current_user.id)
        return profile
    except UserNotFoundError:
        raise HTTPException(status_code=404, detail="User not found")
    except GamificationError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.patch("/profile", response_model=GamificationProfileResponse)
async def update_gamification_profile(
    profile_update: GamificationProfileUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update user's gamification profile preferences."""
    try:
        gamification_service = GamificationService(db)
        profile = await gamification_service.get_or_create_user_gamification(current_user.id)
        
        # Update fields if provided
        if profile_update.gamification_enabled is not None:
            profile.gamification_enabled = profile_update.gamification_enabled
        if profile_update.celebration_style is not None:
            profile.celebration_style = profile_update.celebration_style
        if profile_update.preferred_rewards is not None:
            profile.preferred_rewards = profile_update.preferred_rewards
        
        await db.commit()
        await db.refresh(profile)
        return profile
    except UserNotFoundError:
        raise HTTPException(status_code=404, detail="User not found")
    except GamificationError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/stats", response_model=GamificationStatsResponse)
async def get_gamification_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's gamification statistics."""
    try:
        gamification_service = GamificationService(db)
        stats = await gamification_service.get_gamification_stats(current_user.id)
        return stats
    except UserNotFoundError:
        raise HTTPException(status_code=404, detail="User not found")
    except GamificationError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/points/award", response_model=PointsAwardResponse)
async def award_points(
    points_data: PointsAwardCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Award points to a user (admin or system use)."""
    try:
        gamification_service = GamificationService(db)
        award, level_up = await gamification_service.award_points(
            user_id=points_data.user_id,
            points=points_data.points,
            reason=points_data.reason,
            task_id=points_data.task_id,
            multiplier=points_data.multiplier
        )
        return award
    except UserNotFoundError:
        raise HTTPException(status_code=404, detail="User not found")
    except GamificationError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/points/calculate", response_model=PointsCalculationResponse)
async def calculate_points(
    calculation_request: PointsCalculationRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Calculate points with multipliers for a given scenario."""
    try:
        gamification_service = GamificationService(db)
        multiplier, breakdown = await gamification_service.calculate_points_multiplier(
            base_points=calculation_request.base_points,
            task_difficulty=calculation_request.task_difficulty,
            energy_level=calculation_request.energy_level,
            time_of_day=calculation_request.time_of_day,
            context=calculation_request.context
        )
        
        final_points = int(calculation_request.base_points * multiplier)
        
        # Determine celebration level
        if multiplier >= 2.0:
            celebration_level = "enthusiastic"
        elif multiplier >= 1.5:
            celebration_level = "moderate"
        else:
            celebration_level = "minimal"
        
        return PointsCalculationResponse(
            base_points=calculation_request.base_points,
            final_points=final_points,
            multiplier=multiplier,
            breakdown=breakdown,
            celebration_level=celebration_level
        )
    except GamificationError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/achievements", response_model=List[UserAchievementResponse])
async def get_achievements(
    unlocked_only: bool = Query(False, description="Return only unlocked achievements"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user achievements with progress."""
    try:
        achievement_service = AchievementService(db)
        achievements = await achievement_service.get_user_achievements(
            user_id=current_user.id,
            unlocked_only=unlocked_only
        )
        return achievements
    except UserNotFoundError:
        raise HTTPException(status_code=404, detail="User not found")
    except AchievementError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/achievements/available", response_model=List[AchievementResponse])
async def get_available_achievements(
    category: Optional[str] = Query(None, description="Filter by achievement category"),
    db: AsyncSession = Depends(get_db)
):
    """Get all available achievements."""
    try:
        from sqlalchemy import select
        from app.models.gamification import Achievement
        
        query = select(Achievement).where(Achievement.is_active == True)
        if category:
            query = query.where(Achievement.category == category)
        
        result = await db.execute(query)
        achievements = result.scalars().all()
        return achievements
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/streaks", response_model=List[StreakResponse])
async def get_streaks(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's current streaks."""
    try:
        from sqlalchemy import select
        from app.models.gamification import UserStreak
        
        result = await db.execute(
            select(UserStreak).where(UserStreak.user_id == current_user.id)
        )
        streaks = result.scalars().all()
        return streaks
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/streaks/update", response_model=StreakResponse)
async def update_streak(
    streak_update: StreakUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update a user's streak."""
    try:
        gamification_service = GamificationService(db)
        streak, changed = await gamification_service.update_streak(
            user_id=current_user.id,
            streak_type=streak_update.streak_type,
            action_completed=streak_update.action_completed
        )
        
        # Check for streak achievements if streak changed
        if changed:
            achievement_service = AchievementService(db)
            await achievement_service.check_achievements(
                user_id=current_user.id,
                trigger_event="streak_milestone",
                event_data={"streak_length": streak.current_streak, "streak_type": streak.streak_type}
            )
        
        return streak
    except UserNotFoundError:
        raise HTTPException(status_code=404, detail="User not found")
    except GamificationError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/dashboard", response_model=GamificationDashboardResponse)
async def get_gamification_dashboard(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get comprehensive gamification dashboard data."""
    try:
        gamification_service = GamificationService(db)
        achievement_service = AchievementService(db)
        
        # Get profile and stats
        profile = await gamification_service.get_or_create_user_gamification(current_user.id)
        stats = await gamification_service.get_gamification_stats(current_user.id)
        
        # Get recent achievements
        recent_achievements = await achievement_service.get_user_achievements(
            user_id=current_user.id,
            unlocked_only=True
        )
        # Sort by unlock date and take last 5
        recent_achievements.sort(key=lambda x: x.unlocked_at or x.created_at, reverse=True)
        recent_achievements = recent_achievements[:5]
        
        # Generate suggestions and trends (placeholder)
        trending_up = ["Task completion", "Consistency", "Focus time"]
        suggestions = [
            "Try completing a task during your high-energy time for bonus points",
            "You're close to unlocking the 'Week Warrior' achievement!",
            "Consider setting up a daily task streak to build momentum"
        ]
        next_milestones = [
            {"type": "level", "description": f"Reach level {profile.level + 1}", "progress": 0.7},
            {"type": "achievement", "description": "Complete 10 tasks", "progress": 0.6},
            {"type": "streak", "description": "7-day task streak", "progress": 0.4}
        ]
        
        return GamificationDashboardResponse(
            profile=profile,
            stats=stats,
            recent_achievements=recent_achievements,
            trending_up=trending_up,
            suggestions=suggestions,
            next_milestones=next_milestones
        )
    except UserNotFoundError:
        raise HTTPException(status_code=404, detail="User not found")
    except (GamificationError, AchievementError) as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/initialize")
async def initialize_gamification(
    db: AsyncSession = Depends(get_db)
):
    """Initialize gamification system (admin endpoint)."""
    try:
        achievement_service = AchievementService(db)
        await achievement_service.initialize_achievements()
        return {"message": "Gamification system initialized successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to initialize gamification: {str(e)}")
