"""
Time blocking API endpoints for Project Chronos.

This module provides REST API endpoints for time blocking, calendar integration,
and ADHD-friendly scheduling features.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.schemas.time_blocking import (
    TimeBlockCreate,
    TimeBlockUpdate,
    TimeBlockResponse,
    TimeBlockCommand,
    CalendarIntegrationCreate,
    CalendarIntegrationUpdate,
    CalendarIntegrationResponse,
    WeeklySchedule,
    SchedulingConflict
)
from app.services.time_blocking_service import TimeBlockingService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/time-blocking", tags=["time-blocking"])
service = TimeBlockingService()


@router.post("/blocks", response_model=TimeBlockResponse)
async def create_time_block(
    block_data: TimeBlockCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new time block.
    
    Creates a time block for scheduling tasks, focus sessions, or other activities.
    Supports ADHD-friendly features like buffer time and energy level requirements.
    """
    try:
        time_block = await service.create_time_block(db, block_data, current_user)
        
        # Create response dictionary with computed fields
        response_data = {
            "id": time_block.id,
            "user_id": time_block.user_id,
            "task_id": time_block.task_id,
            "focus_session_id": time_block.focus_session_id,
            "body_doubling_session_id": time_block.body_doubling_session_id,
            "title": time_block.title,
            "description": time_block.description,
            "block_type": time_block.block_type,
            "start_time": time_block.start_time,
            "end_time": time_block.end_time,
            "duration_minutes": time_block.duration_minutes,
            "buffer_before": time_block.buffer_before,
            "buffer_after": time_block.buffer_after,
            "energy_level_required": time_block.energy_level_required,
            "flexibility_level": time_block.flexibility_level,
            "status": time_block.status,
            "completion_percentage": time_block.completion_percentage,
            "actual_start_time": time_block.actual_start_time,
            "actual_end_time": time_block.actual_end_time,
            "is_recurring": time_block.is_recurring,
            "recurrence_pattern": time_block.recurrence_pattern,
            "parent_block_id": time_block.parent_block_id,
            "external_calendar_id": time_block.external_calendar_id,
            "calendar_provider": time_block.calendar_provider,
            "sync_status": time_block.sync_status,
            "block_settings": time_block.block_settings,
            "reminder_settings": time_block.reminder_settings,
            "preparation_notes": time_block.preparation_notes,
            "completion_notes": time_block.completion_notes,
            "created_at": time_block.created_at,
            "updated_at": time_block.updated_at,
            # Computed fields
            "total_duration_with_buffers": time_block.get_total_duration_with_buffers(),
            "actual_duration": time_block.get_actual_duration(),
            "is_active": time_block.is_active(),
            "is_completed": time_block.is_completed(),
            "is_overdue": time_block.is_overdue(),
            "time_until_start": time_block.get_time_until_start()
        }

        return response_data
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to create time block: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create time block"
        )


@router.get("/blocks", response_model=List[TimeBlockResponse])
async def get_time_blocks(
    start_date: Optional[datetime] = Query(None, description="Filter by start date"),
    end_date: Optional[datetime] = Query(None, description="Filter by end date"),
    block_type: Optional[str] = Query(None, description="Filter by block type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    limit: int = Query(100, ge=1, le=500, description="Maximum number of blocks to return"),
    offset: int = Query(0, ge=0, description="Number of blocks to skip"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get user's time blocks with optional filtering.
    
    Returns a list of time blocks with optional date range, type, and status filtering.
    Includes computed fields for real-time status information.
    """
    try:
        time_blocks = await service.get_user_time_blocks(
            db, current_user, start_date, end_date, block_type, status, limit, offset
        )
        
        # Add computed fields to each block
        responses = []
        for block in time_blocks:
            response_data = {
                "id": block.id,
                "user_id": block.user_id,
                "task_id": block.task_id,
                "focus_session_id": block.focus_session_id,
                "body_doubling_session_id": block.body_doubling_session_id,
                "title": block.title,
                "description": block.description,
                "block_type": block.block_type,
                "start_time": block.start_time,
                "end_time": block.end_time,
                "duration_minutes": block.duration_minutes,
                "buffer_before": block.buffer_before,
                "buffer_after": block.buffer_after,
                "energy_level_required": block.energy_level_required,
                "flexibility_level": block.flexibility_level,
                "status": block.status,
                "completion_percentage": block.completion_percentage,
                "actual_start_time": block.actual_start_time,
                "actual_end_time": block.actual_end_time,
                "is_recurring": block.is_recurring,
                "recurrence_pattern": block.recurrence_pattern,
                "parent_block_id": block.parent_block_id,
                "external_calendar_id": block.external_calendar_id,
                "calendar_provider": block.calendar_provider,
                "sync_status": block.sync_status,
                "block_settings": block.block_settings,
                "reminder_settings": block.reminder_settings,
                "preparation_notes": block.preparation_notes,
                "completion_notes": block.completion_notes,
                "created_at": block.created_at,
                "updated_at": block.updated_at,
                # Computed fields
                "total_duration_with_buffers": block.get_total_duration_with_buffers(),
                "actual_duration": block.get_actual_duration(),
                "is_active": block.is_active(),
                "is_completed": block.is_completed(),
                "is_overdue": block.is_overdue(),
                "time_until_start": block.get_time_until_start()
            }
            responses.append(response_data)

        return responses
        
    except Exception as e:
        logger.error(f"Failed to get time blocks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get time blocks"
        )


@router.get("/blocks/{block_id}", response_model=TimeBlockResponse)
async def get_time_block(
    block_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific time block by ID.
    
    Returns detailed information about a time block including computed fields
    and associated task/session information.
    """
    time_block = await service.get_time_block(db, block_id, current_user)
    
    if not time_block:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Time block not found"
        )
    
    # Create response with computed fields
    response = TimeBlockResponse.model_validate(time_block)
    response.total_duration_with_buffers = time_block.get_total_duration_with_buffers()
    response.actual_duration = time_block.get_actual_duration()
    response.is_active = time_block.is_active()
    response.is_completed = time_block.is_completed()
    response.is_overdue = time_block.is_overdue()
    response.time_until_start = time_block.get_time_until_start()

    return response


@router.put("/blocks/{block_id}", response_model=TimeBlockResponse)
async def update_time_block(
    block_id: UUID,
    block_data: TimeBlockUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a time block.
    
    Updates time block details including timing, status, and completion information.
    Automatically recalculates duration if start or end times are changed.
    """
    try:
        time_block = await service.update_time_block(db, block_id, block_data, current_user)
        
        if not time_block:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Time block not found"
            )
        
        # Add computed fields
        response = TimeBlockResponse.from_orm(time_block)
        response.total_duration_with_buffers = time_block.get_total_duration_with_buffers()
        response.actual_duration = time_block.get_actual_duration()
        response.is_active = time_block.is_active()
        response.is_completed = time_block.is_completed()
        response.is_overdue = time_block.is_overdue()
        response.time_until_start = time_block.get_time_until_start()
        
        return response
        
    except Exception as e:
        logger.error(f"Failed to update time block: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update time block"
        )


@router.post("/blocks/{block_id}/commands")
async def execute_block_command(
    block_id: UUID,
    command_data: TimeBlockCommand,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Execute a time block command.
    
    Supports commands: start, complete, cancel, reschedule.
    Updates block status and timing information based on the command.
    """
    try:
        time_block = await service.execute_block_command(db, block_id, command_data, current_user)
        
        if not time_block:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Time block not found"
            )
        
        return {
            "message": f"Command '{command_data.command}' executed successfully",
            "block": {
                "id": str(time_block.id),
                "status": time_block.status,
                "actual_start_time": time_block.actual_start_time,
                "actual_end_time": time_block.actual_end_time,
                "completion_percentage": time_block.completion_percentage
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to execute block command: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to execute command: {str(e)}"
        )


@router.delete("/blocks/{block_id}")
async def delete_time_block(
    block_id: UUID,
    delete_series: bool = Query(False, description="Delete entire recurring series"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a time block.
    
    Optionally delete an entire recurring series if the block is part of one.
    """
    success = await service.delete_time_block(db, block_id, current_user, delete_series)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Time block not found"
        )
    
    return {"message": "Time block deleted successfully"}


@router.get("/blocks/{block_id}/conflicts", response_model=List[SchedulingConflict])
async def check_block_conflicts(
    block_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Check for scheduling conflicts with a time block.
    
    Returns a list of conflicts with other scheduled blocks.
    """
    time_block = await service.get_time_block(db, block_id, current_user)
    
    if not time_block:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Time block not found"
        )
    
    conflicts = await service.check_scheduling_conflicts(
        db, current_user, time_block.start_time, time_block.end_time, block_id
    )
    
    return conflicts


@router.get("/schedule/weekly", response_model=WeeklySchedule)
async def get_weekly_schedule(
    week_start: Optional[datetime] = Query(None, description="Start of the week (defaults to current week)"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get weekly schedule view with analytics.
    
    Returns a comprehensive view of the week's schedule including time blocks,
    analytics, conflicts, and ADHD-friendly suggestions.
    """
    if not week_start:
        # Default to start of current week (Monday)
        now = datetime.now()
        days_since_monday = now.weekday()
        week_start = now.replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=days_since_monday)
    
    try:
        weekly_schedule = await service.get_weekly_schedule(db, current_user, week_start)
        
        # Get time blocks for the week and add to response
        time_blocks = await service.get_user_time_blocks(
            db, current_user, week_start, week_start + timedelta(days=7)
        )
        
        # Convert to response objects with computed fields
        block_responses = []
        for block in time_blocks:
            response_data = {
                "id": block.id,
                "user_id": block.user_id,
                "task_id": block.task_id,
                "focus_session_id": block.focus_session_id,
                "body_doubling_session_id": block.body_doubling_session_id,
                "title": block.title,
                "description": block.description,
                "block_type": block.block_type,
                "start_time": block.start_time,
                "end_time": block.end_time,
                "duration_minutes": block.duration_minutes,
                "buffer_before": block.buffer_before,
                "buffer_after": block.buffer_after,
                "energy_level_required": block.energy_level_required,
                "flexibility_level": block.flexibility_level,
                "status": block.status,
                "completion_percentage": block.completion_percentage,
                "actual_start_time": block.actual_start_time,
                "actual_end_time": block.actual_end_time,
                "is_recurring": block.is_recurring,
                "recurrence_pattern": block.recurrence_pattern,
                "parent_block_id": block.parent_block_id,
                "external_calendar_id": block.external_calendar_id,
                "calendar_provider": block.calendar_provider,
                "sync_status": block.sync_status,
                "block_settings": block.block_settings,
                "reminder_settings": block.reminder_settings,
                "preparation_notes": block.preparation_notes,
                "completion_notes": block.completion_notes,
                "created_at": block.created_at,
                "updated_at": block.updated_at,
                # Computed fields
                "total_duration_with_buffers": block.get_total_duration_with_buffers(),
                "actual_duration": block.get_actual_duration(),
                "is_active": block.is_active(),
                "is_completed": block.is_completed(),
                "is_overdue": block.is_overdue(),
                "time_until_start": block.get_time_until_start()
            }
            block_responses.append(response_data)

        weekly_schedule.time_blocks = block_responses
        
        return weekly_schedule
        
    except Exception as e:
        logger.error(f"Failed to get weekly schedule: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get weekly schedule"
        )


# Calendar Integration Endpoints

@router.post("/calendar-integrations", response_model=CalendarIntegrationResponse)
async def create_calendar_integration(
    integration_data: CalendarIntegrationCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new calendar integration.

    Connects an external calendar (Google, Outlook, etc.) for bidirectional sync
    with time blocks.
    """
    try:
        integration = await service.create_calendar_integration(db, integration_data, current_user)

        # Add computed fields
        response = CalendarIntegrationResponse.from_orm(integration)
        response.is_token_expired = integration.is_token_expired()
        response.needs_refresh = integration.needs_refresh()

        return response

    except Exception as e:
        logger.error(f"Failed to create calendar integration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create calendar integration"
        )


@router.get("/calendar-integrations", response_model=List[CalendarIntegrationResponse])
async def get_calendar_integrations(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get user's calendar integrations.

    Returns a list of configured calendar integrations with sync status.
    """
    try:
        integrations = await service.get_user_calendar_integrations(db, current_user)

        # Add computed fields to each integration
        responses = []
        for integration in integrations:
            response = CalendarIntegrationResponse.from_orm(integration)
            response.is_token_expired = integration.is_token_expired()
            response.needs_refresh = integration.needs_refresh()
            responses.append(response)

        return responses

    except Exception as e:
        logger.error(f"Failed to get calendar integrations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get calendar integrations"
        )


@router.put("/calendar-integrations/{integration_id}", response_model=CalendarIntegrationResponse)
async def update_calendar_integration(
    integration_id: UUID,
    integration_data: CalendarIntegrationUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a calendar integration.

    Updates calendar integration settings and preferences.
    """
    # TODO: Implement update logic in service
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Calendar integration update not yet implemented"
    )


@router.post("/calendar-integrations/{integration_id}/sync")
async def sync_calendar_integration(
    integration_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Manually trigger calendar sync.

    Forces a sync between the external calendar and time blocks.
    """
    try:
        success = await service.sync_calendar_integration(db, integration_id, current_user)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Calendar integration not found"
            )

        return {"message": "Calendar sync completed successfully"}

    except Exception as e:
        logger.error(f"Failed to sync calendar integration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to sync calendar integration"
        )


@router.delete("/calendar-integrations/{integration_id}")
async def delete_calendar_integration(
    integration_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a calendar integration.

    Removes the calendar integration and stops syncing.
    """
    # TODO: Implement delete logic in service
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Calendar integration deletion not yet implemented"
    )


# Utility Endpoints

@router.get("/schedule/conflicts")
async def check_schedule_conflicts(
    start_time: datetime = Query(..., description="Start time to check"),
    end_time: datetime = Query(..., description="End time to check"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Check for scheduling conflicts in a time range.

    Useful for validating new time blocks before creation.
    """
    try:
        conflicts = await service.check_scheduling_conflicts(
            db, current_user, start_time, end_time
        )

        return {
            "conflicts": [conflict.dict() for conflict in conflicts],
            "has_conflicts": len(conflicts) > 0,
            "conflict_count": len(conflicts)
        }

    except Exception as e:
        logger.error(f"Failed to check schedule conflicts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check schedule conflicts"
        )


@router.get("/schedule/suggestions")
async def get_scheduling_suggestions(
    task_id: Optional[UUID] = Query(None, description="Task to schedule"),
    duration_minutes: int = Query(..., ge=5, le=480, description="Duration in minutes"),
    energy_level: str = Query("medium", description="Required energy level"),
    preferred_time: Optional[datetime] = Query(None, description="Preferred start time"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get ADHD-friendly scheduling suggestions.

    Analyzes the user's schedule and energy patterns to suggest optimal
    time slots for new activities.
    """
    # TODO: Implement intelligent scheduling suggestions
    # This would analyze:
    # - User's energy patterns
    # - Existing schedule gaps
    # - Task requirements
    # - ADHD-specific preferences

    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Scheduling suggestions not yet implemented"
    )
