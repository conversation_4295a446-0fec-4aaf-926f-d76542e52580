"""
Motivation API endpoints for Project Chronos.

This module provides ADHD-optimized motivation endpoints including
dopamine menu, activity tracking, and motivation insights.
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.schemas.motivation import (
    DopamineActivityResponse,
    DopamineActivityCreate,
    DopamineActivityUpdate,
    DopamineMenuRequest,
    DopamineMenuResponse,
    UserDopaminePreferenceResponse,
    DopamineActivityCompletionCreate,
    DopamineActivityCompletionResponse,
    MotivationAnalyticsResponse,
    MotivationDashboardResponse,
    CustomActivityRequest,
    EnergyMoodTrackingRequest,
    EnergyMoodTrackingResponse,
)
from app.services.motivation_service import MotivationService
from app.services.gamification_service import GamificationService
from app.services.achievement_service import AchievementService
from app.core.exceptions import MotivationError, UserNotFoundError

router = APIRouter(prefix="/motivation", tags=["motivation"])


@router.get("/dopamine-menu", response_model=DopamineMenuResponse)
async def get_dopamine_menu(
    energy_level: str = Query("medium", description="Current energy level"),
    available_time: int = Query(5, description="Available time in minutes"),
    context: str = Query("pre_task", description="Context for the activity"),
    exclude_categories: List[str] = Query([], description="Categories to exclude"),
    preferred_categories: List[str] = Query([], description="Preferred categories"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get personalized dopamine menu activities."""
    try:
        motivation_service = MotivationService(db)
        
        request = DopamineMenuRequest(
            energy_level=energy_level,
            available_time=available_time,
            context=context,
            exclude_categories=exclude_categories,
            preferred_categories=preferred_categories
        )
        
        menu = await motivation_service.get_dopamine_menu(current_user.id, request)
        return menu
    except UserNotFoundError:
        raise HTTPException(status_code=404, detail="User not found")
    except MotivationError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/dopamine-menu/complete", response_model=DopamineActivityCompletionResponse)
async def complete_dopamine_activity(
    completion_data: DopamineActivityCompletionCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Mark dopamine activity as completed and track effectiveness."""
    try:
        # Ensure the completion is for the current user
        completion_data.user_id = current_user.id
        
        motivation_service = MotivationService(db)
        completion = await motivation_service.complete_dopamine_activity(completion_data)
        
        # Award points for completing dopamine activity
        gamification_service = GamificationService(db)
        await gamification_service.award_points(
            user_id=current_user.id,
            points=10,  # Base points for dopamine activity
            reason="Completed dopamine activity",
            metadata={"activity_id": str(completion_data.activity_id) if completion_data.activity_id else None}
        )
        
        # Check for motivation-related achievements
        achievement_service = AchievementService(db)
        await achievement_service.check_achievements(
            user_id=current_user.id,
            trigger_event="dopamine_activity_completed",
            event_data={"activity_id": completion_data.activity_id}
        )
        
        return completion
    except UserNotFoundError:
        raise HTTPException(status_code=404, detail="User not found")
    except MotivationError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/activities", response_model=List[DopamineActivityResponse])
async def get_dopamine_activities(
    category: Optional[str] = Query(None, description="Filter by category"),
    energy_level: Optional[str] = Query(None, description="Filter by energy requirement"),
    max_duration: Optional[int] = Query(None, description="Maximum duration in minutes"),
    db: AsyncSession = Depends(get_db)
):
    """Get available dopamine activities."""
    try:
        from sqlalchemy import select, and_
        from app.models.motivation import DopamineActivity
        
        query = select(DopamineActivity).where(DopamineActivity.is_active == True)
        
        if category:
            query = query.where(DopamineActivity.category == category)
        if energy_level:
            query = query.where(DopamineActivity.energy_requirement == energy_level)
        if max_duration:
            query = query.where(DopamineActivity.duration_max <= max_duration)
        
        result = await db.execute(query)
        activities = result.scalars().all()
        return activities
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/activities", response_model=DopamineActivityResponse)
async def create_dopamine_activity(
    activity_data: DopamineActivityCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create a new dopamine activity (admin endpoint)."""
    try:
        from app.models.motivation import DopamineActivity
        
        activity = DopamineActivity(
            name=activity_data.name,
            description=activity_data.description,
            category=activity_data.category,
            duration_min=activity_data.duration_min,
            duration_max=activity_data.duration_max,
            energy_requirement=activity_data.energy_requirement,
            energy_boost=activity_data.energy_boost,
            requires_equipment=activity_data.requires_equipment,
            requires_space=activity_data.requires_space,
            can_do_anywhere=activity_data.can_do_anywhere,
            tags=activity_data.tags,
            instructions=activity_data.instructions,
            is_active=activity_data.is_active
        )
        
        db.add(activity)
        await db.commit()
        await db.refresh(activity)
        return activity
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/activities/custom", response_model=UserDopaminePreferenceResponse)
async def create_custom_activity(
    activity_request: CustomActivityRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a custom dopamine activity for the user."""
    try:
        motivation_service = MotivationService(db)
        preference = await motivation_service.create_custom_activity(
            user_id=current_user.id,
            activity_request=activity_request
        )
        return preference
    except UserNotFoundError:
        raise HTTPException(status_code=404, detail="User not found")
    except MotivationError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/preferences", response_model=List[UserDopaminePreferenceResponse])
async def get_user_preferences(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's dopamine activity preferences."""
    try:
        from sqlalchemy import select
        from sqlalchemy.orm import selectinload
        from app.models.motivation import UserDopaminePreference
        
        result = await db.execute(
            select(UserDopaminePreference)
            .options(selectinload(UserDopaminePreference.activity))
            .where(UserDopaminePreference.user_id == current_user.id)
        )
        preferences = result.scalars().all()
        return preferences
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/analytics", response_model=MotivationAnalyticsResponse)
async def get_motivation_analytics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get motivation analytics for the user."""
    try:
        motivation_service = MotivationService(db)
        analytics = await motivation_service.get_motivation_analytics(current_user.id)
        return analytics
    except UserNotFoundError:
        raise HTTPException(status_code=404, detail="User not found")
    except MotivationError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/dashboard", response_model=MotivationDashboardResponse)
async def get_motivation_dashboard(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get comprehensive motivation dashboard data."""
    try:
        motivation_service = MotivationService(db)
        
        # Get recent completions
        from sqlalchemy import select, desc
        from app.models.motivation import DopamineActivityCompletion
        from sqlalchemy.orm import selectinload
        
        recent_completions_result = await db.execute(
            select(DopamineActivityCompletion)
            .options(selectinload(DopamineActivityCompletion.activity))
            .where(DopamineActivityCompletion.user_id == current_user.id)
            .order_by(desc(DopamineActivityCompletion.created_at))
            .limit(10)
        )
        recent_completions = recent_completions_result.scalars().all()
        
        # Get suggested activities (simplified)
        menu_request = DopamineMenuRequest(
            energy_level="medium",
            available_time=10,
            context="general"
        )
        menu = await motivation_service.get_dopamine_menu(current_user.id, menu_request)
        suggested_activities = menu.activities
        
        # Get analytics
        analytics = await motivation_service.get_motivation_analytics(current_user.id)
        
        # Get active insights (placeholder)
        active_insights = []
        
        # Generate personalization tips
        personalization_tips = [
            "Try tracking your energy levels to get better activity suggestions",
            "Rate completed activities to improve recommendations",
            "Create custom activities that work specifically for you"
        ]
        
        return MotivationDashboardResponse(
            recent_completions=recent_completions,
            suggested_activities=suggested_activities,
            active_insights=active_insights,
            analytics=analytics,
            personalization_tips=personalization_tips
        )
    except UserNotFoundError:
        raise HTTPException(status_code=404, detail="User not found")
    except MotivationError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/energy-mood/track", response_model=EnergyMoodTrackingResponse)
async def track_energy_mood(
    tracking_data: EnergyMoodTrackingRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Track energy and mood levels."""
    try:
        from datetime import datetime
        
        # Store tracking data (in a real implementation, this would go to a tracking table)
        # For now, we'll just return suggested activities based on energy level
        
        motivation_service = MotivationService(db)
        menu_request = DopamineMenuRequest(
            energy_level=tracking_data.energy_level,
            available_time=10,
            context="energy_boost"
        )
        menu = await motivation_service.get_dopamine_menu(current_user.id, menu_request)
        
        # Generate insights based on energy level
        insights = []
        if tracking_data.energy_level == "low":
            insights.append("Consider gentle movement or breathing exercises")
            insights.append("Low energy is normal - be kind to yourself")
        elif tracking_data.energy_level == "high":
            insights.append("Great time for challenging tasks!")
            insights.append("Consider activities that channel this energy productively")
        
        return EnergyMoodTrackingResponse(
            recorded_at=datetime.utcnow(),
            energy_level=tracking_data.energy_level,
            mood_rating=tracking_data.mood_rating,
            suggested_activities=menu.activities,
            insights=insights
        )
    except UserNotFoundError:
        raise HTTPException(status_code=404, detail="User not found")
    except MotivationError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/initialize")
async def initialize_motivation(
    db: AsyncSession = Depends(get_db)
):
    """Initialize motivation system with default activities (admin endpoint)."""
    try:
        motivation_service = MotivationService(db)
        await motivation_service.initialize_default_activities()
        return {"message": "Motivation system initialized successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to initialize motivation: {str(e)}")
