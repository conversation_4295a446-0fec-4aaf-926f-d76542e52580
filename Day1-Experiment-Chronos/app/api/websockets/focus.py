"""
Focus session WebSocket endpoints for Project Chronos.

This module provides real-time WebSocket communication for focus sessions,
including group synchronization, progress updates, and session coordination.
"""

import json
import logging
from typing import Dict, Any
from uuid import UUID

from fastapi import WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.websocket_manager import ConnectionManager
from app.middleware.websocket_auth import authenticate_websocket_user
from app.models.user import User
from app.services.focus_service import FocusSessionService
from app.schemas.focus import FocusSessionCommand

logger = logging.getLogger(__name__)

# WebSocket manager for focus sessions
focus_ws_manager = ConnectionManager()
focus_service = FocusSessionService()


async def handle_focus_websocket(
    websocket: WebSocket,
    session_id: str,
    db: AsyncSession = Depends(get_db)
):
    """
    Handle WebSocket connections for focus sessions.
    
    Provides real-time communication for:
    - Session progress updates
    - Group synchronization
    - Timer coordination
    - Participant status changes
    """
    user = None
    try:
        # Authenticate user
        user = await authenticate_websocket_user(websocket)
        if not user:
            await websocket.close(code=4001, reason="Authentication required")
            return
        
        # Validate session access
        session_uuid = UUID(session_id)
        session = await focus_service.get_session(db, session_uuid, user)
        if not session:
            await websocket.close(code=4004, reason="Focus session not found")
            return
        
        # Accept connection and add to manager
        await focus_ws_manager.connect(websocket, user.id, session_uuid)
        
        # Send initial session state
        await websocket.send_text(json.dumps({
            "type": "session_state",
            "data": {
                "session_id": str(session.id),
                "status": session.status,
                "planned_duration": session.planned_duration,
                "elapsed_minutes": session.get_elapsed_minutes(),
                "remaining_minutes": session.get_remaining_minutes(),
                "progress_percentage": session.get_progress_percentage(),
                "is_group_session": session.is_group_session,
                "participant_count": 1 if not session.is_group_session else None  # TODO: Get actual count
            }
        }))
        
        # Notify other participants if group session
        if session.is_group_session:
            from app.schemas.body_doubling import WebSocketMessage
            await focus_ws_manager.broadcast_to_session(
                session_uuid,
                WebSocketMessage(
                    type="participant_joined",
                    data={
                        "user_id": str(user.id),
                        "user_name": user.display_name or user.full_name,
                        "joined_at": session.created_at.isoformat()
                    }
                ),
                exclude_user=user.id
            )
        
        # Listen for messages
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                await handle_focus_message(websocket, message, session_uuid, user, db)
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Invalid JSON format"
                }))
            except Exception as e:
                logger.error(f"Error handling focus WebSocket message: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Internal server error"
                }))
    
    except Exception as e:
        logger.error(f"Focus WebSocket connection error: {e}")
        if websocket.client_state.name != "DISCONNECTED":
            await websocket.close(code=4000, reason="Internal server error")
    
    finally:
        # Clean up connection
        if user:
            await focus_ws_manager.disconnect(user.id)

            # Notify other participants if group session
            session = await focus_service.get_session(db, UUID(session_id), user)
            if session and session.is_group_session:
                from app.schemas.body_doubling import WebSocketMessage
                await focus_ws_manager.broadcast_to_session(
                    UUID(session_id),
                    WebSocketMessage(
                        type="participant_left",
                        data={
                            "user_id": str(user.id),
                            "user_name": user.display_name or user.full_name
                        }
                    ),
                    exclude_user=user.id
                )


async def handle_focus_message(
    websocket: WebSocket,
    message: Dict[str, Any],
    session_id: UUID,
    user: User,
    db: AsyncSession
):
    """
    Handle incoming WebSocket messages for focus sessions.
    
    Message types:
    - session_command: Execute session commands (start, pause, resume, complete)
    - progress_update: Update session progress and metrics
    - sync_request: Request session synchronization
    - participant_status: Update participant status in group sessions
    """
    message_type = message.get("type")
    data = message.get("data", {})
    
    try:
        if message_type == "session_command":
            await handle_session_command(websocket, data, session_id, user, db)
        
        elif message_type == "progress_update":
            await handle_progress_update(websocket, data, session_id, user, db)
        
        elif message_type == "sync_request":
            await handle_sync_request(websocket, session_id, user, db)
        
        elif message_type == "participant_status":
            await handle_participant_status(websocket, data, session_id, user, db)
        
        elif message_type == "heartbeat":
            await websocket.send_text(json.dumps({
                "type": "heartbeat_ack",
                "timestamp": data.get("timestamp")
            }))
        
        else:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": f"Unknown message type: {message_type}"
            }))
    
    except Exception as e:
        logger.error(f"Error handling focus message type {message_type}: {e}")
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": f"Failed to handle {message_type}"
        }))


async def handle_session_command(
    websocket: WebSocket,
    data: Dict[str, Any],
    session_id: UUID,
    user: User,
    db: AsyncSession
):
    """Handle session command messages (start, pause, resume, complete)."""
    try:
        command = data.get("command")
        notes = data.get("notes")
        
        if not command:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "Command is required"
            }))
            return
        
        # Execute command
        command_data = FocusSessionCommand(command=command, notes=notes)
        session = await focus_service.execute_command(db, session_id, command_data, user)
        
        if not session:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "Session not found"
            }))
            return
        
        # Send response to user
        response = {
            "type": "command_executed",
            "data": {
                "command": command,
                "session_id": str(session.id),
                "status": session.status,
                "started_at": session.started_at.isoformat() if session.started_at else None,
                "completed_at": session.completed_at.isoformat() if session.completed_at else None,
                "paused_at": session.paused_at.isoformat() if session.paused_at else None,
                "elapsed_minutes": session.get_elapsed_minutes(),
                "remaining_minutes": session.get_remaining_minutes(),
                "progress_percentage": session.get_progress_percentage()
            }
        }
        
        await websocket.send_text(json.dumps(response))
        
        # Broadcast to group session participants
        if session.is_group_session:
            from app.schemas.body_doubling import WebSocketMessage
            await focus_ws_manager.broadcast_to_session(
                session_id,
                WebSocketMessage(
                    type="session_updated",
                    data={
                        "session_id": str(session.id),
                        "status": session.status,
                        "command": command,
                        "updated_by": user.display_name or user.full_name,
                        "elapsed_minutes": session.get_elapsed_minutes(),
                        "remaining_minutes": session.get_remaining_minutes(),
                        "progress_percentage": session.get_progress_percentage()
                    }
                ),
                exclude_user=user.id
            )
    
    except ValueError as e:
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": str(e)
        }))
    except Exception as e:
        logger.error(f"Error executing session command: {e}")
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": "Failed to execute command"
        }))


async def handle_progress_update(
    websocket: WebSocket,
    data: Dict[str, Any],
    session_id: UUID,
    user: User,
    db: AsyncSession
):
    """Handle progress update messages."""
    try:
        # Update session with progress data
        from app.schemas.focus import FocusSessionUpdate
        
        update_data = FocusSessionUpdate(
            distraction_count=data.get("distraction_count"),
            energy_level_start=data.get("energy_level_start"),
            energy_level_end=data.get("energy_level_end"),
            session_notes=data.get("session_notes"),
            hyperfocus_detected=data.get("hyperfocus_detected"),
            hyperfocus_duration=data.get("hyperfocus_duration")
        )
        
        session = await focus_service.update_session(db, session_id, update_data, user)
        
        if not session:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "Session not found"
            }))
            return
        
        # Confirm update
        await websocket.send_text(json.dumps({
            "type": "progress_updated",
            "data": {
                "session_id": str(session.id),
                "distraction_count": session.distraction_count,
                "hyperfocus_detected": session.hyperfocus_detected
            }
        }))
        
        # Broadcast to group if applicable
        if session.is_group_session:
            from app.schemas.body_doubling import WebSocketMessage
            await focus_ws_manager.broadcast_to_session(
                session_id,
                WebSocketMessage(
                    type="participant_progress",
                    data={
                        "user_id": str(user.id),
                        "user_name": user.display_name or user.full_name,
                        "progress_update": data
                    }
                ),
                exclude_user=user.id
            )
    
    except Exception as e:
        logger.error(f"Error updating progress: {e}")
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": "Failed to update progress"
        }))


async def handle_sync_request(
    websocket: WebSocket,
    session_id: UUID,
    user: User,
    db: AsyncSession
):
    """Handle session synchronization requests."""
    try:
        session = await focus_service.get_session(db, session_id, user)
        
        if not session:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "Session not found"
            }))
            return
        
        # Send current session state
        await websocket.send_text(json.dumps({
            "type": "session_sync",
            "data": {
                "session_id": str(session.id),
                "status": session.status,
                "planned_duration": session.planned_duration,
                "elapsed_minutes": session.get_elapsed_minutes(),
                "remaining_minutes": session.get_remaining_minutes(),
                "progress_percentage": session.get_progress_percentage(),
                "started_at": session.started_at.isoformat() if session.started_at else None,
                "paused_at": session.paused_at.isoformat() if session.paused_at else None
            }
        }))
    
    except Exception as e:
        logger.error(f"Error handling sync request: {e}")
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": "Failed to sync session"
        }))


async def handle_participant_status(
    websocket: WebSocket,
    data: Dict[str, Any],
    session_id: UUID,
    user: User,
    db: AsyncSession
):
    """Handle participant status updates in group sessions."""
    try:
        # This would update participant status in the database
        # For now, just broadcast the status change
        
        session = await focus_service.get_session(db, session_id, user)
        if not session or not session.is_group_session:
            return
        
        from app.schemas.body_doubling import WebSocketMessage
        await focus_ws_manager.broadcast_to_session(
            session_id,
            WebSocketMessage(
                type="participant_status_changed",
                data={
                    "user_id": str(user.id),
                    "user_name": user.display_name or user.full_name,
                    "status": data.get("status"),
                    "notes": data.get("notes")
                }
            ),
            exclude_user=user.id
        )
    
    except Exception as e:
        logger.error(f"Error handling participant status: {e}")
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": "Failed to update participant status"
        }))
