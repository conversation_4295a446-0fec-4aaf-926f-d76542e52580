"""
Task model with ADHD-optimized features for Project Chronos.

This module defines the Task model with AI chunking support,
energy levels, context tags, and adaptive filtering capabilities.
"""

from datetime import datetime
from typing import Optional, List
from uuid import UUID
from sqlalchemy import String, Text, Integer, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.models.base import Base


class Task(Base):
    """
    Task model with ADHD-specific features and AI chunking support.
    
    Designed to combat task paralysis through intelligent chunking,
    adaptive filtering, and context-aware task management.
    """
    
    __tablename__ = "tasks"
    
    # Basic task information
    title: Mapped[str] = mapped_column(
        String(500),
        doc="Task title (max 500 chars for ADHD-friendly brevity)"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Detailed task description"
    )
    
    # Task status and priority
    status: Mapped[str] = mapped_column(
        String(20),
        default="pending",
        doc="Task status (pending/in_progress/completed/cancelled)"
    )
    
    priority: Mapped[str] = mapped_column(
        String(10),
        default="medium",
        doc="Task priority (low/medium/high/urgent)"
    )
    
    # ADHD-specific fields
    energy_level: Mapped[str] = mapped_column(
        String(10),
        default="medium",
        doc="Required energy level (low/medium/high)"
    )
    
    estimated_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Estimated duration in minutes"
    )
    
    actual_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Actual time spent in minutes"
    )
    
    context_tags: Mapped[Optional[List[str]]] = mapped_column(
        JSON,
        nullable=True,
        doc="Context tags for adaptive filtering (home, office, phone, etc.)"
    )
    
    # AI chunking support
    is_chunked: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        doc="Whether this task has been broken down by AI"
    )
    
    chunk_size: Mapped[Optional[str]] = mapped_column(
        String(20),
        nullable=True,
        doc="Size of chunks if task was chunked (small/medium/large)"
    )
    
    ai_chunking_metadata: Mapped[Optional[dict]] = mapped_column(
        Text,  # Store as JSON string
        nullable=True,
        doc="Metadata from AI chunking process"
    )
    
    # Task relationships for chunking
    parent_task_id: Mapped[Optional[UUID]] = mapped_column(
        PGUUID(as_uuid=True),
        ForeignKey("tasks.id", ondelete="CASCADE"),
        nullable=True,
        doc="Parent task ID if this is a chunk"
    )
    
    # Timing
    due_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Task due date"
    )
    
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the task was completed"
    )
    
    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When work on the task began"
    )
    
    # User relationship
    user_id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        doc="ID of the user who owns this task"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="tasks",
        doc="User who owns this task"
    )

    focus_sessions: Mapped[List["FocusSession"]] = relationship(
        "FocusSession",
        back_populates="task",
        doc="Focus sessions associated with this task"
    )

    time_blocks: Mapped[List["TimeBlock"]] = relationship(
        "TimeBlock",
        back_populates="task",
        doc="Time blocks associated with this task"
    )

    notifications: Mapped[List["Notification"]] = relationship(
        "Notification",
        back_populates="task",
        doc="Notifications associated with this task"
    )
    
    parent_task: Mapped[Optional["Task"]] = relationship(
        "Task",
        remote_side="Task.id",
        back_populates="subtasks",
        doc="Parent task if this is a chunk"
    )
    
    subtasks: Mapped[List["Task"]] = relationship(
        "Task",
        back_populates="parent_task",
        cascade="all, delete-orphan",
        doc="Subtasks created by AI chunking"
    )

    focus_sessions: Mapped[List["FocusSession"]] = relationship(
        "FocusSession",
        back_populates="task",
        doc="Focus sessions associated with this task"
    )

    time_blocks: Mapped[List["TimeBlock"]] = relationship(
        "TimeBlock",
        back_populates="task",
        doc="Time blocks scheduled for this task"
    )
    
    def __repr__(self) -> str:
        """
        String representation of the task.
        
        Returns:
            str: Human-readable representation with key task info
        """
        return (
            f"<Task("
            f"title='{self.title[:30]}...', "
            f"status={self.status}, "
            f"energy_level={self.energy_level}, "
            f"is_chunked={self.is_chunked}"
            f")>"
        )
    
    @property
    def is_overdue(self) -> bool:
        """
        Check if the task is overdue.
        
        Returns:
            bool: True if task has a due date and it's passed
        """
        if not self.due_date or self.status == "completed":
            return False
        return datetime.utcnow() > self.due_date
    
    @property
    def is_completed(self) -> bool:
        """
        Check if the task is completed.
        
        Returns:
            bool: True if task status is completed
        """
        return self.status == "completed"
    
    @property
    def duration_accuracy(self) -> Optional[float]:
        """
        Calculate accuracy of time estimation.
        
        Returns:
            float: Ratio of estimated to actual duration, or None if incomplete
        """
        if not self.estimated_duration or not self.actual_duration:
            return None
        return self.estimated_duration / self.actual_duration
    
    def mark_completed(self) -> None:
        """
        Mark the task as completed.
        
        Sets status to completed and records completion timestamp.
        ADHD-friendly with clear state transition.
        """
        self.status = "completed"
        self.completed_at = datetime.utcnow()
    
    def start_work(self) -> None:
        """
        Mark the task as started.
        
        Sets status to in_progress and records start timestamp.
        Helps with time tracking for ADHD users.
        """
        if self.status == "pending":
            self.status = "in_progress"
            self.started_at = datetime.utcnow()
    
    def calculate_urgency_score(self) -> float:
        """
        Calculate urgency score for task prioritization.
        
        Considers due date, priority, and ADHD-specific factors.
        
        Returns:
            float: Urgency score (higher = more urgent)
        """
        score = 0.0
        
        # Base priority score
        priority_scores = {"low": 1.0, "medium": 2.0, "high": 3.0, "urgent": 4.0}
        score += priority_scores.get(self.priority, 2.0)
        
        # Due date urgency
        if self.due_date:
            days_until_due = (self.due_date - datetime.utcnow()).days
            if days_until_due <= 0:
                score += 5.0  # Overdue
            elif days_until_due <= 1:
                score += 3.0  # Due today/tomorrow
            elif days_until_due <= 7:
                score += 1.0  # Due this week
        
        # Energy level consideration (lower energy = higher urgency for low-energy tasks)
        if self.energy_level == "low":
            score += 0.5  # Easier to do when tired
        
        return score
