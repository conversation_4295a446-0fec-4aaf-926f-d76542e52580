"""
Motivation and dopamine menu models for Project Chronos.

This module defines models for the motivation system including dopamine
menu activities, user preferences, and motivation analytics for ADHD users.
"""

from datetime import datetime
from typing import Any, Dict, Optional, List
from uuid import UUID

from sqlalchemy import Boolean, Foreign<PERSON>ey, Integer, String, DateTime, Float, Text, JSON
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import Base


class DopamineActivity(Base):
    """
    Dopamine menu activity definition.
    
    This model defines activities that can be suggested to users
    as pre-task motivation boosters or rewards.
    """
    
    __tablename__ = "dopamine_activities"
    
    # Activity details
    name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="Name of the dopamine activity"
    )
    
    description: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Description of the activity"
    )
    
    category: Mapped[str] = mapped_column(
        String(30),
        nullable=False,
        index=True,
        doc="Activity category: movement, creative, social, sensory, mental"
    )
    
    # Activity properties
    duration_min: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Minimum duration in minutes"
    )
    
    duration_max: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Maximum duration in minutes"
    )
    
    energy_requirement: Mapped[str] = mapped_column(
        String(10),
        nullable=False,
        doc="Energy level required: low, medium, high"
    )
    
    energy_boost: Mapped[str] = mapped_column(
        String(10),
        nullable=False,
        doc="Energy boost provided: low, medium, high"
    )
    
    # Accessibility
    requires_equipment: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the activity requires special equipment"
    )
    
    requires_space: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the activity requires significant space"
    )
    
    can_do_anywhere: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether the activity can be done anywhere"
    )
    
    # Metadata
    tags: Mapped[List[str]] = mapped_column(
        JSON,
        default=list,
        nullable=False,
        doc="Tags for filtering and categorization"
    )
    
    instructions: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Optional instructions for the activity"
    )
    
    # Status
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether this activity is currently active"
    )
    
    # Relationships
    user_preferences: Mapped[List["UserDopaminePreference"]] = relationship(
        "UserDopaminePreference", back_populates="activity", cascade="all, delete-orphan"
    )
    completions: Mapped[List["DopamineActivityCompletion"]] = relationship(
        "DopamineActivityCompletion", back_populates="activity", cascade="all, delete-orphan"
    )


class UserDopaminePreference(Base):
    """
    User preferences for dopamine menu activities.

    This model tracks which activities users prefer, their custom
    activities, and personalization settings.
    """
    
    __tablename__ = "user_dopamine_preferences"
    
    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user"
    )
    
    activity_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("dopamine_activities.id", ondelete="CASCADE"),
        nullable=True,
        doc="ID of the predefined activity (null for custom activities)"
    )
    
    # Preference details
    preference_type: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        doc="Type: liked, disliked, custom, favorite"
    )
    
    rating: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="User rating from 1-5"
    )
    
    # Custom activity details (for user-created activities)
    custom_name: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        doc="Name of custom activity"
    )
    
    custom_description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Description of custom activity"
    )
    
    custom_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Duration of custom activity in minutes"
    )
    
    custom_category: Mapped[Optional[str]] = mapped_column(
        String(30),
        nullable=True,
        doc="Category of custom activity"
    )
    
    # Usage tracking
    times_suggested: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of times this activity was suggested"
    )
    
    times_completed: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of times this activity was completed"
    )
    
    last_suggested: Mapped[Optional[datetime]] = mapped_column(
        DateTime,
        nullable=True,
        doc="When this activity was last suggested"
    )
    
    last_completed: Mapped[Optional[datetime]] = mapped_column(
        DateTime,
        nullable=True,
        doc="When this activity was last completed"
    )
    
    # Relationships
    activity: Mapped[Optional["DopamineActivity"]] = relationship(
        "DopamineActivity", back_populates="user_preferences"
    )


class DopamineActivityCompletion(Base):
    """
    Record of dopamine activity completions.

    This model tracks when users complete dopamine activities,
    their effectiveness, and user feedback.
    """
    
    __tablename__ = "dopamine_activity_completions"
    
    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who completed the activity"
    )
    
    activity_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("dopamine_activities.id", ondelete="SET NULL"),
        nullable=True,
        doc="ID of the completed activity"
    )
    
    task_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("tasks.id", ondelete="SET NULL"),
        nullable=True,
        doc="Associated task (if activity was done before a task)"
    )
    
    # Completion details
    actual_duration: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Actual duration spent on activity in minutes"
    )
    
    energy_before: Mapped[str] = mapped_column(
        String(10),
        nullable=False,
        doc="Energy level before activity: low, medium, high"
    )
    
    energy_after: Mapped[str] = mapped_column(
        String(10),
        nullable=False,
        doc="Energy level after activity: low, medium, high"
    )
    
    mood_before: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Mood rating before activity (1-10)"
    )
    
    mood_after: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Mood rating after activity (1-10)"
    )
    
    # Effectiveness tracking
    helped_with_task: Mapped[Optional[bool]] = mapped_column(
        Boolean,
        nullable=True,
        doc="Whether the activity helped with subsequent task"
    )
    
    satisfaction_rating: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="User satisfaction with activity (1-5)"
    )
    
    would_do_again: Mapped[Optional[bool]] = mapped_column(
        Boolean,
        nullable=True,
        doc="Whether user would do this activity again"
    )
    
    # Context
    context: Mapped[str] = mapped_column(
        String(30),
        nullable=False,
        doc="Context: pre_task, break, reward, spontaneous"
    )
    
    notes: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Optional user notes about the activity"
    )
    
    # Relationships
    activity: Mapped[Optional["DopamineActivity"]] = relationship(
        "DopamineActivity", back_populates="completions"
    )


class MotivationInsight(Base):
    """
    Motivation insights and analytics for users.

    This model stores insights about user motivation patterns,
    effective activities, and personalized recommendations.
    """
    
    __tablename__ = "motivation_insights"
    
    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user"
    )
    
    # Insight details
    insight_type: Mapped[str] = mapped_column(
        String(30),
        nullable=False,
        index=True,
        doc="Type of insight: pattern, recommendation, achievement, warning"
    )
    
    title: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="Title of the insight"
    )
    
    description: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Detailed description of the insight"
    )
    
    # Data and metrics
    insight_data: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Data supporting the insight"
    )
    
    confidence_score: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        doc="Confidence score for the insight (0.0-1.0)"
    )
    
    # Status
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether this insight is currently active"
    )
    
    is_read: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the user has read this insight"
    )
    
    read_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime,
        nullable=True,
        doc="When the insight was read"
    )
    
    # Validity
    valid_until: Mapped[Optional[datetime]] = mapped_column(
        DateTime,
        nullable=True,
        doc="When this insight expires (if applicable)"
    )
