"""
Database models for Project Chronos.

This package contains all SQLAlchemy models for the ADHD-focused productivity
application, including ADHD-specific fields and relationships.
"""

from app.models.base import Base as BaseModel
from app.models.user import User
from app.models.task import Task
from app.models.gamification import (
    UserGamification,
    PointsAward,
    Achievement,
    UserAchievement,
    UserStreak,
)
from app.models.motivation import (
    DopamineActivity,
    UserDopaminePreference,
    DopamineActivityCompletion,
    MotivationInsight,
)
from app.models.body_doubling import (
    BodyDoublingSession,
    SessionParticipant,
    SessionMessage,
)
from app.models.focus import (
    FocusSession,
    FocusSessionParticipant,
)
from app.models.time_blocking import (
    TimeBlock,
    CalendarIntegration,
)
from app.models.notification import Notification

__all__ = [
    "BaseModel",
    "User",
    "Task",
    # Gamification models
    "UserGamification",
    "PointsAward",
    "Achievement",
    "UserAchievement",
    "UserStreak",
    # Motivation models
    "DopamineActivity",
    "UserDopaminePreference",
    "DopamineActivityCompletion",
    "MotivationInsight",
    # Body doubling models
    "BodyDoublingSession",
    "SessionParticipant",
    "SessionMessage",
    # Focus session models
    "FocusSession",
    "FocusSessionParticipant",
    # Time blocking models
    "TimeBlock",
    "CalendarIntegration",
    # Notification models
    "Notification",
]
