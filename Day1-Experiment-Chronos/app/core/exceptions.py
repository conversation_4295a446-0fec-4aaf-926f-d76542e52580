"""
Custom exception hierarchy for Project Chronos.

This module defines ADHD-specific exceptions for task management,
AI chunking, and adaptive filtering operations.
"""

from typing import Any, Dict, Optional


class ChronosException(Exception):
    """
    Base exception for Project Chronos.
    
    All custom exceptions should inherit from this base class
    to maintain consistent error handling across the application.
    """
    
    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None
    ):
        self.message = message
        self.details = details or {}
        self.error_code = error_code
        super().__init__(self.message)


class TaskException(ChronosException):
    """Base exception for task-related operations."""
    pass


class TaskNotFoundError(TaskException):
    """Raised when a requested task cannot be found."""
    
    def __init__(self, task_id: str):
        super().__init__(
            f"Task with ID {task_id} not found",
            details={"task_id": task_id},
            error_code="TASK_NOT_FOUND"
        )


class TaskValidationError(TaskException):
    """Raised when task data fails validation."""
    
    def __init__(self, field: str, value: Any, reason: str):
        super().__init__(
            f"Invalid value for field '{field}': {reason}",
            details={"field": field, "value": value, "reason": reason},
            error_code="TASK_VALIDATION_ERROR"
        )


class AIServiceException(ChronosException):
    """Base exception for AI service operations."""
    pass


class AIServiceUnavailableError(AIServiceException):
    """Raised when AI service is unavailable."""
    
    def __init__(self, service_name: str, reason: str):
        super().__init__(
            f"AI service '{service_name}' is unavailable: {reason}",
            details={"service": service_name, "reason": reason},
            error_code="AI_SERVICE_UNAVAILABLE"
        )


class AIChunkingError(AIServiceException):
    """Raised when AI chunking fails."""
    
    def __init__(self, task_title: str, reason: str):
        super().__init__(
            f"Failed to chunk task '{task_title}': {reason}",
            details={"task_title": task_title, "reason": reason},
            error_code="AI_CHUNKING_FAILED"
        )


class FilterException(ChronosException):
    """Base exception for filtering operations."""
    pass


class InvalidFilterError(FilterException):
    """Raised when filter parameters are invalid."""
    
    def __init__(self, filter_name: str, value: Any, reason: str):
        super().__init__(
            f"Invalid filter '{filter_name}' with value '{value}': {reason}",
            details={"filter": filter_name, "value": value, "reason": reason},
            error_code="INVALID_FILTER"
        )


class AuthenticationError(ChronosException):
    """Raised when authentication fails."""
    
    def __init__(self, reason: str = "Invalid credentials"):
        super().__init__(
            f"Authentication failed: {reason}",
            details={"reason": reason},
            error_code="AUTHENTICATION_FAILED"
        )


class AuthorizationError(ChronosException):
    """Raised when user lacks required permissions."""

    def __init__(self, resource: str, action: str):
        super().__init__(
            f"Access denied for action '{action}' on resource '{resource}'",
            details={"resource": resource, "action": action},
            error_code="ACCESS_DENIED"
        )


class UserNotFoundError(ChronosException):
    """Raised when a user cannot be found."""

    def __init__(self, user_id: str):
        super().__init__(
            f"User with ID {user_id} not found",
            details={"user_id": user_id},
            error_code="USER_NOT_FOUND"
        )


class GamificationError(ChronosException):
    """Base exception for gamification operations."""
    pass


class AchievementError(ChronosException):
    """Base exception for achievement operations."""
    pass


class MotivationError(ChronosException):
    """Base exception for motivation operations."""
    pass


class BodyDoublingError(ChronosException):
    """Base exception for body doubling operations."""
    pass


class SessionNotFoundError(BodyDoublingError):
    """Raised when a body doubling session is not found."""

    def __init__(self, session_id: str):
        super().__init__(
            f"Body doubling session {session_id} not found",
            details={"session_id": session_id},
            error_code="SESSION_NOT_FOUND"
        )


class SessionFullError(BodyDoublingError):
    """Raised when trying to join a full session."""

    def __init__(self, session_id: str, max_participants: int):
        super().__init__(
            f"Session {session_id} is full (max {max_participants} participants)",
            details={"session_id": session_id, "max_participants": max_participants},
            error_code="SESSION_FULL"
        )


class PermissionDeniedError(ChronosException):
    """Raised when user lacks permission for an action."""

    def __init__(self, action: str, reason: str = "Insufficient permissions"):
        super().__init__(
            f"Permission denied for action '{action}': {reason}",
            details={"action": action, "reason": reason},
            error_code="PERMISSION_DENIED"
        )


class WebSocketError(ChronosException):
    """Base exception for WebSocket-related errors."""
    pass


class FocusSessionError(ChronosException):
    """Base exception for focus session operations."""

    def __init__(self, message: str, session_id: str = None):
        super().__init__(
            message,
            details={"session_id": session_id} if session_id else None,
            error_code="FOCUS_SESSION_ERROR"
        )
