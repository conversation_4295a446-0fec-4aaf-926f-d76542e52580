"""
WebSocket connection manager for Project Chronos.

This module provides centralized WebSocket connection management for real-time
features including body doubling sessions, focus session synchronization,
and live progress updates.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Set
from uuid import UUID

from fastapi import WebSocket, WebSocketDisconnect
from pydantic import ValidationError

from app.schemas.body_doubling import WebSocketMessage

logger = logging.getLogger(__name__)


class ConnectionManager:
    """
    Manages WebSocket connections for real-time features.
    
    Handles connection lifecycle, message broadcasting, and session-based
    communication for body doubling and focus sessions.
    """
    
    def __init__(self):
        # Active connections by user ID
        self.active_connections: Dict[UUID, WebSocket] = {}
        
        # Session participants mapping
        self.session_participants: Dict[UUID, Set[UUID]] = {}
        
        # User to session mapping for quick lookups
        self.user_sessions: Dict[UUID, UUID] = {}
        
        # Connection metadata
        self.connection_metadata: Dict[UUID, Dict] = {}
    
    async def connect(self, websocket: WebSocket, user_id: UUID, session_id: Optional[UUID] = None):
        """
        Accept a new WebSocket connection.
        
        Args:
            websocket: WebSocket connection
            user_id: ID of the connecting user
            session_id: Optional session ID for body doubling
        """
        await websocket.accept()
        
        # Store connection
        self.active_connections[user_id] = websocket
        
        # Store metadata
        self.connection_metadata[user_id] = {
            "session_id": session_id,
            "connected_at": asyncio.get_event_loop().time(),
            "last_heartbeat": asyncio.get_event_loop().time()
        }
        
        # Add to session if specified
        if session_id:
            await self.join_session(user_id, session_id)
        
        logger.info(f"User {user_id} connected to WebSocket")
    
    async def disconnect(self, user_id: UUID):
        """
        Handle WebSocket disconnection.
        
        Args:
            user_id: ID of the disconnecting user
        """
        # Remove from session if in one
        if user_id in self.user_sessions:
            session_id = self.user_sessions[user_id]
            await self.leave_session(user_id, session_id)
        
        # Clean up connection data
        self.active_connections.pop(user_id, None)
        self.connection_metadata.pop(user_id, None)
        
        logger.info(f"User {user_id} disconnected from WebSocket")
    
    async def join_session(self, user_id: UUID, session_id: UUID):
        """
        Add user to a body doubling session.
        
        Args:
            user_id: ID of the user joining
            session_id: ID of the session to join
        """
        # Initialize session participants if needed
        if session_id not in self.session_participants:
            self.session_participants[session_id] = set()
        
        # Add user to session
        self.session_participants[session_id].add(user_id)
        self.user_sessions[user_id] = session_id
        
        # Update metadata
        if user_id in self.connection_metadata:
            self.connection_metadata[user_id]["session_id"] = session_id
        
        # Notify other participants
        await self.broadcast_to_session(
            session_id,
            WebSocketMessage(
                type="participant_joined",
                data={"user_id": str(user_id), "session_id": str(session_id)}
            ),
            exclude_user=user_id
        )
        
        logger.info(f"User {user_id} joined session {session_id}")
    
    async def leave_session(self, user_id: UUID, session_id: UUID):
        """
        Remove user from a body doubling session.
        
        Args:
            user_id: ID of the user leaving
            session_id: ID of the session to leave
        """
        # Remove from session
        if session_id in self.session_participants:
            self.session_participants[session_id].discard(user_id)
            
            # Clean up empty sessions
            if not self.session_participants[session_id]:
                del self.session_participants[session_id]
        
        # Remove user session mapping
        self.user_sessions.pop(user_id, None)
        
        # Update metadata
        if user_id in self.connection_metadata:
            self.connection_metadata[user_id]["session_id"] = None
        
        # Notify other participants
        await self.broadcast_to_session(
            session_id,
            WebSocketMessage(
                type="participant_left",
                data={"user_id": str(user_id), "session_id": str(session_id)}
            ),
            exclude_user=user_id
        )
        
        logger.info(f"User {user_id} left session {session_id}")
    
    async def send_personal_message(self, user_id: UUID, message: WebSocketMessage):
        """
        Send a message to a specific user.
        
        Args:
            user_id: ID of the target user
            message: Message to send
        """
        if user_id in self.active_connections:
            try:
                websocket = self.active_connections[user_id]
                await websocket.send_text(message.json())
            except Exception as e:
                logger.error(f"Error sending message to user {user_id}: {e}")
                # Connection might be stale, remove it
                await self.disconnect(user_id)
    
    async def broadcast_to_session(
        self, 
        session_id: UUID, 
        message: WebSocketMessage, 
        exclude_user: Optional[UUID] = None
    ):
        """
        Broadcast a message to all participants in a session.
        
        Args:
            session_id: ID of the target session
            message: Message to broadcast
            exclude_user: Optional user ID to exclude from broadcast
        """
        if session_id not in self.session_participants:
            return
        
        participants = self.session_participants[session_id].copy()
        if exclude_user:
            participants.discard(exclude_user)
        
        # Send to all participants
        for user_id in participants:
            await self.send_personal_message(user_id, message)
    
    async def broadcast_to_all(self, message: WebSocketMessage):
        """
        Broadcast a message to all connected users.
        
        Args:
            message: Message to broadcast
        """
        for user_id in list(self.active_connections.keys()):
            await self.send_personal_message(user_id, message)
    
    def get_session_participants(self, session_id: UUID) -> List[UUID]:
        """
        Get list of participants in a session.
        
        Args:
            session_id: ID of the session
            
        Returns:
            List of user IDs in the session
        """
        return list(self.session_participants.get(session_id, set()))
    
    def get_user_session(self, user_id: UUID) -> Optional[UUID]:
        """
        Get the session ID for a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Session ID if user is in a session, None otherwise
        """
        return self.user_sessions.get(user_id)
    
    def is_user_connected(self, user_id: UUID) -> bool:
        """
        Check if a user is connected.
        
        Args:
            user_id: ID of the user
            
        Returns:
            True if user is connected, False otherwise
        """
        return user_id in self.active_connections
    
    def get_connection_count(self) -> int:
        """Get total number of active connections."""
        return len(self.active_connections)
    
    def get_session_count(self) -> int:
        """Get total number of active sessions."""
        return len(self.session_participants)
    
    async def handle_heartbeat(self, user_id: UUID):
        """
        Handle heartbeat from a user.
        
        Args:
            user_id: ID of the user sending heartbeat
        """
        if user_id in self.connection_metadata:
            self.connection_metadata[user_id]["last_heartbeat"] = asyncio.get_event_loop().time()
    
    async def cleanup_stale_connections(self, timeout_seconds: int = 300):
        """
        Clean up connections that haven't sent heartbeat recently.
        
        Args:
            timeout_seconds: Timeout in seconds for stale connections
        """
        current_time = asyncio.get_event_loop().time()
        stale_users = []
        
        for user_id, metadata in self.connection_metadata.items():
            last_heartbeat = metadata.get("last_heartbeat", 0)
            if current_time - last_heartbeat > timeout_seconds:
                stale_users.append(user_id)
        
        for user_id in stale_users:
            logger.warning(f"Cleaning up stale connection for user {user_id}")
            await self.disconnect(user_id)


# Global connection manager instance
connection_manager = ConnectionManager()


async def get_connection_manager() -> ConnectionManager:
    """Get the global connection manager instance."""
    return connection_manager
