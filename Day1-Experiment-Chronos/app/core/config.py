"""
Configuration management for Project Chronos.

This module provides environment-based configuration using Pydantic Settings,
supporting the ADHD-focused task management system.
"""

from typing import Optional, List, Union
from pydantic import Field, AnyHttpUrl, EmailStr, AnyUrl
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings with environment variable support.
    
    Supports ADHD-specific features like AI integration and adaptive filtering.
    """
    
    # Application
    app_name: str = "Project Chronos - Task Management"
    app_version: str = "0.1.0"
    debug: bool = False
    testing: bool = False
    log_level: str = "INFO"
    
    # Database
    database_url: str = Field(
        default="postgresql+asyncpg://chronos:chronos@localhost/chronos",
        description="PostgreSQL database URL with async support"
    )

    @property
    def DATABASE_URL(self) -> str:
        """Uppercase alias for database_url."""
        return self.database_url

    @property
    def DEBUG(self) -> bool:
        """Uppercase alias for debug."""
        return self.debug

    @property
    def TESTING(self) -> bool:
        """Uppercase alias for testing."""
        return self.testing

    @property
    def PROJECT_NAME(self) -> str:
        """Uppercase alias for app_name."""
        return self.app_name

    @property
    def PROJECT_DESCRIPTION(self) -> str:
        """Project description."""
        return "Neuro-affirming digital planner for ADHD users"

    @property
    def VERSION(self) -> str:
        """Uppercase alias for app_version."""
        return self.app_version

    @property
    def API_V1_STR(self) -> str:
        """API v1 prefix."""
        return "/api/v1"
    
    # Security
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        description="Secret key for JWT token generation"
    )
    access_token_expire_minutes: int = Field(
        default=15,
        description="Access token expiration time in minutes"
    )
    refresh_token_expire_days: int = Field(
        default=7,
        description="Refresh token expiration time in days"
    )
    algorithm: str = Field(
        default="HS256",
        description="JWT algorithm"
    )
    
    # AI Integration
    openai_api_key: Optional[str] = Field(
        default=None,
        description="OpenAI API key for task chunking"
    )
    anthropic_api_key: Optional[str] = Field(
        default=None,
        description="Anthropic API key for task chunking"
    )
    ai_cache_ttl: int = Field(
        default=3600,
        description="AI response cache TTL in seconds"
    )
    
    # Redis
    redis_url: str = Field(
        default="redis://localhost:6379/0",
        description="Redis URL for caching and sessions"
    )
    
    # Celery
    celery_broker_url: str = Field(
        default="redis://localhost:6379/1",
        description="Celery broker URL for background tasks"
    )
    celery_result_backend: str = Field(
        default="redis://localhost:6379/2",
        description="Celery result backend URL"
    )
    
    # ADHD-specific settings
    default_chunk_size: str = Field(
        default="small",
        description="Default task chunk size for AI chunking"
    )
    max_task_jar_size: int = Field(
        default=10,
        description="Maximum number of tasks in task jar selection"
    )
    energy_levels: list[str] = Field(
        default=["low", "medium", "high"],
        description="Available energy levels for task filtering"
    )
    default_focus_duration: int = Field(
        default=25,
        description="Default focus session duration in minutes"
    )
    default_break_duration: int = Field(
        default=5,
        description="Default break duration in minutes"
    )
    max_task_chunk_size: int = Field(
        default=30,
        description="Maximum task chunk size in minutes"
    )
    notification_persistence_hours: int = Field(
        default=24,
        description="How long to persist notifications in hours"
    )

    # CORS Configuration
    backend_cors_origins: str = Field(
        default="",
        description="Comma-separated list of allowed CORS origins"
    )

    @property
    def BACKEND_CORS_ORIGINS(self) -> List[str]:
        """Parse CORS origins from comma-separated string."""
        if not self.backend_cors_origins:
            return ["*"]  # Allow all origins in development
        return [origin.strip() for origin in self.backend_cors_origins.split(",")]

    # Email Configuration (optional)
    smtp_tls: bool = True
    smtp_port: Optional[int] = None
    smtp_host: Optional[str] = None
    smtp_user: Optional[str] = None
    smtp_password: Optional[str] = None
    emails_from_email: Optional[EmailStr] = None
    emails_from_name: Optional[str] = None

    # Rate Limiting
    rate_limit_per_minute: int = 60
    rate_limit_burst: int = 10
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "extra": "ignore"  # Allow extra fields from environment
    }


# Global settings instance
settings = Settings()
