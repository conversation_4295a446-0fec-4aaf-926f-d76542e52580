# Core FastAPI and web framework dependencies
fastapi>=0.109.0
uvicorn[standard]>=0.27.0
python-multipart>=0.0.6
jinja2>=3.1.3

# Database dependencies
sqlalchemy==2.0.25
alembic==1.13.1
psycopg2-binary==2.9.9

# Data processing and visualization
plotly==5.18.0
pandas==2.2.0

# Authentication and security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-decouple>=3.8
ldap3>=2.9.1

# HTTP client for API calls
requests>=2.31.0
httpx==0.26.0
aiohttp>=3.9.1

# Data validation and serialization
pydantic==2.5.3
pydantic-settings==2.1.0

# Machine Learning and AI dependencies
scikit-learn==1.5.2  # Updated from 1.4.0 to fix CVE-2024-5206 (sensitive data leakage)
numpy==1.26.3
scipy==1.12.0
joblib==1.3.2

# Natural Language Processing
nltk==3.9.1  # Updated from 3.8.1 to fix CVE-2024-39705 (unsafe deserialization)

# Advanced ML libraries for Enterprise AI
xgboost==2.0.3
lightgbm==4.6.0  # Updated from 4.3.0 to fix CVE-2024-43598 (remote code execution)
catboost==1.2.2

# Data manipulation and analysis
pandas==2.2.0
matplotlib==3.8.2
seaborn==0.13.2

# Utilities
python-dateutil==2.8.2
pytz==2024.1
click==8.1.7

# Development and testing dependencies
pytest==8.0.0
pytest-asyncio==0.23.4
pytest-cov==4.1.0
black==24.3.0  # Updated from 24.1.1 to fix CVE-2024-21503 (ReDoS vulnerability)
flake8==7.0.0
mypy==1.8.0

# Logging and monitoring
structlog==24.1.0

# File handling
openpyxl==3.1.2
xlsxwriter==3.2.0
reportlab==4.0.9

# Image processing (for future features)
Pillow>=10.2.0

# Async support
aiofiles==23.2.1

# Environment and configuration
python-dotenv==1.0.1

# API documentation
swagger-ui-bundle==0.0.9

# Caching
redis==5.0.1
cachetools==5.3.2

# Background tasks
celery==5.3.6

# Email support
fastapi-mail==1.4.1

# File upload and storage
boto3==1.34.34

# Monitoring and health checks
prometheus-client==0.20.0

# Rate limiting
slowapi==0.1.9

# CORS support
fastapi-cors==0.0.6

# WebSocket support
websockets==12.0

# JSON handling
orjson==3.9.15

# Type hints
typing-extensions==4.9.0

# Optional dependencies for enhanced features
# Uncomment as needed:

# Advanced ML models
# tensorflow==2.15.0
# torch==2.1.1
# transformers==4.36.0

# Advanced NLP
# spacy==3.7.2
# gensim==4.3.2

# Computer vision
# opencv-python==********

# Graph databases
# neo4j==5.14.1

# Time series analysis
# statsmodels==0.14.0

# Advanced plotting
# bokeh==3.3.2
# altair==5.2.0

# Jupyter notebook support
# jupyter==1.0.0
# ipykernel==6.26.0

# API rate limiting
# limits==3.6.0

# Advanced caching
# diskcache==5.6.3

# Message queues
# pika==1.3.2  # RabbitMQ
# kafka-python==2.0.2  # Apache Kafka

# Distributed computing
# dask==2023.11.0

# Advanced database features
# sqlalchemy-utils==0.41.1
# psycopg2cffi==2.9.0  # Alternative PostgreSQL adapter

# Security enhancements
# cryptography==41.0.7
# bcrypt==4.1.2

# Performance monitoring
# py-spy==0.3.14
# memory-profiler==0.61.0

# Code quality
# pre-commit==3.6.0
# bandit==1.7.5  # Security linting

# Documentation
# mkdocs==1.5.3
# mkdocs-material==9.4.8

# API testing
# httpx==0.25.2
# respx==0.20.2

# Load testing
# locust==2.17.0

# Configuration management
# dynaconf==3.2.4

# Internationalization
# babel==2.13.1

# Task scheduling
# apscheduler==3.10.4

# File watching
# watchdog==3.0.0

# Progress bars
# tqdm==4.66.1

# Rich text and formatting
# rich==13.7.0

# Command line interfaces
# typer==0.9.0
# click==8.1.7

# Data validation
# cerberus==1.3.5
# marshmallow==3.20.1

# API versioning
# fastapi-versioning==0.10.0

# Health checks
# healthcheck==1.3.3

# Metrics and telemetry
# opentelemetry-api==1.21.0
# opentelemetry-sdk==1.21.0

# Container support
# gunicorn==21.2.0

# Development tools
# ipython==8.17.2
# jupyter-lab==4.0.8
